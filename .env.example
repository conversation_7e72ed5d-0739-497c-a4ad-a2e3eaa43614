# إعدادات قاعدة البيانات PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=scalping_trading
DB_USER=scalping_user
DB_PASSWORD=8576
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_ECHO=false
DB_ECHO_POOL=false

# إعدادات Redis للتخزين المؤقت
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# إعدادات Pocket Option
POCKET_OPTION_SSID=42["auth",{"session":"sblkni7rg1bo7pqipkqb9mcv66","isDemo":1,"uid":99087051,"platform":2,"isFastHistory":true}]

# إعدادات التداول
MIN_TRADE_AMOUNT=1.0
MAX_TRADE_AMOUNT=100.0
DEFAULT_TRADE_AMOUNT=5.0
MAX_DAILY_LOSS=200.0
MAX_CONSECUTIVE_LOSSES=10
MAX_WEEKLY_LOSS=1000.0
AVOID_FIRST_MINUTES=5
AVOID_LAST_MINUTES=5
TRADING_HOURS_START=08:00
TRADING_HOURS_END=22:00
MIN_CONFIDENCE_SCORE=80.0

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_DIR=logs

# إعدادات الذكاء الاصطناعي
AI_MODELS_DIR=ai_models/saved_models
TRAINING_DATA_DIR=data/training
RETRAIN_INTERVAL=24
MIN_TRAINING_SAMPLES=1000
VALIDATION_SPLIT=0.2
TEST_SPLIT=0.1

# إعدادات XGBoost
XGB_N_ESTIMATORS=100
XGB_MAX_DEPTH=6
XGB_LEARNING_RATE=0.1
XGB_SUBSAMPLE=0.8
XGB_COLSAMPLE=0.8
XGB_RANDOM_STATE=42
XGB_N_JOBS=-1
XGB_MIN_CONFIDENCE=80.0
XGB_FEATURE_THRESHOLD=0.01

# إعدادات LSTM
LSTM_SEQUENCE_LENGTH=100
LSTM_HIDDEN_UNITS=50
LSTM_NUM_LAYERS=2
LSTM_DROPOUT=0.2
LSTM_LEARNING_RATE=0.001
LSTM_EPOCHS=50
LSTM_BATCH_SIZE=32
LSTM_PATIENCE=10
LSTM_MIN_DELTA=0.001
LSTM_VALIDATION_SPLIT=0.2

# إعدادات Random Forest
RF_N_ESTIMATORS=100
RF_MAX_DEPTH=10
RF_MIN_SAMPLES_SPLIT=5
RF_MIN_SAMPLES_LEAF=2
RF_MAX_FEATURES=sqrt
RF_RANDOM_STATE=42
RF_N_JOBS=-1
RF_BOOTSTRAP=true

# إعدادات Ensemble
ENSEMBLE_VOTING=weighted
ENSEMBLE_XGB_WEIGHT=0.5
ENSEMBLE_LSTM_WEIGHT=0.3
ENSEMBLE_RF_WEIGHT=0.2
ENSEMBLE_MIN_AGREEMENT=2
ENSEMBLE_CONFIDENCE_THRESHOLD=75.0

# إعدادات تقييم النماذج
CV_FOLDS=5
MIN_ACCURACY=0.65
MIN_PRECISION=0.60
MIN_RECALL=0.60
PERFORMANCE_DEGRADATION=0.05

# إعدادات الميزات
AI_MAX_FEATURES=50

# إعدادات حفظ النماذج
KEEP_MODEL_VERSIONS=5

# إعدادات البيئة
ENVIRONMENT=development
DEBUG=false
TESTING=false
