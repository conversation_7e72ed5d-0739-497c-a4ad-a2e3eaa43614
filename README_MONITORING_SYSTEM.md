# نظام المراقبة والأداء المتكامل
# Integrated Monitoring and Performance System

## نظرة عامة | Overview

تم تطوير نظام مراقبة وأداء متكامل شامل لمراقبة وتحليل أداء نظام التداول السكالبينج للـ70 زوج عملة. يتضمن النظام خمسة مكونات رئيسية تعمل بشكل متناسق لضمان الأداء الأمثل والموثوقية العالية.

A comprehensive integrated monitoring and performance system has been developed to monitor and analyze the performance of the scalping trading system for 70 currency pairs. The system includes five main components working in harmony to ensure optimal performance and high reliability.

## المكونات الرئيسية | Main Components

### 1. نظام مراقبة الأداء المتقدم | Advanced Performance Monitor
**الملف:** `execution/advanced_performance_monitor.py`

**الوظائف الرئيسية:**
- مراقبة أداء جمع البيانات لكل زوج عملة
- قياس أوقات حساب المؤشرات الفنية
- مراقبة استخدام الموارد (CPU, Memory, Network)
- تحليل زمن الاستجابة لقاعدة البيانات و Redis
- إنشاء تقارير أداء شاملة مع التوصيات

**المقاييس المراقبة:**
- سرعة جمع البيانات
- وقت حساب المؤشرات
- زمن استجابة قاعدة البيانات
- استخدام الذاكرة والمعالج
- زمن استجابة الشبكة

### 2. نظام تسجيل العمليات المتقدم | Advanced Operations Logger
**الملف:** `execution/advanced_operations_logger.py`

**الوظائف الرئيسية:**
- تسجيل جميع العمليات والأحداث في النظام
- تصنيف السجلات حسب النوع والخطورة
- نظام مخزن مؤقت ذكي لتحسين الأداء
- تدوير وضغط ملفات السجلات تلقائياً
- البحث والتحليل في السجلات

**فئات السجلات:**
- عمليات النظام (System)
- العمليات التشغيلية (Operations)
- الأداء (Performance)
- الأخطاء (Errors)
- التدقيق (Audit)
- الأمان (Security)

### 3. نظام التنبيهات المتقدم | Advanced Alert System
**الملف:** `execution/advanced_alert_system.py`

**الوظائف الرئيسية:**
- إطلاق تنبيهات ذكية عند حدوث مشاكل
- تصنيف التنبيهات حسب الخطورة
- إرسال الإشعارات عبر قنوات متعددة
- نظام تصعيد التنبيهات
- تتبع وحل التنبيهات

**قنوات الإشعار:**
- البريد الإلكتروني (Email)
- Webhook
- Telegram
- Slack
- ملفات السجل
- وحدة التحكم

**مستويات الخطورة:**
- منخفض (Low)
- متوسط (Medium)
- عالي (High)
- حرج (Critical)
- طوارئ (Emergency)

### 4. نظام الإحصائيات | Performance Statistics System
**الملف:** `execution/performance_statistics_system.py`

**الوظائف الرئيسية:**
- جمع وتحليل إحصائيات الأداء
- حساب المتوسطات والاتجاهات
- تحليل مقارن بين الأصول
- إنشاء تقارير إحصائية مفصلة
- نظام كاش ذكي لتحسين الأداء

**الإطارات الزمنية:**
- دقيقة واحدة (1m)
- 5 دقائق (5m)
- 15 دقيقة (15m)
- ساعة واحدة (1h)
- 4 ساعات (4h)
- يوم واحد (1d)
- أسبوع واحد (1w)

### 5. نظام مراقبة جودة البيانات | Data Quality Monitor
**الملف:** `execution/data_quality_monitor.py`

**الوظائف الرئيسية:**
- مراقبة جودة واكتمال البيانات
- اكتشاف البيانات المفقودة أو المكررة
- فحص صحة القيم والأسعار
- مراقبة حداثة البيانات
- تحليل الشذوذ في الأسعار والحجم

**فحوصات الجودة:**
- البيانات المفقودة
- البيانات المكررة
- القيم غير الصحيحة
- الفجوات الزمنية
- شذوذ الأسعار
- شذوذ الحجم
- البيانات القديمة

## التثبيت والتشغيل | Installation and Running

### المتطلبات | Requirements
```bash
pip install -r requirements.txt
```

### تشغيل الاختبار الشامل | Running Comprehensive Test

**Windows:**
```cmd
test_monitoring.bat
```

**Linux/Mac:**
```bash
chmod +x test_monitoring.sh
./test_monitoring.sh
```

**Python مباشرة:**
```bash
python run_comprehensive_monitoring_test.py
```

### تشغيل اختبار unittest | Running unittest
```bash
python -m pytest tests/test_comprehensive_monitoring_system.py -v
```

## استخدام النظام | System Usage

### بدء أنظمة المراقبة | Starting Monitoring Systems

```python
from execution.advanced_performance_monitor import start_performance_monitoring
from execution.advanced_operations_logger import start_operations_logging
from execution.advanced_alert_system import start_alert_system
from execution.performance_statistics_system import start_statistics_collection
from execution.data_quality_monitor import start_quality_monitoring

# بدء جميع الأنظمة
start_performance_monitoring()
start_operations_logging()
start_alert_system()
start_statistics_collection()
start_quality_monitoring()
```

### تسجيل البيانات | Recording Data

```python
from execution.advanced_performance_monitor import record_performance_metric, PerformanceMetricType
from execution.advanced_operations_logger import log_operation, OperationType, LogLevel, LogCategory
from execution.performance_statistics_system import record_statistic, StatisticType

# تسجيل مقياس أداء
record_performance_metric({
    'metric_type': PerformanceMetricType.DATA_COLLECTION_SPEED,
    'value': 2.5,
    'asset': 'EURUSD',
    'unit': 'seconds'
})

# تسجيل عملية
log_operation(
    OperationType.DATA_COLLECTION,
    LogLevel.INFO,
    LogCategory.OPERATIONS,
    "تم جمع البيانات بنجاح",
    asset='EURUSD',
    duration_ms=250
)

# تسجيل إحصائية
record_statistic(
    StatisticType.DATA_COLLECTION_RATE,
    60,
    'EURUSD'
)
```

### إطلاق التنبيهات | Triggering Alerts

```python
from execution.advanced_alert_system import trigger_alert, AlertType, AlertSeverity

# إطلاق تنبيه
alert_id = trigger_alert(
    AlertType.PERFORMANCE_ISSUE,
    AlertSeverity.HIGH,
    "مشكلة في الأداء",
    "انخفاض في سرعة جمع البيانات",
    asset='EURUSD',
    details={'current_speed': 5.2, 'expected_speed': 2.0}
)
```

### الحصول على التقارير | Getting Reports

```python
from execution.advanced_performance_monitor import get_performance_report
from execution.advanced_operations_logger import get_operations_report
from execution.advanced_alert_system import get_alert_report
from execution.performance_statistics_system import get_performance_report
from execution.data_quality_monitor import get_quality_report

# تقارير شاملة
performance_report = get_performance_report()
operations_report = get_operations_report(hours=24)
alerts_report = get_alert_report(hours=24)
statistics_report = get_performance_report()
quality_report = get_quality_report()
```

## الميزات المتقدمة | Advanced Features

### 1. التكامل التلقائي | Automatic Integration
- تكامل تلقائي بين جميع أنظمة المراقبة
- مشاركة البيانات والأحداث بين الأنظمة
- تنسيق التنبيهات والإجراءات

### 2. الأداء المحسن | Optimized Performance
- نظام مخزن مؤقت ذكي
- معالجة غير متزامنة
- ضغط وتدوير البيانات تلقائياً

### 3. المرونة والقابلية للتوسع | Flexibility and Scalability
- إعدادات قابلة للتخصيص
- دعم إضافة مقاييس جديدة
- قابلية التوسع الأفقي

### 4. الموثوقية | Reliability
- نظام تعافي من الأخطاء
- نسخ احتياطية تلقائية
- مراقبة صحة النظام

## التكوين | Configuration

### إعدادات الأداء | Performance Settings
```python
# في ملف config/trading_config.py
MONITORING_CONFIG = {
    'performance_monitor': {
        'collection_interval': 30,  # ثانية
        'retention_days': 30,
        'alert_thresholds': {
            'cpu_usage': 80,
            'memory_usage': 85,
            'response_time': 5000  # مللي ثانية
        }
    }
}
```

### إعدادات التنبيهات | Alert Settings
```python
ALERT_CONFIG = {
    'email': {
        'enabled': True,
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'recipients': ['<EMAIL>']
    },
    'telegram': {
        'enabled': True,
        'bot_token': 'YOUR_BOT_TOKEN',
        'chat_id': 'YOUR_CHAT_ID'
    }
}
```

## الاختبارات | Testing

### اختبارات الوحدة | Unit Tests
```bash
python -m pytest tests/test_comprehensive_monitoring_system.py::TestComprehensiveMonitoringSystem::test_performance_monitor_system -v
```

### اختبار التكامل | Integration Test
```bash
python -m pytest tests/test_comprehensive_monitoring_system.py::TestComprehensiveMonitoringSystem::test_system_integration -v
```

### اختبار الأداء | Performance Test
```bash
python -m pytest tests/test_comprehensive_monitoring_system.py::TestComprehensiveMonitoringSystem::test_monitoring_systems_coordination -v
```

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

1. **فشل في بدء النظام**
   - تحقق من اتصال قاعدة البيانات
   - تأكد من وجود الأذونات المطلوبة
   - فحص ملفات السجل

2. **بطء في الأداء**
   - زيادة فترات التجميع
   - تحسين إعدادات المخزن المؤقت
   - مراقبة استخدام الموارد

3. **عدم وصول التنبيهات**
   - فحص إعدادات قنوات الإشعار
   - تحقق من صحة المعلومات
   - اختبار الاتصال

## الدعم والمساهمة | Support and Contribution

### الحصول على المساعدة | Getting Help
- مراجعة ملفات السجل في مجلد `logs/`
- فحص حالة النظام باستخدام `get_system_status()`
- تشغيل الاختبار الشامل للتشخيص

### المساهمة | Contributing
- إضافة مقاييس أداء جديدة
- تحسين خوارزميات التحليل
- إضافة قنوات إشعار جديدة
- تحسين واجهات المستخدم

## الترخيص | License

هذا النظام جزء من مشروع نظام التداول السكالبينج ومرخص تحت نفس شروط المشروع الأساسي.

This system is part of the Scalping Trading System project and is licensed under the same terms as the main project.

---

**تم التطوير بواسطة:** فريق تطوير نظام التداول السكالبينج  
**تاريخ آخر تحديث:** 2025-06-21  
**الإصدار:** 2.0.0
