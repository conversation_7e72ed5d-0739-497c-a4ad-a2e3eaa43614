"""
محرك تقييم درجة الثقة في التنبؤات
Confidence Evaluation Engine
"""

import os
import sys
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import math

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.currency_pairs import CURRENCY_PAIRS
from database.repository import HistoricalDataRepository
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("confidence_evaluation_engine")

class ConfidenceEvaluationEngine:
    """محرك تقييم درجة الثقة في التنبؤات"""

    def __init__(self):
        """تهيئة محرك تقييم الثقة"""
        self.db_repo = HistoricalDataRepository()
        
        # معايير تقييم الثقة
        self.confidence_criteria = {
            'model_consensus': {
                'weight': 0.25,
                'min_threshold': 60.0,
                'max_threshold': 95.0
            },
            'signal_strength': {
                'weight': 0.20,
                'strength_mapping': {
                    'قوية جداً': 95,
                    'قوية': 85,
                    'متوسطة': 70,
                    'ضعيفة': 55,
                    'ضعيفة جداً': 40
                }
            },
            'historical_accuracy': {
                'weight': 0.20,
                'lookback_period': 30  # آخر 30 تنبؤ
            },
            'market_volatility': {
                'weight': 0.15,
                'low_volatility_bonus': 10,
                'high_volatility_penalty': -15
            },
            'data_quality': {
                'weight': 0.10,
                'min_data_points': 50,
                'optimal_data_points': 200
            },
            'time_consistency': {
                'weight': 0.10,
                'peak_hours_bonus': 5,
                'off_hours_penalty': -10
            }
        }
        
        # إحصائيات الأداء التاريخي
        self.historical_performance = {}
        
        # ساعات الذروة للتداول
        self.peak_trading_hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]  # UTC
        
        logger.info("تم تهيئة محرك تقييم درجة الثقة")

    async def evaluate_prediction_confidence(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم درجة الثقة في التنبؤ"""
        try:
            currency_pair = prediction_data.get('currency_pair')
            logger.info(f"🔍 تقييم درجة الثقة للتنبؤ: {currency_pair}")
            
            # جمع معايير التقييم
            evaluation_scores = {}
            
            # 1. إجماع النماذج
            model_consensus_score = self._evaluate_model_consensus(prediction_data)
            evaluation_scores['model_consensus'] = model_consensus_score
            
            # 2. قوة الإشارة
            signal_strength_score = self._evaluate_signal_strength(prediction_data)
            evaluation_scores['signal_strength'] = signal_strength_score
            
            # 3. الدقة التاريخية
            historical_accuracy_score = await self._evaluate_historical_accuracy(currency_pair)
            evaluation_scores['historical_accuracy'] = historical_accuracy_score
            
            # 4. تقلبات السوق
            market_volatility_score = await self._evaluate_market_volatility(currency_pair)
            evaluation_scores['market_volatility'] = market_volatility_score
            
            # 5. جودة البيانات
            data_quality_score = await self._evaluate_data_quality(currency_pair)
            evaluation_scores['data_quality'] = data_quality_score
            
            # 6. اتساق الوقت
            time_consistency_score = self._evaluate_time_consistency()
            evaluation_scores['time_consistency'] = time_consistency_score
            
            # حساب النتيجة الإجمالية
            final_confidence = self._calculate_weighted_confidence(evaluation_scores)
            
            # تصنيف مستوى الثقة
            confidence_level = self._classify_confidence_level(final_confidence)
            
            # إنشاء التقرير النهائي
            confidence_report = {
                'currency_pair': currency_pair,
                'final_confidence_score': final_confidence,
                'confidence_level': confidence_level,
                'evaluation_breakdown': evaluation_scores,
                'criteria_weights': self.confidence_criteria,
                'recommendations': self._generate_confidence_recommendations(final_confidence, evaluation_scores),
                'risk_assessment': self._assess_confidence_risk(final_confidence, evaluation_scores),
                'timestamp': datetime.now().isoformat(),
                'evaluation_id': self._generate_evaluation_id(currency_pair)
            }
            
            logger.info(f"✅ تقييم الثقة: {final_confidence:.1f}% - {confidence_level}")
            return confidence_report
            
        except Exception as e:
            logger.error(f"خطأ في تقييم درجة الثقة: {str(e)}")
            raise

    def _evaluate_model_consensus(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم إجماع النماذج"""
        try:
            ai_prediction = prediction_data.get('ai_prediction', {})
            consensus_ratio = ai_prediction.get('consensus_ratio', 0)
            
            # تحويل نسبة الإجماع إلى نقاط
            criteria = self.confidence_criteria['model_consensus']
            
            if consensus_ratio >= 90:
                score = 95
            elif consensus_ratio >= 80:
                score = 85
            elif consensus_ratio >= 70:
                score = 75
            elif consensus_ratio >= 60:
                score = 65
            else:
                score = 50
            
            return {
                'score': score,
                'consensus_ratio': consensus_ratio,
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 85 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم إجماع النماذج: {str(e)}")
            return {'score': 50, 'weight': 0.25, 'weighted_score': 12.5, 'evaluation': 'غير محدد'}

    def _evaluate_signal_strength(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم قوة الإشارة"""
        try:
            signal_strength = prediction_data.get('signal_strength', 'متوسطة')
            criteria = self.confidence_criteria['signal_strength']
            
            score = criteria['strength_mapping'].get(signal_strength, 70)
            
            return {
                'score': score,
                'signal_strength': signal_strength,
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 85 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم قوة الإشارة: {str(e)}")
            return {'score': 70, 'weight': 0.20, 'weighted_score': 14.0, 'evaluation': 'غير محدد'}

    async def _evaluate_historical_accuracy(self, currency_pair: str) -> Dict[str, Any]:
        """تقييم الدقة التاريخية"""
        try:
            criteria = self.confidence_criteria['historical_accuracy']
            
            # محاكاة الدقة التاريخية (سيتم ربطها بنظام التتبع لاحقاً)
            if currency_pair not in self.historical_performance:
                # قيم افتراضية للمحاكاة
                historical_accuracy = np.random.uniform(65, 85)
                total_predictions = np.random.randint(20, 100)
                
                self.historical_performance[currency_pair] = {
                    'accuracy': historical_accuracy,
                    'total_predictions': total_predictions,
                    'last_update': datetime.now()
                }
            
            performance = self.historical_performance[currency_pair]
            accuracy = performance['accuracy']
            
            # تحويل الدقة إلى نقاط
            if accuracy >= 80:
                score = 90
            elif accuracy >= 75:
                score = 80
            elif accuracy >= 70:
                score = 70
            elif accuracy >= 65:
                score = 60
            else:
                score = 50
            
            return {
                'score': score,
                'historical_accuracy': accuracy,
                'total_predictions': performance['total_predictions'],
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 80 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم الدقة التاريخية: {str(e)}")
            return {'score': 70, 'weight': 0.20, 'weighted_score': 14.0, 'evaluation': 'غير محدد'}

    async def _evaluate_market_volatility(self, currency_pair: str) -> Dict[str, Any]:
        """تقييم تقلبات السوق"""
        try:
            criteria = self.confidence_criteria['market_volatility']
            
            # جلب البيانات الحديثة لحساب التقلبات
            recent_candles = self.db_repo.get_latest_candles(currency_pair, count=20)
            
            if not recent_candles or len(recent_candles) < 10:
                # قيم افتراضية
                volatility = np.random.uniform(0.5, 2.0)
            else:
                # حساب التقلبات الفعلية
                prices = [float(candle['close']) for candle in recent_candles]
                returns = np.diff(prices) / prices[:-1]
                volatility = np.std(returns) * 100
            
            # تقييم التقلبات
            base_score = 70
            
            if volatility < 0.5:  # تقلبات منخفضة
                score = base_score + criteria['low_volatility_bonus']
                volatility_level = 'منخفضة'
            elif volatility > 1.5:  # تقلبات عالية
                score = base_score + criteria['high_volatility_penalty']
                volatility_level = 'عالية'
            else:  # تقلبات متوسطة
                score = base_score
                volatility_level = 'متوسطة'
            
            score = max(30, min(95, score))  # ضمان البقاء في النطاق
            
            return {
                'score': score,
                'volatility': volatility,
                'volatility_level': volatility_level,
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 80 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم تقلبات السوق: {str(e)}")
            return {'score': 70, 'weight': 0.15, 'weighted_score': 10.5, 'evaluation': 'غير محدد'}

    async def _evaluate_data_quality(self, currency_pair: str) -> Dict[str, Any]:
        """تقييم جودة البيانات"""
        try:
            criteria = self.confidence_criteria['data_quality']
            
            # عدد الشموع المتاحة
            candles_count = self.db_repo.get_candles_count(currency_pair)
            
            # تقييم جودة البيانات
            if candles_count >= criteria['optimal_data_points']:
                score = 90
                quality_level = 'ممتازة'
            elif candles_count >= criteria['min_data_points']:
                # تدرج خطي بين الحد الأدنى والأمثل
                ratio = (candles_count - criteria['min_data_points']) / (criteria['optimal_data_points'] - criteria['min_data_points'])
                score = 60 + (ratio * 30)
                quality_level = 'جيدة'
            else:
                # بيانات قليلة
                score = 40 + (candles_count / criteria['min_data_points'] * 20)
                quality_level = 'ضعيفة'
            
            score = max(30, min(95, score))
            
            return {
                'score': score,
                'candles_count': candles_count,
                'quality_level': quality_level,
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 80 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم جودة البيانات: {str(e)}")
            return {'score': 70, 'weight': 0.10, 'weighted_score': 7.0, 'evaluation': 'غير محدد'}

    def _evaluate_time_consistency(self) -> Dict[str, Any]:
        """تقييم اتساق الوقت"""
        try:
            criteria = self.confidence_criteria['time_consistency']
            current_hour = datetime.now().hour
            
            base_score = 70
            
            if current_hour in self.peak_trading_hours:
                score = base_score + criteria['peak_hours_bonus']
                time_evaluation = 'ساعات ذروة'
            else:
                score = base_score + criteria['off_hours_penalty']
                time_evaluation = 'خارج ساعات الذروة'
            
            score = max(30, min(95, score))
            
            return {
                'score': score,
                'current_hour': current_hour,
                'time_evaluation': time_evaluation,
                'weight': criteria['weight'],
                'weighted_score': score * criteria['weight'],
                'evaluation': 'ممتاز' if score >= 80 else 'جيد' if score >= 70 else 'متوسط' if score >= 60 else 'ضعيف'
            }
            
        except Exception as e:
            logger.warning(f"خطأ في تقييم اتساق الوقت: {str(e)}")
            return {'score': 70, 'weight': 0.10, 'weighted_score': 7.0, 'evaluation': 'غير محدد'}

    def _calculate_weighted_confidence(self, evaluation_scores: Dict[str, Any]) -> float:
        """حساب درجة الثقة المرجحة"""
        try:
            total_weighted_score = 0
            total_weight = 0

            for criterion, score_data in evaluation_scores.items():
                weighted_score = score_data.get('weighted_score', 0)
                weight = score_data.get('weight', 0)

                total_weighted_score += weighted_score
                total_weight += weight

            # حساب المتوسط المرجح
            if total_weight > 0:
                final_confidence = (total_weighted_score / total_weight) * 100
            else:
                final_confidence = 50.0

            # ضمان البقاء في النطاق المطلوب
            final_confidence = max(20.0, min(95.0, final_confidence))

            return round(final_confidence, 2)

        except Exception as e:
            logger.error(f"خطأ في حساب الثقة المرجحة: {str(e)}")
            return 50.0

    def _classify_confidence_level(self, confidence_score: float) -> str:
        """تصنيف مستوى الثقة"""
        if confidence_score >= 85:
            return "ثقة عالية جداً"
        elif confidence_score >= 75:
            return "ثقة عالية"
        elif confidence_score >= 65:
            return "ثقة متوسطة"
        elif confidence_score >= 55:
            return "ثقة منخفضة"
        else:
            return "ثقة منخفضة جداً"

    def _generate_confidence_recommendations(self, confidence_score: float, evaluation_scores: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات بناءً على تقييم الثقة"""
        try:
            recommendations = []

            # توصيات عامة بناءً على النتيجة الإجمالية
            if confidence_score >= 80:
                recommendations.append("✅ مستوى ثقة ممتاز - يُنصح بالتنفيذ")
                recommendations.append("💰 يمكن استخدام حجم صفقة أكبر")
            elif confidence_score >= 70:
                recommendations.append("✅ مستوى ثقة جيد - يُنصح بالتنفيذ الحذر")
                recommendations.append("💰 استخدام حجم صفقة متوسط")
            elif confidence_score >= 60:
                recommendations.append("⚠️ مستوى ثقة متوسط - تنفيذ حذر جداً")
                recommendations.append("💰 استخدام حجم صفقة صغير")
            else:
                recommendations.append("❌ مستوى ثقة منخفض - تجنب التنفيذ")
                recommendations.append("🛑 انتظار فرصة أفضل")

            # توصيات محددة بناءً على نقاط الضعف
            for criterion, score_data in evaluation_scores.items():
                score = score_data.get('score', 0)

                if score < 60:
                    if criterion == 'model_consensus':
                        recommendations.append("🔄 تحسين إجماع النماذج مطلوب")
                    elif criterion == 'signal_strength':
                        recommendations.append("📊 انتظار إشارة أقوى")
                    elif criterion == 'historical_accuracy':
                        recommendations.append("📈 مراجعة الأداء التاريخي")
                    elif criterion == 'market_volatility':
                        recommendations.append("🌊 حذر من تقلبات السوق")
                    elif criterion == 'data_quality':
                        recommendations.append("📊 تحسين جودة البيانات مطلوب")
                    elif criterion == 'time_consistency':
                        recommendations.append("⏰ انتظار توقيت أفضل")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            return ["خطأ في تحليل التوصيات"]

    def _assess_confidence_risk(self, confidence_score: float, evaluation_scores: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم مخاطر الثقة"""
        try:
            # حساب مستوى المخاطر الإجمالي
            risk_score = 100 - confidence_score

            if risk_score <= 15:
                risk_level = "منخفض جداً"
                risk_color = "green"
            elif risk_score <= 25:
                risk_level = "منخفض"
                risk_color = "lightgreen"
            elif risk_score <= 35:
                risk_level = "متوسط"
                risk_color = "yellow"
            elif risk_score <= 45:
                risk_level = "مرتفع"
                risk_color = "orange"
            else:
                risk_level = "مرتفع جداً"
                risk_color = "red"

            # تحليل مصادر المخاطر
            risk_factors = []
            for criterion, score_data in evaluation_scores.items():
                score = score_data.get('score', 0)
                if score < 60:
                    risk_factors.append({
                        'factor': criterion,
                        'score': score,
                        'impact': 'عالي' if score < 50 else 'متوسط'
                    })

            return {
                'overall_risk_score': risk_score,
                'risk_level': risk_level,
                'risk_color': risk_color,
                'risk_factors': risk_factors,
                'risk_mitigation': self._suggest_risk_mitigation(risk_factors)
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الثقة: {str(e)}")
            return {
                'overall_risk_score': 50,
                'risk_level': "غير محدد",
                'risk_color': "gray",
                'risk_factors': [],
                'risk_mitigation': []
            }

    def _suggest_risk_mitigation(self, risk_factors: List[Dict]) -> List[str]:
        """اقتراح استراتيجيات تخفيف المخاطر"""
        mitigation_strategies = []

        for factor in risk_factors:
            factor_name = factor['factor']

            if factor_name == 'model_consensus':
                mitigation_strategies.append("انتظار إجماع أقوى بين النماذج")
            elif factor_name == 'signal_strength':
                mitigation_strategies.append("البحث عن إشارات تأكيدية إضافية")
            elif factor_name == 'historical_accuracy':
                mitigation_strategies.append("مراجعة وتحسين استراتيجية التداول")
            elif factor_name == 'market_volatility':
                mitigation_strategies.append("تقليل حجم الصفقة أو تجنب التداول")
            elif factor_name == 'data_quality':
                mitigation_strategies.append("انتظار توفر بيانات أكثر")
            elif factor_name == 'time_consistency':
                mitigation_strategies.append("التداول في ساعات الذروة فقط")

        if not mitigation_strategies:
            mitigation_strategies.append("مواصلة المراقبة والتحليل")

        return mitigation_strategies

    def _generate_evaluation_id(self, currency_pair: str) -> str:
        """إنشاء معرف فريد للتقييم"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"CONF_{currency_pair}_{timestamp}"

    async def batch_evaluate_confidence(self, predictions_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم الثقة لمجموعة من التنبؤات"""
        try:
            logger.info(f"🔍 تقييم الثقة لـ {len(predictions_list)} تنبؤ")

            batch_results = {
                'total_predictions': len(predictions_list),
                'evaluations': {},
                'summary': {
                    'high_confidence': 0,
                    'medium_confidence': 0,
                    'low_confidence': 0,
                    'average_confidence': 0
                },
                'batch_id': f"BATCH_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'timestamp': datetime.now().isoformat()
            }

            total_confidence = 0

            for prediction in predictions_list:
                try:
                    evaluation = await self.evaluate_prediction_confidence(prediction)
                    currency_pair = prediction.get('currency_pair')

                    batch_results['evaluations'][currency_pair] = evaluation

                    confidence_score = evaluation['final_confidence_score']
                    total_confidence += confidence_score

                    # تصنيف مستوى الثقة
                    if confidence_score >= 75:
                        batch_results['summary']['high_confidence'] += 1
                    elif confidence_score >= 60:
                        batch_results['summary']['medium_confidence'] += 1
                    else:
                        batch_results['summary']['low_confidence'] += 1

                except Exception as e:
                    logger.error(f"خطأ في تقييم {prediction.get('currency_pair', 'unknown')}: {str(e)}")

            # حساب المتوسط
            if len(predictions_list) > 0:
                batch_results['summary']['average_confidence'] = total_confidence / len(predictions_list)

            logger.info(f"✅ تم تقييم {len(batch_results['evaluations'])} تنبؤ بنجاح")
            return batch_results

        except Exception as e:
            logger.error(f"خطأ في التقييم المجمع: {str(e)}")
            raise

    def get_confidence_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات محرك تقييم الثقة"""
        return {
            'engine_name': 'Confidence Evaluation Engine',
            'criteria_count': len(self.confidence_criteria),
            'criteria_weights': {k: v['weight'] for k, v in self.confidence_criteria.items()},
            'supported_pairs': len(CURRENCY_PAIRS),
            'historical_performance_tracked': len(self.historical_performance),
            'peak_trading_hours': self.peak_trading_hours,
            'last_update': datetime.now().isoformat()
        }

# إنشاء instance افتراضي
confidence_evaluation_engine = ConfidenceEvaluationEngine()
