"""
محرك التنبؤ بالاتجاه السعري المتكامل
Direction Prediction Engine - Integrated System
"""

import os
import sys
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
import json

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_models.ai_predictor_engine import ai_predictor_engine
from ai_models.confidence_evaluation_engine import confidence_evaluation_engine
from data_layer.technical_signals_engine import technical_signals_engine
from data_layer.realtime_behavior_analyzer import realtime_behavior_analyzer
from data_layer.zscore_deviation_analyzer import zscore_deviation_analyzer
from data_layer.historical_probability_filter import historical_probability_filter
from data_layer.sharpe_ratio_analyzer import sharpe_ratio_analyzer
from data_layer.atr_volatility_analyzer import atr_volatility_analyzer
from config.currency_pairs import CURRENCY_PAIRS
from database.repository import HistoricalDataRepository
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("direction_prediction_engine")

class DirectionPredictionEngine:
    """محرك التنبؤ بالاتجاه السعري المتكامل"""

    def __init__(self):
        """تهيئة محرك التنبؤ بالاتجاه"""
        self.ai_engine = ai_predictor_engine
        self.confidence_engine = confidence_evaluation_engine
        self.technical_engine = technical_signals_engine
        self.behavior_analyzer = realtime_behavior_analyzer
        self.zscore_analyzer = zscore_deviation_analyzer
        self.probability_filter = historical_probability_filter
        self.sharpe_analyzer = sharpe_ratio_analyzer
        self.atr_analyzer = atr_volatility_analyzer
        self.db_repo = HistoricalDataRepository()

        # إعدادات التنبؤ
        self.timeframes = [1, 3, 5]  # دقائق
        self.confidence_threshold = 70.0  # حد الثقة الأدنى
        self.consensus_threshold = 80.0  # حد الإجماع المطلوب

        # إحصائيات الأداء
        self.prediction_stats = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'accuracy_by_timeframe': {},
            'accuracy_by_pair': {},
            'layer_performance': {}
        }

        # تخزين التنبؤات للتتبع
        self.active_predictions = {}

        logger.info("تم تهيئة محرك التنبؤ بالاتجاه السعري")

    def get_performance_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        try:
            total_predictions = self.prediction_stats['total_predictions']
            successful_predictions = self.prediction_stats['successful_predictions']

            overall_accuracy = (successful_predictions / total_predictions * 100) if total_predictions > 0 else 0

            return {
                'total_predictions': total_predictions,
                'successful_predictions': successful_predictions,
                'overall_accuracy': overall_accuracy,
                'accuracy_by_timeframe': self.prediction_stats.get('accuracy_by_timeframe', {}),
                'accuracy_by_pair': self.prediction_stats.get('accuracy_by_pair', {}),
                'layer_performance': self.prediction_stats.get('layer_performance', {}),
                'confidence_thresholds': {
                    'confidence_threshold': self.confidence_threshold,
                    'consensus_threshold': self.consensus_threshold
                },
                'supported_timeframes': self.timeframes,
                'last_update': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الأداء: {str(e)}")
            return {
                'total_predictions': 0,
                'successful_predictions': 0,
                'overall_accuracy': 0,
                'error': str(e)
            }

    async def predict_direction_comprehensive(self, currency_pair: str, timeframe: int = 5) -> Dict[str, Any]:
        """التنبؤ الشامل بالاتجاه السعري"""
        try:
            logger.info(f"🎯 بدء التنبؤ الشامل للزوج {currency_pair} - إطار زمني {timeframe} دقيقة")

            # جمع تحليلات جميع الطبقات
            layer_analyses = await self._collect_all_layer_analyses(currency_pair)

            # التنبؤ بالذكاء الاصطناعي
            ai_prediction = await self.ai_engine.predict_direction(currency_pair)

            # دمج جميع التحليلات
            comprehensive_analysis = self._merge_all_analyses(
                layer_analyses, ai_prediction, currency_pair, timeframe
            )

            # حساب التنبؤ النهائي
            final_prediction = self._calculate_final_prediction(comprehensive_analysis)

            # تقييم قوة الإشارة
            signal_strength = self._evaluate_comprehensive_signal_strength(final_prediction)

            # إنشاء التنبؤ النهائي
            prediction_result = {
                'currency_pair': currency_pair,
                'timeframe': timeframe,
                'direction': final_prediction['direction'],
                'confidence': final_prediction['confidence'],
                'signal_strength': signal_strength,
                'consensus_score': final_prediction['consensus_score'],
                'layer_analyses': layer_analyses,
                'ai_prediction': ai_prediction,
                'risk_assessment': self._assess_risk(final_prediction),
                'recommended_action': self._get_recommended_action(final_prediction),
                'timestamp': datetime.now().isoformat(),
                'prediction_id': self._generate_prediction_id(currency_pair, timeframe)
            }

            # تقييم درجة الثقة المتقدم
            try:
                confidence_evaluation = await self.confidence_engine.evaluate_prediction_confidence(prediction_result)
                prediction_result['confidence_evaluation'] = confidence_evaluation

                # تحديث التوصية بناءً على تقييم الثقة
                confidence_score = confidence_evaluation.get('final_confidence_score', 0)
                if confidence_score < 60:
                    prediction_result['recommended_action']['action'] = "تجنب التداول - ثقة منخفضة"
                    prediction_result['recommended_action']['confidence_override'] = True

                logger.info(f"🎯 تقييم الثقة: {confidence_score:.1f}% - {confidence_evaluation.get('confidence_level', 'غير محدد')}")

            except Exception as e:
                logger.warning(f"⚠️ فشل تقييم الثقة: {str(e)}")
                prediction_result['confidence_evaluation'] = {'error': str(e)}

            # حفظ التنبؤ للتتبع
            await self._save_prediction_for_tracking(prediction_result)

            # تحديث الإحصائيات
            self._update_prediction_statistics(prediction_result)

            logger.info(f"✅ تم التنبؤ: {final_prediction['direction']} بثقة {final_prediction['confidence']:.1f}%")
            return prediction_result

        except Exception as e:
            logger.error(f"خطأ في التنبؤ الشامل: {str(e)}")
            raise

    async def predict_multiple_timeframes(self, currency_pair: str) -> Dict[str, Any]:
        """التنبؤ متعدد الإطارات الزمنية مع تقييم الثقة"""
        try:
            logger.info(f"🎯 التنبؤ متعدد الإطارات للزوج {currency_pair}")

            timeframe_predictions = {}
            confidence_scores = []
            direction_votes = {'CALL': 0, 'PUT': 0}

            # التنبؤ لكل إطار زمني
            for timeframe in self.timeframes:
                try:
                    prediction = await self.predict_direction_comprehensive(currency_pair, timeframe)
                    timeframe_predictions[f"{timeframe}m"] = prediction

                    # جمع الأصوات
                    direction = prediction.get('direction', 'HOLD')
                    if direction in direction_votes:
                        direction_votes[direction] += 1

                    # جمع درجات الثقة
                    confidence_eval = prediction.get('confidence_evaluation', {})
                    confidence_score = confidence_eval.get('final_confidence_score', 0)
                    confidence_scores.append(confidence_score)

                    logger.info(f"  ✅ {timeframe}m: {direction} (ثقة: {confidence_score:.1f}%)")

                except Exception as e:
                    logger.warning(f"  ❌ فشل التنبؤ للإطار {timeframe}m: {str(e)}")
                    timeframe_predictions[f"{timeframe}m"] = {'error': str(e)}

            # حساب الإجماع الإجمالي
            total_votes = sum(direction_votes.values())
            overall_direction = max(direction_votes, key=direction_votes.get) if total_votes > 0 else 'HOLD'
            consensus_strength = (direction_votes[overall_direction] / total_votes * 100) if total_votes > 0 else 0

            # حساب متوسط الثقة
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

            # تقييم الثقة الإجمالي
            overall_confidence_level = self.confidence_engine._classify_confidence_level(avg_confidence)

            result = {
                'currency_pair': currency_pair,
                'timeframe_predictions': timeframe_predictions,
                'overall_consensus': {
                    'direction': overall_direction,
                    'consensus_strength': consensus_strength,
                    'average_confidence': avg_confidence,
                    'confidence_level': overall_confidence_level,
                    'direction_votes': direction_votes
                },
                'analysis_summary': {
                    'total_timeframes': len(self.timeframes),
                    'successful_predictions': len([p for p in timeframe_predictions.values() if 'error' not in p]),
                    'failed_predictions': len([p for p in timeframe_predictions.values() if 'error' in p]),
                    'confidence_range': {
                        'min': min(confidence_scores) if confidence_scores else 0,
                        'max': max(confidence_scores) if confidence_scores else 0,
                        'avg': avg_confidence
                    }
                },
                'recommendation': self._get_multi_timeframe_recommendation(overall_direction, consensus_strength, avg_confidence),
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"✅ إجماع متعدد الإطارات: {overall_direction} بقوة {consensus_strength:.1f}%")
            return result

        except Exception as e:
            logger.error(f"خطأ في التنبؤ متعدد الإطارات: {str(e)}")
            raise

    def _get_multi_timeframe_recommendation(self, direction: str, consensus_strength: float, avg_confidence: float) -> Dict[str, Any]:
        """الحصول على توصية للتنبؤ متعدد الإطارات"""
        try:
            if consensus_strength >= 80 and avg_confidence >= 75:
                action = "تنفيذ قوي - إجماع ممتاز"
                position_size = "كبير"
                risk_level = "منخفض"
            elif consensus_strength >= 70 and avg_confidence >= 65:
                action = "تنفيذ متوسط - إجماع جيد"
                position_size = "متوسط"
                risk_level = "متوسط"
            elif consensus_strength >= 60 and avg_confidence >= 55:
                action = "تنفيذ حذر - إجماع ضعيف"
                position_size = "صغير"
                risk_level = "مرتفع"
            else:
                action = "تجنب التنفيذ - لا يوجد إجماع"
                position_size = "لا يوجد"
                risk_level = "مرتفع جداً"

            return {
                'action': action,
                'direction': direction if action != "تجنب التنفيذ - لا يوجد إجماع" else "HOLD",
                'position_size': position_size,
                'risk_level': risk_level,
                'consensus_required': 70.0,
                'confidence_required': 65.0,
                'meets_criteria': consensus_strength >= 70 and avg_confidence >= 65
            }

        except Exception as e:
            logger.warning(f"خطأ في توصية متعدد الإطارات: {str(e)}")
            return {
                'action': "انتظار",
                'direction': "HOLD",
                'position_size': "لا يوجد",
                'risk_level': "غير محدد",
                'meets_criteria': False
            }

    async def _collect_all_layer_analyses(self, currency_pair: str) -> Dict[str, Any]:
        """جمع تحليلات جميع الطبقات"""
        try:
            layer_analyses = {}

            # طبقة التحليل الفني
            try:
                technical_analysis = await self.technical_engine.analyze_comprehensive(currency_pair)
                layer_analyses['technical'] = {
                    'signals': technical_analysis,
                    'weight': 0.25,
                    'status': 'success'
                }
                logger.info("✅ تم جمع التحليل الفني")
            except Exception as e:
                logger.warning(f"⚠️ فشل التحليل الفني: {str(e)}")
                layer_analyses['technical'] = {'status': 'failed', 'weight': 0.0}

            # طبقة التحليل السلوكي
            try:
                behavior_analysis = await self.behavior_analyzer.analyze_realtime_behavior(currency_pair)
                layer_analyses['behavioral'] = {
                    'analysis': behavior_analysis,
                    'weight': 0.20,
                    'status': 'success'
                }
                logger.info("✅ تم جمع التحليل السلوكي")
            except Exception as e:
                logger.warning(f"⚠️ فشل التحليل السلوكي: {str(e)}")
                layer_analyses['behavioral'] = {'status': 'failed', 'weight': 0.0}

            # طبقة التحليل الكمي - Z-Score
            try:
                zscore_analysis = self.zscore_analyzer.analyze_zscore_deviation(currency_pair)
                layer_analyses['zscore'] = {
                    'analysis': zscore_analysis,
                    'weight': 0.15,
                    'status': 'success'
                }
                logger.info("✅ تم جمع تحليل Z-Score")
            except Exception as e:
                logger.warning(f"⚠️ فشل تحليل Z-Score: {str(e)}")
                layer_analyses['zscore'] = {'status': 'failed', 'weight': 0.0}

            # طبقة الاحتمالات التاريخية
            try:
                probability_analysis = self.probability_filter.filter_by_historical_probability(currency_pair)
                layer_analyses['probability'] = {
                    'analysis': probability_analysis,
                    'weight': 0.15,
                    'status': 'success'
                }
                logger.info("✅ تم جمع تحليل الاحتمالات")
            except Exception as e:
                logger.warning(f"⚠️ فشل تحليل الاحتمالات: {str(e)}")
                layer_analyses['probability'] = {'status': 'failed', 'weight': 0.0}

            # طبقة نسبة شارب
            try:
                sharpe_analysis = self.sharpe_analyzer.analyze_sharpe_ratio(currency_pair)
                layer_analyses['sharpe'] = {
                    'analysis': sharpe_analysis,
                    'weight': 0.10,
                    'status': 'success'
                }
                logger.info("✅ تم جمع تحليل شارب")
            except Exception as e:
                logger.warning(f"⚠️ فشل تحليل شارب: {str(e)}")
                layer_analyses['sharpe'] = {'status': 'failed', 'weight': 0.0}

            # طبقة تحليل التقلبات ATR
            try:
                atr_analysis = self.atr_analyzer.analyze_atr_volatility(currency_pair)
                layer_analyses['atr'] = {
                    'analysis': atr_analysis,
                    'weight': 0.15,
                    'status': 'success'
                }
                logger.info("✅ تم جمع تحليل ATR")
            except Exception as e:
                logger.warning(f"⚠️ فشل تحليل ATR: {str(e)}")
                layer_analyses['atr'] = {'status': 'failed', 'weight': 0.0}

            return layer_analyses

        except Exception as e:
            logger.error(f"خطأ في جمع تحليلات الطبقات: {str(e)}")
            raise

    def _merge_all_analyses(self, layer_analyses: Dict[str, Any], ai_prediction: Dict[str, Any],
                           currency_pair: str, timeframe: int) -> Dict[str, Any]:
        """دمج جميع التحليلات"""
        try:
            merged_analysis = {
                'currency_pair': currency_pair,
                'timeframe': timeframe,
                'layer_signals': [],
                'ai_weight': 0.30,  # وزن الذكاء الاصطناعي
                'total_weight': 0.0,
                'call_score': 0.0,
                'put_score': 0.0
            }

            # معالجة تحليلات الطبقات
            for layer_name, layer_data in layer_analyses.items():
                if layer_data.get('status') == 'success':
                    weight = layer_data.get('weight', 0.0)
                    merged_analysis['total_weight'] += weight

                    # استخراج الإشارة من كل طبقة
                    signal = self._extract_signal_from_layer(layer_name, layer_data)
                    if signal:
                        merged_analysis['layer_signals'].append({
                            'layer': layer_name,
                            'signal': signal,
                            'weight': weight,
                            'confidence': signal.get('confidence', 50.0)
                        })

                        # إضافة النقاط حسب الاتجاه
                        if signal.get('direction') == 'CALL':
                            merged_analysis['call_score'] += weight * (signal.get('confidence', 50.0) / 100.0)
                        else:
                            merged_analysis['put_score'] += weight * (signal.get('confidence', 50.0) / 100.0)

            # إضافة تنبؤ الذكاء الاصطناعي
            if ai_prediction and 'direction' in ai_prediction:
                ai_weight = merged_analysis['ai_weight']
                merged_analysis['total_weight'] += ai_weight

                ai_confidence = ai_prediction.get('confidence', 50.0)
                if ai_prediction['direction'] == 'CALL':
                    merged_analysis['call_score'] += ai_weight * (ai_confidence / 100.0)
                else:
                    merged_analysis['put_score'] += ai_weight * (ai_confidence / 100.0)

                merged_analysis['ai_signal'] = {
                    'direction': ai_prediction['direction'],
                    'confidence': ai_confidence,
                    'weight': ai_weight
                }

            return merged_analysis

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {str(e)}")
            raise

    def _extract_signal_from_layer(self, layer_name: str, layer_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """استخراج الإشارة من طبقة معينة"""
        try:
            if layer_name == 'technical':
                signals = layer_data.get('signals', {})
                # استخراج الإشارة الإجمالية من التحليل الفني
                if 'overall_signal' in signals:
                    return signals['overall_signal']
                elif 'signals' in signals and signals['signals']:
                    # حساب متوسط الإشارات
                    call_count = sum(1 for s in signals['signals'] if s.get('direction') == 'CALL')
                    put_count = len(signals['signals']) - call_count
                    direction = 'CALL' if call_count > put_count else 'PUT'
                    confidence = (max(call_count, put_count) / len(signals['signals'])) * 100
                    return {'direction': direction, 'confidence': confidence}

            elif layer_name == 'behavioral':
                analysis = layer_data.get('analysis', {})
                if 'recommendation' in analysis:
                    rec = analysis['recommendation']
                    return {
                        'direction': rec.get('action', 'HOLD'),
                        'confidence': rec.get('confidence', 50.0)
                    }

            elif layer_name in ['zscore', 'probability', 'sharpe', 'atr']:
                analysis = layer_data.get('analysis', {})
                if 'recommendation' in analysis:
                    rec = analysis['recommendation']
                    return {
                        'direction': rec.get('action', 'HOLD'),
                        'confidence': rec.get('confidence', 50.0)
                    }

            # إشارة افتراضية إذا لم نجد إشارة واضحة
            return {'direction': 'HOLD', 'confidence': 50.0}

        except Exception as e:
            logger.warning(f"خطأ في استخراج إشارة {layer_name}: {str(e)}")
            return {'direction': 'HOLD', 'confidence': 50.0}

    def _calculate_final_prediction(self, merged_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """حساب التنبؤ النهائي"""
        try:
            call_score = merged_analysis.get('call_score', 0.0)
            put_score = merged_analysis.get('put_score', 0.0)
            total_weight = merged_analysis.get('total_weight', 1.0)

            # تطبيع النقاط
            if total_weight > 0:
                call_normalized = (call_score / total_weight) * 100
                put_normalized = (put_score / total_weight) * 100
            else:
                call_normalized = put_normalized = 50.0

            # تحديد الاتجاه والثقة
            if call_normalized > put_normalized:
                direction = 'CALL'
                confidence = call_normalized
                consensus_score = (call_normalized / (call_normalized + put_normalized)) * 100
            else:
                direction = 'PUT'
                confidence = put_normalized
                consensus_score = (put_normalized / (call_normalized + put_normalized)) * 100

            # حساب قوة الإجماع
            signal_count = len(merged_analysis.get('layer_signals', []))
            if 'ai_signal' in merged_analysis:
                signal_count += 1

            agreement_count = 0
            for signal in merged_analysis.get('layer_signals', []):
                if signal.get('signal', {}).get('direction') == direction:
                    agreement_count += 1

            if 'ai_signal' in merged_analysis and merged_analysis['ai_signal'].get('direction') == direction:
                agreement_count += 1

            consensus_ratio = (agreement_count / signal_count * 100) if signal_count > 0 else 0

            return {
                'direction': direction,
                'confidence': min(confidence, 95.0),  # حد أقصى 95%
                'consensus_score': consensus_score,
                'consensus_ratio': consensus_ratio,
                'call_score': call_normalized,
                'put_score': put_normalized,
                'signal_count': signal_count,
                'agreement_count': agreement_count
            }

        except Exception as e:
            logger.error(f"خطأ في حساب التنبؤ النهائي: {str(e)}")
            return {
                'direction': 'HOLD',
                'confidence': 50.0,
                'consensus_score': 50.0,
                'consensus_ratio': 0.0,
                'call_score': 50.0,
                'put_score': 50.0,
                'signal_count': 0,
                'agreement_count': 0
            }

    def _evaluate_comprehensive_signal_strength(self, prediction: Dict[str, Any]) -> str:
        """تقييم قوة الإشارة الشاملة"""
        try:
            confidence = prediction.get('confidence', 0.0)
            consensus_ratio = prediction.get('consensus_ratio', 0.0)
            signal_count = prediction.get('signal_count', 0)

            # حساب النقاط الإجمالية
            confidence_points = confidence / 100 * 40  # 40 نقطة للثقة
            consensus_points = consensus_ratio / 100 * 30  # 30 نقطة للإجماع
            signal_points = min(signal_count / 6 * 30, 30)  # 30 نقطة لعدد الإشارات

            total_score = confidence_points + consensus_points + signal_points

            if total_score >= 85:
                return "قوية جداً"
            elif total_score >= 70:
                return "قوية"
            elif total_score >= 55:
                return "متوسطة"
            elif total_score >= 40:
                return "ضعيفة"
            else:
                return "ضعيفة جداً"

        except Exception as e:
            logger.warning(f"خطأ في تقييم قوة الإشارة: {str(e)}")
            return "غير محدد"

    def _assess_risk(self, prediction: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم المخاطر"""
        try:
            confidence = prediction.get('confidence', 0.0)
            consensus_ratio = prediction.get('consensus_ratio', 0.0)

            # تحديد مستوى المخاطر
            if confidence >= 80 and consensus_ratio >= 80:
                risk_level = "منخفض"
                risk_score = 20
            elif confidence >= 70 and consensus_ratio >= 70:
                risk_level = "متوسط"
                risk_score = 40
            elif confidence >= 60 and consensus_ratio >= 60:
                risk_level = "مرتفع"
                risk_score = 60
            else:
                risk_level = "مرتفع جداً"
                risk_score = 80

            return {
                'level': risk_level,
                'score': risk_score,
                'factors': {
                    'confidence_risk': 100 - confidence,
                    'consensus_risk': 100 - consensus_ratio,
                    'overall_risk': risk_score
                }
            }

        except Exception as e:
            logger.warning(f"خطأ في تقييم المخاطر: {str(e)}")
            return {
                'level': "غير محدد",
                'score': 50,
                'factors': {}
            }

    def _get_recommended_action(self, prediction: Dict[str, Any]) -> Dict[str, Any]:
        """الحصول على التوصية المقترحة"""
        try:
            confidence = prediction.get('confidence', 0.0)
            consensus_ratio = prediction.get('consensus_ratio', 0.0)
            direction = prediction.get('direction', 'HOLD')

            # تحديد التوصية
            if confidence >= self.confidence_threshold and consensus_ratio >= self.consensus_threshold:
                if direction in ['CALL', 'PUT']:
                    action = "تنفيذ قوي"
                    position_size = "كبير"
                else:
                    action = "انتظار"
                    position_size = "لا يوجد"
            elif confidence >= 60 and consensus_ratio >= 60:
                if direction in ['CALL', 'PUT']:
                    action = "تنفيذ حذر"
                    position_size = "متوسط"
                else:
                    action = "انتظار"
                    position_size = "لا يوجد"
            else:
                action = "تجنب التداول"
                position_size = "لا يوجد"

            return {
                'action': action,
                'direction': direction if action != "تجنب التداول" else "HOLD",
                'position_size': position_size,
                'confidence_required': self.confidence_threshold,
                'consensus_required': self.consensus_threshold,
                'meets_criteria': confidence >= self.confidence_threshold and consensus_ratio >= self.consensus_threshold
            }

        except Exception as e:
            logger.warning(f"خطأ في تحديد التوصية: {str(e)}")
            return {
                'action': "انتظار",
                'direction': "HOLD",
                'position_size': "لا يوجد",
                'meets_criteria': False
            }

    def _generate_prediction_id(self, currency_pair: str, timeframe: int) -> str:
        """إنشاء معرف فريد للتنبؤ"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{currency_pair}_{timeframe}m_{timestamp}"

    async def _save_prediction_for_tracking(self, prediction: Dict[str, Any]):
        """حفظ التنبؤ للتتبع"""
        try:
            prediction_id = prediction['prediction_id']
            self.active_predictions[prediction_id] = {
                'prediction': prediction,
                'created_at': datetime.now(),
                'status': 'active',
                'verified': False
            }

            # تنظيف التنبؤات القديمة (أكثر من ساعة)
            cutoff_time = datetime.now() - timedelta(hours=1)
            expired_ids = [
                pid for pid, data in self.active_predictions.items()
                if data['created_at'] < cutoff_time
            ]

            for pid in expired_ids:
                del self.active_predictions[pid]

            logger.info(f"تم حفظ التنبؤ {prediction_id} للتتبع")

        except Exception as e:
            logger.warning(f"خطأ في حفظ التنبؤ للتتبع: {str(e)}")

    def _update_prediction_statistics(self, prediction: Dict[str, Any]):
        """تحديث إحصائيات التنبؤ"""
        try:
            self.prediction_stats['total_predictions'] += 1

            currency_pair = prediction['currency_pair']
            timeframe = prediction['timeframe']

            # إحصائيات حسب الإطار الزمني
            if timeframe not in self.prediction_stats['accuracy_by_timeframe']:
                self.prediction_stats['accuracy_by_timeframe'][timeframe] = {
                    'total': 0, 'successful': 0, 'accuracy': 0.0
                }
            self.prediction_stats['accuracy_by_timeframe'][timeframe]['total'] += 1

            # إحصائيات حسب الزوج
            if currency_pair not in self.prediction_stats['accuracy_by_pair']:
                self.prediction_stats['accuracy_by_pair'][currency_pair] = {
                    'total': 0, 'successful': 0, 'accuracy': 0.0
                }
            self.prediction_stats['accuracy_by_pair'][currency_pair]['total'] += 1

        except Exception as e:
            logger.warning(f"خطأ في تحديث الإحصائيات: {str(e)}")

    async def predict_multiple_timeframes(self, currency_pair: str) -> Dict[str, Any]:
        """التنبؤ لعدة إطارات زمنية"""
        try:
            logger.info(f"🎯 التنبؤ متعدد الإطارات الزمنية للزوج {currency_pair}")

            predictions = {}
            overall_consensus = {'CALL': 0, 'PUT': 0, 'HOLD': 0}

            for timeframe in self.timeframes:
                try:
                    prediction = await self.predict_direction_comprehensive(currency_pair, timeframe)
                    predictions[f"{timeframe}m"] = prediction

                    # تجميع الإجماع
                    direction = prediction.get('direction', 'HOLD')
                    overall_consensus[direction] += 1

                    logger.info(f"✅ تنبؤ {timeframe}م: {direction}")

                except Exception as e:
                    logger.warning(f"⚠️ فشل تنبؤ {timeframe}م: {str(e)}")
                    predictions[f"{timeframe}m"] = {
                        'error': str(e),
                        'status': 'failed'
                    }

            # تحديد الاتجاه الإجمالي
            overall_direction = max(overall_consensus, key=overall_consensus.get)
            consensus_strength = overall_consensus[overall_direction] / len(self.timeframes) * 100

            result = {
                'currency_pair': currency_pair,
                'timeframe_predictions': predictions,
                'overall_consensus': {
                    'direction': overall_direction,
                    'strength': consensus_strength,
                    'votes': overall_consensus
                },
                'recommendation': self._get_multi_timeframe_recommendation(overall_direction, consensus_strength),
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"🎯 الإجماع الإجمالي: {overall_direction} بقوة {consensus_strength:.1f}%")
            return result

        except Exception as e:
            logger.error(f"خطأ في التنبؤ متعدد الإطارات: {str(e)}")
            raise

    def _get_multi_timeframe_recommendation(self, direction: str, strength: float) -> Dict[str, Any]:
        """الحصول على توصية متعددة الإطارات الزمنية"""
        try:
            if strength >= 80 and direction in ['CALL', 'PUT']:
                return {
                    'action': "تنفيذ قوي - إجماع عالي",
                    'direction': direction,
                    'confidence': strength,
                    'risk_level': "منخفض"
                }
            elif strength >= 60 and direction in ['CALL', 'PUT']:
                return {
                    'action': "تنفيذ متوسط",
                    'direction': direction,
                    'confidence': strength,
                    'risk_level': "متوسط"
                }
            else:
                return {
                    'action': "انتظار - لا يوجد إجماع واضح",
                    'direction': "HOLD",
                    'confidence': strength,
                    'risk_level': "مرتفع"
                }

        except Exception as e:
            logger.warning(f"خطأ في توصية متعددة الإطارات: {str(e)}")
            return {
                'action': "انتظار",
                'direction': "HOLD",
                'confidence': 0.0,
                'risk_level': "غير محدد"
            }

    async def verify_prediction_accuracy(self, prediction_id: str) -> Dict[str, Any]:
        """التحقق من دقة التنبؤ"""
        try:
            if prediction_id not in self.active_predictions:
                return {'error': 'التنبؤ غير موجود'}

            prediction_data = self.active_predictions[prediction_id]
            prediction = prediction_data['prediction']

            currency_pair = prediction['currency_pair']
            timeframe = prediction['timeframe']
            predicted_direction = prediction['direction']
            prediction_time = prediction_data['created_at']

            # جلب البيانات الحديثة للتحقق
            verification_time = prediction_time + timedelta(minutes=timeframe)

            if datetime.now() < verification_time:
                return {
                    'status': 'pending',
                    'message': 'لم يحن وقت التحقق بعد',
                    'verification_time': verification_time.isoformat()
                }

            # جلب البيانات للتحقق
            candles = self.db_repo.get_latest_candles(currency_pair, count=10)

            if not candles:
                return {'error': 'لا توجد بيانات للتحقق'}

            # العثور على الشمعة المناسبة للتحقق
            verification_candle = None
            for candle in candles:
                candle_time = datetime.fromisoformat(candle['timestamp'].replace('Z', '+00:00'))
                if candle_time >= verification_time:
                    verification_candle = candle
                    break

            if not verification_candle:
                return {'error': 'لم يتم العثور على شمعة التحقق'}

            # تحديد الاتجاه الفعلي
            actual_direction = 'CALL' if verification_candle['close'] > verification_candle['open'] else 'PUT'

            # التحقق من الدقة
            is_correct = predicted_direction == actual_direction

            # تحديث الإحصائيات
            if is_correct:
                self.prediction_stats['successful_predictions'] += 1
                self.prediction_stats['accuracy_by_timeframe'][timeframe]['successful'] += 1
                self.prediction_stats['accuracy_by_pair'][currency_pair]['successful'] += 1

            # حساب الدقة المحدثة
            total_predictions = self.prediction_stats['total_predictions']
            if total_predictions > 0:
                overall_accuracy = (self.prediction_stats['successful_predictions'] / total_predictions) * 100
            else:
                overall_accuracy = 0.0

            # تحديث حالة التنبؤ
            prediction_data['verified'] = True
            prediction_data['status'] = 'verified'
            prediction_data['result'] = {
                'predicted': predicted_direction,
                'actual': actual_direction,
                'correct': is_correct,
                'verification_time': datetime.now().isoformat()
            }

            verification_result = {
                'prediction_id': prediction_id,
                'predicted_direction': predicted_direction,
                'actual_direction': actual_direction,
                'is_correct': is_correct,
                'accuracy_rate': overall_accuracy,
                'verification_candle': verification_candle,
                'verification_time': datetime.now().isoformat()
            }

            logger.info(f"تم التحقق من التنبؤ {prediction_id}: {'صحيح' if is_correct else 'خاطئ'}")
            return verification_result

        except Exception as e:
            logger.error(f"خطأ في التحقق من دقة التنبؤ: {str(e)}")
            return {'error': str(e)}

    def get_performance_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        try:
            # حساب الدقة الإجمالية
            total = self.prediction_stats['total_predictions']
            successful = self.prediction_stats['successful_predictions']
            overall_accuracy = (successful / total * 100) if total > 0 else 0.0

            # حساب دقة كل إطار زمني
            for timeframe_data in self.prediction_stats['accuracy_by_timeframe'].values():
                if timeframe_data['total'] > 0:
                    timeframe_data['accuracy'] = (timeframe_data['successful'] / timeframe_data['total']) * 100

            # حساب دقة كل زوج
            for pair_data in self.prediction_stats['accuracy_by_pair'].values():
                if pair_data['total'] > 0:
                    pair_data['accuracy'] = (pair_data['successful'] / pair_data['total']) * 100

            return {
                'overall_accuracy': overall_accuracy,
                'total_predictions': total,
                'successful_predictions': successful,
                'accuracy_by_timeframe': self.prediction_stats['accuracy_by_timeframe'],
                'accuracy_by_pair': self.prediction_stats['accuracy_by_pair'],
                'active_predictions_count': len(self.active_predictions),
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الأداء: {str(e)}")
            return {'error': str(e)}

# إنشاء instance افتراضي
direction_prediction_engine = DirectionPredictionEngine()