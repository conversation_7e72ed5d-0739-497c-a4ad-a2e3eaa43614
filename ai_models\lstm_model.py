"""
نموذج LSTM للتنبؤ بالاتجاه السعري
LSTM Model for Price Direction Prediction
"""

import os
import sys
import numpy as np
import pandas as pd
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("⚠️ تحذير: مكتبة TensorFlow غير مثبتة. سيتم تشغيل النموذج في وضع المحاكاة")

from config.ai_config import default_ai_config
from utils.logger import scalping_logger
from database.repository import HistoricalDataRepository

logger = scalping_logger.get_logger("lstm_model")

class LSTMPredictor:
    """نموذج LSTM للتنبؤ بالاتجاه السعري"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """تهيئة نموذج LSTM"""
        self.config = config or default_ai_config.lstm_config
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1)) if TENSORFLOW_AVAILABLE else None
        self.sequence_length = self.config['sequence_length']  # استخدام القيمة الأصلية
        self.is_trained = False
        self.model_path = os.path.join(default_ai_config.models_directory, "lstm_model.h5")
        self.scaler_path = os.path.join(default_ai_config.models_directory, "lstm_scaler.pkl")
        
        # إنشاء مجلد النماذج إذا لم يكن موجوداً
        os.makedirs(default_ai_config.models_directory, exist_ok=True)
        
        # إعداد قاعدة البيانات
        self.db_repo = HistoricalDataRepository()
        
        # إعداد TensorFlow
        if TENSORFLOW_AVAILABLE:
            tf.random.set_seed(42)
            # تقليل استخدام GPU memory إذا كان متوفراً
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                try:
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                except RuntimeError as e:
                    logger.warning(f"خطأ في إعداد GPU: {e}")
        
        logger.info("تم تهيئة نموذج LSTM")

    def prepare_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """إعداد التسلسلات الزمنية للتدريب"""
        try:
            # اختيار الميزات المهمة
            feature_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # إضافة المؤشرات الفنية إذا كانت متوفرة
            technical_indicators = [
                'ema_5', 'ema_10', 'ema_21', 'sma_10', 'rsi_5', 'rsi_14',
                'macd', 'momentum_10', 'atr_5', 'atr_14'
            ]
            
            for indicator in technical_indicators:
                if indicator in data.columns:
                    feature_columns.append(indicator)
            
            # استخراج البيانات
            features_data = data[feature_columns].values
            
            # تطبيع البيانات
            scaled_data = self.scaler.fit_transform(features_data)
            
            # إنشاء التسلسلات
            X, y = [], []
            
            for i in range(self.sequence_length, len(scaled_data)):
                # التسلسل (sequence_length شمعة سابقة)
                X.append(scaled_data[i-self.sequence_length:i])
                
                # التصنيف (اتجاه الشمعة التالية)
                current_close = data.iloc[i]['close']
                next_close = data.iloc[i+1]['close'] if i+1 < len(data) else current_close
                y.append(1 if next_close > current_close else 0)
            
            # إزالة آخر عنصر من y (لا يوجد شمعة تالية)
            if len(y) > 0:
                y = y[:-1]
                X = X[:-1]
            
            X = np.array(X)
            y = np.array(y)
            
            logger.info(f"تم إعداد {len(X)} تسلسل بطول {self.sequence_length}")
            return X, y
            
        except Exception as e:
            logger.error(f"خطأ في إعداد التسلسلات: {str(e)}")
            raise

    def build_model(self, input_shape: Tuple[int, int]) -> None:
        """بناء نموذج LSTM"""
        try:
            if not TENSORFLOW_AVAILABLE:
                return
            
            self.model = Sequential([
                # الطبقة الأولى LSTM
                LSTM(
                    units=self.config['hidden_units'],
                    return_sequences=True,
                    input_shape=input_shape
                ),
                Dropout(self.config['dropout_rate']),
                BatchNormalization(),
                
                # الطبقة الثانية LSTM
                LSTM(
                    units=self.config['hidden_units'] // 2,
                    return_sequences=False
                ),
                Dropout(self.config['dropout_rate']),
                BatchNormalization(),
                
                # طبقات Dense
                Dense(units=25, activation='relu'),
                Dropout(0.1),
                Dense(units=1, activation='sigmoid')
            ])
            
            # تجميع النموذج
            self.model.compile(
                optimizer=Adam(learning_rate=self.config['learning_rate']),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )
            
            logger.info("تم بناء نموذج LSTM بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في بناء النموذج: {str(e)}")
            raise

    def train(self, currency_pair: str, timeframe: int = 60, samples_count: int = 5000) -> Dict[str, Any]:
        """تدريب النموذج"""
        try:
            if not TENSORFLOW_AVAILABLE:
                logger.warning("TensorFlow غير متوفر - تشغيل وضع المحاكاة")
                return self._simulate_training()
            
            logger.info(f"بدء تدريب نموذج LSTM للزوج {currency_pair}")
            
            # جلب البيانات من قاعدة البيانات
            data = self._fetch_training_data(currency_pair, samples_count)
            
            min_samples = self.sequence_length + 100  # استخدام القيمة الأصلية
            if len(data) < min_samples:
                raise ValueError(f"البيانات غير كافية للتدريب: {len(data)} < {min_samples}")
            
            # إعداد التسلسلات
            X, y = self.prepare_sequences(data)
            
            if len(X) == 0:
                raise ValueError("فشل في إعداد التسلسلات")
            
            # تقسيم البيانات
            split_index = int(len(X) * (1 - self.config['validation_split']))
            X_train, X_val = X[:split_index], X[split_index:]
            y_train, y_val = y[:split_index], y[split_index:]
            
            # بناء النموذج
            self.build_model((X.shape[1], X.shape[2]))
            
            # إعداد callbacks
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=self.config['patience'],
                min_delta=self.config['min_delta'],
                restore_best_weights=True
            )
            
            model_checkpoint = ModelCheckpoint(
                self.model_path,
                monitor='val_accuracy',
                save_best_only=True,
                mode='max'
            )
            
            # التدريب
            history = self.model.fit(
                X_train, y_train,
                epochs=self.config['epochs'],
                batch_size=self.config['batch_size'],
                validation_data=(X_val, y_val),
                callbacks=[early_stopping, model_checkpoint],
                verbose=1
            )
            
            # التقييم
            y_pred = (self.model.predict(X_val) > 0.5).astype(int).flatten()
            
            accuracy = accuracy_score(y_val, y_pred)
            precision = precision_score(y_val, y_pred, average='weighted')
            recall = recall_score(y_val, y_pred, average='weighted')
            f1 = f1_score(y_val, y_pred, average='weighted')
            
            self.is_trained = True
            
            # حفظ المعايرة
            self.save_scaler()
            
            training_results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'final_loss': history.history['loss'][-1],
                'final_val_loss': history.history['val_loss'][-1],
                'training_samples': len(X_train),
                'validation_samples': len(X_val),
                'epochs_trained': len(history.history['loss']),
                'sequence_length': self.sequence_length,
                'currency_pair': currency_pair,
                'training_date': datetime.now().isoformat()
            }
            
            logger.info(f"تم تدريب النموذج بنجاح - دقة: {accuracy:.3f}")
            return training_results
            
        except Exception as e:
            logger.error(f"خطأ في تدريب النموذج: {str(e)}")
            raise

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """التنبؤ بالاتجاه السعري"""
        try:
            if not self.is_trained and not self.load_model():
                raise ValueError("النموذج غير مدرب")
            
            if not TENSORFLOW_AVAILABLE:
                return self._simulate_prediction()
            
            # التأكد من وجود بيانات كافية
            if len(data) < self.sequence_length:
                raise ValueError(f"البيانات غير كافية للتنبؤ: {len(data)} < {self.sequence_length}")
            
            # إعداد الميزات
            feature_columns = ['open', 'high', 'low', 'close', 'volume']
            
            # إضافة المؤشرات الفنية إذا كانت متوفرة
            technical_indicators = [
                'ema_5', 'ema_10', 'ema_21', 'sma_10', 'rsi_5', 'rsi_14',
                'macd', 'momentum_10', 'atr_5', 'atr_14'
            ]
            
            for indicator in technical_indicators:
                if indicator in data.columns:
                    feature_columns.append(indicator)
            
            # استخراج آخر sequence_length شمعة
            recent_data = data[feature_columns].tail(self.sequence_length).values
            
            # تطبيع البيانات
            scaled_data = self.scaler.transform(recent_data)
            
            # إعداد التسلسل للتنبؤ
            sequence = scaled_data.reshape(1, self.sequence_length, -1)
            
            # التنبؤ
            prediction_proba = self.model.predict(sequence)[0][0]
            prediction = 1 if prediction_proba > 0.5 else 0
            
            # حساب الثقة
            confidence = max(prediction_proba, 1 - prediction_proba) * 100
            
            # تحديد الاتجاه
            direction = "CALL" if prediction == 1 else "PUT"
            
            # تقييم قوة الإشارة
            signal_strength = self._evaluate_signal_strength(confidence)
            
            result = {
                'prediction': int(prediction),
                'direction': direction,
                'confidence': confidence,
                'signal_strength': signal_strength,
                'probabilities': {
                    'down': (1 - prediction_proba) * 100,
                    'up': prediction_proba * 100
                },
                'model_type': 'LSTM',
                'sequence_length': self.sequence_length,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"تنبؤ LSTM: {direction} بثقة {confidence:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ: {str(e)}")
            raise

    def _evaluate_signal_strength(self, confidence: float) -> str:
        """تقييم قوة الإشارة"""
        if confidence >= 90:
            return "قوية جداً"
        elif confidence >= 80:
            return "قوية"
        elif confidence >= 70:
            return "متوسطة"
        elif confidence >= 60:
            return "ضعيفة"
        else:
            return "ضعيفة جداً"

    def _fetch_training_data(self, currency_pair: str, samples_count: int) -> pd.DataFrame:
        """جلب بيانات التدريب من قاعدة البيانات"""
        try:
            # جلب البيانات التاريخية فقط
            candles = self.db_repo.get_latest_candles(currency_pair, count=samples_count)

            if not candles:
                raise ValueError(f"لا توجد بيانات للزوج {currency_pair}")

            # تحويل إلى DataFrame
            data = []
            for i, candle in enumerate(candles):
                data.append({
                    'id': i + 1,  # معرف تسلسلي
                    'currency_pair': currency_pair,
                    'timestamp': candle['timestamp'],
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle['volume']) if candle['volume'] else 1.0,
                    # إضافة قيم افتراضية للمؤشرات
                    'ema_5': 0.0, 'ema_10': 0.0, 'ema_21': 0.0, 'sma_10': 0.0,
                    'rsi_5': 50.0, 'rsi_14': 50.0, 'macd': 0.0, 'momentum_10': 1.0,
                    'atr_5': 0.0, 'atr_14': 0.0
                })

            df = pd.DataFrame(data)

            # ترتيب البيانات حسب الوقت (الأقدم أولاً)
            df = df.sort_values('timestamp').reset_index(drop=True)

            # ملء القيم المفقودة
            df = df.fillna(method='ffill').fillna(0)

            logger.info(f"تم جلب {len(df)} شمعة للتدريب")
            return df

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات التدريب: {str(e)}")
            raise

    def save_scaler(self) -> bool:
        """حفظ المعايرة"""
        try:
            with open(self.scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            logger.info("تم حفظ معايرة LSTM بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ المعايرة: {str(e)}")
            return False

    def load_model(self) -> bool:
        """تحميل النموذج"""
        try:
            if not os.path.exists(self.model_path):
                return False
            
            if not TENSORFLOW_AVAILABLE:
                return False
            
            # تحميل النموذج
            self.model = load_model(self.model_path)
            
            # تحميل المعايرة
            if os.path.exists(self.scaler_path):
                with open(self.scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
            
            self.is_trained = True
            logger.info("تم تحميل نموذج LSTM بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل النموذج: {str(e)}")
            return False

    def _simulate_training(self) -> Dict[str, Any]:
        """محاكاة التدريب عند عدم توفر TensorFlow"""
        self.is_trained = True
        return {
            'accuracy': 0.72,
            'precision': 0.70,
            'recall': 0.69,
            'f1_score': 0.69,
            'final_loss': 0.45,
            'final_val_loss': 0.48,
            'training_samples': 3500,
            'validation_samples': 500,
            'epochs_trained': 25,
            'sequence_length': self.sequence_length,
            'currency_pair': 'SIMULATION',
            'training_date': datetime.now().isoformat(),
            'note': 'محاكاة - TensorFlow غير مثبت'
        }

    def _simulate_prediction(self) -> Dict[str, Any]:
        """محاكاة التنبؤ عند عدم توفر TensorFlow"""
        import random
        
        prediction = random.choice([0, 1])
        confidence = random.uniform(55, 80)
        direction = "CALL" if prediction == 1 else "PUT"
        
        return {
            'prediction': prediction,
            'direction': direction,
            'confidence': confidence,
            'signal_strength': self._evaluate_signal_strength(confidence),
            'probabilities': {
                'down': 100 - confidence,
                'up': confidence
            },
            'model_type': 'LSTM (محاكاة)',
            'sequence_length': self.sequence_length,
            'timestamp': datetime.now().isoformat(),
            'note': 'محاكاة - TensorFlow غير مثبت'
        }

# إنشاء instance افتراضي
lstm_predictor = LSTMPredictor()
