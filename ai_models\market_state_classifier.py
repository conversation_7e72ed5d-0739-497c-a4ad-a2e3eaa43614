"""
مصنف حالة السوق المتقدم
Advanced Market State Classifier
"""

import os
import sys
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import logging
import json
import statistics

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_layer.atr_volatility_analyzer import atr_volatility_analyzer, VolatilityLevel, VolatilityTrend, MarketCondition
from data_layer.market_evaluator import market_evaluator
from config.currency_pairs import CURRENCY_PAIRS
from database.repository import HistoricalDataRepository
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("market_state_classifier")

class MarketState(Enum):
    """حالات السوق المتقدمة"""
    STRONG_TRENDING = "STRONG_TRENDING"      # اتجاه قوي
    WEAK_TRENDING = "WEAK_TRENDING"          # اتجاه ضعيف
    RANGING_TIGHT = "RANGING_TIGHT"          # نطاق ضيق
    RANGING_WIDE = "RANGING_WIDE"            # نطاق واسع
    VOLATILE_CHAOTIC = "VOLATILE_CHAOTIC"    # متقلب فوضوي
    VOLATILE_TRENDING = "VOLATILE_TRENDING"  # متقلب مع اتجاه
    BREAKOUT_IMMINENT = "BREAKOUT_IMMINENT"  # اختراق وشيك
    TRAP_FORMATION = "TRAP_FORMATION"        # تشكيل فخ
    CONSOLIDATION = "CONSOLIDATION"          # توطيد
    REVERSAL_PATTERN = "REVERSAL_PATTERN"    # نمط انعكاس

class TrendStrength(Enum):
    """قوة الاتجاه"""
    VERY_STRONG = "VERY_STRONG"    # قوي جداً
    STRONG = "STRONG"              # قوي
    MODERATE = "MODERATE"          # متوسط
    WEAK = "WEAK"                  # ضعيف
    VERY_WEAK = "VERY_WEAK"        # ضعيف جداً

class MarketStateClassifier:
    """مصنف حالة السوق المتقدم"""

    def __init__(self):
        """تهيئة مصنف حالة السوق"""
        self.db_repo = HistoricalDataRepository()
        self.atr_analyzer = atr_volatility_analyzer
        self.market_evaluator = market_evaluator
        
        # معايير التصنيف
        self.classification_criteria = {
            'trend_strength_thresholds': {
                'very_strong': 80.0,
                'strong': 60.0,
                'moderate': 40.0,
                'weak': 20.0
            },
            'volatility_thresholds': {
                'very_low': 0.5,
                'low': 1.0,
                'normal': 2.0,
                'high': 3.5,
                'very_high': 5.0
            },
            'range_detection': {
                'tight_range_ratio': 0.01,  # 1% من السعر
                'wide_range_ratio': 0.05,   # 5% من السعر
                'breakout_threshold': 0.8    # 80% من النطاق
            },
            'pattern_detection': {
                'reversal_confirmation': 3,   # عدد الشموع للتأكيد
                'trap_detection_period': 10, # فترة كشف الفخ
                'consolidation_period': 15   # فترة التوطيد
            }
        }
        
        # إحصائيات التصنيف
        self.classification_stats = {
            'total_classifications': 0,
            'state_distribution': {},
            'accuracy_tracking': {},
            'last_update': None
        }
        
        logger.info("تم تهيئة مصنف حالة السوق المتقدم")

    async def classify_market_condition(self, currency_pair: str, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تصنيف حالة السوق"""
        return await self.classify_market_state(currency_pair, 5)

    async def classify_market_state(self, currency_pair: str, timeframe: int = 5) -> Dict[str, Any]:
        """تصنيف حالة السوق الشامل"""
        try:
            logger.info(f"🔍 تصنيف حالة السوق للزوج {currency_pair} - إطار زمني {timeframe} دقيقة")
            
            # جلب البيانات الحديثة
            candles_data = await self._get_market_data(currency_pair, count=100)
            
            if not candles_data or len(candles_data) < 20:
                raise ValueError(f"بيانات غير كافية لتصنيف السوق: {currency_pair}")
            
            # تحليل المكونات المختلفة
            trend_analysis = await self._analyze_trend_comprehensive(candles_data)
            volatility_analysis = await self._analyze_volatility_comprehensive(currency_pair, candles_data)
            range_analysis = await self._analyze_range_behavior(candles_data)
            pattern_analysis = await self._detect_market_patterns(candles_data)
            
            # تصنيف الحالة النهائية
            market_state = self._classify_final_state(
                trend_analysis, volatility_analysis, range_analysis, pattern_analysis
            )
            
            # تقييم الثقة في التصنيف
            confidence_score = self._calculate_classification_confidence(
                trend_analysis, volatility_analysis, range_analysis, pattern_analysis
            )
            
            # إنشاء التقرير النهائي
            classification_result = {
                'currency_pair': currency_pair,
                'timeframe': timeframe,
                'market_state': market_state.value,
                'confidence_score': confidence_score,
                'trend_analysis': trend_analysis,
                'volatility_analysis': volatility_analysis,
                'range_analysis': range_analysis,
                'pattern_analysis': pattern_analysis,
                'trading_implications': self._get_trading_implications(market_state, confidence_score),
                'risk_assessment': self._assess_state_risks(market_state, volatility_analysis),
                'timestamp': datetime.now().isoformat(),
                'classification_id': self._generate_classification_id(currency_pair, timeframe)
            }
            
            # تحديث الإحصائيات
            self._update_classification_stats(market_state, confidence_score)
            
            logger.info(f"✅ تصنيف السوق: {market_state.value} بثقة {confidence_score:.1f}%")
            return classification_result
            
        except Exception as e:
            logger.error(f"خطأ في تصنيف حالة السوق: {str(e)}")
            raise

    async def _get_market_data(self, currency_pair: str, count: int = 100) -> List[Dict[str, Any]]:
        """جلب بيانات السوق"""
        try:
            candles = self.db_repo.get_latest_candles(currency_pair, count=count)
            
            if not candles:
                logger.warning(f"لا توجد بيانات للزوج {currency_pair}")
                return []
            
            # تحويل إلى تنسيق قياسي
            formatted_candles = []
            for candle in candles:
                formatted_candles.append({
                    'timestamp': candle['timestamp'],
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle['volume']) if candle['volume'] else 1.0
                })
            
            return sorted(formatted_candles, key=lambda x: x['timestamp'])
            
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات السوق: {str(e)}")
            return []

    async def _analyze_trend_comprehensive(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الاتجاه الشامل"""
        try:
            if len(candles_data) < 20:
                return {'direction': 'NEUTRAL', 'strength': TrendStrength.VERY_WEAK, 'confidence': 0}
            
            prices = [candle['close'] for candle in candles_data]
            highs = [candle['high'] for candle in candles_data]
            lows = [candle['low'] for candle in candles_data]
            
            # تحليل المتوسطات المتحركة
            ma_short = np.mean(prices[-10:])  # 10 فترات
            ma_medium = np.mean(prices[-20:])  # 20 فترة
            ma_long = np.mean(prices[-50:]) if len(prices) >= 50 else np.mean(prices)  # 50 فترة
            
            # تحليل الميل
            x = np.arange(len(prices))
            slope, intercept = np.polyfit(x, prices, 1)
            
            # تحديد الاتجاه
            current_price = prices[-1]
            
            if ma_short > ma_medium > ma_long and slope > 0:
                direction = 'STRONG_UP'
                strength_score = min(100, abs(slope) * 10000)
            elif ma_short > ma_medium and slope > 0:
                direction = 'UP'
                strength_score = min(80, abs(slope) * 8000)
            elif ma_short < ma_medium < ma_long and slope < 0:
                direction = 'STRONG_DOWN'
                strength_score = min(100, abs(slope) * 10000)
            elif ma_short < ma_medium and slope < 0:
                direction = 'DOWN'
                strength_score = min(80, abs(slope) * 8000)
            else:
                direction = 'NEUTRAL'
                strength_score = 20
            
            # تصنيف قوة الاتجاه
            thresholds = self.classification_criteria['trend_strength_thresholds']
            if strength_score >= thresholds['very_strong']:
                strength = TrendStrength.VERY_STRONG
            elif strength_score >= thresholds['strong']:
                strength = TrendStrength.STRONG
            elif strength_score >= thresholds['moderate']:
                strength = TrendStrength.MODERATE
            elif strength_score >= thresholds['weak']:
                strength = TrendStrength.WEAK
            else:
                strength = TrendStrength.VERY_WEAK
            
            # حساب الثقة
            ma_alignment = self._calculate_ma_alignment(ma_short, ma_medium, ma_long)
            price_momentum = self._calculate_price_momentum(prices[-10:])
            confidence = (strength_score + ma_alignment + price_momentum) / 3
            
            return {
                'direction': direction,
                'strength': strength,
                'strength_score': strength_score,
                'slope': slope,
                'ma_short': ma_short,
                'ma_medium': ma_medium,
                'ma_long': ma_long,
                'confidence': min(95, confidence),
                'momentum': price_momentum
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاه: {str(e)}")
            return {'direction': 'NEUTRAL', 'strength': TrendStrength.VERY_WEAK, 'confidence': 0}

    async def _analyze_volatility_comprehensive(self, currency_pair: str, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل التقلبات الشامل"""
        try:
            # استخدام محلل ATR الموجود
            atr_analysis = self.atr_analyzer.analyze_atr_volatility(currency_pair)
            
            # تحليل إضافي للتقلبات
            prices = [candle['close'] for candle in candles_data]
            returns = np.diff(prices) / prices[:-1]
            
            # حساب مقاييس التقلبات المختلفة
            std_volatility = np.std(returns) * 100
            mean_reversion = self._calculate_mean_reversion(prices)
            volatility_clustering = self._detect_volatility_clustering(returns)
            
            # دمج التحليلات
            if isinstance(atr_analysis, dict) and 'volatility_level' in atr_analysis:
                volatility_level = atr_analysis['volatility_level']
                volatility_trend = atr_analysis.get('volatility_trend', 'STABLE')
            else:
                # تحليل احتياطي
                volatility_level = self._classify_volatility_level(std_volatility)
                volatility_trend = 'STABLE'
            
            return {
                'level': volatility_level,
                'trend': volatility_trend,
                'std_volatility': std_volatility,
                'mean_reversion': mean_reversion,
                'clustering': volatility_clustering,
                'atr_analysis': atr_analysis if isinstance(atr_analysis, dict) else {},
                'stability_score': self._calculate_volatility_stability(returns)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التقلبات: {str(e)}")
            return {
                'level': 'NORMAL',
                'trend': 'STABLE',
                'std_volatility': 1.0,
                'mean_reversion': 50.0,
                'clustering': False,
                'stability_score': 50.0
            }

    async def _analyze_range_behavior(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل سلوك النطاق"""
        try:
            if len(candles_data) < 20:
                return {'type': 'UNKNOWN', 'strength': 0, 'boundaries': {}}
            
            prices = [candle['close'] for candle in candles_data]
            highs = [candle['high'] for candle in candles_data]
            lows = [candle['low'] for candle in candles_data]
            
            # حساب النطاق
            recent_high = max(highs[-20:])
            recent_low = min(lows[-20:])
            range_size = recent_high - recent_low
            current_price = prices[-1]
            
            # نسبة النطاق إلى السعر
            range_ratio = range_size / current_price if current_price > 0 else 0
            
            # تحديد نوع النطاق
            criteria = self.classification_criteria['range_detection']
            
            if range_ratio <= criteria['tight_range_ratio']:
                range_type = 'TIGHT'
            elif range_ratio >= criteria['wide_range_ratio']:
                range_type = 'WIDE'
            else:
                range_type = 'NORMAL'
            
            # تحليل موقع السعر في النطاق
            price_position = (current_price - recent_low) / range_size if range_size > 0 else 0.5
            
            # كشف الاختراق المحتمل
            breakout_probability = self._calculate_breakout_probability(
                prices, highs, lows, price_position, range_ratio
            )
            
            return {
                'type': range_type,
                'size': range_size,
                'ratio': range_ratio,
                'boundaries': {
                    'upper': recent_high,
                    'lower': recent_low,
                    'middle': (recent_high + recent_low) / 2
                },
                'price_position': price_position,
                'breakout_probability': breakout_probability,
                'strength': self._calculate_range_strength(prices, recent_high, recent_low)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النطاق: {str(e)}")
            return {'type': 'UNKNOWN', 'strength': 0, 'boundaries': {}}

    async def _detect_market_patterns(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """كشف أنماط السوق"""
        try:
            if len(candles_data) < 15:
                return {'patterns': [], 'dominant_pattern': 'NONE', 'confidence': 0}

            patterns_detected = []

            # كشف نمط الانعكاس
            reversal_pattern = self._detect_reversal_pattern(candles_data)
            if reversal_pattern['detected']:
                patterns_detected.append(reversal_pattern)

            # كشف نمط الفخ
            trap_pattern = self._detect_trap_formation(candles_data)
            if trap_pattern['detected']:
                patterns_detected.append(trap_pattern)

            # كشف نمط التوطيد
            consolidation_pattern = self._detect_consolidation(candles_data)
            if consolidation_pattern['detected']:
                patterns_detected.append(consolidation_pattern)

            # كشف نمط الاختراق
            breakout_pattern = self._detect_breakout_setup(candles_data)
            if breakout_pattern['detected']:
                patterns_detected.append(breakout_pattern)

            # تحديد النمط المهيمن
            dominant_pattern = 'NONE'
            max_confidence = 0

            for pattern in patterns_detected:
                if pattern['confidence'] > max_confidence:
                    max_confidence = pattern['confidence']
                    dominant_pattern = pattern['type']

            return {
                'patterns': patterns_detected,
                'dominant_pattern': dominant_pattern,
                'confidence': max_confidence,
                'pattern_count': len(patterns_detected)
            }

        except Exception as e:
            logger.error(f"خطأ في كشف الأنماط: {str(e)}")
            return {'patterns': [], 'dominant_pattern': 'NONE', 'confidence': 0}

    def _classify_final_state(self, trend_analysis: Dict, volatility_analysis: Dict,
                             range_analysis: Dict, pattern_analysis: Dict) -> MarketState:
        """تصنيف الحالة النهائية للسوق"""
        try:
            trend_direction = trend_analysis.get('direction', 'NEUTRAL')
            trend_strength = trend_analysis.get('strength', TrendStrength.VERY_WEAK)
            volatility_level = volatility_analysis.get('level', 'NORMAL')
            range_type = range_analysis.get('type', 'NORMAL')
            dominant_pattern = pattern_analysis.get('dominant_pattern', 'NONE')
            breakout_probability = range_analysis.get('breakout_probability', 0)

            # منطق التصنيف المتقدم

            # 1. اتجاه قوي
            if trend_strength in [TrendStrength.VERY_STRONG, TrendStrength.STRONG]:
                if volatility_level in ['HIGH', 'VERY_HIGH']:
                    return MarketState.VOLATILE_TRENDING
                else:
                    return MarketState.STRONG_TRENDING

            # 2. اتجاه ضعيف
            elif trend_strength in [TrendStrength.MODERATE, TrendStrength.WEAK]:
                if volatility_level in ['HIGH', 'VERY_HIGH']:
                    return MarketState.VOLATILE_CHAOTIC
                else:
                    return MarketState.WEAK_TRENDING

            # 3. أنماط خاصة
            elif dominant_pattern == 'REVERSAL':
                return MarketState.REVERSAL_PATTERN
            elif dominant_pattern == 'TRAP':
                return MarketState.TRAP_FORMATION
            elif dominant_pattern == 'CONSOLIDATION':
                return MarketState.CONSOLIDATION

            # 4. اختراق وشيك
            elif breakout_probability > 70:
                return MarketState.BREAKOUT_IMMINENT

            # 5. نطاقات
            elif range_type == 'TIGHT':
                return MarketState.RANGING_TIGHT
            elif range_type == 'WIDE':
                return MarketState.RANGING_WIDE

            # 6. تقلبات عالية بدون اتجاه
            elif volatility_level in ['HIGH', 'VERY_HIGH']:
                return MarketState.VOLATILE_CHAOTIC

            # 7. حالة افتراضية
            else:
                return MarketState.RANGING_TIGHT

        except Exception as e:
            logger.error(f"خطأ في التصنيف النهائي: {str(e)}")
            return MarketState.RANGING_TIGHT

    def _calculate_classification_confidence(self, trend_analysis: Dict, volatility_analysis: Dict,
                                           range_analysis: Dict, pattern_analysis: Dict) -> float:
        """حساب ثقة التصنيف"""
        try:
            # جمع نقاط الثقة من المكونات المختلفة
            trend_confidence = trend_analysis.get('confidence', 0)
            volatility_stability = volatility_analysis.get('stability_score', 50)
            range_strength = range_analysis.get('strength', 0)
            pattern_confidence = pattern_analysis.get('confidence', 0)

            # حساب المتوسط المرجح
            weights = [0.35, 0.25, 0.25, 0.15]  # أوزان المكونات
            scores = [trend_confidence, volatility_stability, range_strength, pattern_confidence]

            weighted_confidence = sum(w * s for w, s in zip(weights, scores)) / sum(weights)

            return min(95.0, max(20.0, weighted_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة التصنيف: {str(e)}")
            return 50.0

    def _get_trading_implications(self, market_state: MarketState, confidence_score: float) -> Dict[str, Any]:
        """الحصول على التداعيات التجارية"""
        try:
            implications = {
                'recommended_strategy': 'WAIT',
                'risk_level': 'MEDIUM',
                'position_sizing': 'NORMAL',
                'entry_conditions': [],
                'exit_conditions': [],
                'timeframe_suitability': {}
            }

            # تحديد الاستراتيجية المناسبة
            if market_state == MarketState.STRONG_TRENDING:
                implications.update({
                    'recommended_strategy': 'TREND_FOLLOWING',
                    'risk_level': 'LOW',
                    'position_sizing': 'LARGE',
                    'entry_conditions': ['اتجاه قوي', 'تأكيد الزخم', 'كسر المقاومة/الدعم'],
                    'exit_conditions': ['ضعف الزخم', 'إشارات انعكاس', 'أهداف الربح']
                })

            elif market_state == MarketState.RANGING_TIGHT:
                implications.update({
                    'recommended_strategy': 'RANGE_TRADING',
                    'risk_level': 'MEDIUM',
                    'position_sizing': 'SMALL',
                    'entry_conditions': ['اقتراب من حدود النطاق', 'إشارات ارتداد'],
                    'exit_conditions': ['وصول للحد المقابل', 'كسر النطاق']
                })

            elif market_state == MarketState.VOLATILE_CHAOTIC:
                implications.update({
                    'recommended_strategy': 'AVOID_TRADING',
                    'risk_level': 'VERY_HIGH',
                    'position_sizing': 'VERY_SMALL',
                    'entry_conditions': ['تجنب التداول'],
                    'exit_conditions': ['إغلاق فوري عند الخسارة']
                })

            elif market_state == MarketState.BREAKOUT_IMMINENT:
                implications.update({
                    'recommended_strategy': 'BREAKOUT_TRADING',
                    'risk_level': 'MEDIUM',
                    'position_sizing': 'NORMAL',
                    'entry_conditions': ['كسر مؤكد للنطاق', 'حجم تداول عالي'],
                    'exit_conditions': ['فشل الاختراق', 'عودة للنطاق']
                })

            elif market_state == MarketState.TRAP_FORMATION:
                implications.update({
                    'recommended_strategy': 'AVOID_TRADING',
                    'risk_level': 'HIGH',
                    'position_sizing': 'VERY_SMALL',
                    'entry_conditions': ['انتظار وضوح الاتجاه'],
                    'exit_conditions': ['إغلاق سريع عند الخسارة']
                })

            # تعديل بناءً على مستوى الثقة
            if confidence_score < 60:
                implications['risk_level'] = 'HIGH'
                implications['position_sizing'] = 'SMALL'
            elif confidence_score > 80:
                if implications['risk_level'] == 'LOW':
                    implications['position_sizing'] = 'LARGE'

            # ملاءمة الإطارات الزمنية
            implications['timeframe_suitability'] = {
                '1m': self._evaluate_timeframe_suitability(market_state, 1),
                '3m': self._evaluate_timeframe_suitability(market_state, 3),
                '5m': self._evaluate_timeframe_suitability(market_state, 5),
                '15m': self._evaluate_timeframe_suitability(market_state, 15)
            }

            return implications

        except Exception as e:
            logger.error(f"خطأ في تحديد التداعيات التجارية: {str(e)}")
            return {'recommended_strategy': 'WAIT', 'risk_level': 'HIGH'}

    def _assess_state_risks(self, market_state: MarketState, volatility_analysis: Dict) -> Dict[str, Any]:
        """تقييم مخاطر الحالة"""
        try:
            volatility_level = volatility_analysis.get('level', 'NORMAL')
            volatility_trend = volatility_analysis.get('trend', 'STABLE')

            risk_factors = []
            risk_score = 50  # نقطة البداية

            # تقييم المخاطر حسب حالة السوق
            if market_state == MarketState.VOLATILE_CHAOTIC:
                risk_score = 90
                risk_factors.extend(['تقلبات عالية', 'عدم قابلية التنبؤ', 'تغيرات سريعة'])

            elif market_state == MarketState.TRAP_FORMATION:
                risk_score = 80
                risk_factors.extend(['إشارات مضللة', 'انعكاسات مفاجئة', 'فخاخ السوق'])

            elif market_state == MarketState.STRONG_TRENDING:
                risk_score = 30
                risk_factors.extend(['انعكاس محتمل', 'تصحيحات مؤقتة'])

            elif market_state == MarketState.RANGING_TIGHT:
                risk_score = 40
                risk_factors.extend(['اختراق مفاجئ', 'إشارات خاطئة عند الحدود'])

            # تعديل بناءً على التقلبات
            if volatility_level in ['HIGH', 'VERY_HIGH']:
                risk_score += 15
                risk_factors.append('تقلبات عالية')

            if volatility_trend == 'INCREASING_RAPIDLY':
                risk_score += 10
                risk_factors.append('تزايد سريع في التقلبات')

            # تحديد مستوى المخاطر
            if risk_score >= 80:
                risk_level = 'VERY_HIGH'
            elif risk_score >= 65:
                risk_level = 'HIGH'
            elif risk_score >= 45:
                risk_level = 'MEDIUM'
            elif risk_score >= 30:
                risk_level = 'LOW'
            else:
                risk_level = 'VERY_LOW'

            return {
                'risk_level': risk_level,
                'risk_score': min(95, risk_score),
                'risk_factors': risk_factors,
                'mitigation_strategies': self._suggest_risk_mitigation(market_state, risk_factors)
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر: {str(e)}")
            return {'risk_level': 'HIGH', 'risk_score': 70, 'risk_factors': []}

    # الوظائف المساعدة
    def _calculate_ma_alignment(self, ma_short: float, ma_medium: float, ma_long: float) -> float:
        """حساب محاذاة المتوسطات المتحركة"""
        try:
            if ma_short > ma_medium > ma_long:
                return 90  # محاذاة صعودية قوية
            elif ma_short > ma_medium:
                return 70  # محاذاة صعودية متوسطة
            elif ma_short < ma_medium < ma_long:
                return 90  # محاذاة هبوطية قوية
            elif ma_short < ma_medium:
                return 70  # محاذاة هبوطية متوسطة
            else:
                return 30  # لا توجد محاذاة واضحة
        except:
            return 50

    def _calculate_price_momentum(self, recent_prices: List[float]) -> float:
        """حساب زخم السعر"""
        try:
            if len(recent_prices) < 3:
                return 50

            momentum_score = 0
            for i in range(1, len(recent_prices)):
                if recent_prices[i] > recent_prices[i-1]:
                    momentum_score += 10
                elif recent_prices[i] < recent_prices[i-1]:
                    momentum_score -= 10

            # تطبيع النتيجة
            max_possible = (len(recent_prices) - 1) * 10
            normalized = ((momentum_score + max_possible) / (2 * max_possible)) * 100
            return max(0, min(100, normalized))
        except:
            return 50

    def _calculate_mean_reversion(self, prices: List[float]) -> float:
        """حساب العودة للمتوسط"""
        try:
            if len(prices) < 10:
                return 50

            mean_price = np.mean(prices)
            current_price = prices[-1]
            std_dev = np.std(prices)

            if std_dev == 0:
                return 50

            z_score = abs(current_price - mean_price) / std_dev

            # كلما زاد z-score، زادت احتمالية العودة للمتوسط
            reversion_probability = min(90, z_score * 30)
            return reversion_probability
        except:
            return 50

    def _detect_volatility_clustering(self, returns: np.ndarray) -> bool:
        """كشف تجمع التقلبات"""
        try:
            if len(returns) < 10:
                return False

            # حساب التقلبات المتحركة
            window_size = 5
            volatilities = []

            for i in range(window_size, len(returns)):
                window_vol = np.std(returns[i-window_size:i])
                volatilities.append(window_vol)

            if len(volatilities) < 3:
                return False

            # كشف التجمع (فترات متتالية من التقلبات العالية أو المنخفضة)
            high_vol_threshold = np.percentile(volatilities, 75)
            consecutive_high = 0
            max_consecutive = 0

            for vol in volatilities:
                if vol > high_vol_threshold:
                    consecutive_high += 1
                    max_consecutive = max(max_consecutive, consecutive_high)
                else:
                    consecutive_high = 0

            return max_consecutive >= 3
        except:
            return False

    def _classify_volatility_level(self, std_volatility: float) -> str:
        """تصنيف مستوى التقلبات"""
        thresholds = self.classification_criteria['volatility_thresholds']

        if std_volatility <= thresholds['very_low']:
            return 'VERY_LOW'
        elif std_volatility <= thresholds['low']:
            return 'LOW'
        elif std_volatility <= thresholds['normal']:
            return 'NORMAL'
        elif std_volatility <= thresholds['high']:
            return 'HIGH'
        else:
            return 'VERY_HIGH'

    def _calculate_volatility_stability(self, returns: np.ndarray) -> float:
        """حساب استقرار التقلبات"""
        try:
            if len(returns) < 10:
                return 50

            # حساب التقلبات المتحركة
            window_size = 5
            volatilities = []

            for i in range(window_size, len(returns)):
                window_vol = np.std(returns[i-window_size:i])
                volatilities.append(window_vol)

            if len(volatilities) < 2:
                return 50

            # حساب استقرار التقلبات (كلما قل التباين، زاد الاستقرار)
            vol_std = np.std(volatilities)
            vol_mean = np.mean(volatilities)

            if vol_mean == 0:
                return 50

            coefficient_of_variation = vol_std / vol_mean
            stability_score = max(0, 100 - (coefficient_of_variation * 100))

            return min(95, stability_score)
        except:
            return 50

    def _calculate_breakout_probability(self, prices: List[float], highs: List[float],
                                      lows: List[float], price_position: float, range_ratio: float) -> float:
        """حساب احتمالية الاختراق"""
        try:
            probability = 0

            # عامل موقع السعر
            if price_position > 0.8 or price_position < 0.2:
                probability += 30  # قريب من الحدود

            # عامل ضيق النطاق
            if range_ratio < 0.01:
                probability += 25  # نطاق ضيق جداً
            elif range_ratio < 0.02:
                probability += 15  # نطاق ضيق

            # عامل الزخم
            recent_momentum = self._calculate_price_momentum(prices[-5:])
            if recent_momentum > 70 or recent_momentum < 30:
                probability += 20  # زخم قوي

            # عامل الحجم (محاكاة)
            volume_factor = 15  # افتراضي
            probability += volume_factor

            return min(95, probability)
        except:
            return 30

    def _calculate_range_strength(self, prices: List[float], upper: float, lower: float) -> float:
        """حساب قوة النطاق"""
        try:
            if len(prices) < 10:
                return 50

            # عدد مرات اختبار الحدود
            upper_tests = sum(1 for p in prices[-20:] if abs(p - upper) / upper < 0.005)
            lower_tests = sum(1 for p in prices[-20:] if abs(p - lower) / lower < 0.005)

            # قوة النطاق تزيد مع عدد الاختبارات
            total_tests = upper_tests + lower_tests
            strength = min(90, total_tests * 15)

            return max(20, strength)
        except:
            return 50

    def _detect_reversal_pattern(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """كشف نمط الانعكاس"""
        try:
            if len(candles_data) < 5:
                return {'detected': False, 'type': 'REVERSAL', 'confidence': 0}

            recent_candles = candles_data[-5:]

            # بحث عن أنماط الانعكاس البسيطة
            # نمط المطرقة أو الدوجي
            reversal_signals = 0

            for candle in recent_candles:
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']

                if total_range > 0:
                    body_ratio = body_size / total_range

                    # شمعة دوجي (جسم صغير)
                    if body_ratio < 0.1:
                        reversal_signals += 1

                    # شمعة مطرقة (ظل طويل)
                    if candle['close'] > candle['open']:  # شمعة صعودية
                        lower_shadow = candle['open'] - candle['low']
                        if lower_shadow > body_size * 2:
                            reversal_signals += 1

            confidence = min(80, reversal_signals * 20)
            detected = confidence > 40

            return {
                'detected': detected,
                'type': 'REVERSAL',
                'confidence': confidence,
                'signals_count': reversal_signals
            }
        except:
            return {'detected': False, 'type': 'REVERSAL', 'confidence': 0}

    def _detect_trap_formation(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """كشف تشكيل الفخ"""
        try:
            if len(candles_data) < 10:
                return {'detected': False, 'type': 'TRAP', 'confidence': 0}

            recent_candles = candles_data[-10:]
            prices = [c['close'] for c in recent_candles]

            # البحث عن حركة خادعة (اختراق وهمي ثم عودة)
            trap_signals = 0

            # كشف الاختراق الوهمي
            max_price = max(prices)
            min_price = min(prices)
            current_price = prices[-1]

            # إذا وصل السعر لأعلى مستوى ثم انخفض بسرعة
            max_index = prices.index(max_price)
            if max_index < len(prices) - 2:  # ليس في آخر شمعتين
                decline_after_high = (max_price - current_price) / max_price
                if decline_after_high > 0.01:  # انخفاض أكثر من 1%
                    trap_signals += 1

            # نفس الشيء للحد الأدنى
            min_index = prices.index(min_price)
            if min_index < len(prices) - 2:
                rise_after_low = (current_price - min_price) / min_price
                if rise_after_low > 0.01:
                    trap_signals += 1

            confidence = min(75, trap_signals * 35)
            detected = confidence > 30

            return {
                'detected': detected,
                'type': 'TRAP',
                'confidence': confidence,
                'signals_count': trap_signals
            }
        except:
            return {'detected': False, 'type': 'TRAP', 'confidence': 0}

    def _detect_consolidation(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """كشف نمط التوطيد"""
        try:
            if len(candles_data) < 15:
                return {'detected': False, 'type': 'CONSOLIDATION', 'confidence': 0}

            recent_candles = candles_data[-15:]
            prices = [c['close'] for c in recent_candles]

            # حساب النطاق السعري
            price_range = max(prices) - min(prices)
            avg_price = np.mean(prices)
            range_ratio = price_range / avg_price if avg_price > 0 else 0

            # التوطيد يتميز بنطاق ضيق ومستقر
            consolidation_signals = 0

            # نطاق ضيق
            if range_ratio < 0.02:  # أقل من 2%
                consolidation_signals += 1

            # استقرار السعر حول المتوسط
            price_stability = sum(1 for p in prices if abs(p - avg_price) / avg_price < 0.01)
            if price_stability > len(prices) * 0.6:  # 60% من الأسعار قريبة من المتوسط
                consolidation_signals += 1

            # قلة التقلبات
            price_std = np.std(prices)
            if price_std / avg_price < 0.01:  # تقلبات منخفضة
                consolidation_signals += 1

            confidence = min(70, consolidation_signals * 25)
            detected = confidence > 40

            return {
                'detected': detected,
                'type': 'CONSOLIDATION',
                'confidence': confidence,
                'range_ratio': range_ratio,
                'stability_score': price_stability / len(prices) * 100
            }
        except:
            return {'detected': False, 'type': 'CONSOLIDATION', 'confidence': 0}

    def _detect_breakout_setup(self, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """كشف إعداد الاختراق"""
        try:
            if len(candles_data) < 10:
                return {'detected': False, 'type': 'BREAKOUT_SETUP', 'confidence': 0}

            recent_candles = candles_data[-10:]
            prices = [c['close'] for c in recent_candles]
            volumes = [c.get('volume', 1.0) for c in recent_candles]

            breakout_signals = 0

            # ضغط السعر (نطاق ضيق)
            price_range = max(prices) - min(prices)
            avg_price = np.mean(prices)
            if price_range / avg_price < 0.015:  # نطاق ضيق
                breakout_signals += 1

            # زيادة الحجم (محاكاة)
            recent_volume = np.mean(volumes[-3:])
            earlier_volume = np.mean(volumes[:-3])
            if recent_volume > earlier_volume * 1.2:  # زيادة 20% في الحجم
                breakout_signals += 1

            # اقتراب من حدود النطاق
            current_price = prices[-1]
            price_position = (current_price - min(prices)) / price_range if price_range > 0 else 0.5
            if price_position > 0.8 or price_position < 0.2:
                breakout_signals += 1

            confidence = min(75, breakout_signals * 25)
            detected = confidence > 40

            return {
                'detected': detected,
                'type': 'BREAKOUT_SETUP',
                'confidence': confidence,
                'signals_count': breakout_signals,
                'price_position': price_position
            }
        except:
            return {'detected': False, 'type': 'BREAKOUT_SETUP', 'confidence': 0}

    def _evaluate_timeframe_suitability(self, market_state: MarketState, timeframe: int) -> str:
        """تقييم ملاءمة الإطار الزمني"""
        try:
            # قواعد ملاءمة الإطارات الزمنية
            suitability_rules = {
                MarketState.STRONG_TRENDING: {1: 'GOOD', 3: 'EXCELLENT', 5: 'EXCELLENT', 15: 'GOOD'},
                MarketState.WEAK_TRENDING: {1: 'POOR', 3: 'FAIR', 5: 'GOOD', 15: 'EXCELLENT'},
                MarketState.RANGING_TIGHT: {1: 'EXCELLENT', 3: 'GOOD', 5: 'FAIR', 15: 'POOR'},
                MarketState.RANGING_WIDE: {1: 'GOOD', 3: 'GOOD', 5: 'GOOD', 15: 'FAIR'},
                MarketState.VOLATILE_CHAOTIC: {1: 'POOR', 3: 'POOR', 5: 'POOR', 15: 'FAIR'},
                MarketState.VOLATILE_TRENDING: {1: 'FAIR', 3: 'GOOD', 5: 'GOOD', 15: 'EXCELLENT'},
                MarketState.BREAKOUT_IMMINENT: {1: 'GOOD', 3: 'EXCELLENT', 5: 'GOOD', 15: 'FAIR'},
                MarketState.TRAP_FORMATION: {1: 'POOR', 3: 'POOR', 5: 'POOR', 15: 'POOR'},
                MarketState.CONSOLIDATION: {1: 'FAIR', 3: 'FAIR', 5: 'GOOD', 15: 'EXCELLENT'},
                MarketState.REVERSAL_PATTERN: {1: 'GOOD', 3: 'EXCELLENT', 5: 'GOOD', 15: 'FAIR'}
            }

            return suitability_rules.get(market_state, {}).get(timeframe, 'FAIR')
        except:
            return 'FAIR'

    def _suggest_risk_mitigation(self, market_state: MarketState, risk_factors: List[str]) -> List[str]:
        """اقتراح استراتيجيات تخفيف المخاطر"""
        strategies = []

        if market_state == MarketState.VOLATILE_CHAOTIC:
            strategies.extend([
                'تجنب التداول في هذه الحالة',
                'انتظار استقرار السوق',
                'استخدام أحجام صفقات صغيرة جداً'
            ])

        elif market_state == MarketState.TRAP_FORMATION:
            strategies.extend([
                'انتظار تأكيد الاتجاه',
                'تجنب الدخول عند الاختراقات الأولى',
                'استخدام وقف خسارة ضيق'
            ])

        elif 'تقلبات عالية' in risk_factors:
            strategies.extend([
                'تقليل حجم الصفقات',
                'توسيع وقف الخسارة',
                'تجنب التداول في الأخبار'
            ])

        if not strategies:
            strategies.append('مراقبة مستمرة للسوق')

        return strategies

    def _generate_classification_id(self, currency_pair: str, timeframe: int) -> str:
        """إنشاء معرف فريد للتصنيف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"MARKET_CLASS_{currency_pair}_{timeframe}m_{timestamp}"

    def _update_classification_stats(self, market_state: MarketState, confidence_score: float):
        """تحديث إحصائيات التصنيف"""
        try:
            self.classification_stats['total_classifications'] += 1

            state_name = market_state.value
            if state_name not in self.classification_stats['state_distribution']:
                self.classification_stats['state_distribution'][state_name] = 0
            self.classification_stats['state_distribution'][state_name] += 1

            self.classification_stats['last_update'] = datetime.now().isoformat()
        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    async def classify_multiple_pairs(self, currency_pairs: List[str], timeframe: int = 5) -> Dict[str, Any]:
        """تصنيف حالة السوق لعدة أزواج"""
        try:
            logger.info(f"🔍 تصنيف حالة السوق لـ {len(currency_pairs)} أزواج")

            classifications = {}
            successful_classifications = 0

            for pair in currency_pairs:
                try:
                    classification = await self.classify_market_state(pair, timeframe)
                    classifications[pair] = classification
                    successful_classifications += 1
                except Exception as e:
                    logger.warning(f"فشل تصنيف {pair}: {str(e)}")
                    classifications[pair] = {'error': str(e)}

            # تحليل النتائج الإجمالية
            summary = self._analyze_market_summary(classifications)

            result = {
                'classifications': classifications,
                'summary': summary,
                'successful_classifications': successful_classifications,
                'total_pairs': len(currency_pairs),
                'success_rate': (successful_classifications / len(currency_pairs)) * 100,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"✅ تم تصنيف {successful_classifications}/{len(currency_pairs)} أزواج")
            return result

        except Exception as e:
            logger.error(f"خطأ في التصنيف المتعدد: {str(e)}")
            raise

    def _analyze_market_summary(self, classifications: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل ملخص السوق"""
        try:
            successful_classifications = {k: v for k, v in classifications.items() if 'error' not in v}

            if not successful_classifications:
                return {'message': 'لا توجد تصنيفات ناجحة'}

            # تجميع الحالات
            state_distribution = {}
            confidence_levels = {'high': 0, 'medium': 0, 'low': 0}
            risk_levels = {'low': 0, 'medium': 0, 'high': 0, 'very_high': 0}

            total_confidence = 0

            for pair, classification in successful_classifications.items():
                market_state = classification.get('market_state', 'UNKNOWN')
                confidence = classification.get('confidence_score', 0)
                risk_level = classification.get('risk_assessment', {}).get('risk_level', 'MEDIUM')

                # تجميع الحالات
                if market_state not in state_distribution:
                    state_distribution[market_state] = 0
                state_distribution[market_state] += 1

                total_confidence += confidence

                # تصنيف مستوى الثقة
                if confidence >= 75:
                    confidence_levels['high'] += 1
                elif confidence >= 60:
                    confidence_levels['medium'] += 1
                else:
                    confidence_levels['low'] += 1

                # تجميع مستويات المخاطر
                risk_key = risk_level.lower().replace(' ', '_')
                if risk_key in risk_levels:
                    risk_levels[risk_key] += 1

            # حساب المتوسطات
            avg_confidence = total_confidence / len(successful_classifications)

            # تحديد الحالة السائدة
            dominant_state = max(state_distribution, key=state_distribution.get)

            return {
                'total_analyzed': len(successful_classifications),
                'state_distribution': state_distribution,
                'dominant_state': dominant_state,
                'average_confidence': avg_confidence,
                'confidence_distribution': confidence_levels,
                'risk_distribution': risk_levels,
                'market_sentiment': self._determine_overall_sentiment(state_distribution, avg_confidence)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل ملخص السوق: {str(e)}")
            return {'error': str(e)}

    def _determine_overall_sentiment(self, state_distribution: Dict[str, int], avg_confidence: float) -> str:
        """تحديد المعنويات الإجمالية للسوق"""
        try:
            total_pairs = sum(state_distribution.values())

            # حساب نسب الحالات المختلفة
            trending_states = ['STRONG_TRENDING', 'WEAK_TRENDING', 'VOLATILE_TRENDING']
            ranging_states = ['RANGING_TIGHT', 'RANGING_WIDE', 'CONSOLIDATION']
            volatile_states = ['VOLATILE_CHAOTIC', 'TRAP_FORMATION']

            trending_count = sum(state_distribution.get(state, 0) for state in trending_states)
            ranging_count = sum(state_distribution.get(state, 0) for state in ranging_states)
            volatile_count = sum(state_distribution.get(state, 0) for state in volatile_states)

            trending_ratio = trending_count / total_pairs if total_pairs > 0 else 0
            ranging_ratio = ranging_count / total_pairs if total_pairs > 0 else 0
            volatile_ratio = volatile_count / total_pairs if total_pairs > 0 else 0

            # تحديد المعنويات
            if trending_ratio >= 0.6 and avg_confidence >= 70:
                return "اتجاهي قوي"
            elif trending_ratio >= 0.4:
                return "اتجاهي"
            elif ranging_ratio >= 0.6:
                return "نطاقي"
            elif volatile_ratio >= 0.4:
                return "متقلب"
            elif avg_confidence < 60:
                return "غير واضح"
            else:
                return "مختلط"

        except Exception as e:
            logger.warning(f"خطأ في تحديد المعنويات: {str(e)}")
            return "غير محدد"

    def get_classification_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التصنيف"""
        return {
            'engine_name': 'Market State Classifier',
            'classification_stats': self.classification_stats,
            'supported_states': [state.value for state in MarketState],
            'supported_pairs': len(CURRENCY_PAIRS),
            'classification_criteria': self.classification_criteria,
            'last_update': datetime.now().isoformat()
        }

# إنشاء instance افتراضي
market_state_classifier = MarketStateClassifier()
