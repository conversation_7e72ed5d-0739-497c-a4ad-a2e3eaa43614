"""
نظام تتبع التنبؤات والتحقق من الدقة
Prediction Tracking and Accuracy Verification System
"""

import os
import sys
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_models.direction_prediction_engine import direction_prediction_engine
from database.repository import HistoricalDataRepository
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("prediction_tracker")

class PredictionTracker:
    """نظام تتبع التنبؤات والتحقق من الدقة"""
    
    def __init__(self):
        """تهيئة نظام التتبع"""
        self.prediction_engine = direction_prediction_engine
        self.db_repo = HistoricalDataRepository()
        
        # إعدادات التتبع
        self.tracking_enabled = True
        self.auto_verification = True
        self.verification_interval = 300  # 5 دقائق
        
        # إحصائيات مفصلة
        self.detailed_stats = {
            'daily_performance': {},
            'hourly_performance': {},
            'pair_performance': {},
            'timeframe_performance': {},
            'signal_strength_performance': {}
        }
        
        logger.info("تم تهيئة نظام تتبع التنبؤات")

    async def start_tracking_service(self):
        """بدء خدمة التتبع التلقائي"""
        try:
            if not self.tracking_enabled:
                logger.info("خدمة التتبع معطلة")
                return
            
            logger.info("🚀 بدء خدمة تتبع التنبؤات التلقائي")
            
            while self.tracking_enabled:
                try:
                    # التحقق من التنبؤات المعلقة
                    await self._verify_pending_predictions()
                    
                    # تحديث الإحصائيات المفصلة
                    await self._update_detailed_statistics()
                    
                    # تنظيف البيانات القديمة
                    await self._cleanup_old_data()
                    
                    # انتظار الفترة التالية
                    await asyncio.sleep(self.verification_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة التتبع: {str(e)}")
                    await asyncio.sleep(60)  # انتظار دقيقة عند الخطأ
            
        except Exception as e:
            logger.error(f"خطأ في خدمة التتبع: {str(e)}")

    async def _verify_pending_predictions(self):
        """التحقق من التنبؤات المعلقة"""
        try:
            active_predictions = self.prediction_engine.active_predictions
            
            for prediction_id, prediction_data in active_predictions.items():
                if not prediction_data.get('verified', False):
                    try:
                        verification_result = await self.prediction_engine.verify_prediction_accuracy(prediction_id)
                        
                        if verification_result.get('is_correct') is not None:
                            logger.info(f"تم التحقق من التنبؤ {prediction_id}: {'✅ صحيح' if verification_result['is_correct'] else '❌ خاطئ'}")
                            
                    except Exception as e:
                        logger.warning(f"فشل التحقق من التنبؤ {prediction_id}: {str(e)}")
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من التنبؤات المعلقة: {str(e)}")

    async def _update_detailed_statistics(self):
        """تحديث الإحصائيات المفصلة"""
        try:
            current_time = datetime.now()
            current_date = current_time.date().isoformat()
            current_hour = current_time.hour
            
            # الحصول على إحصائيات الأداء الحالية
            performance_stats = self.prediction_engine.get_performance_statistics()
            
            # تحديث الأداء اليومي
            if current_date not in self.detailed_stats['daily_performance']:
                self.detailed_stats['daily_performance'][current_date] = {
                    'total': 0, 'successful': 0, 'accuracy': 0.0
                }
            
            daily_stats = self.detailed_stats['daily_performance'][current_date]
            daily_stats.update({
                'total': performance_stats.get('total_predictions', 0),
                'successful': performance_stats.get('successful_predictions', 0),
                'accuracy': performance_stats.get('overall_accuracy', 0.0)
            })
            
            # تحديث الأداء الساعي
            hour_key = f"{current_date}_{current_hour:02d}"
            if hour_key not in self.detailed_stats['hourly_performance']:
                self.detailed_stats['hourly_performance'][hour_key] = {
                    'total': 0, 'successful': 0, 'accuracy': 0.0
                }
            
            # تحديث أداء الأزواج والإطارات الزمنية
            self.detailed_stats['pair_performance'] = performance_stats.get('accuracy_by_pair', {})
            self.detailed_stats['timeframe_performance'] = performance_stats.get('accuracy_by_timeframe', {})
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات المفصلة: {str(e)}")

    async def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=7)).date().isoformat()
            
            # تنظيف الأداء اليومي (الاحتفاظ بآخر 7 أيام)
            old_dates = [date for date in self.detailed_stats['daily_performance'].keys() if date < cutoff_date]
            for date in old_dates:
                del self.detailed_stats['daily_performance'][date]
            
            # تنظيف الأداء الساعي (الاحتفاظ بآخر 24 ساعة)
            cutoff_hour = datetime.now() - timedelta(hours=24)
            old_hours = []
            
            for hour_key in self.detailed_stats['hourly_performance'].keys():
                try:
                    date_str, hour_str = hour_key.split('_')
                    hour_datetime = datetime.strptime(f"{date_str} {hour_str}", "%Y-%m-%d %H")
                    if hour_datetime < cutoff_hour:
                        old_hours.append(hour_key)
                except:
                    old_hours.append(hour_key)  # إزالة المفاتيح غير الصحيحة
            
            for hour_key in old_hours:
                del self.detailed_stats['hourly_performance'][hour_key]
            
            logger.info("تم تنظيف البيانات القديمة")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")

    async def generate_performance_report(self, period: str = "daily") -> Dict[str, Any]:
        """إنشاء تقرير أداء مفصل"""
        try:
            logger.info(f"📊 إنشاء تقرير أداء {period}")
            
            current_time = datetime.now()
            performance_stats = self.prediction_engine.get_performance_statistics()
            
            report = {
                'report_type': period,
                'generated_at': current_time.isoformat(),
                'overall_performance': performance_stats,
                'detailed_analysis': {}
            }
            
            if period == "daily":
                report['detailed_analysis'] = await self._generate_daily_analysis()
            elif period == "hourly":
                report['detailed_analysis'] = await self._generate_hourly_analysis()
            elif period == "comprehensive":
                report['detailed_analysis'] = await self._generate_comprehensive_analysis()
            
            # إضافة توصيات للتحسين
            report['improvement_recommendations'] = await self._generate_improvement_recommendations(performance_stats)
            
            logger.info("✅ تم إنشاء تقرير الأداء")
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الأداء: {str(e)}")
            return {'error': str(e)}

    async def _generate_daily_analysis(self) -> Dict[str, Any]:
        """تحليل الأداء اليومي"""
        try:
            daily_data = self.detailed_stats['daily_performance']
            
            if not daily_data:
                return {'message': 'لا توجد بيانات يومية'}
            
            # حساب الاتجاهات
            dates = sorted(daily_data.keys())
            accuracies = [daily_data[date]['accuracy'] for date in dates]
            
            # حساب المتوسطات
            avg_accuracy = sum(accuracies) / len(accuracies) if accuracies else 0
            
            # العثور على أفضل وأسوأ يوم
            best_day = max(dates, key=lambda d: daily_data[d]['accuracy']) if dates else None
            worst_day = min(dates, key=lambda d: daily_data[d]['accuracy']) if dates else None
            
            return {
                'period_days': len(dates),
                'average_accuracy': avg_accuracy,
                'best_day': {
                    'date': best_day,
                    'accuracy': daily_data[best_day]['accuracy'] if best_day else 0,
                    'total_predictions': daily_data[best_day]['total'] if best_day else 0
                },
                'worst_day': {
                    'date': worst_day,
                    'accuracy': daily_data[worst_day]['accuracy'] if worst_day else 0,
                    'total_predictions': daily_data[worst_day]['total'] if worst_day else 0
                },
                'trend': self._calculate_trend(accuracies),
                'daily_breakdown': daily_data
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل اليومي: {str(e)}")
            return {'error': str(e)}

    async def _generate_hourly_analysis(self) -> Dict[str, Any]:
        """تحليل الأداء الساعي"""
        try:
            hourly_data = self.detailed_stats['hourly_performance']
            
            if not hourly_data:
                return {'message': 'لا توجد بيانات ساعية'}
            
            # تجميع البيانات حسب الساعة
            hour_performance = {}
            for hour_key, data in hourly_data.items():
                try:
                    hour = int(hour_key.split('_')[1])
                    if hour not in hour_performance:
                        hour_performance[hour] = {'total': 0, 'successful': 0, 'count': 0}
                    
                    hour_performance[hour]['total'] += data['total']
                    hour_performance[hour]['successful'] += data['successful']
                    hour_performance[hour]['count'] += 1
                except:
                    continue
            
            # حساب متوسط الدقة لكل ساعة
            for hour_data in hour_performance.values():
                if hour_data['total'] > 0:
                    hour_data['accuracy'] = (hour_data['successful'] / hour_data['total']) * 100
                else:
                    hour_data['accuracy'] = 0
            
            # العثور على أفضل وأسوأ ساعة
            best_hour = max(hour_performance.keys(), key=lambda h: hour_performance[h]['accuracy']) if hour_performance else None
            worst_hour = min(hour_performance.keys(), key=lambda h: hour_performance[h]['accuracy']) if hour_performance else None
            
            return {
                'hours_analyzed': len(hour_performance),
                'best_hour': {
                    'hour': best_hour,
                    'accuracy': hour_performance[best_hour]['accuracy'] if best_hour else 0
                },
                'worst_hour': {
                    'hour': worst_hour,
                    'accuracy': hour_performance[worst_hour]['accuracy'] if worst_hour else 0
                },
                'hourly_breakdown': hour_performance
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الساعي: {str(e)}")
            return {'error': str(e)}

    async def _generate_comprehensive_analysis(self) -> Dict[str, Any]:
        """تحليل شامل للأداء"""
        try:
            daily_analysis = await self._generate_daily_analysis()
            hourly_analysis = await self._generate_hourly_analysis()
            
            # تحليل أداء الأزواج
            pair_analysis = self._analyze_pair_performance()
            
            # تحليل أداء الإطارات الزمنية
            timeframe_analysis = self._analyze_timeframe_performance()
            
            return {
                'daily_analysis': daily_analysis,
                'hourly_analysis': hourly_analysis,
                'pair_analysis': pair_analysis,
                'timeframe_analysis': timeframe_analysis,
                'summary': {
                    'total_analysis_points': 4,
                    'analysis_date': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل: {str(e)}")
            return {'error': str(e)}

    def _analyze_pair_performance(self) -> Dict[str, Any]:
        """تحليل أداء الأزواج"""
        try:
            pair_data = self.detailed_stats['pair_performance']
            
            if not pair_data:
                return {'message': 'لا توجد بيانات أزواج'}
            
            # ترتيب الأزواج حسب الدقة
            sorted_pairs = sorted(pair_data.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            
            best_pairs = sorted_pairs[:5]  # أفضل 5 أزواج
            worst_pairs = sorted_pairs[-5:]  # أسوأ 5 أزواج
            
            return {
                'total_pairs': len(pair_data),
                'best_performing_pairs': [{'pair': pair, 'accuracy': data['accuracy'], 'total': data['total']} for pair, data in best_pairs],
                'worst_performing_pairs': [{'pair': pair, 'accuracy': data['accuracy'], 'total': data['total']} for pair, data in worst_pairs],
                'average_accuracy': sum(data['accuracy'] for data in pair_data.values()) / len(pair_data)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل أداء الأزواج: {str(e)}")
            return {'error': str(e)}

    def _analyze_timeframe_performance(self) -> Dict[str, Any]:
        """تحليل أداء الإطارات الزمنية"""
        try:
            timeframe_data = self.detailed_stats['timeframe_performance']
            
            if not timeframe_data:
                return {'message': 'لا توجد بيانات إطارات زمنية'}
            
            # ترتيب الإطارات حسب الدقة
            sorted_timeframes = sorted(timeframe_data.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            
            return {
                'total_timeframes': len(timeframe_data),
                'performance_ranking': [
                    {
                        'timeframe': f"{tf} دقيقة",
                        'accuracy': data['accuracy'],
                        'total_predictions': data['total'],
                        'successful_predictions': data['successful']
                    }
                    for tf, data in sorted_timeframes
                ],
                'best_timeframe': sorted_timeframes[0] if sorted_timeframes else None,
                'average_accuracy': sum(data['accuracy'] for data in timeframe_data.values()) / len(timeframe_data)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل أداء الإطارات الزمنية: {str(e)}")
            return {'error': str(e)}

    async def _generate_improvement_recommendations(self, performance_stats: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات للتحسين"""
        try:
            recommendations = []
            overall_accuracy = performance_stats.get('overall_accuracy', 0)
            
            if overall_accuracy < 60:
                recommendations.append("الدقة الإجمالية منخفضة - يُنصح بمراجعة معايير الثقة والإجماع")
            
            if overall_accuracy < 70:
                recommendations.append("تحسين خوارزميات دمج الإشارات من الطبقات المختلفة")
            
            # تحليل أداء الأزواج
            pair_performance = performance_stats.get('accuracy_by_pair', {})
            if pair_performance:
                low_performing_pairs = [pair for pair, data in pair_performance.items() if data['accuracy'] < 50]
                if low_performing_pairs:
                    recommendations.append(f"مراجعة استراتيجية التداول للأزواج ضعيفة الأداء: {', '.join(low_performing_pairs[:3])}")
            
            # تحليل أداء الإطارات الزمنية
            timeframe_performance = performance_stats.get('accuracy_by_timeframe', {})
            if timeframe_performance:
                low_performing_timeframes = [tf for tf, data in timeframe_performance.items() if data['accuracy'] < 50]
                if low_performing_timeframes:
                    recommendations.append(f"تحسين نماذج التنبؤ للإطارات الزمنية: {', '.join(map(str, low_performing_timeframes))}")
            
            if not recommendations:
                recommendations.append("الأداء جيد - الاستمرار في المراقبة والتحسين التدريجي")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء توصيات التحسين: {str(e)}")
            return ["خطأ في تحليل الأداء"]

    def _calculate_trend(self, values: List[float]) -> str:
        """حساب الاتجاه العام للقيم"""
        try:
            if len(values) < 2:
                return "غير محدد"
            
            # حساب الاتجاه باستخدام المتوسط المتحرك البسيط
            first_half = sum(values[:len(values)//2]) / (len(values)//2)
            second_half = sum(values[len(values)//2:]) / (len(values) - len(values)//2)
            
            if second_half > first_half * 1.05:
                return "تحسن"
            elif second_half < first_half * 0.95:
                return "تراجع"
            else:
                return "مستقر"
                
        except Exception as e:
            logger.warning(f"خطأ في حساب الاتجاه: {str(e)}")
            return "غير محدد"

    def stop_tracking(self):
        """إيقاف خدمة التتبع"""
        self.tracking_enabled = False
        logger.info("تم إيقاف خدمة التتبع")

# إنشاء instance افتراضي
prediction_tracker = PredictionTracker()
