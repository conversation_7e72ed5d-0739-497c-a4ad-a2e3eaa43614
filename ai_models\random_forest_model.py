"""
نموذج Random Forest للتنبؤ بالاتجاه السعري
Random Forest Model for Price Direction Prediction
"""

import os
import sys
import numpy as np
import pandas as pd
import pickle
import joblib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
    from sklearn.feature_selection import SelectKBest, f_classif
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ تحذير: مكتبة scikit-learn غير مثبتة. سيتم تشغيل النموذج في وضع المحاكاة")

from config.ai_config import default_ai_config
from utils.logger import scalping_logger
from database.repository import HistoricalDataRepository

logger = scalping_logger.get_logger("random_forest_model")

class RandomForestPredictor:
    """نموذج Random Forest للتنبؤ بالاتجاه السعري"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """تهيئة نموذج Random Forest"""
        self.config = config or default_ai_config.random_forest_config
        self.model = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.feature_selector = None
        self.feature_names = []
        self.selected_features = []
        self.is_trained = False
        self.model_path = os.path.join(default_ai_config.models_directory, "random_forest_model.pkl")
        self.scaler_path = os.path.join(default_ai_config.models_directory, "random_forest_scaler.pkl")
        self.selector_path = os.path.join(default_ai_config.models_directory, "random_forest_selector.pkl")
        
        # إنشاء مجلد النماذج إذا لم يكن موجوداً
        os.makedirs(default_ai_config.models_directory, exist_ok=True)
        
        # إعداد قاعدة البيانات
        self.db_repo = HistoricalDataRepository()
        
        logger.info("تم تهيئة نموذج Random Forest")

    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """إعداد الميزات للتدريب والتنبؤ"""
        try:
            features = pd.DataFrame()
            
            # الميزات الأساسية للسعر
            features['open'] = data['open']
            features['high'] = data['high']
            features['low'] = data['low']
            features['close'] = data['close']
            features['volume'] = data.get('volume', 1.0)
            
            # الميزات المشتقة
            features['price_change'] = data['close'] - data['open']
            features['price_change_pct'] = (data['close'] - data['open']) / data['open'] * 100
            features['high_low_range'] = data['high'] - data['low']
            features['body_size'] = abs(data['close'] - data['open'])
            features['upper_shadow'] = data['high'] - np.maximum(data['open'], data['close'])
            features['lower_shadow'] = np.minimum(data['open'], data['close']) - data['low']
            
            # نسب السعر
            features['close_to_high'] = data['close'] / data['high']
            features['close_to_low'] = data['close'] / data['low']
            features['open_to_high'] = data['open'] / data['high']
            features['open_to_low'] = data['open'] / data['low']
            
            # المؤشرات الفنية (إذا كانت متوفرة)
            technical_indicators = [
                'ema_5', 'ema_10', 'ema_21', 'sma_10', 'rsi_5', 'rsi_14',
                'macd', 'macd_signal', 'macd_histogram', 'momentum_10',
                'bb_upper', 'bb_middle', 'bb_lower', 'atr_5', 'atr_14', 'zscore'
            ]
            
            for indicator in technical_indicators:
                if indicator in data.columns:
                    features[indicator] = data[indicator]
                else:
                    features[indicator] = 0.0
            
            # ميزات الاتجاه والزخم
            for period in [3, 5, 10, 20]:
                if len(data) > period:
                    features[f'sma_{period}'] = data['close'].rolling(period).mean()
                    features[f'price_vs_sma_{period}'] = data['close'] / features[f'sma_{period}']
                    features[f'volatility_{period}'] = data['close'].rolling(period).std()
                    features[f'momentum_{period}'] = data['close'] / data['close'].shift(period)
                else:
                    features[f'sma_{period}'] = data['close']
                    features[f'price_vs_sma_{period}'] = 1.0
                    features[f'volatility_{period}'] = 0.0
                    features[f'momentum_{period}'] = 1.0
            
            # ميزات زمنية
            if 'timestamp' in data.columns:
                timestamps = pd.to_datetime(data['timestamp'])
                features['hour'] = timestamps.dt.hour
                features['day_of_week'] = timestamps.dt.dayofweek
                features['minute'] = timestamps.dt.minute
                features['is_weekend'] = (timestamps.dt.dayofweek >= 5).astype(int)
            else:
                features['hour'] = 12
                features['day_of_week'] = 1
                features['minute'] = 0
                features['is_weekend'] = 0
            
            # ميزات إحصائية متقدمة
            for window in [5, 10]:
                if len(data) > window:
                    features[f'price_rank_{window}'] = data['close'].rolling(window).rank(pct=True)
                    features[f'volume_rank_{window}'] = data.get('volume', pd.Series([1]*len(data))).rolling(window).rank(pct=True)
                else:
                    features[f'price_rank_{window}'] = 0.5
                    features[f'volume_rank_{window}'] = 0.5
            
            # ملء القيم المفقودة
            features = features.fillna(method='ffill').fillna(0)
            
            # استبدال القيم اللانهائية
            features = features.replace([np.inf, -np.inf], 0)
            
            self.feature_names = list(features.columns)
            logger.info(f"تم إعداد {len(self.feature_names)} ميزة للنموذج")
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في إعداد الميزات: {str(e)}")
            raise

    def create_labels(self, data: pd.DataFrame) -> np.ndarray:
        """إنشاء التصنيفات للتدريب"""
        try:
            # تحديد الاتجاه بناءً على الشمعة التالية
            future_close = data['close'].shift(-1)
            current_close = data['close']
            
            # 0 = هبوط، 1 = صعود
            labels = (future_close > current_close).astype(int)
            
            # إزالة القيمة الأخيرة (لا يوجد شمعة تالية)
            labels = labels[:-1]
            
            logger.info(f"تم إنشاء {len(labels)} تصنيف")
            return labels.values
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التصنيفات: {str(e)}")
            raise

    def train(self, currency_pair: str, timeframe: int = 60, samples_count: int = 5000) -> Dict[str, Any]:
        """تدريب النموذج"""
        try:
            if not SKLEARN_AVAILABLE:
                logger.warning("scikit-learn غير متوفر - تشغيل وضع المحاكاة")
                return self._simulate_training()
            
            logger.info(f"بدء تدريب نموذج Random Forest للزوج {currency_pair}")
            
            # جلب البيانات من قاعدة البيانات
            data = self._fetch_training_data(currency_pair, samples_count)
            
            if len(data) < 50:  # تقليل الحد الأدنى للاختبار
                raise ValueError(f"البيانات غير كافية للتدريب: {len(data)} < 50")
            
            # إعداد الميزات والتصنيفات
            features = self.prepare_features(data)
            labels = self.create_labels(data)
            
            # التأكد من تطابق الأحجام
            min_length = min(len(features), len(labels))
            features = features.iloc[:min_length]
            labels = labels[:min_length]
            
            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(
                features, labels, 
                test_size=default_ai_config.test_split,
                random_state=self.config['random_state'],
                stratify=labels
            )
            
            # تطبيع الميزات
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # اختيار أفضل الميزات
            self.feature_selector = SelectKBest(score_func=f_classif, k=min(30, X_train_scaled.shape[1]))
            X_train_selected = self.feature_selector.fit_transform(X_train_scaled, y_train)
            X_test_selected = self.feature_selector.transform(X_test_scaled)
            
            # حفظ أسماء الميزات المختارة
            selected_indices = self.feature_selector.get_support(indices=True)
            self.selected_features = [self.feature_names[i] for i in selected_indices]
            
            # إنشاء وتدريب النموذج
            self.model = RandomForestClassifier(
                n_estimators=self.config['n_estimators'],
                max_depth=self.config['max_depth'],
                min_samples_split=self.config['min_samples_split'],
                min_samples_leaf=self.config['min_samples_leaf'],
                max_features=self.config['max_features'],
                random_state=self.config['random_state'],
                n_jobs=self.config['n_jobs'],
                bootstrap=self.config['bootstrap']
            )
            
            # التدريب
            self.model.fit(X_train_selected, y_train)
            
            # التقييم
            y_pred = self.model.predict(X_test_selected)
            y_pred_proba = self.model.predict_proba(X_test_selected)
            
            # حساب المقاييس
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            # التحقق من Cross Validation
            cv_scores = cross_val_score(self.model, X_train_selected, y_train, cv=5)
            
            self.is_trained = True
            
            # حفظ النموذج
            self.save_model()
            
            training_results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features_count': len(self.feature_names),
                'selected_features_count': len(self.selected_features),
                'currency_pair': currency_pair,
                'training_date': datetime.now().isoformat()
            }
            
            logger.info(f"تم تدريب النموذج بنجاح - دقة: {accuracy:.3f}")
            return training_results
            
        except Exception as e:
            logger.error(f"خطأ في تدريب النموذج: {str(e)}")
            raise

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """التنبؤ بالاتجاه السعري"""
        try:
            if not self.is_trained and not self.load_model():
                raise ValueError("النموذج غير مدرب")
            
            if not SKLEARN_AVAILABLE:
                return self._simulate_prediction()
            
            # إعداد الميزات
            features = self.prepare_features(data)
            
            # أخذ آخر صف فقط للتنبؤ
            latest_features = features.iloc[-1:].values
            
            # تطبيع الميزات
            latest_features_scaled = self.scaler.transform(latest_features)
            
            # اختيار الميزات
            latest_features_selected = self.feature_selector.transform(latest_features_scaled)
            
            # التنبؤ
            prediction = self.model.predict(latest_features_selected)[0]
            prediction_proba = self.model.predict_proba(latest_features_selected)[0]
            
            # حساب الثقة
            confidence = max(prediction_proba) * 100
            
            # تحديد الاتجاه
            direction = "CALL" if prediction == 1 else "PUT"
            
            # تقييم قوة الإشارة
            signal_strength = self._evaluate_signal_strength(confidence)
            
            result = {
                'prediction': int(prediction),
                'direction': direction,
                'confidence': confidence,
                'signal_strength': signal_strength,
                'probabilities': {
                    'down': prediction_proba[0] * 100,
                    'up': prediction_proba[1] * 100
                },
                'model_type': 'Random Forest',
                'features_used': len(self.selected_features),
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"تنبؤ Random Forest: {direction} بثقة {confidence:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ: {str(e)}")
            raise

    def _evaluate_signal_strength(self, confidence: float) -> str:
        """تقييم قوة الإشارة"""
        if confidence >= 90:
            return "قوية جداً"
        elif confidence >= 80:
            return "قوية"
        elif confidence >= 70:
            return "متوسطة"
        elif confidence >= 60:
            return "ضعيفة"
        else:
            return "ضعيفة جداً"

    def _fetch_training_data(self, currency_pair: str, samples_count: int) -> pd.DataFrame:
        """جلب بيانات التدريب من قاعدة البيانات"""
        try:
            # جلب البيانات التاريخية فقط
            candles = self.db_repo.get_latest_candles(currency_pair, count=samples_count)

            if not candles:
                raise ValueError(f"لا توجد بيانات للزوج {currency_pair}")

            # تحويل إلى DataFrame
            data = []
            for i, candle in enumerate(candles):
                data.append({
                    'id': i + 1,  # معرف تسلسلي
                    'currency_pair': currency_pair,
                    'timestamp': candle['timestamp'],
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle['volume']) if candle['volume'] else 1.0,
                    # إضافة قيم افتراضية للمؤشرات
                    'ema_5': 0.0, 'ema_10': 0.0, 'ema_21': 0.0, 'sma_10': 0.0,
                    'rsi_5': 50.0, 'rsi_14': 50.0, 'macd': 0.0, 'macd_signal': 0.0,
                    'macd_histogram': 0.0, 'momentum_10': 1.0, 'bb_upper': 0.0,
                    'bb_middle': 0.0, 'bb_lower': 0.0, 'atr_5': 0.0, 'atr_14': 0.0,
                    'zscore': 0.0
                })

            df = pd.DataFrame(data)

            # ترتيب البيانات حسب الوقت (الأقدم أولاً)
            df = df.sort_values('timestamp').reset_index(drop=True)

            logger.info(f"تم جلب {len(df)} شمعة للتدريب")
            return df

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات التدريب: {str(e)}")
            raise

    def save_model(self) -> bool:
        """حفظ النموذج"""
        try:
            if not self.is_trained:
                return False
            
            # حفظ النموذج
            joblib.dump(self.model, self.model_path)
            
            # حفظ المعايرة
            joblib.dump(self.scaler, self.scaler_path)
            
            # حفظ محدد الميزات
            joblib.dump(self.feature_selector, self.selector_path)
            
            # حفظ معلومات إضافية
            model_info = {
                'feature_names': self.feature_names,
                'selected_features': self.selected_features,
                'config': self.config,
                'training_date': datetime.now().isoformat(),
                'is_trained': self.is_trained
            }
            
            info_path = os.path.join(default_ai_config.models_directory, "random_forest_info.pkl")
            with open(info_path, 'wb') as f:
                pickle.dump(model_info, f)
            
            logger.info("تم حفظ نموذج Random Forest بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ النموذج: {str(e)}")
            return False

    def load_model(self) -> bool:
        """تحميل النموذج"""
        try:
            if not os.path.exists(self.model_path):
                return False
            
            # تحميل النموذج
            self.model = joblib.load(self.model_path)
            
            # تحميل المعايرة
            if os.path.exists(self.scaler_path):
                self.scaler = joblib.load(self.scaler_path)
            
            # تحميل محدد الميزات
            if os.path.exists(self.selector_path):
                self.feature_selector = joblib.load(self.selector_path)
            
            # تحميل معلومات إضافية
            info_path = os.path.join(default_ai_config.models_directory, "random_forest_info.pkl")
            if os.path.exists(info_path):
                with open(info_path, 'rb') as f:
                    model_info = pickle.load(f)
                    self.feature_names = model_info.get('feature_names', [])
                    self.selected_features = model_info.get('selected_features', [])
                    self.is_trained = model_info.get('is_trained', False)
            
            logger.info("تم تحميل نموذج Random Forest بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل النموذج: {str(e)}")
            return False

    def _simulate_training(self) -> Dict[str, Any]:
        """محاكاة التدريب عند عدم توفر scikit-learn"""
        self.is_trained = True
        return {
            'accuracy': 0.78,
            'precision': 0.76,
            'recall': 0.75,
            'f1_score': 0.75,
            'cv_mean': 0.77,
            'cv_std': 0.03,
            'training_samples': 4000,
            'test_samples': 1000,
            'features_count': 35,
            'selected_features_count': 25,
            'currency_pair': 'SIMULATION',
            'training_date': datetime.now().isoformat(),
            'note': 'محاكاة - scikit-learn غير مثبت'
        }

    def _simulate_prediction(self) -> Dict[str, Any]:
        """محاكاة التنبؤ عند عدم توفر scikit-learn"""
        import random
        
        prediction = random.choice([0, 1])
        confidence = random.uniform(65, 90)
        direction = "CALL" if prediction == 1 else "PUT"
        
        return {
            'prediction': prediction,
            'direction': direction,
            'confidence': confidence,
            'signal_strength': self._evaluate_signal_strength(confidence),
            'probabilities': {
                'down': 100 - confidence,
                'up': confidence
            },
            'model_type': 'Random Forest (محاكاة)',
            'features_used': 25,
            'timestamp': datetime.now().isoformat(),
            'note': 'محاكاة - scikit-learn غير مثبت'
        }

    def get_feature_importance(self) -> Dict[str, float]:
        """الحصول على أهمية الميزات"""
        try:
            if not self.is_trained or not SKLEARN_AVAILABLE:
                return {}
            
            importance = self.model.feature_importances_
            feature_importance = dict(zip(self.selected_features, importance))
            
            # ترتيب حسب الأهمية
            sorted_importance = dict(sorted(feature_importance.items(), 
                                         key=lambda x: x[1], reverse=True))
            
            return sorted_importance
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return {}

# إنشاء instance افتراضي
random_forest_predictor = RandomForestPredictor()
