"""
نظام التعلم التعزيزي للتدريب التلقائي
Reinforcement Learning System for Automatic Retraining
"""

import os
import sys
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import logging
import json
import pickle
from dataclasses import dataclass, asdict
import threading
import time

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_models.ai_predictor_engine import ai_predictor_engine
from ai_models.prediction_tracker import prediction_tracker
from ai_models.direction_prediction_engine import direction_prediction_engine
from ai_models.confidence_evaluation_engine import confidence_evaluation_engine
from ai_models.market_state_classifier import market_state_classifier
from config.currency_pairs import CURRENCY_PAIRS
from database.repository import HistoricalDataRepository
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("reinforcement_learning_system")

class LearningAction(Enum):
    """إجراءات التعلم المتاحة"""
    RETRAIN_MODEL = "RETRAIN_MODEL"
    ADJUST_PARAMETERS = "ADJUST_PARAMETERS"
    UPDATE_FEATURES = "UPDATE_FEATURES"
    CHANGE_STRATEGY = "CHANGE_STRATEGY"
    INCREASE_CONFIDENCE = "INCREASE_CONFIDENCE"
    DECREASE_CONFIDENCE = "DECREASE_CONFIDENCE"
    NO_ACTION = "NO_ACTION"

class PerformanceState(Enum):
    """حالات الأداء"""
    EXCELLENT = "EXCELLENT"      # أداء ممتاز
    GOOD = "GOOD"               # أداء جيد
    AVERAGE = "AVERAGE"         # أداء متوسط
    POOR = "POOR"               # أداء ضعيف
    CRITICAL = "CRITICAL"       # أداء حرج

@dataclass
class LearningReward:
    """مكافأة التعلم"""
    accuracy_reward: float
    profit_reward: float
    consistency_reward: float
    risk_penalty: float
    total_reward: float
    timestamp: str

@dataclass
class SystemState:
    """حالة النظام للتعلم التعزيزي"""
    accuracy_rate: float
    profit_rate: float
    win_streak: int
    loss_streak: int
    confidence_avg: float
    market_volatility: float
    recent_performance: List[float]
    model_age_hours: float
    data_quality_score: float
    timestamp: str

class ReinforcementLearningSystem:
    """نظام التعلم التعزيزي للتدريب التلقائي"""

    def __init__(self):
        """تهيئة نظام التعلم التعزيزي"""
        self.db_repo = HistoricalDataRepository()
        self.ai_engine = ai_predictor_engine
        self.prediction_tracker = prediction_tracker
        self.direction_engine = direction_prediction_engine
        self.confidence_engine = confidence_evaluation_engine
        self.market_classifier = market_state_classifier
        
        # إعدادات التعلم
        self.learning_config = {
            'learning_rate': 0.1,
            'discount_factor': 0.95,
            'exploration_rate': 0.1,
            'min_samples_for_learning': 50,
            'performance_window': 100,  # عدد التنبؤات للتقييم
            'retraining_threshold': 0.6,  # حد إعادة التدريب
            'critical_threshold': 0.4,   # حد الأداء الحرج
        }
        
        # جدول Q للتعلم التعزيزي
        self.q_table = {}
        
        # تاريخ الحالات والإجراءات
        self.state_history = []
        self.action_history = []
        self.reward_history = []
        
        # إحصائيات التعلم
        self.learning_stats = {
            'total_learning_episodes': 0,
            'successful_improvements': 0,
            'failed_improvements': 0,
            'best_performance': 0.0,
            'current_performance': 0.0,
            'last_learning_time': None,
            'learning_frequency': 3600,  # كل ساعة
        }
        
        # حالة النظام
        self.system_active = False
        self.learning_thread = None
        
        # ملفات الحفظ
        self.q_table_path = "models/q_table.pkl"
        self.learning_history_path = "models/learning_history.json"
        
        # تحميل البيانات المحفوظة
        self._load_learning_data()
        
        logger.info("تم تهيئة نظام التعلم التعزيزي")

    async def start_learning_system(self):
        """بدء نظام التعلم التعزيزي"""
        try:
            if self.system_active:
                logger.warning("نظام التعلم التعزيزي يعمل بالفعل")
                return
            
            self.system_active = True
            logger.info("🚀 بدء نظام التعلم التعزيزي")
            
            # بدء خيط التعلم المستمر
            self.learning_thread = threading.Thread(target=self._continuous_learning_loop, daemon=True)
            self.learning_thread.start()
            
            # تقييم الحالة الأولية
            initial_state = await self._assess_current_state()
            logger.info(f"📊 الحالة الأولية: دقة {initial_state.accuracy_rate:.1f}%, ربح {initial_state.profit_rate:.1f}%")
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام التعلم: {str(e)}")
            raise

    def stop_learning_system(self):
        """إيقاف نظام التعلم التعزيزي"""
        try:
            self.system_active = False
            if self.learning_thread and self.learning_thread.is_alive():
                self.learning_thread.join(timeout=10)
            
            # حفظ البيانات
            self._save_learning_data()
            
            logger.info("تم إيقاف نظام التعلم التعزيزي")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام التعلم: {str(e)}")

    def _continuous_learning_loop(self):
        """حلقة التعلم المستمر"""
        try:
            while self.system_active:
                try:
                    # تشغيل دورة تعلم
                    asyncio.run(self._learning_cycle())
                    
                    # انتظار حتى الدورة التالية
                    time.sleep(self.learning_stats['learning_frequency'])
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة التعلم: {str(e)}")
                    time.sleep(300)  # انتظار 5 دقائق عند الخطأ
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة التعلم المستمر: {str(e)}")

    async def _learning_cycle(self):
        """دورة تعلم واحدة"""
        try:
            logger.info("🔄 بدء دورة تعلم جديدة")
            
            # 1. تقييم الحالة الحالية
            current_state = await self._assess_current_state()
            
            # 2. تحديد الإجراء المطلوب
            action = self._select_action(current_state)
            
            # 3. تنفيذ الإجراء
            execution_result = await self._execute_action(action, current_state)
            
            # 4. تقييم النتائج وحساب المكافأة
            reward = await self._calculate_reward(current_state, action, execution_result)
            
            # 5. تحديث جدول Q
            self._update_q_table(current_state, action, reward)
            
            # 6. تسجيل النتائج
            self._record_learning_episode(current_state, action, reward, execution_result)
            
            # 7. حفظ البيانات
            self._save_learning_data()
            
            logger.info(f"✅ انتهت دورة التعلم - إجراء: {action.value}, مكافأة: {reward.total_reward:.3f}")
            
        except Exception as e:
            logger.error(f"خطأ في دورة التعلم: {str(e)}")

    async def _assess_current_state(self) -> SystemState:
        """تقييم الحالة الحالية للنظام"""
        try:
            # جمع إحصائيات الأداء
            performance_stats = await self._get_performance_statistics()
            
            # تقييم جودة البيانات
            data_quality = await self._assess_data_quality()
            
            # حساب عمر النماذج
            model_age = self._calculate_model_age()
            
            # تقييم تقلبات السوق
            market_volatility = await self._assess_market_volatility()
            
            # إنشاء حالة النظام
            system_state = SystemState(
                accuracy_rate=performance_stats.get('accuracy_rate', 0.0),
                profit_rate=performance_stats.get('profit_rate', 0.0),
                win_streak=performance_stats.get('win_streak', 0),
                loss_streak=performance_stats.get('loss_streak', 0),
                confidence_avg=performance_stats.get('confidence_avg', 0.0),
                market_volatility=market_volatility,
                recent_performance=performance_stats.get('recent_performance', []),
                model_age_hours=model_age,
                data_quality_score=data_quality,
                timestamp=datetime.now().isoformat()
            )
            
            return system_state
            
        except Exception as e:
            logger.error(f"خطأ في تقييم الحالة: {str(e)}")
            # إرجاع حالة افتراضية
            return SystemState(
                accuracy_rate=0.5, profit_rate=0.0, win_streak=0, loss_streak=0,
                confidence_avg=0.5, market_volatility=0.5, recent_performance=[],
                model_age_hours=24.0, data_quality_score=0.5,
                timestamp=datetime.now().isoformat()
            )

    def _select_action(self, state: SystemState) -> LearningAction:
        """اختيار الإجراء المناسب بناءً على الحالة"""
        try:
            # تحويل الحالة إلى مفتاح للجدول Q
            state_key = self._state_to_key(state)
            
            # التحقق من وجود الحالة في جدول Q
            if state_key not in self.q_table:
                self.q_table[state_key] = {action.value: 0.0 for action in LearningAction}
            
            # استراتيجية epsilon-greedy
            if np.random.random() < self.learning_config['exploration_rate']:
                # استكشاف: اختيار إجراء عشوائي
                action = np.random.choice(list(LearningAction))
                logger.debug(f"اختيار استكشافي: {action.value}")
            else:
                # استغلال: اختيار أفضل إجراء معروف
                q_values = self.q_table[state_key]
                best_action_value = max(q_values.values())
                best_actions = [action for action, value in q_values.items() if value == best_action_value]
                action = LearningAction(np.random.choice(best_actions))
                logger.debug(f"اختيار استغلالي: {action.value}")
            
            # تطبيق قواعد خاصة للحالات الحرجة
            if state.accuracy_rate < self.learning_config['critical_threshold']:
                action = LearningAction.RETRAIN_MODEL
                logger.info("🚨 أداء حرج - إجبار إعادة التدريب")
            
            elif state.loss_streak > 10:
                action = LearningAction.ADJUST_PARAMETERS
                logger.info("📉 سلسلة خسائر طويلة - تعديل المعايير")
            
            elif state.model_age_hours > 168:  # أسبوع
                action = LearningAction.RETRAIN_MODEL
                logger.info("⏰ النماذج قديمة - إعادة تدريب")
            
            return action
            
        except Exception as e:
            logger.error(f"خطأ في اختيار الإجراء: {str(e)}")
            return LearningAction.NO_ACTION

    async def _execute_action(self, action: LearningAction, state: SystemState) -> Dict[str, Any]:
        """تنفيذ الإجراء المحدد"""
        try:
            logger.info(f"🔧 تنفيذ الإجراء: {action.value}")
            
            execution_result = {
                'action': action.value,
                'success': False,
                'details': {},
                'timestamp': datetime.now().isoformat()
            }
            
            if action == LearningAction.RETRAIN_MODEL:
                result = await self._retrain_models()
                execution_result.update(result)
                
            elif action == LearningAction.ADJUST_PARAMETERS:
                result = await self._adjust_parameters(state)
                execution_result.update(result)
                
            elif action == LearningAction.UPDATE_FEATURES:
                result = await self._update_features(state)
                execution_result.update(result)
                
            elif action == LearningAction.CHANGE_STRATEGY:
                result = await self._change_strategy(state)
                execution_result.update(result)
                
            elif action == LearningAction.INCREASE_CONFIDENCE:
                result = await self._adjust_confidence_threshold(increase=True)
                execution_result.update(result)
                
            elif action == LearningAction.DECREASE_CONFIDENCE:
                result = await self._adjust_confidence_threshold(increase=False)
                execution_result.update(result)
                
            elif action == LearningAction.NO_ACTION:
                execution_result['success'] = True
                execution_result['details'] = {'message': 'لا يوجد إجراء مطلوب'}
            
            return execution_result
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الإجراء: {str(e)}")
            return {
                'action': action.value,
                'success': False,
                'details': {'error': str(e)},
                'timestamp': datetime.now().isoformat()
            }

    async def _retrain_models(self) -> Dict[str, Any]:
        """إعادة تدريب النماذج"""
        try:
            logger.info("🔄 بدء إعادة تدريب النماذج")

            # إعادة تدريب جميع النماذج
            training_result = await self.ai_engine.train_all_models()

            if training_result and training_result.get('trained_pairs', 0) > 0:
                success = True
                details = {
                    'trained_pairs': training_result.get('trained_pairs', 0),
                    'total_pairs': training_result.get('total_pairs', 0),
                    'success_rate': training_result.get('trained_pairs', 0) / training_result.get('total_pairs', 1) * 100
                }
                logger.info(f"✅ تم إعادة تدريب {details['trained_pairs']} نموذج")
            else:
                success = False
                details = {'error': 'فشل في إعادة التدريب'}
                logger.warning("❌ فشل في إعادة تدريب النماذج")

            return {
                'success': success,
                'details': details
            }

        except Exception as e:
            logger.error(f"خطأ في إعادة التدريب: {str(e)}")
            return {
                'success': False,
                'details': {'error': str(e)}
            }

    async def _adjust_parameters(self, state: SystemState) -> Dict[str, Any]:
        """تعديل معايير النظام"""
        try:
            logger.info("⚙️ تعديل معايير النظام")

            adjustments = {}

            # تعديل حد الثقة بناءً على الأداء
            if state.accuracy_rate < 0.6:
                # زيادة حد الثقة لتقليل التداولات الضعيفة
                new_threshold = min(0.8, self.direction_engine.confidence_threshold + 0.05)
                self.direction_engine.confidence_threshold = new_threshold
                adjustments['confidence_threshold'] = new_threshold

            elif state.accuracy_rate > 0.8:
                # تقليل حد الثقة للاستفادة من الأداء الجيد
                new_threshold = max(0.6, self.direction_engine.confidence_threshold - 0.05)
                self.direction_engine.confidence_threshold = new_threshold
                adjustments['confidence_threshold'] = new_threshold

            # تعديل حد الإجماع
            if state.win_streak > 5:
                # تقليل حد الإجماع عند الأداء الجيد
                new_consensus = max(0.6, self.direction_engine.consensus_threshold - 0.05)
                self.direction_engine.consensus_threshold = new_consensus
                adjustments['consensus_threshold'] = new_consensus

            elif state.loss_streak > 3:
                # زيادة حد الإجماع عند الأداء الضعيف
                new_consensus = min(0.9, self.direction_engine.consensus_threshold + 0.05)
                self.direction_engine.consensus_threshold = new_consensus
                adjustments['consensus_threshold'] = new_consensus

            success = len(adjustments) > 0

            return {
                'success': success,
                'details': adjustments if success else {'message': 'لا توجد تعديلات مطلوبة'}
            }

        except Exception as e:
            logger.error(f"خطأ في تعديل المعايير: {str(e)}")
            return {
                'success': False,
                'details': {'error': str(e)}
            }

    async def _update_features(self, state: SystemState) -> Dict[str, Any]:
        """تحديث الميزات المستخدمة في النماذج"""
        try:
            logger.info("🔧 تحديث ميزات النماذج")

            # محاكاة تحديث الميزات (يمكن تطويرها لاحقاً)
            updates = {
                'feature_selection': 'optimized',
                'new_indicators': ['momentum_divergence', 'volume_profile'],
                'removed_features': ['outdated_indicator']
            }

            # هنا يمكن إضافة منطق فعلي لتحديث الميزات

            return {
                'success': True,
                'details': updates
            }

        except Exception as e:
            logger.error(f"خطأ في تحديث الميزات: {str(e)}")
            return {
                'success': False,
                'details': {'error': str(e)}
            }

    async def _change_strategy(self, state: SystemState) -> Dict[str, Any]:
        """تغيير استراتيجية التداول"""
        try:
            logger.info("🎯 تغيير استراتيجية التداول")

            strategy_changes = {}

            # تغيير الاستراتيجية بناءً على تقلبات السوق
            if state.market_volatility > 0.7:
                # سوق متقلب - استراتيجية محافظة
                strategy_changes['approach'] = 'conservative'
                strategy_changes['risk_reduction'] = 0.2

            elif state.market_volatility < 0.3:
                # سوق هادئ - استراتيجية أكثر جرأة
                strategy_changes['approach'] = 'aggressive'
                strategy_changes['risk_increase'] = 0.1

            # تعديل الإطارات الزمنية المفضلة
            if state.accuracy_rate < 0.6:
                strategy_changes['preferred_timeframes'] = [5, 15]  # إطارات أطول
            else:
                strategy_changes['preferred_timeframes'] = [1, 3, 5]  # إطارات متنوعة

            return {
                'success': True,
                'details': strategy_changes
            }

        except Exception as e:
            logger.error(f"خطأ في تغيير الاستراتيجية: {str(e)}")
            return {
                'success': False,
                'details': {'error': str(e)}
            }

    async def _adjust_confidence_threshold(self, increase: bool) -> Dict[str, Any]:
        """تعديل حد الثقة"""
        try:
            current_threshold = self.direction_engine.confidence_threshold

            if increase:
                new_threshold = min(0.9, current_threshold + 0.05)
                action_desc = "زيادة"
            else:
                new_threshold = max(0.5, current_threshold - 0.05)
                action_desc = "تقليل"

            self.direction_engine.confidence_threshold = new_threshold

            logger.info(f"📊 {action_desc} حد الثقة من {current_threshold:.2f} إلى {new_threshold:.2f}")

            return {
                'success': True,
                'details': {
                    'old_threshold': current_threshold,
                    'new_threshold': new_threshold,
                    'change': new_threshold - current_threshold
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تعديل حد الثقة: {str(e)}")
            return {
                'success': False,
                'details': {'error': str(e)}
            }

    async def _calculate_reward(self, state: SystemState, action: LearningAction,
                              execution_result: Dict[str, Any]) -> LearningReward:
        """حساب مكافأة التعلم"""
        try:
            # مكافأة الدقة
            accuracy_reward = state.accuracy_rate * 10  # 0-10 نقاط

            # مكافأة الربح
            profit_reward = max(0, state.profit_rate) * 5  # 0-5 نقاط للربح الإيجابي

            # مكافأة الاستمرارية
            consistency_reward = 0
            if len(state.recent_performance) > 5:
                performance_std = np.std(state.recent_performance)
                consistency_reward = max(0, 5 - performance_std)  # كلما قل التباين، زادت المكافأة

            # عقوبة المخاطر
            risk_penalty = 0
            if state.loss_streak > 5:
                risk_penalty = state.loss_streak * 0.5  # عقوبة متزايدة

            # مكافأة إضافية لنجاح التنفيذ
            execution_bonus = 2.0 if execution_result.get('success', False) else -1.0

            # حساب المكافأة الإجمالية
            total_reward = (accuracy_reward + profit_reward + consistency_reward +
                          execution_bonus - risk_penalty)

            reward = LearningReward(
                accuracy_reward=accuracy_reward,
                profit_reward=profit_reward,
                consistency_reward=consistency_reward,
                risk_penalty=risk_penalty,
                total_reward=total_reward,
                timestamp=datetime.now().isoformat()
            )

            logger.debug(f"💰 مكافأة التعلم: {total_reward:.3f} (دقة: {accuracy_reward:.1f}, ربح: {profit_reward:.1f})")

            return reward

        except Exception as e:
            logger.error(f"خطأ في حساب المكافأة: {str(e)}")
            return LearningReward(0, 0, 0, 0, -5, datetime.now().isoformat())

    def _update_q_table(self, state: SystemState, action: LearningAction, reward: LearningReward):
        """تحديث جدول Q للتعلم التعزيزي"""
        try:
            state_key = self._state_to_key(state)
            action_key = action.value

            # التأكد من وجود الحالة في الجدول
            if state_key not in self.q_table:
                self.q_table[state_key] = {a.value: 0.0 for a in LearningAction}

            # تحديث Q-value باستخدام معادلة Bellman
            current_q = self.q_table[state_key][action_key]
            learning_rate = self.learning_config['learning_rate']
            discount_factor = self.learning_config['discount_factor']

            # Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
            # في هذه الحالة البسيطة، نستخدم المكافأة مباشرة
            new_q = current_q + learning_rate * (reward.total_reward - current_q)

            self.q_table[state_key][action_key] = new_q

            logger.debug(f"📈 تحديث Q-table: {action_key} = {new_q:.3f}")

        except Exception as e:
            logger.error(f"خطأ في تحديث جدول Q: {str(e)}")

    def _state_to_key(self, state: SystemState) -> str:
        """تحويل الحالة إلى مفتاح للجدول Q"""
        try:
            # تبسيط الحالة إلى فئات منفصلة
            accuracy_category = "high" if state.accuracy_rate > 0.7 else "medium" if state.accuracy_rate > 0.5 else "low"
            profit_category = "positive" if state.profit_rate > 0 else "negative"
            streak_category = "win" if state.win_streak > state.loss_streak else "loss"
            volatility_category = "high" if state.market_volatility > 0.6 else "medium" if state.market_volatility > 0.3 else "low"

            return f"{accuracy_category}_{profit_category}_{streak_category}_{volatility_category}"

        except Exception as e:
            logger.error(f"خطأ في تحويل الحالة: {str(e)}")
            return "unknown_state"

    def _record_learning_episode(self, state: SystemState, action: LearningAction,
                                reward: LearningReward, execution_result: Dict[str, Any]):
        """تسجيل حلقة تعلم"""
        try:
            # إضافة إلى التاريخ
            self.state_history.append(asdict(state))
            self.action_history.append(action.value)
            self.reward_history.append(asdict(reward))

            # تحديث الإحصائيات
            self.learning_stats['total_learning_episodes'] += 1
            self.learning_stats['last_learning_time'] = datetime.now().isoformat()

            if execution_result.get('success', False):
                self.learning_stats['successful_improvements'] += 1
            else:
                self.learning_stats['failed_improvements'] += 1

            # تحديث أفضل أداء
            if reward.total_reward > 0:
                current_performance = state.accuracy_rate
                if current_performance > self.learning_stats['best_performance']:
                    self.learning_stats['best_performance'] = current_performance

            self.learning_stats['current_performance'] = state.accuracy_rate

            # الاحتفاظ بآخر 1000 حلقة فقط
            max_history = 1000
            if len(self.state_history) > max_history:
                self.state_history = self.state_history[-max_history:]
                self.action_history = self.action_history[-max_history:]
                self.reward_history = self.reward_history[-max_history:]

            logger.debug(f"📝 تم تسجيل حلقة التعلم #{self.learning_stats['total_learning_episodes']}")

        except Exception as e:
            logger.error(f"خطأ في تسجيل حلقة التعلم: {str(e)}")

    async def _get_performance_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        try:
            # محاكاة إحصائيات الأداء (يمكن ربطها بنظام التتبع الفعلي)
            stats = {
                'accuracy_rate': np.random.uniform(0.5, 0.9),  # محاكاة
                'profit_rate': np.random.uniform(-0.1, 0.2),   # محاكاة
                'win_streak': np.random.randint(0, 10),
                'loss_streak': np.random.randint(0, 5),
                'confidence_avg': np.random.uniform(0.6, 0.9),
                'recent_performance': [np.random.uniform(0.4, 0.9) for _ in range(10)]
            }

            # يمكن استبدال هذا بالحصول على الإحصائيات الفعلية من نظام التتبع
            # stats = await self.prediction_tracker.get_performance_summary()

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الأداء: {str(e)}")
            return {
                'accuracy_rate': 0.5, 'profit_rate': 0.0, 'win_streak': 0,
                'loss_streak': 0, 'confidence_avg': 0.5, 'recent_performance': []
            }

    async def _assess_data_quality(self) -> float:
        """تقييم جودة البيانات"""
        try:
            # تقييم جودة البيانات لعينة من الأزواج
            total_quality = 0
            pairs_checked = 0

            for pair in CURRENCY_PAIRS[:5]:  # فحص أول 5 أزواج
                try:
                    candles_count = self.db_repo.get_candles_count(pair)
                    if candles_count > 0:
                        # تقييم الجودة بناءً على كمية البيانات
                        quality_score = min(1.0, candles_count / 1000)  # 1000 شمعة = جودة مثالية
                        total_quality += quality_score
                        pairs_checked += 1
                except:
                    continue

            if pairs_checked > 0:
                avg_quality = total_quality / pairs_checked
            else:
                avg_quality = 0.5  # جودة متوسطة افتراضية

            return avg_quality

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة البيانات: {str(e)}")
            return 0.5

    def _calculate_model_age(self) -> float:
        """حساب عمر النماذج بالساعات"""
        try:
            # محاكاة عمر النماذج (يمكن ربطها بتواريخ التدريب الفعلية)
            model_age_hours = np.random.uniform(1, 72)  # من ساعة إلى 3 أيام

            # يمكن استبدال هذا بالحصول على تاريخ التدريب الفعلي
            # training_status = self.ai_engine.get_training_status()
            # if training_status and 'last_update' in training_status:
            #     last_update = datetime.fromisoformat(training_status['last_update'])
            #     model_age_hours = (datetime.now() - last_update).total_seconds() / 3600

            return model_age_hours

        except Exception as e:
            logger.error(f"خطأ في حساب عمر النماذج: {str(e)}")
            return 24.0  # يوم واحد افتراضي

    async def _assess_market_volatility(self) -> float:
        """تقييم تقلبات السوق"""
        try:
            # استخدام مصنف حالة السوق لتقييم التقلبات
            volatility_scores = []

            for pair in CURRENCY_PAIRS[:3]:  # فحص أول 3 أزواج
                try:
                    classification = await self.market_classifier.classify_market_state(pair, 5)
                    volatility_analysis = classification.get('volatility_analysis', {})

                    # تحويل مستوى التقلبات إلى نقاط
                    volatility_level = volatility_analysis.get('level', 'NORMAL')
                    if volatility_level == 'VERY_HIGH':
                        score = 1.0
                    elif volatility_level == 'HIGH':
                        score = 0.8
                    elif volatility_level == 'NORMAL':
                        score = 0.5
                    elif volatility_level == 'LOW':
                        score = 0.3
                    else:  # VERY_LOW
                        score = 0.1

                    volatility_scores.append(score)

                except:
                    continue

            if volatility_scores:
                avg_volatility = np.mean(volatility_scores)
            else:
                avg_volatility = 0.5  # تقلبات متوسطة افتراضية

            return avg_volatility

        except Exception as e:
            logger.error(f"خطأ في تقييم تقلبات السوق: {str(e)}")
            return 0.5

    def _save_learning_data(self):
        """حفظ بيانات التعلم"""
        try:
            # إنشاء مجلد النماذج إذا لم يكن موجوداً
            os.makedirs("models", exist_ok=True)

            # حفظ جدول Q
            with open(self.q_table_path, 'wb') as f:
                pickle.dump(self.q_table, f)

            # حفظ تاريخ التعلم
            learning_history = {
                'learning_stats': self.learning_stats,
                'state_history': self.state_history[-100:],  # آخر 100 حالة
                'action_history': self.action_history[-100:],
                'reward_history': self.reward_history[-100:],
                'last_save': datetime.now().isoformat()
            }

            with open(self.learning_history_path, 'w', encoding='utf-8') as f:
                json.dump(learning_history, f, ensure_ascii=False, indent=2)

            logger.debug("💾 تم حفظ بيانات التعلم")

        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات التعلم: {str(e)}")

    def _load_learning_data(self):
        """تحميل بيانات التعلم المحفوظة"""
        try:
            # تحميل جدول Q
            if os.path.exists(self.q_table_path):
                with open(self.q_table_path, 'rb') as f:
                    self.q_table = pickle.load(f)
                logger.info("📂 تم تحميل جدول Q المحفوظ")

            # تحميل تاريخ التعلم
            if os.path.exists(self.learning_history_path):
                with open(self.learning_history_path, 'r', encoding='utf-8') as f:
                    learning_history = json.load(f)

                self.learning_stats.update(learning_history.get('learning_stats', {}))
                self.state_history = learning_history.get('state_history', [])
                self.action_history = learning_history.get('action_history', [])
                self.reward_history = learning_history.get('reward_history', [])

                logger.info("📂 تم تحميل تاريخ التعلم المحفوظ")

        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات التعلم: {str(e)}")

    async def get_learning_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التعلم"""
        try:
            current_state = await self._assess_current_state()

            # حساب معدل التحسن
            if len(self.reward_history) > 10:
                recent_rewards = [r['total_reward'] for r in self.reward_history[-10:]]
                improvement_rate = np.mean(recent_rewards)
            else:
                improvement_rate = 0.0

            # تحليل الإجراءات المفضلة
            action_frequency = {}
            for action in self.action_history:
                action_frequency[action] = action_frequency.get(action, 0) + 1

            return {
                'learning_stats': self.learning_stats,
                'current_state': asdict(current_state),
                'q_table_size': len(self.q_table),
                'total_episodes': len(self.state_history),
                'improvement_rate': improvement_rate,
                'action_frequency': action_frequency,
                'system_active': self.system_active,
                'last_update': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات التعلم: {str(e)}")
            return {
                'error': str(e),
                'system_active': self.system_active
            }

    async def force_learning_cycle(self) -> Dict[str, Any]:
        """إجبار تشغيل دورة تعلم فورية"""
        try:
            logger.info("🔄 تشغيل دورة تعلم فورية")
            await self._learning_cycle()
            return {'success': True, 'message': 'تم تشغيل دورة التعلم بنجاح'}

        except Exception as e:
            logger.error(f"خطأ في دورة التعلم الفورية: {str(e)}")
            return {'success': False, 'error': str(e)}

# إنشاء instance افتراضي
reinforcement_learning_system = ReinforcementLearningSystem()
