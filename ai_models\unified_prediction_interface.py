"""
الواجهة الموحدة لنظام التنبؤ بالاتجاه
Unified Prediction Interface
"""

import os
import sys
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_models.direction_prediction_engine import direction_prediction_engine
from ai_models.prediction_tracker import prediction_tracker
from config.currency_pairs import CURRENCY_PAIRS
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("unified_prediction_interface")

class UnifiedPredictionInterface:
    """الواجهة الموحدة لنظام التنبؤ بالاتجاه"""
    
    def __init__(self):
        """تهيئة الواجهة الموحدة"""
        self.prediction_engine = direction_prediction_engine
        self.tracker = prediction_tracker
        self.is_running = False
        
        logger.info("تم تهيئة الواجهة الموحدة لنظام التنبؤ")

    async def start_prediction_system(self):
        """بدء نظام التنبؤ الكامل"""
        try:
            if self.is_running:
                logger.warning("نظام التنبؤ يعمل بالفعل")
                return
            
            logger.info("🚀 بدء نظام التنبؤ بالاتجاه الكامل")
            
            # تهيئة نماذج الذكاء الاصطناعي
            logger.info("🧠 تهيئة نماذج الذكاء الاصطناعي...")
            ai_results = await self.prediction_engine.ai_engine.initialize_all_models()
            logger.info(f"✅ تم تدريب {ai_results['trained_pairs']}/{ai_results['total_pairs']} زوج")
            
            # بدء خدمة التتبع
            logger.info("📊 بدء خدمة تتبع التنبؤات...")
            asyncio.create_task(self.tracker.start_tracking_service())
            
            self.is_running = True
            logger.info("✅ تم بدء نظام التنبؤ بنجاح")
            
            return {
                'status': 'started',
                'ai_initialization': ai_results,
                'tracking_enabled': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام التنبؤ: {str(e)}")
            raise

    async def predict_direction(self, currency_pair: str, candles_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """التنبؤ بالاتجاه"""
        return await self.predict_single_pair(currency_pair, 5)

    async def predict_single_pair(self, currency_pair: str, timeframe: int = 5) -> Dict[str, Any]:
        """التنبؤ لزوج واحد"""
        try:
            if not self.is_running:
                logger.warning("نظام التنبؤ غير مُفعل")
                return {'error': 'نظام التنبؤ غير مُفعل'}
            
            if currency_pair not in CURRENCY_PAIRS:
                return {'error': f'الزوج {currency_pair} غير مدعوم'}
            
            logger.info(f"🎯 التنبؤ للزوج {currency_pair} - إطار زمني {timeframe} دقيقة")
            
            # التنبؤ الشامل
            prediction = await self.prediction_engine.predict_direction_comprehensive(currency_pair, timeframe)
            
            # إضافة معلومات إضافية
            prediction['system_status'] = 'active'
            prediction['supported_timeframes'] = self.prediction_engine.timeframes
            
            logger.info(f"✅ تنبؤ {currency_pair}: {prediction['direction']} بثقة {prediction['confidence']:.1f}%")
            return prediction
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ للزوج {currency_pair}: {str(e)}")
            return {'error': str(e)}

    async def predict_multiple_pairs(self, currency_pairs: List[str], timeframe: int = 5) -> Dict[str, Any]:
        """التنبؤ لعدة أزواج"""
        try:
            if not self.is_running:
                return {'error': 'نظام التنبؤ غير مُفعل'}
            
            logger.info(f"🎯 التنبؤ لـ {len(currency_pairs)} أزواج")
            
            predictions = {}
            successful_predictions = 0
            
            for pair in currency_pairs:
                if pair in CURRENCY_PAIRS:
                    try:
                        prediction = await self.predict_single_pair(pair, timeframe)
                        if 'error' not in prediction:
                            predictions[pair] = prediction
                            successful_predictions += 1
                        else:
                            predictions[pair] = prediction
                    except Exception as e:
                        predictions[pair] = {'error': str(e)}
                else:
                    predictions[pair] = {'error': f'الزوج {pair} غير مدعوم'}
            
            # تحليل النتائج الإجمالية
            summary = self._analyze_multiple_predictions(predictions)
            
            result = {
                'predictions': predictions,
                'summary': summary,
                'successful_predictions': successful_predictions,
                'total_pairs': len(currency_pairs),
                'success_rate': (successful_predictions / len(currency_pairs)) * 100,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ تم التنبؤ لـ {successful_predictions}/{len(currency_pairs)} أزواج")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ لعدة أزواج: {str(e)}")
            return {'error': str(e)}

    async def predict_all_supported_pairs(self, timeframe: int = 5) -> Dict[str, Any]:
        """التنبؤ لجميع الأزواج المدعومة"""
        try:
            logger.info(f"🎯 التنبؤ لجميع الأزواج المدعومة ({len(CURRENCY_PAIRS)} زوج)")
            
            return await self.predict_multiple_pairs(CURRENCY_PAIRS, timeframe)
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ لجميع الأزواج: {str(e)}")
            return {'error': str(e)}

    async def predict_multi_timeframe(self, currency_pair: str) -> Dict[str, Any]:
        """التنبؤ متعدد الإطارات الزمنية"""
        try:
            if not self.is_running:
                return {'error': 'نظام التنبؤ غير مُفعل'}
            
            if currency_pair not in CURRENCY_PAIRS:
                return {'error': f'الزوج {currency_pair} غير مدعوم'}
            
            logger.info(f"🎯 التنبؤ متعدد الإطارات للزوج {currency_pair}")
            
            result = await self.prediction_engine.predict_multiple_timeframes(currency_pair)
            
            logger.info(f"✅ تنبؤ متعدد الإطارات {currency_pair}: {result['overall_consensus']['direction']}")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ متعدد الإطارات: {str(e)}")
            return {'error': str(e)}

    def _analyze_multiple_predictions(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل التنبؤات المتعددة"""
        try:
            successful_predictions = {k: v for k, v in predictions.items() if 'error' not in v}
            
            if not successful_predictions:
                return {'message': 'لا توجد تنبؤات ناجحة'}
            
            # تجميع الاتجاهات
            directions = {'CALL': 0, 'PUT': 0, 'HOLD': 0}
            confidence_levels = {'high': 0, 'medium': 0, 'low': 0}
            signal_strengths = {}
            
            total_confidence = 0
            
            for pair, prediction in successful_predictions.items():
                direction = prediction.get('direction', 'HOLD')
                confidence = prediction.get('confidence', 0)
                signal_strength = prediction.get('signal_strength', 'غير محدد')
                
                directions[direction] += 1
                total_confidence += confidence
                
                # تصنيف مستوى الثقة
                if confidence >= 80:
                    confidence_levels['high'] += 1
                elif confidence >= 60:
                    confidence_levels['medium'] += 1
                else:
                    confidence_levels['low'] += 1
                
                # تجميع قوة الإشارات
                if signal_strength not in signal_strengths:
                    signal_strengths[signal_strength] = 0
                signal_strengths[signal_strength] += 1
            
            # حساب المتوسطات
            avg_confidence = total_confidence / len(successful_predictions)
            
            # تحديد الاتجاه السائد
            dominant_direction = max(directions, key=directions.get)
            direction_consensus = (directions[dominant_direction] / len(successful_predictions)) * 100
            
            return {
                'total_analyzed': len(successful_predictions),
                'direction_distribution': directions,
                'dominant_direction': dominant_direction,
                'direction_consensus': direction_consensus,
                'average_confidence': avg_confidence,
                'confidence_distribution': confidence_levels,
                'signal_strength_distribution': signal_strengths,
                'market_sentiment': self._determine_market_sentiment(directions, avg_confidence)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التنبؤات المتعددة: {str(e)}")
            return {'error': str(e)}

    def _determine_market_sentiment(self, directions: Dict[str, int], avg_confidence: float) -> str:
        """تحديد معنويات السوق"""
        try:
            total_signals = sum(directions.values())
            call_ratio = directions['CALL'] / total_signals if total_signals > 0 else 0
            put_ratio = directions['PUT'] / total_signals if total_signals > 0 else 0
            
            if call_ratio >= 0.7 and avg_confidence >= 70:
                return "صعودي قوي"
            elif call_ratio >= 0.6:
                return "صعودي"
            elif put_ratio >= 0.7 and avg_confidence >= 70:
                return "هبوطي قوي"
            elif put_ratio >= 0.6:
                return "هبوطي"
            elif avg_confidence < 60:
                return "غير واضح"
            else:
                return "محايد"
                
        except Exception as e:
            logger.warning(f"خطأ في تحديد معنويات السوق: {str(e)}")
            return "غير محدد"

    async def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            # حالة النظام الأساسية
            system_status = {
                'is_running': self.is_running,
                'prediction_engine_status': 'active' if self.is_running else 'inactive',
                'tracking_service_status': 'active' if self.tracker.tracking_enabled else 'inactive'
            }
            
            # إحصائيات الأداء
            if self.is_running:
                performance_stats = self.prediction_engine.get_performance_statistics()
                ai_status = self.prediction_engine.ai_engine.get_system_status()
                
                system_status.update({
                    'performance_statistics': performance_stats,
                    'ai_engine_status': ai_status,
                    'supported_pairs': len(CURRENCY_PAIRS),
                    'supported_timeframes': self.prediction_engine.timeframes,
                    'active_predictions': len(self.prediction_engine.active_predictions)
                })
            
            system_status['timestamp'] = datetime.now().isoformat()
            return system_status
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة النظام: {str(e)}")
            return {'error': str(e)}

    async def generate_performance_report(self, report_type: str = "comprehensive") -> Dict[str, Any]:
        """إنشاء تقرير أداء"""
        try:
            if not self.is_running:
                return {'error': 'نظام التنبؤ غير مُفعل'}
            
            logger.info(f"📊 إنشاء تقرير أداء {report_type}")
            
            report = await self.tracker.generate_performance_report(report_type)
            
            # إضافة معلومات النظام
            report['system_info'] = {
                'total_supported_pairs': len(CURRENCY_PAIRS),
                'active_timeframes': self.prediction_engine.timeframes,
                'ai_models_status': self.prediction_engine.ai_engine.get_system_status()
            }
            
            logger.info("✅ تم إنشاء تقرير الأداء")
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الأداء: {str(e)}")
            return {'error': str(e)}

    async def verify_prediction(self, prediction_id: str) -> Dict[str, Any]:
        """التحقق من دقة تنبؤ معين"""
        try:
            if not self.is_running:
                return {'error': 'نظام التنبؤ غير مُفعل'}
            
            result = await self.prediction_engine.verify_prediction_accuracy(prediction_id)
            
            logger.info(f"تم التحقق من التنبؤ {prediction_id}")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من التنبؤ: {str(e)}")
            return {'error': str(e)}

    def stop_prediction_system(self):
        """إيقاف نظام التنبؤ"""
        try:
            if not self.is_running:
                logger.warning("نظام التنبؤ غير مُفعل")
                return
            
            logger.info("🛑 إيقاف نظام التنبؤ")
            
            # إيقاف خدمة التتبع
            self.tracker.stop_tracking()
            
            self.is_running = False
            logger.info("✅ تم إيقاف نظام التنبؤ")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام التنبؤ: {str(e)}")

# إنشاء instance افتراضي
unified_prediction_interface = UnifiedPredictionInterface()
