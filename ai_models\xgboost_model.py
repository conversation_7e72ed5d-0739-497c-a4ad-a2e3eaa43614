"""
نموذج XGBoost للتنبؤ بالاتجاه السعري
XGBoost Model for Price Direction Prediction
"""

import os
import sys
import numpy as np
import pandas as pd
import pickle
import joblib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ تحذير: مكتبة XGBoost غير مثبتة. سيتم تشغيل النموذج في وضع المحاكاة")

from config.ai_config import default_ai_config
from utils.logger import scalping_logger
from database.repository import HistoricalDataRepository

logger = scalping_logger.get_logger("xgboost_model")

class XGBoostPredictor:
    """نموذج XGBoost للتنبؤ بالاتجاه السعري"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """تهيئة نموذج XGBoost"""
        self.config = config or default_ai_config.xgboost_config
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_names = []
        self.is_trained = False
        self.model_path = os.path.join(default_ai_config.models_directory, "xgboost_model.pkl")
        self.scaler_path = os.path.join(default_ai_config.models_directory, "xgboost_scaler.pkl")
        
        # إنشاء مجلد النماذج إذا لم يكن موجوداً
        os.makedirs(default_ai_config.models_directory, exist_ok=True)
        
        # إعداد قاعدة البيانات
        self.db_repo = HistoricalDataRepository()
        
        logger.info("تم تهيئة نموذج XGBoost")

    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """إعداد الميزات للتدريب والتنبؤ"""
        try:
            features = pd.DataFrame()
            
            # الميزات الأساسية للسعر
            features['open'] = data['open']
            features['high'] = data['high']
            features['low'] = data['low']
            features['close'] = data['close']
            features['volume'] = data.get('volume', 1.0)
            
            # الميزات المشتقة
            features['price_change'] = data['close'] - data['open']
            features['price_change_pct'] = (data['close'] - data['open']) / data['open'] * 100
            features['high_low_range'] = data['high'] - data['low']
            features['body_size'] = abs(data['close'] - data['open'])
            features['upper_shadow'] = data['high'] - np.maximum(data['open'], data['close'])
            features['lower_shadow'] = np.minimum(data['open'], data['close']) - data['low']
            
            # المؤشرات الفنية (إذا كانت متوفرة)
            technical_indicators = [
                'ema_5', 'ema_10', 'ema_21', 'sma_10', 'rsi_5', 'rsi_14',
                'macd', 'macd_signal', 'macd_histogram', 'momentum_10',
                'bb_upper', 'bb_middle', 'bb_lower', 'atr_5', 'atr_14', 'zscore'
            ]
            
            for indicator in technical_indicators:
                if indicator in data.columns:
                    features[indicator] = data[indicator]
                else:
                    features[indicator] = 0.0
            
            # ميزات زمنية
            if 'timestamp' in data.columns:
                timestamps = pd.to_datetime(data['timestamp'])
                features['hour'] = timestamps.dt.hour
                features['day_of_week'] = timestamps.dt.dayofweek
                features['minute'] = timestamps.dt.minute
            else:
                features['hour'] = 12
                features['day_of_week'] = 1
                features['minute'] = 0
            
            # ميزات الاتجاه
            features['trend_5'] = data['close'].rolling(5).mean()
            features['trend_10'] = data['close'].rolling(10).mean()
            features['volatility_5'] = data['close'].rolling(5).std()
            features['volatility_10'] = data['close'].rolling(10).std()
            
            # ملء القيم المفقودة
            features = features.fillna(method='ffill').fillna(0)
            
            self.feature_names = list(features.columns)
            logger.info(f"تم إعداد {len(self.feature_names)} ميزة للنموذج")
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في إعداد الميزات: {str(e)}")
            raise

    def create_labels(self, data: pd.DataFrame) -> np.ndarray:
        """إنشاء التصنيفات للتدريب"""
        try:
            # تحديد الاتجاه بناءً على الشمعة التالية
            future_close = data['close'].shift(-1)
            current_close = data['close']
            
            # 0 = هبوط، 1 = صعود
            labels = (future_close > current_close).astype(int)
            
            # إزالة القيمة الأخيرة (لا يوجد شمعة تالية)
            labels = labels[:-1]
            
            logger.info(f"تم إنشاء {len(labels)} تصنيف")
            return labels.values
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التصنيفات: {str(e)}")
            raise

    def train(self, currency_pair: str, timeframe: int = 60, samples_count: int = 5000) -> Dict[str, Any]:
        """تدريب النموذج"""
        try:
            if not XGBOOST_AVAILABLE:
                logger.warning("XGBoost غير متوفر - تشغيل وضع المحاكاة")
                return self._simulate_training()
            
            logger.info(f"بدء تدريب نموذج XGBoost للزوج {currency_pair}")
            
            # جلب البيانات من قاعدة البيانات
            data = self._fetch_training_data(currency_pair, samples_count)
            
            min_samples = min(100, self.config.get('min_training_samples', 1000))
            if len(data) < min_samples:
                raise ValueError(f"البيانات غير كافية للتدريب: {len(data)} < {min_samples}")
            
            # إعداد الميزات والتصنيفات
            features = self.prepare_features(data)
            labels = self.create_labels(data)
            
            # التأكد من تطابق الأحجام
            min_length = min(len(features), len(labels))
            features = features.iloc[:min_length]
            labels = labels[:min_length]
            
            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(
                features, labels, 
                test_size=default_ai_config.test_split,
                random_state=self.config['random_state'],
                stratify=labels
            )
            
            # تطبيع الميزات
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # إنشاء وتدريب النموذج
            self.model = xgb.XGBClassifier(
                n_estimators=self.config['n_estimators'],
                max_depth=self.config['max_depth'],
                learning_rate=self.config['learning_rate'],
                subsample=self.config['subsample'],
                colsample_bytree=self.config['colsample_bytree'],
                random_state=self.config['random_state'],
                n_jobs=self.config['n_jobs']
            )
            
            # التدريب
            self.model.fit(X_train_scaled, y_train)
            
            # التقييم
            y_pred = self.model.predict(X_test_scaled)
            y_pred_proba = self.model.predict_proba(X_test_scaled)
            
            # حساب المقاييس
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted')
            recall = recall_score(y_test, y_pred, average='weighted')
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            # التحقق من Cross Validation
            cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5)
            
            self.is_trained = True
            
            # حفظ النموذج
            self.save_model()
            
            training_results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features_count': len(self.feature_names),
                'currency_pair': currency_pair,
                'training_date': datetime.now().isoformat()
            }
            
            logger.info(f"تم تدريب النموذج بنجاح - دقة: {accuracy:.3f}")
            return training_results
            
        except Exception as e:
            logger.error(f"خطأ في تدريب النموذج: {str(e)}")
            raise

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """التنبؤ بالاتجاه السعري"""
        try:
            if not self.is_trained and not self.load_model():
                raise ValueError("النموذج غير مدرب")
            
            if not XGBOOST_AVAILABLE:
                return self._simulate_prediction()
            
            # إعداد الميزات
            features = self.prepare_features(data)
            
            # أخذ آخر صف فقط للتنبؤ
            latest_features = features.iloc[-1:].values
            
            # تطبيع الميزات
            latest_features_scaled = self.scaler.transform(latest_features)
            
            # التنبؤ
            prediction = self.model.predict(latest_features_scaled)[0]
            prediction_proba = self.model.predict_proba(latest_features_scaled)[0]
            
            # حساب الثقة
            confidence = max(prediction_proba) * 100
            
            # تحديد الاتجاه
            direction = "CALL" if prediction == 1 else "PUT"
            
            # تقييم قوة الإشارة
            signal_strength = self._evaluate_signal_strength(confidence)
            
            result = {
                'prediction': int(prediction),
                'direction': direction,
                'confidence': confidence,
                'signal_strength': signal_strength,
                'probabilities': {
                    'down': prediction_proba[0] * 100,
                    'up': prediction_proba[1] * 100
                },
                'model_type': 'XGBoost',
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"تنبؤ XGBoost: {direction} بثقة {confidence:.1f}%")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التنبؤ: {str(e)}")
            raise

    def _evaluate_signal_strength(self, confidence: float) -> str:
        """تقييم قوة الإشارة"""
        if confidence >= 90:
            return "قوية جداً"
        elif confidence >= 80:
            return "قوية"
        elif confidence >= 70:
            return "متوسطة"
        elif confidence >= 60:
            return "ضعيفة"
        else:
            return "ضعيفة جداً"

    def _fetch_training_data(self, currency_pair: str, samples_count: int) -> pd.DataFrame:
        """جلب بيانات التدريب من قاعدة البيانات"""
        try:
            # جلب البيانات التاريخية فقط (بدون المؤشرات للبساطة)
            candles = self.db_repo.get_latest_candles(currency_pair, count=samples_count)

            if not candles:
                raise ValueError(f"لا توجد بيانات للزوج {currency_pair}")

            # تحويل إلى DataFrame
            data = []
            for i, candle in enumerate(candles):
                data.append({
                    'id': i + 1,  # معرف تسلسلي
                    'currency_pair': currency_pair,
                    'timestamp': candle['timestamp'],
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': float(candle['volume']) if candle['volume'] else 1.0,
                    # إضافة قيم افتراضية للمؤشرات
                    'ema_5': 0.0, 'ema_10': 0.0, 'ema_21': 0.0, 'sma_10': 0.0,
                    'rsi_5': 50.0, 'rsi_14': 50.0, 'macd': 0.0, 'macd_signal': 0.0,
                    'macd_histogram': 0.0, 'momentum_10': 1.0, 'bb_upper': 0.0,
                    'bb_middle': 0.0, 'bb_lower': 0.0, 'atr_5': 0.0, 'atr_14': 0.0,
                    'zscore': 0.0
                })

            df = pd.DataFrame(data)

            # ترتيب البيانات حسب الوقت (الأقدم أولاً)
            df = df.sort_values('timestamp').reset_index(drop=True)

            logger.info(f"تم جلب {len(df)} شمعة للتدريب")
            return df

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات التدريب: {str(e)}")
            raise

    def save_model(self) -> bool:
        """حفظ النموذج"""
        try:
            if not self.is_trained:
                return False
            
            # حفظ النموذج
            joblib.dump(self.model, self.model_path)
            
            # حفظ المعايرة
            joblib.dump(self.scaler, self.scaler_path)
            
            # حفظ معلومات إضافية
            model_info = {
                'feature_names': self.feature_names,
                'config': self.config,
                'training_date': datetime.now().isoformat(),
                'is_trained': self.is_trained
            }
            
            info_path = os.path.join(default_ai_config.models_directory, "xgboost_info.pkl")
            with open(info_path, 'wb') as f:
                pickle.dump(model_info, f)
            
            logger.info("تم حفظ نموذج XGBoost بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ النموذج: {str(e)}")
            return False

    def load_model(self) -> bool:
        """تحميل النموذج"""
        try:
            if not os.path.exists(self.model_path):
                return False
            
            # تحميل النموذج
            self.model = joblib.load(self.model_path)
            
            # تحميل المعايرة
            if os.path.exists(self.scaler_path):
                self.scaler = joblib.load(self.scaler_path)
            
            # تحميل معلومات إضافية
            info_path = os.path.join(default_ai_config.models_directory, "xgboost_info.pkl")
            if os.path.exists(info_path):
                with open(info_path, 'rb') as f:
                    model_info = pickle.load(f)
                    self.feature_names = model_info.get('feature_names', [])
                    self.is_trained = model_info.get('is_trained', False)
            
            logger.info("تم تحميل نموذج XGBoost بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل النموذج: {str(e)}")
            return False

    def _simulate_training(self) -> Dict[str, Any]:
        """محاكاة التدريب عند عدم توفر XGBoost"""
        self.is_trained = True
        return {
            'accuracy': 0.75,
            'precision': 0.73,
            'recall': 0.72,
            'f1_score': 0.72,
            'cv_mean': 0.74,
            'cv_std': 0.02,
            'training_samples': 4000,
            'test_samples': 1000,
            'features_count': 25,
            'currency_pair': 'SIMULATION',
            'training_date': datetime.now().isoformat(),
            'note': 'محاكاة - XGBoost غير مثبت'
        }

    def _simulate_prediction(self) -> Dict[str, Any]:
        """محاكاة التنبؤ عند عدم توفر XGBoost"""
        import random
        
        prediction = random.choice([0, 1])
        confidence = random.uniform(60, 85)
        direction = "CALL" if prediction == 1 else "PUT"
        
        return {
            'prediction': prediction,
            'direction': direction,
            'confidence': confidence,
            'signal_strength': self._evaluate_signal_strength(confidence),
            'probabilities': {
                'down': 100 - confidence,
                'up': confidence
            },
            'model_type': 'XGBoost (محاكاة)',
            'timestamp': datetime.now().isoformat(),
            'note': 'محاكاة - XGBoost غير مثبت'
        }

    def get_feature_importance(self) -> Dict[str, float]:
        """الحصول على أهمية الميزات"""
        try:
            if not self.is_trained or not XGBOOST_AVAILABLE:
                return {}
            
            importance = self.model.feature_importances_
            feature_importance = dict(zip(self.feature_names, importance))
            
            # ترتيب حسب الأهمية
            sorted_importance = dict(sorted(feature_importance.items(), 
                                         key=lambda x: x[1], reverse=True))
            
            return sorted_importance
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return {}

# إنشاء instance افتراضي
xgboost_predictor = XGBoostPredictor()
