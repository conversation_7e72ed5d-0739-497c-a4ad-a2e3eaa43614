"""
إعدادات الذكاء الاصطناعي لنظام السكالبينغ
"""

import os
from dataclasses import dataclass, field
from typing import Dict, Any, List
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

@dataclass
class AIConfig:
    """إعدادات نماذج الذكاء الاصطناعي"""
    
    # إعدادات عامة للذكاء الاصطناعي
    enable_ai: bool = True
    models_directory: str = os.getenv("AI_MODELS_DIR", "ai_models/saved_models")
    training_data_directory: str = os.getenv("TRAINING_DATA_DIR", "data/training")
    
    # إعدادات التدريب العامة
    retrain_interval_hours: int = int(os.getenv("RETRAIN_INTERVAL", "24"))
    min_training_samples: int = int(os.getenv("MIN_TRAINING_SAMPLES", "1000"))
    validation_split: float = float(os.getenv("VALIDATION_SPLIT", "0.2"))
    test_split: float = float(os.getenv("TEST_SPLIT", "0.1"))
    
    # إعدادات XGBoost
    xgboost_config: Dict[str, Any] = field(default_factory=lambda: {
        "n_estimators": int(os.getenv("XGB_N_ESTIMATORS", "100")),
        "max_depth": int(os.getenv("XGB_MAX_DEPTH", "6")),
        "learning_rate": float(os.getenv("XGB_LEARNING_RATE", "0.1")),
        "subsample": float(os.getenv("XGB_SUBSAMPLE", "0.8")),
        "colsample_bytree": float(os.getenv("XGB_COLSAMPLE", "0.8")),
        "random_state": int(os.getenv("XGB_RANDOM_STATE", "42")),
        "n_jobs": int(os.getenv("XGB_N_JOBS", "-1")),
        "min_confidence_threshold": float(os.getenv("XGB_MIN_CONFIDENCE", "80.0")),
        "feature_importance_threshold": float(os.getenv("XGB_FEATURE_THRESHOLD", "0.01"))
    })
    
    # إعدادات LSTM
    lstm_config: Dict[str, Any] = field(default_factory=lambda: {
        "sequence_length": int(os.getenv("LSTM_SEQUENCE_LENGTH", "100")),
        "hidden_units": int(os.getenv("LSTM_HIDDEN_UNITS", "50")),
        "num_layers": int(os.getenv("LSTM_NUM_LAYERS", "2")),
        "dropout_rate": float(os.getenv("LSTM_DROPOUT", "0.2")),
        "learning_rate": float(os.getenv("LSTM_LEARNING_RATE", "0.001")),
        "epochs": int(os.getenv("LSTM_EPOCHS", "50")),
        "batch_size": int(os.getenv("LSTM_BATCH_SIZE", "32")),
        "patience": int(os.getenv("LSTM_PATIENCE", "10")),
        "min_delta": float(os.getenv("LSTM_MIN_DELTA", "0.001")),
        "validation_split": float(os.getenv("LSTM_VALIDATION_SPLIT", "0.2"))
    })
    
    # إعدادات Random Forest
    random_forest_config: Dict[str, Any] = field(default_factory=lambda: {
        "n_estimators": int(os.getenv("RF_N_ESTIMATORS", "100")),
        "max_depth": int(os.getenv("RF_MAX_DEPTH", "10")),
        "min_samples_split": int(os.getenv("RF_MIN_SAMPLES_SPLIT", "5")),
        "min_samples_leaf": int(os.getenv("RF_MIN_SAMPLES_LEAF", "2")),
        "max_features": os.getenv("RF_MAX_FEATURES", "sqrt"),
        "random_state": int(os.getenv("RF_RANDOM_STATE", "42")),
        "n_jobs": int(os.getenv("RF_N_JOBS", "-1")),
        "bootstrap": os.getenv("RF_BOOTSTRAP", "true").lower() == "true"
    })
    
    # إعدادات Ensemble (التجميع)
    ensemble_config: Dict[str, Any] = field(default_factory=lambda: {
        "voting_method": os.getenv("ENSEMBLE_VOTING", "weighted"),  # weighted, majority, average
        "model_weights": {
            "xgboost": float(os.getenv("ENSEMBLE_XGB_WEIGHT", "0.5")),
            "lstm": float(os.getenv("ENSEMBLE_LSTM_WEIGHT", "0.3")),
            "random_forest": float(os.getenv("ENSEMBLE_RF_WEIGHT", "0.2"))
        },
        "min_models_agreement": int(os.getenv("ENSEMBLE_MIN_AGREEMENT", "2")),
        "confidence_threshold": float(os.getenv("ENSEMBLE_CONFIDENCE_THRESHOLD", "75.0"))
    })
    
    # إعدادات معالجة البيانات للذكاء الاصطناعي
    data_preprocessing: Dict[str, Any] = field(default_factory=lambda: {
        "normalize_features": True,
        "handle_missing_values": "interpolate",  # interpolate, drop, fill_zero
        "feature_scaling_method": "standard",    # standard, minmax, robust
        "outlier_detection": True,
        "outlier_method": "iqr",                # iqr, zscore, isolation_forest
        "feature_selection": True,
        "max_features": int(os.getenv("AI_MAX_FEATURES", "50"))
    })
    
    # إعدادات تقييم النماذج
    model_evaluation: Dict[str, Any] = field(default_factory=lambda: {
        "cross_validation_folds": int(os.getenv("CV_FOLDS", "5")),
        "scoring_metrics": ["accuracy", "precision", "recall", "f1", "roc_auc"],
        "min_accuracy_threshold": float(os.getenv("MIN_ACCURACY", "0.65")),
        "min_precision_threshold": float(os.getenv("MIN_PRECISION", "0.60")),
        "min_recall_threshold": float(os.getenv("MIN_RECALL", "0.60")),
        "performance_degradation_threshold": float(os.getenv("PERFORMANCE_DEGRADATION", "0.05"))
    })
    
    # إعدادات الميزات (Features)
    feature_engineering: Dict[str, Any] = field(default_factory=lambda: {
        "include_technical_indicators": True,
        "include_candlestick_patterns": True,
        "include_price_action": True,
        "include_volume_indicators": True,
        "include_time_features": True,
        "include_market_sentiment": True,
        "lookback_periods": [1, 3, 5, 10, 15],  # فترات النظر للخلف
        "rolling_statistics": ["mean", "std", "min", "max"],
        "lag_features": [1, 2, 3, 5]  # الميزات المتأخرة
    })
    
    # إعدادات الحفظ والتحميل
    model_persistence: Dict[str, Any] = field(default_factory=lambda: {
        "auto_save": True,
        "save_frequency": "daily",  # daily, weekly, after_training
        "keep_model_versions": int(os.getenv("KEEP_MODEL_VERSIONS", "5")),
        "backup_to_cloud": False,
        "compression": True
    })
    
    def validate(self) -> bool:
        """التحقق من صحة إعدادات الذكاء الاصطناعي"""
        # التحقق من أوزان Ensemble
        total_weight = sum(self.ensemble_config["model_weights"].values())
        if abs(total_weight - 1.0) > 0.01:
            return False
        
        # التحقق من حدود الثقة
        if not (0 <= self.xgboost_config["min_confidence_threshold"] <= 100):
            return False
        
        # التحقق من معاملات LSTM
        if self.lstm_config["sequence_length"] <= 0:
            return False
        
        # التحقق من معاملات Random Forest
        if self.random_forest_config["n_estimators"] <= 0:
            return False
        
        return True
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """الحصول على إعدادات نموذج معين"""
        model_configs = {
            "xgboost": self.xgboost_config,
            "lstm": self.lstm_config,
            "random_forest": self.random_forest_config,
            "ensemble": self.ensemble_config
        }
        return model_configs.get(model_name, {})
    
    def update_model_config(self, model_name: str, config: Dict[str, Any]) -> None:
        """تحديث إعدادات نموذج معين"""
        if model_name == "xgboost":
            self.xgboost_config.update(config)
        elif model_name == "lstm":
            self.lstm_config.update(config)
        elif model_name == "random_forest":
            self.random_forest_config.update(config)
        elif model_name == "ensemble":
            self.ensemble_config.update(config)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الإعدادات إلى قاموس"""
        return {
            "enable_ai": self.enable_ai,
            "retrain_interval_hours": self.retrain_interval_hours,
            "min_training_samples": self.min_training_samples,
            "xgboost_config": self.xgboost_config,
            "lstm_config": self.lstm_config,
            "random_forest_config": self.random_forest_config,
            "ensemble_config": self.ensemble_config,
            "data_preprocessing": self.data_preprocessing,
            "model_evaluation": self.model_evaluation,
            "feature_engineering": self.feature_engineering
        }

# إنشاء instance افتراضي
default_ai_config = AIConfig()
