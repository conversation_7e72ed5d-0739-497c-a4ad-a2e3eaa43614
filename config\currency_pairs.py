"""
تكوين أزواج العملات للتداول - القائمة الكاملة للـ70 زوج
"""

# قائمة الأزواج الـ70 المحددة للمشروع (حسب الطلب الأصلي)
CURRENCY_PAIRS_70 = [
    "USDJPY",
    "USDPHP_otc",
    "CHFJPY",
    "USDCHF",
    "USDSGD_otc",
    "USDVND_otc",
    "USDJPY_otc",
    "USDCAD",
    "AUDJPY_otc",
    "GBPAUD_otc",
    "USDMYR_otc",
    "NGNUSD_otc",
    "USDRUB_otc",
    "GBPUSD",
    "EURCHF_otc",
    "AUDCAD",
    "TNDUSD_otc",
    "EURGBP",
    "NZDJPY_otc",
    "USDTHB_otc",
    "LBPUSD_otc",
    "CADCHF_otc",
    "USDCHF_otc",
    "EURGBP_otc",
    "CADJPY_otc",
    "USDBRL_otc",
    "USDPKR_otc",
    "EURCAD",
    "EURAUD",
    "EURNZD_otc",
    "USDDZD_otc",
    "USDEGP_otc",
    "AUDUSD",
    "CADCHF",
    "EURJPY",
    "NZDUSD_otc",
    "CHFJPY_otc",
    "AUDNZD_otc",
    "GBPJPY_otc",
    "GBPCHF",
    "AUDCHF",
    "YERUSD_otc",
    "EURHUF_otc",
    "USDMXN_otc",
    "IRRUSD_otc",
    "USDBDT_otc",
    "EURTRY_otc",
    "GBPJPY",
    "GBPCAD",
    "USDIDR_otc",
    "USDINR_otc",
    "GBPAUD",
    "USDCLP_otc",
    "USDCNH_otc",
    "USDCOP_otc",
    "ZARUSD_otc",
    "AUDUSD_otc",
    "AUDJPY",
    "EURJPY_otc",
    "CADJPY",
    "USDARS_otc",
    "USDCAD_otc",
    "AUDCAD_otc",
    "EURCHF",
    "EURRUB_otc",
    "EURUSD_otc",
    "GBPUSD_otc",
    "CHFNOK_otc",
    "EURUSD",
    "NZDUSD"
]

# معلومات إضافية عن الأزواج
PAIR_INFO = {
    "categories": {
        "major": ["USDJPY", "USDCHF", "USDCAD", "GBPUSD", "AUDUSD", "EURUSD"],
        "minor": ["EURJPY", "GBPJPY", "AUDJPY", "CADJPY", "CHFJPY"],
        "exotic": ["USDPHP_otc", "USDSGD_otc", "USDVND_otc", "USDMYR_otc"],
        "emerging": ["NGNUSD_otc", "TNDUSD_otc", "LBPUSD_otc", "YERUSD_otc", "IRRUSD_otc", "ZARUSD_otc"]
    },
    "total_pairs": len(CURRENCY_PAIRS_70),
    "otc_pairs": len([pair for pair in CURRENCY_PAIRS_70 if "_otc" in pair]),
    "regular_pairs": len([pair for pair in CURRENCY_PAIRS_70 if "_otc" not in pair])
}

# التحقق من العدد الصحيح
assert len(CURRENCY_PAIRS_70) == 70, f"عدد الأزواج غير صحيح: {len(CURRENCY_PAIRS_70)} بدلاً من 70"

# قائمة الأزواج القديمة للتوافق مع النظام الحالي
CURRENCY_PAIRS = [
    "EURUSD",
    "GBPUSD", 
    "USDJPY",
    "USDCHF",
    "AUDUSD",
    "USDCAD",
    "NZDUSD",
    "EURJPY",
    "GBPJPY",
    "EURGBP"
]

def get_all_pairs():
    """إرجاع جميع الأزواج الـ70"""
    return CURRENCY_PAIRS_70.copy()

def get_pair_category(pair):
    """تحديد فئة الزوج"""
    for category, pairs in PAIR_INFO["categories"].items():
        if pair in pairs:
            return category
    return "other"

def is_otc_pair(pair):
    """التحقق من كون الزوج OTC"""
    return "_otc" in pair

def get_pairs_by_category(category):
    """الحصول على الأزواج حسب الفئة"""
    return PAIR_INFO["categories"].get(category, [])

def print_pairs_summary():
    """طباعة ملخص الأزواج"""
    print(f"إجمالي الأزواج: {PAIR_INFO['total_pairs']}")
    print(f"أزواج OTC: {PAIR_INFO['otc_pairs']}")
    print(f"أزواج عادية: {PAIR_INFO['regular_pairs']}")
    
    for category, pairs in PAIR_INFO["categories"].items():
        print(f"{category}: {len(pairs)} أزواج")

if __name__ == "__main__":
    print_pairs_summary()
