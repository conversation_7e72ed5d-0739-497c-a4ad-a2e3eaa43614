"""
إعدادات قاعدة البيانات لنظام السكالبينغ
"""

import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

@dataclass
class DatabaseConfig:
    """إعدادات قاعدة البيانات PostgreSQL"""
    
    # إعدادات الاتصال الأساسية
    host: str = os.getenv("DB_HOST", "localhost")
    port: int = int(os.getenv("DB_PORT", "5432"))
    database: str = os.getenv("DB_NAME", "scalping_trading")
    username: str = os.getenv("DB_USER", "postgres")
    password: str = os.getenv("DB_PASSWORD", "8576")
    
    # إعدادات تجمع الاتصالات
    pool_size: int = int(os.getenv("DB_POOL_SIZE", "10"))
    max_overflow: int = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    pool_timeout: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    pool_recycle: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    
    # إعدادات الأداء
    echo: bool = os.getenv("DB_ECHO", "false").lower() == "true"
    echo_pool: bool = os.getenv("DB_ECHO_POOL", "false").lower() == "true"
    
    @property
    def connection_string(self) -> str:
        """إنشاء نص الاتصال الكامل"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    @property
    def async_connection_string(self) -> str:
        """إنشاء نص الاتصال غير المتزامن"""
        return f"postgresql+asyncpg://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def validate(self) -> bool:
        """التحقق من صحة الإعدادات"""
        required_fields = [self.host, self.database, self.username, self.password]
        return all(field for field in required_fields)
    
    def to_dict(self) -> dict:
        """تحويل الإعدادات إلى قاموس"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'username': self.username,
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'echo': self.echo
        }

# إنشاء instance افتراضي
default_db_config = DatabaseConfig()
