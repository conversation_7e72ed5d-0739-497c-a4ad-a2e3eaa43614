"""
إعدادات التداول لنظام السكالبينغ
"""

import os
from dataclasses import dataclass, field
from typing import List, Dict, Any
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

@dataclass
class TradingConfig:
    """إعدادات التداول الشاملة"""
    
    # إعدادات الاتصال مع Pocket Option
    pocket_option_ssid: str = os.getenv("POCKET_OPTION_SSID", "")
    
    # الأصول المستهدفة
    target_assets: List[str] = field(default_factory=lambda: [
        "EURUSD_otc", "GBPJPY_otc", "USDJPY_otc",
        "GBPUSD_otc", "AUDUSD_otc", "USDCAD_otc",
        "EURJPY_otc", "AUDCAD_otc", "CADJPY_otc"
    ])
    
    # الإطارات الزمنية المدعومة (بالثواني)
    supported_timeframes: List[int] = field(default_factory=lambda: [60, 300])  # 1m, 5m
    default_timeframe: int = 60  # 1 دقيقة
    
    # إعدادات المخاطر الأساسية
    min_trade_amount: float = float(os.getenv("MIN_TRADE_AMOUNT", "1.0"))
    max_trade_amount: float = float(os.getenv("MAX_TRADE_AMOUNT", "100.0"))
    default_trade_amount: float = float(os.getenv("DEFAULT_TRADE_AMOUNT", "5.0"))
    
    # حدود الخسائر
    max_daily_loss: float = float(os.getenv("MAX_DAILY_LOSS", "200.0"))
    max_consecutive_losses: int = int(os.getenv("MAX_CONSECUTIVE_LOSSES", "10"))
    max_weekly_loss: float = float(os.getenv("MAX_WEEKLY_LOSS", "1000.0"))
    
    # إعدادات التوقيت والفلترة
    avoid_first_minutes: int = int(os.getenv("AVOID_FIRST_MINUTES", "5"))
    avoid_last_minutes: int = int(os.getenv("AVOID_LAST_MINUTES", "5"))
    trading_hours_start: str = os.getenv("TRADING_HOURS_START", "08:00")
    trading_hours_end: str = os.getenv("TRADING_HOURS_END", "22:00")
    
    # إعدادات الثقة والقرار
    min_confidence_score: float = float(os.getenv("MIN_CONFIDENCE_SCORE", "80.0"))
    max_confidence_score: float = 100.0
    
    # أوزان طبقات التحليل
    technical_analysis_weight: float = 0.4
    behavioral_analysis_weight: float = 0.3
    ai_analysis_weight: float = 0.3
    
    # إعدادات جمع البيانات
    candles_history_count: int = 30  # عدد الشموع التاريخية المطلوبة
    data_refresh_interval: int = 5   # فترة تحديث البيانات (ثواني)

    # إعدادات الاتصال والمهلة الزمنية
    connection_timeout: int = 120    # مهلة الاتصال (ثواني)
    websocket_timeout: int = 60      # مهلة WebSocket (ثواني)
    subscription_timeout: int = 30   # مهلة الاشتراك في البث المباشر (ثواني)
    reconnect_delay: int = 10        # تأخير إعادة الاتصال (ثواني)
    max_reconnect_attempts: int = 5  # عدد محاولات إعادة الاتصال
    
    # إعدادات المؤشرات الفنية
    indicators_config: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "ema_fast": {"period": 5},
        "ema_medium": {"period": 10},
        "ema_slow": {"period": 21},
        "sma": {"period": 10},
        "rsi_fast": {"period": 5, "overbought": 80, "oversold": 20},
        "rsi_standard": {"period": 14, "overbought": 70, "oversold": 30},
        "macd": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
        "momentum": {"period": 10},
        "bollinger_bands": {"period": 20, "std_dev": 2},
        "atr_fast": {"period": 5},
        "atr_standard": {"period": 14}
    })
    
    # إعدادات التحليل السلوكي
    candlestick_patterns_config: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "doji": {"body_ratio_threshold": 0.1},
        "engulfing": {"min_body_ratio": 0.6},
        "pin_bar": {"shadow_ratio_threshold": 2.0},
        "marubozu": {"shadow_ratio_threshold": 0.1}
    })
    
    # إعدادات نماذج الذكاء الاصطناعي
    ai_models_config: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "xgboost": {
            "n_estimators": 100,
            "max_depth": 6,
            "learning_rate": 0.1,
            "random_state": 42,
            "min_confidence": 80.0
        },
        "lstm": {
            "sequence_length": 100,
            "hidden_units": 50,
            "epochs": 50,
            "batch_size": 32,
            "validation_split": 0.2
        },
        "random_forest": {
            "n_estimators": 100,
            "max_depth": 10,
            "random_state": 42,
            "min_samples_split": 5
        }
    })
    
    # إعدادات الأداء والمراقبة
    performance_monitoring: Dict[str, Any] = field(default_factory=lambda: {
        "track_metrics": True,
        "save_predictions": True,
        "log_decisions": True,
        "alert_on_losses": True,
        "daily_report": True
    })
    
    def validate(self) -> bool:
        """التحقق من صحة الإعدادات"""
        if not self.pocket_option_ssid:
            return False
        
        if self.min_trade_amount >= self.max_trade_amount:
            return False
        
        if self.min_confidence_score < 0 or self.min_confidence_score > 100:
            return False
        
        if not self.target_assets:
            return False
        
        # التحقق من مجموع الأوزان
        total_weight = (
            self.technical_analysis_weight + 
            self.behavioral_analysis_weight + 
            self.ai_analysis_weight
        )
        if abs(total_weight - 1.0) > 0.01:  # السماح بخطأ صغير
            return False
        
        return True
    
    def get_asset_config(self, asset: str) -> Dict[str, Any]:
        """الحصول على إعدادات أصل معين"""
        return {
            "timeframe": self.default_timeframe,
            "trade_amount": self.default_trade_amount,
            "min_confidence": self.min_confidence_score
        }
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """تحديث الإعدادات من قاموس"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل الإعدادات إلى قاموس"""
        return {
            "target_assets": self.target_assets,
            "min_trade_amount": self.min_trade_amount,
            "max_trade_amount": self.max_trade_amount,
            "max_daily_loss": self.max_daily_loss,
            "min_confidence_score": self.min_confidence_score,
            "technical_weight": self.technical_analysis_weight,
            "behavioral_weight": self.behavioral_analysis_weight,
            "ai_weight": self.ai_analysis_weight
        }

# إنشاء instance افتراضي
default_trading_config = TradingConfig()

# قاموس إعدادات Pocket Option للتوافق مع النظام القديم
POCKET_OPTION_CONFIG = {
    "ssid": default_trading_config.pocket_option_ssid,
    "min_trade_amount": default_trading_config.min_trade_amount,
    "max_trade_amount": default_trading_config.max_trade_amount,
    "default_timeframe": default_trading_config.default_timeframe,
    "target_assets": default_trading_config.target_assets
}
