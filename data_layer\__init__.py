# ملف تهيئة حزمة طبقة البيانات

# تصدير المجمعات الأساسية
from .historical_collector import (
    HistoricalDataCollector,
    AsyncParallelHistoricalCollector,
    AsyncHistoricalCollectorWrapper,
    historical_collector,
    parallel_collector,
    async_historical_collector
)

from .realtime_collector import realtime_collector
from .live_candle_processor import live_candle_processor
from .candle_transfer_manager import candle_transfer_manager
from .payout_monitor import payout_monitor
from .profit_balance_manager import profit_balance_manager
from .pair_validator import pair_validator
from .realtime_indicator_analyzer import realtime_indicator_analyzer
from .fifo_manager import fifo_manager
from .gap_resolver import gap_resolver

# تصدير المجمع الافتراضي (غير المتزامن للأداء الأفضل)
default_collector = async_historical_collector

__all__ = [
    'HistoricalDataCollector',
    'AsyncParallelHistoricalCollector',
    'AsyncHistoricalCollectorWrapper',
    'historical_collector',
    'parallel_collector',
    'async_historical_collector',
    'default_collector',
    'realtime_collector',
    'live_candle_processor',
    'candle_transfer_manager',
    'payout_monitor',
    'profit_balance_manager',
    'pair_validator',
    'realtime_indicator_analyzer',
    'fifo_manager',
    'gap_resolver'
]
