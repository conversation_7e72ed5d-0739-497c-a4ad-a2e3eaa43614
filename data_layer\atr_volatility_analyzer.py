"""
نظام تحليل التقلبات ATR - المرحلة الرابعة
ATR (Average True Range) Volatility Analysis System for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("atr_volatility_analyzer")

class VolatilityLevel(Enum):
    """مستوى التقلبات"""
    EXTREMELY_HIGH = "EXTREMELY_HIGH"    # تقلبات عالية جداً
    VERY_HIGH = "VERY_HIGH"              # تقلبات عالية جداً
    HIGH = "HIGH"                        # تقلبات عالية
    MODERATE_HIGH = "MODERATE_HIGH"      # تقلبات معتدلة عالية
    NORMAL = "NORMAL"                    # تقلبات طبيعية
    MODERATE_LOW = "MODERATE_LOW"        # تقلبات معتدلة منخفضة
    LOW = "LOW"                          # تقلبات منخفضة
    VERY_LOW = "VERY_LOW"                # تقلبات منخفضة جداً

class VolatilityTrend(Enum):
    """اتجاه التقلبات"""
    INCREASING_RAPIDLY = "INCREASING_RAPIDLY"    # تزايد سريع
    INCREASING = "INCREASING"                    # تزايد
    STABLE = "STABLE"                           # مستقر
    DECREASING = "DECREASING"                   # تناقص
    DECREASING_RAPIDLY = "DECREASING_RAPIDLY"   # تناقص سريع

class MarketCondition(Enum):
    """حالة السوق"""
    BREAKOUT_READY = "BREAKOUT_READY"        # جاهز للاختراق
    TRENDING = "TRENDING"                    # في اتجاه
    CONSOLIDATING = "CONSOLIDATING"          # توطيد
    RANGING = "RANGING"                      # في نطاق
    VOLATILE = "VOLATILE"                    # متقلب

@dataclass
class ATRAnalysis:
    """تحليل ATR"""
    atr_value: float
    atr_percentage: float
    volatility_level: VolatilityLevel
    volatility_trend: VolatilityTrend
    
    # مقارنات ATR
    atr_vs_sma: float      # مقارنة مع المتوسط المتحرك
    atr_percentile: float  # المئوية التاريخية
    
    # تحليل النطاقات
    support_level: float
    resistance_level: float
    breakout_threshold: float
    
    # معلومات الفترة
    period_analyzed: int
    data_quality: float
    
    # وصف التحليل
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class VolatilityAnalysisResult:
    """نتيجة تحليل التقلبات"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # تحليلات ATR لفترات مختلفة
    atr_analyses: List[ATRAnalysis]
    
    # التقييم الإجمالي
    overall_volatility: VolatilityLevel
    overall_trend: VolatilityTrend
    market_condition: MarketCondition
    
    # مؤشرات التداول
    is_breakout_imminent: bool
    optimal_stop_loss: float
    optimal_take_profit: float
    position_sizing_factor: float
    
    # توصيات
    trading_strategy: str
    risk_management: str
    entry_timing: str
    recommendations: List[str]

class ATRVolatilityAnalyzer:
    """محلل التقلبات ATR"""
    
    def __init__(self):
        self.atr_periods = [14, 21, 50]  # فترات ATR
        self.volatility_thresholds = {
            'extremely_high': 3.0,    # 3% من السعر
            'very_high': 2.0,         # 2% من السعر
            'high': 1.5,              # 1.5% من السعر
            'moderate_high': 1.0,     # 1% من السعر
            'normal': 0.7,            # 0.7% من السعر
            'moderate_low': 0.5,      # 0.5% من السعر
            'low': 0.3,               # 0.3% من السعر
            'very_low': 0.1           # 0.1% من السعر
        }
        self.volatility_history = {}  # تاريخ التقلبات لكل أصل
        
        logger.info("تم تهيئة محلل التقلبات ATR")

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_atr_volatility(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[VolatilityAnalysisResult]:
        """تحليل التقلبات باستخدام ATR"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 50:
                logger.warning(f"بيانات غير كافية لتحليل ATR لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل التقلبات ATR لـ {asset} مع {len(candles_data)} شمعة")
            
            # 1. تحليل ATR لفترات مختلفة
            atr_analyses = []
            for period in self.atr_periods:
                if len(candles_data) >= period:
                    analysis = await self._analyze_atr_period(candles_data, period)
                    if analysis:
                        atr_analyses.append(analysis)
            
            if not atr_analyses:
                logger.warning(f"لم يتم إنتاج أي تحليل ATR لـ {asset}")
                return None
            
            # 2. التقييم الإجمالي
            overall_assessment = self._evaluate_overall_volatility(atr_analyses)
            
            # 3. تحديد حالة السوق
            market_condition = self._determine_market_condition(atr_analyses, candles_data)
            
            # 4. حساب مؤشرات التداول
            trading_indicators = self._calculate_trading_indicators(atr_analyses, candles_data)
            
            # 5. إنتاج التوصيات
            recommendations = self._generate_volatility_recommendations(overall_assessment, trading_indicators)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = VolatilityAnalysisResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                atr_analyses=atr_analyses,
                overall_volatility=overall_assessment['volatility'],
                overall_trend=overall_assessment['trend'],
                market_condition=market_condition,
                is_breakout_imminent=trading_indicators['breakout_imminent'],
                optimal_stop_loss=trading_indicators['stop_loss'],
                optimal_take_profit=trading_indicators['take_profit'],
                position_sizing_factor=trading_indicators['position_sizing'],
                trading_strategy=trading_indicators['strategy'],
                risk_management=trading_indicators['risk_management'],
                entry_timing=trading_indicators['entry_timing'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.volatility_history:
                self.volatility_history[asset] = []
            
            self.volatility_history[asset].append(result)
            
            # الاحتفاظ بآخر 30 تحليل فقط
            if len(self.volatility_history[asset]) > 30:
                self.volatility_history[asset] = self.volatility_history[asset][-30:]
            
            logger.info(f"تم إكمال تحليل ATR لـ {asset}: {overall_assessment['volatility'].value}, الاتجاه: {overall_assessment['trend'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التقلبات ATR لـ {asset}: {str(e)}")
            return None

    async def _analyze_atr_period(self, candles: List[Dict[str, Any]], period: int) -> Optional[ATRAnalysis]:
        """تحليل ATR لفترة محددة"""
        try:
            if len(candles) < period:
                return None
            
            # حساب True Range لكل شمعة
            true_ranges = []
            for i in range(1, len(candles)):
                current = candles[i]
                previous = candles[i-1]
                
                # True Range = max(high-low, |high-prev_close|, |low-prev_close|)
                tr1 = current['high'] - current['low']
                tr2 = abs(current['high'] - previous['close'])
                tr3 = abs(current['low'] - previous['close'])
                
                true_range = max(tr1, tr2, tr3)
                true_ranges.append(true_range)
            
            if len(true_ranges) < period:
                return None
            
            # حساب ATR (متوسط True Range)
            recent_trs = true_ranges[-period:]
            atr_value = statistics.mean(recent_trs)
            
            # حساب ATR كنسبة مئوية من السعر
            current_price = candles[-1]['close']
            atr_percentage = (atr_value / current_price) * 100
            
            # تحديد مستوى التقلبات
            volatility_level = self._determine_volatility_level(atr_percentage)
            
            # تحليل اتجاه التقلبات
            volatility_trend = await self._analyze_volatility_trend(true_ranges, period)
            
            # مقارنة مع المتوسط المتحرك
            if len(true_ranges) >= period * 2:
                longer_period_trs = true_ranges[-(period*2):-period]
                longer_atr = statistics.mean(longer_period_trs)
                atr_vs_sma = (atr_value / longer_atr) if longer_atr > 0 else 1.0
            else:
                atr_vs_sma = 1.0
            
            # حساب المئوية التاريخية
            atr_percentile = self._calculate_atr_percentile(true_ranges, atr_value)
            
            # حساب مستويات الدعم والمقاومة
            support_level = current_price - (atr_value * 2)
            resistance_level = current_price + (atr_value * 2)
            breakout_threshold = atr_value * 1.5
            
            # حساب جودة البيانات
            data_quality = self._calculate_atr_data_quality(candles[-period:])
            
            return ATRAnalysis(
                atr_value=atr_value,
                atr_percentage=atr_percentage,
                volatility_level=volatility_level,
                volatility_trend=volatility_trend,
                atr_vs_sma=atr_vs_sma,
                atr_percentile=atr_percentile,
                support_level=support_level,
                resistance_level=resistance_level,
                breakout_threshold=breakout_threshold,
                period_analyzed=period,
                data_quality=data_quality,
                description=f"ATR {period} شموع: {atr_value:.5f} ({atr_percentage:.2f}%)",
                reasoning=f"التقلبات {volatility_level.value} مع اتجاه {volatility_trend.value}",
                supporting_data={
                    'period': period,
                    'true_ranges_sample': recent_trs[-5:],  # آخر 5 قيم
                    'price_range': max([c['high'] for c in candles[-period:]]) - min([c['low'] for c in candles[-period:]])
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل ATR للفترة {period}: {str(e)}")
            return None

    def _determine_volatility_level(self, atr_percentage: float) -> VolatilityLevel:
        """تحديد مستوى التقلبات"""
        try:
            if atr_percentage >= self.volatility_thresholds['extremely_high']:
                return VolatilityLevel.EXTREMELY_HIGH
            elif atr_percentage >= self.volatility_thresholds['very_high']:
                return VolatilityLevel.VERY_HIGH
            elif atr_percentage >= self.volatility_thresholds['high']:
                return VolatilityLevel.HIGH
            elif atr_percentage >= self.volatility_thresholds['moderate_high']:
                return VolatilityLevel.MODERATE_HIGH
            elif atr_percentage >= self.volatility_thresholds['normal']:
                return VolatilityLevel.NORMAL
            elif atr_percentage >= self.volatility_thresholds['moderate_low']:
                return VolatilityLevel.MODERATE_LOW
            elif atr_percentage >= self.volatility_thresholds['low']:
                return VolatilityLevel.LOW
            else:
                return VolatilityLevel.VERY_LOW

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى التقلبات: {str(e)}")
            return VolatilityLevel.NORMAL

    async def _analyze_volatility_trend(self, true_ranges: List[float], period: int) -> VolatilityTrend:
        """تحليل اتجاه التقلبات"""
        try:
            if len(true_ranges) < period * 2:
                return VolatilityTrend.STABLE

            # مقارنة الفترة الحالية مع الفترة السابقة
            current_period = true_ranges[-period:]
            previous_period = true_ranges[-(period*2):-period]

            current_avg = statistics.mean(current_period)
            previous_avg = statistics.mean(previous_period)

            if previous_avg == 0:
                return VolatilityTrend.STABLE

            change_ratio = (current_avg - previous_avg) / previous_avg

            # تحليل الاتجاه قصير المدى (آخر ربع الفترة)
            recent_quarter = true_ranges[-period//4:]
            older_quarter = true_ranges[-(period//2):-(period//4)]

            if recent_quarter and older_quarter:
                recent_avg = statistics.mean(recent_quarter)
                older_avg = statistics.mean(older_quarter)

                if older_avg > 0:
                    short_term_change = (recent_avg - older_avg) / older_avg
                else:
                    short_term_change = 0
            else:
                short_term_change = 0

            # تحديد الاتجاه
            if change_ratio >= 0.2 or short_term_change >= 0.3:
                return VolatilityTrend.INCREASING_RAPIDLY
            elif change_ratio >= 0.1 or short_term_change >= 0.15:
                return VolatilityTrend.INCREASING
            elif change_ratio <= -0.2 or short_term_change <= -0.3:
                return VolatilityTrend.DECREASING_RAPIDLY
            elif change_ratio <= -0.1 or short_term_change <= -0.15:
                return VolatilityTrend.DECREASING
            else:
                return VolatilityTrend.STABLE

        except Exception as e:
            logger.error(f"خطأ في تحليل اتجاه التقلبات: {str(e)}")
            return VolatilityTrend.STABLE

    def _calculate_atr_percentile(self, true_ranges: List[float], current_atr: float) -> float:
        """حساب المئوية التاريخية لـ ATR"""
        try:
            if len(true_ranges) < 20:
                return 50.0

            # حساب ATR لفترات متحركة
            atr_values = []
            period = 14  # فترة ATR القياسية

            for i in range(period, len(true_ranges)):
                period_trs = true_ranges[i-period:i]
                atr_val = statistics.mean(period_trs)
                atr_values.append(atr_val)

            if not atr_values:
                return 50.0

            # حساب المئوية
            lower_values = [atr for atr in atr_values if atr < current_atr]
            percentile = (len(lower_values) / len(atr_values)) * 100

            return percentile

        except Exception as e:
            logger.error(f"خطأ في حساب المئوية التاريخية: {str(e)}")
            return 50.0

    def _calculate_atr_data_quality(self, candles: List[Dict[str, Any]]) -> float:
        """حساب جودة بيانات ATR"""
        try:
            if not candles:
                return 0.0

            quality_factors = []

            # فحص اكتمال البيانات
            complete_candles = 0
            for candle in candles:
                if (candle.get('high') is not None and
                    candle.get('low') is not None and
                    candle.get('close') is not None and
                    candle.get('open') is not None):
                    complete_candles += 1

            completeness = complete_candles / len(candles)
            quality_factors.append(completeness)

            # فحص منطقية البيانات (High >= Low, etc.)
            logical_candles = 0
            for candle in candles:
                if (candle.get('high', 0) >= candle.get('low', 0) and
                    candle.get('high', 0) >= candle.get('open', 0) and
                    candle.get('high', 0) >= candle.get('close', 0) and
                    candle.get('low', 0) <= candle.get('open', 0) and
                    candle.get('low', 0) <= candle.get('close', 0)):
                    logical_candles += 1

            logic_quality = logical_candles / len(candles)
            quality_factors.append(logic_quality)

            # فحص عدم وجود فجوات كبيرة
            gap_quality = 1.0
            for i in range(1, len(candles)):
                current = candles[i]
                previous = candles[i-1]

                gap = abs(current.get('open', 0) - previous.get('close', 0))
                price_range = previous.get('high', 0) - previous.get('low', 0)

                if price_range > 0 and gap > price_range * 3:  # فجوة أكبر من 3 أضعاف النطاق
                    gap_quality -= 0.1

            gap_quality = max(0.0, gap_quality)
            quality_factors.append(gap_quality)

            return statistics.mean(quality_factors)

        except Exception as e:
            logger.error(f"خطأ في حساب جودة بيانات ATR: {str(e)}")
            return 0.5

    def _evaluate_overall_volatility(self, atr_analyses: List[ATRAnalysis]) -> Dict[str, Any]:
        """تقييم التقلبات الإجمالية"""
        try:
            if not atr_analyses:
                return {
                    'volatility': VolatilityLevel.NORMAL,
                    'trend': VolatilityTrend.STABLE
                }

            # حساب متوسط مستوى التقلبات مرجح بجودة البيانات
            volatility_levels = {"VERY_LOW": 1, "LOW": 2, "MODERATE_LOW": 3, "NORMAL": 4,
                               "MODERATE_HIGH": 5, "HIGH": 6, "VERY_HIGH": 7, "EXTREMELY_HIGH": 8}

            weighted_volatility = 0
            total_weight = 0

            for analysis in atr_analyses:
                weight = analysis.data_quality
                volatility_value = volatility_levels[analysis.volatility_level.value]
                weighted_volatility += volatility_value * weight
                total_weight += weight

            if total_weight > 0:
                avg_volatility_value = weighted_volatility / total_weight
            else:
                avg_volatility_value = 4  # NORMAL

            # تحديد المستوى الإجمالي
            if avg_volatility_value >= 7.5:
                overall_volatility = VolatilityLevel.EXTREMELY_HIGH
            elif avg_volatility_value >= 6.5:
                overall_volatility = VolatilityLevel.VERY_HIGH
            elif avg_volatility_value >= 5.5:
                overall_volatility = VolatilityLevel.HIGH
            elif avg_volatility_value >= 4.5:
                overall_volatility = VolatilityLevel.MODERATE_HIGH
            elif avg_volatility_value >= 3.5:
                overall_volatility = VolatilityLevel.NORMAL
            elif avg_volatility_value >= 2.5:
                overall_volatility = VolatilityLevel.MODERATE_LOW
            elif avg_volatility_value >= 1.5:
                overall_volatility = VolatilityLevel.LOW
            else:
                overall_volatility = VolatilityLevel.VERY_LOW

            # تحديد الاتجاه الإجمالي
            trend_values = {"DECREASING_RAPIDLY": 1, "DECREASING": 2, "STABLE": 3,
                          "INCREASING": 4, "INCREASING_RAPIDLY": 5}

            trend_scores = [trend_values[analysis.volatility_trend.value] for analysis in atr_analyses]
            avg_trend_value = statistics.mean(trend_scores)

            if avg_trend_value >= 4.5:
                overall_trend = VolatilityTrend.INCREASING_RAPIDLY
            elif avg_trend_value >= 3.5:
                overall_trend = VolatilityTrend.INCREASING
            elif avg_trend_value >= 2.5:
                overall_trend = VolatilityTrend.STABLE
            elif avg_trend_value >= 1.5:
                overall_trend = VolatilityTrend.DECREASING
            else:
                overall_trend = VolatilityTrend.DECREASING_RAPIDLY

            return {
                'volatility': overall_volatility,
                'trend': overall_trend
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم التقلبات الإجمالية: {str(e)}")
            return {
                'volatility': VolatilityLevel.NORMAL,
                'trend': VolatilityTrend.STABLE
            }

    def _determine_market_condition(self, atr_analyses: List[ATRAnalysis], candles: List[Dict[str, Any]]) -> MarketCondition:
        """تحديد حالة السوق"""
        try:
            if not atr_analyses or not candles:
                return MarketCondition.RANGING

            # تحليل التقلبات
            latest_analysis = atr_analyses[0]  # أحدث تحليل
            volatility_level = latest_analysis.volatility_level
            volatility_trend = latest_analysis.volatility_trend

            # تحليل حركة السعر
            recent_candles = candles[-20:]  # آخر 20 شمعة
            prices = [candle['close'] for candle in recent_candles]

            # حساب النطاق السعري
            current_price = prices[-1]
            avg_price = statistics.mean(prices)

            # تحديد الاتجاه
            price_trend = "NEUTRAL"
            if current_price > avg_price * 1.01:
                price_trend = "UP"
            elif current_price < avg_price * 0.99:
                price_trend = "DOWN"

            # تحديد حالة السوق
            if (volatility_level in [VolatilityLevel.VERY_LOW, VolatilityLevel.LOW] and
                volatility_trend == VolatilityTrend.DECREASING):
                return MarketCondition.BREAKOUT_READY

            elif (volatility_level in [VolatilityLevel.HIGH, VolatilityLevel.VERY_HIGH] and
                  price_trend != "NEUTRAL"):
                return MarketCondition.TRENDING

            elif (volatility_level in [VolatilityLevel.MODERATE_LOW, VolatilityLevel.NORMAL] and
                  volatility_trend == VolatilityTrend.STABLE):
                return MarketCondition.CONSOLIDATING

            elif volatility_level in [VolatilityLevel.EXTREMELY_HIGH, VolatilityLevel.VERY_HIGH]:
                return MarketCondition.VOLATILE

            else:
                return MarketCondition.RANGING

        except Exception as e:
            logger.error(f"خطأ في تحديد حالة السوق: {str(e)}")
            return MarketCondition.RANGING

    def _calculate_trading_indicators(self, atr_analyses: List[ATRAnalysis], candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مؤشرات التداول"""
        try:
            if not atr_analyses or not candles:
                return {
                    'breakout_imminent': False,
                    'stop_loss': 0.0,
                    'take_profit': 0.0,
                    'position_sizing': 1.0,
                    'strategy': 'WAIT',
                    'risk_management': 'HIGH_RISK',
                    'entry_timing': 'WAIT'
                }

            latest_analysis = atr_analyses[0]
            atr_value = latest_analysis.atr_value

            # تحديد احتمالية الاختراق
            breakout_imminent = (
                latest_analysis.volatility_level in [VolatilityLevel.VERY_LOW, VolatilityLevel.LOW] and
                latest_analysis.volatility_trend == VolatilityTrend.DECREASING and
                latest_analysis.atr_percentile < 25  # في أدنى 25% تاريخياً
            )

            # حساب Stop Loss الأمثل (2x ATR)
            optimal_stop_loss = atr_value * 2

            # حساب Take Profit الأمثل (3x ATR)
            optimal_take_profit = atr_value * 3

            # حساب عامل حجم الصفقة
            if latest_analysis.volatility_level == VolatilityLevel.VERY_LOW:
                position_sizing = 1.5  # زيادة حجم الصفقة
            elif latest_analysis.volatility_level == VolatilityLevel.LOW:
                position_sizing = 1.2
            elif latest_analysis.volatility_level in [VolatilityLevel.NORMAL, VolatilityLevel.MODERATE_LOW]:
                position_sizing = 1.0
            elif latest_analysis.volatility_level in [VolatilityLevel.MODERATE_HIGH, VolatilityLevel.HIGH]:
                position_sizing = 0.8
            else:  # VERY_HIGH, EXTREMELY_HIGH
                position_sizing = 0.5

            # تحديد الاستراتيجية
            if latest_analysis.volatility_level in [VolatilityLevel.VERY_LOW, VolatilityLevel.LOW]:
                strategy = 'BREAKOUT'
            elif latest_analysis.volatility_level in [VolatilityLevel.HIGH, VolatilityLevel.VERY_HIGH]:
                strategy = 'TREND_FOLLOWING'
            elif latest_analysis.volatility_level == VolatilityLevel.NORMAL:
                strategy = 'SCALPING'
            else:
                strategy = 'RANGE_TRADING'

            # تحديد إدارة المخاطر
            if latest_analysis.volatility_level in [VolatilityLevel.EXTREMELY_HIGH, VolatilityLevel.VERY_HIGH]:
                risk_management = 'VERY_HIGH_RISK'
            elif latest_analysis.volatility_level == VolatilityLevel.HIGH:
                risk_management = 'HIGH_RISK'
            elif latest_analysis.volatility_level in [VolatilityLevel.MODERATE_HIGH, VolatilityLevel.NORMAL]:
                risk_management = 'MEDIUM_RISK'
            else:
                risk_management = 'LOW_RISK'

            # تحديد توقيت الدخول
            if breakout_imminent:
                entry_timing = 'WAIT_FOR_BREAKOUT'
            elif latest_analysis.volatility_trend == VolatilityTrend.INCREASING:
                entry_timing = 'ENTER_NOW'
            elif latest_analysis.volatility_trend == VolatilityTrend.STABLE:
                entry_timing = 'MONITOR'
            else:
                entry_timing = 'WAIT'

            return {
                'breakout_imminent': breakout_imminent,
                'stop_loss': optimal_stop_loss,
                'take_profit': optimal_take_profit,
                'position_sizing': position_sizing,
                'strategy': strategy,
                'risk_management': risk_management,
                'entry_timing': entry_timing
            }

        except Exception as e:
            logger.error(f"خطأ في حساب مؤشرات التداول: {str(e)}")
            return {
                'breakout_imminent': False,
                'stop_loss': 0.0,
                'take_profit': 0.0,
                'position_sizing': 1.0,
                'strategy': 'WAIT',
                'risk_management': 'HIGH_RISK',
                'entry_timing': 'WAIT'
            }

    def _generate_volatility_recommendations(self, overall_assessment: Dict[str, Any], trading_indicators: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات التقلبات"""
        try:
            recommendations = []

            volatility = overall_assessment['volatility']
            trend = overall_assessment['trend']
            strategy = trading_indicators['strategy']

            # توصيات حسب مستوى التقلبات
            if volatility == VolatilityLevel.EXTREMELY_HIGH:
                recommendations.append("تقلبات عالية جداً - تجنب التداول أو استخدم أحجام صغيرة جداً")
                recommendations.append("انتظار انخفاض التقلبات قبل الدخول")
            elif volatility == VolatilityLevel.VERY_HIGH:
                recommendations.append("تقلبات عالية - حذر شديد مطلوب")
                recommendations.append("استخدم stop loss ضيق جداً")
            elif volatility == VolatilityLevel.HIGH:
                recommendations.append("تقلبات عالية - فرص جيدة للأرباح السريعة")
                recommendations.append("مناسب لاستراتيجية trend following")
            elif volatility == VolatilityLevel.NORMAL:
                recommendations.append("تقلبات طبيعية - مثالي للسكالبينغ")
                recommendations.append("يمكن استخدام أحجام صفقات عادية")
            elif volatility in [VolatilityLevel.LOW, VolatilityLevel.VERY_LOW]:
                recommendations.append("تقلبات منخفضة - انتظار اختراق محتمل")
                recommendations.append("مراقبة إشارات الاختراق عن كثب")

            # توصيات حسب اتجاه التقلبات
            if trend == VolatilityTrend.INCREASING_RAPIDLY:
                recommendations.append("التقلبات تزداد بسرعة - توقع حركات سعرية كبيرة")
            elif trend == VolatilityTrend.INCREASING:
                recommendations.append("التقلبات في ازدياد - استعد لزيادة النشاط")
            elif trend == VolatilityTrend.DECREASING:
                recommendations.append("التقلبات تتناقص - توقع هدوء السوق")
            elif trend == VolatilityTrend.DECREASING_RAPIDLY:
                recommendations.append("التقلبات تنخفض بسرعة - احتمالية اختراق قريب")

            # توصيات الاستراتيجية
            if strategy == 'BREAKOUT':
                recommendations.append("استراتيجية الاختراق مناسبة - انتظار كسر المستويات")
                recommendations.append("ضع أوامر معلقة عند مستويات الدعم والمقاومة")
            elif strategy == 'TREND_FOLLOWING':
                recommendations.append("استراتيجية تتبع الاتجاه مناسبة - ادخل مع الاتجاه")
                recommendations.append("استخدم trailing stop لحماية الأرباح")
            elif strategy == 'SCALPING':
                recommendations.append("السكالبينغ مناسب - استهدف أرباح سريعة صغيرة")
                recommendations.append("ادخل واخرج بسرعة")
            elif strategy == 'RANGE_TRADING':
                recommendations.append("التداول في النطاق مناسب - اشتري عند الدعم وبع عند المقاومة")

            # توصيات إدارة المخاطر
            risk_level = trading_indicators['risk_management']
            if risk_level == 'VERY_HIGH_RISK':
                recommendations.append("مخاطر عالية جداً - لا تتجاوز 1% من رأس المال")
            elif risk_level == 'HIGH_RISK':
                recommendations.append("مخاطر عالية - لا تتجاوز 2% من رأس المال")
            elif risk_level == 'MEDIUM_RISK':
                recommendations.append("مخاطر متوسطة - يمكن استخدام 3-5% من رأس المال")
            else:
                recommendations.append("مخاطر منخفضة - يمكن زيادة حجم الصفقات")

            # توصيات التوقيت
            entry_timing = trading_indicators['entry_timing']
            if entry_timing == 'WAIT_FOR_BREAKOUT':
                recommendations.append("انتظار الاختراق - لا تدخل قبل كسر المستوى")
            elif entry_timing == 'ENTER_NOW':
                recommendations.append("توقيت جيد للدخول - التقلبات تدعم الحركة")
            elif entry_timing == 'MONITOR':
                recommendations.append("راقب السوق عن كثب - انتظار إشارة واضحة")
            else:
                recommendations.append("انتظار ظروف أفضل للتداول")

            # توصيات Stop Loss و Take Profit
            stop_loss = trading_indicators['stop_loss']
            take_profit = trading_indicators['take_profit']
            recommendations.append(f"Stop Loss المقترح: {stop_loss:.5f} نقطة")
            recommendations.append(f"Take Profit المقترح: {take_profit:.5f} نقطة")

            # توصية حجم الصفقة
            position_sizing = trading_indicators['position_sizing']
            recommendations.append(f"مضاعف حجم الصفقة: {position_sizing:.1f}x")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات التقلبات: {str(e)}")
            return ["خطأ في إنتاج التوصيات"]

    def get_volatility_history(self, asset: str, limit: int = 10) -> List[VolatilityAnalysisResult]:
        """الحصول على تاريخ تحليل التقلبات"""
        try:
            if asset not in self.volatility_history:
                return []

            return self.volatility_history[asset][-limit:]

        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ التقلبات لـ {asset}: {str(e)}")
            return []

    def get_current_volatility_summary(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملخص التقلبات الحالي"""
        try:
            if asset not in self.volatility_history or not self.volatility_history[asset]:
                return None

            latest_analysis = self.volatility_history[asset][-1]

            return {
                'asset': asset,
                'timestamp': latest_analysis.analysis_timestamp,
                'overall_volatility': latest_analysis.overall_volatility.value,
                'overall_trend': latest_analysis.overall_trend.value,
                'market_condition': latest_analysis.market_condition.value,
                'is_breakout_imminent': latest_analysis.is_breakout_imminent,
                'optimal_stop_loss': latest_analysis.optimal_stop_loss,
                'optimal_take_profit': latest_analysis.optimal_take_profit,
                'position_sizing_factor': latest_analysis.position_sizing_factor,
                'trading_strategy': latest_analysis.trading_strategy,
                'atr_analyses_count': len(latest_analysis.atr_analyses)
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص التقلبات لـ {asset}: {str(e)}")
            return None

# إنشاء instance عام للاستخدام
atr_volatility_analyzer = ATRVolatilityAnalyzer()

if __name__ == "__main__":
    # اختبار سريع
    import asyncio

    async def test_analyzer():
        # بيانات اختبار
        test_candles = []
        base_price = 1.1000

        # إنشاء بيانات اختبار مع تقلبات متغيرة
        for i in range(100):
            # تقلبات متزايدة تدريجياً
            volatility = 0.0005 + (i * 0.00001)

            high = base_price + volatility
            low = base_price - volatility
            close = base_price + (volatility * 0.5 if i % 2 == 0 else -volatility * 0.5)

            candle = {
                'open': base_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': 1000 + i * 10,
                'timestamp': i
            }
            test_candles.append(candle)
            base_price = close  # تحديث السعر الأساسي

        # تشغيل التحليل
        result = await atr_volatility_analyzer.analyze_atr_volatility("TEST_PAIR", test_candles)

        if result:
            print(f"تحليل التقلبات ATR:")
            print(f"مستوى التقلبات: {result.overall_volatility.value}")
            print(f"اتجاه التقلبات: {result.overall_trend.value}")
            print(f"حالة السوق: {result.market_condition.value}")
            print(f"اختراق وشيك: {result.is_breakout_imminent}")
            print(f"Stop Loss الأمثل: {result.optimal_stop_loss:.5f}")
            print(f"Take Profit الأمثل: {result.optimal_take_profit:.5f}")
            print(f"عامل حجم الصفقة: {result.position_sizing_factor:.1f}x")
            print(f"الاستراتيجية المقترحة: {result.trading_strategy}")

            print(f"\nتحليلات ATR:")
            for analysis in result.atr_analyses:
                print(f"  فترة {analysis.period_analyzed}: ATR = {analysis.atr_value:.5f} ({analysis.atr_percentage:.2f}%)")
        else:
            print("فشل في التحليل")

    # تشغيل الاختبار
    asyncio.run(test_analyzer())
