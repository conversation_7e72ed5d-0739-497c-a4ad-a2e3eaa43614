"""
مدير نقل الشموع المغلقة - نظام نقل تلقائي من Redis إلى PostgreSQL
Closed Candles Transfer Manager - Automatic transfer from Redis to PostgreSQL
"""

import asyncio
import json
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional

from config.trading_config import default_trading_config
from config.currency_pairs import CURRENCY_PAIRS_70
from data_layer.realtime_collector import realtime_collector
from database.repository import historical_data_repo
from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors, handle_errors

logger = scalping_logger.get_logger("candle_transfer_manager")

class CandleTransferManager:
    """مدير نقل الشموع المغلقة من Redis إلى PostgreSQL"""
    
    def __init__(self, config=None):
        self.config = config or default_trading_config
        self.realtime_collector = realtime_collector
        self.historical_repo = historical_data_repo
        
        # إعدادات النظام
        self.is_transferring = False
        self.transfer_interval = 30.0  # نقل كل 30 ثانية
        self.batch_size = 100  # نقل 100 شمعة في المرة الواحدة
        self.redis_retention_time = 300  # الاحتفاظ في Redis لـ 5 دقائق
        
        # قوائم النقل
        self.pending_transfers = {}  # {asset: [candles]}
        self.transfer_queue = []  # قائمة انتظار النقل
        
        # إحصائيات النقل
        self.transfer_stats = {
            'total_transferred': 0,
            'successful_transfers': 0,
            'failed_transfers': 0,
            'last_transfer_time': None,
            'start_time': None,
            'errors': 0,
            'redis_cleanups': 0
        }
        
        logger.info("تم تهيئة مدير نقل الشموع المغلقة")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_system(self) -> bool:
        """تهيئة النظام"""
        try:
            logger.info("🔧 تهيئة نظام نقل الشموع المغلقة...")
            
            # التأكد من اتصال Redis
            if not self.realtime_collector.redis_client:
                await self.realtime_collector.initialize_redis()
            
            if not self.realtime_collector.redis_client:
                logger.error("فشل في الاتصال بـ Redis")
                return False
            
            # التأكد من اتصال قاعدة البيانات
            try:
                # اختبار الاتصال بقاعدة البيانات
                test_assets = self.historical_repo.get_all_assets_with_data()
                logger.info(f"اتصال قاعدة البيانات متاح - {len(test_assets)} أصل موجود")
            except Exception as e:
                logger.error(f"فشل في الاتصال بقاعدة البيانات: {str(e)}")
                return False
            
            # تحميل الشموع المعلقة من Redis
            await self._load_pending_candles()
            
            logger.info("✅ تم تهيئة نظام نقل الشموع المغلقة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة النظام: {str(e)}")
            return False
    
    async def _load_pending_candles(self):
        """تحميل الشموع المعلقة من Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return
            
            # البحث عن الشموع المغلقة في Redis
            pattern = "closed_candle:*"
            keys = self.realtime_collector.redis_client.keys(pattern)
            
            loaded_count = 0
            for key in keys:
                try:
                    candle_data = self.realtime_collector.redis_client.get(key)
                    if candle_data:
                        candle = json.loads(candle_data)
                        asset = candle.get('asset')
                        
                        if asset:
                            if asset not in self.pending_transfers:
                                self.pending_transfers[asset] = []
                            
                            self.pending_transfers[asset].append(candle)
                            loaded_count += 1
                            
                except Exception as e:
                    logger.debug(f"خطأ في تحميل شمعة من {key}: {str(e)}")
                    continue
            
            if loaded_count > 0:
                logger.info(f"تم تحميل {loaded_count} شمعة معلقة من Redis")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الشموع المعلقة: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def start_transfer_system(self, assets: List[str] = None) -> bool:
        """بدء نظام النقل التلقائي"""
        try:
            if self.is_transferring:
                logger.warning("نظام النقل قيد التشغيل بالفعل")
                return True
            
            assets = assets or CURRENCY_PAIRS_70
            logger.info(f"🚀 بدء نظام نقل الشموع المغلقة لـ {len(assets)} أصل")
            
            self.is_transferring = True
            self.transfer_stats['start_time'] = datetime.now()
            
            # بدء حلقة النقل
            asyncio.create_task(self._transfer_loop(assets))
            
            logger.info("✅ تم بدء نظام النقل التلقائي بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام النقل: {str(e)}")
            self.is_transferring = False
            return False
    
    async def _transfer_loop(self, assets: List[str]):
        """حلقة النقل التلقائي"""
        try:
            logger.info("🔄 بدء حلقة نقل الشموع المغلقة")
            
            while self.is_transferring:
                start_time = time.time()
                
                try:
                    # البحث عن الشموع المغلقة الجديدة
                    await self._scan_for_closed_candles(assets)
                    
                    # نقل الشموع المعلقة
                    await self._process_pending_transfers()
                    
                    # تنظيف Redis من الشموع القديمة
                    await self._cleanup_old_redis_candles()
                    
                    # تحديث الإحصائيات
                    self.transfer_stats['last_transfer_time'] = datetime.now()
                    
                except Exception as e:
                    logger.error(f"خطأ في حلقة النقل: {str(e)}")
                    self.transfer_stats['errors'] += 1
                
                # انتظار حتى الدورة التالية
                processing_time = time.time() - start_time
                sleep_time = max(0, self.transfer_interval - processing_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
        except Exception as e:
            logger.error(f"خطأ في حلقة النقل: {str(e)}")
        finally:
            logger.info("انتهت حلقة نقل الشموع المغلقة")
    
    async def _scan_for_closed_candles(self, assets: List[str]):
        """البحث عن الشموع المغلقة الجديدة في Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return
            
            # البحث عن الشموع المغلقة
            pattern = "closed_candle:*"
            keys = self.realtime_collector.redis_client.keys(pattern)
            
            new_candles = 0
            for key in keys:
                try:
                    candle_data = self.realtime_collector.redis_client.get(key)
                    if candle_data:
                        candle = json.loads(candle_data)
                        asset = candle.get('asset')
                        
                        if asset and asset in assets:
                            # التحقق من عدم وجود الشمعة مسبقاً
                            if not self._is_candle_already_pending(asset, candle):
                                if asset not in self.pending_transfers:
                                    self.pending_transfers[asset] = []
                                
                                self.pending_transfers[asset].append(candle)
                                new_candles += 1
                                
                except Exception as e:
                    logger.debug(f"خطأ في معالجة شمعة من {key}: {str(e)}")
                    continue
            
            if new_candles > 0:
                logger.debug(f"تم العثور على {new_candles} شمعة مغلقة جديدة")
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن الشموع المغلقة: {str(e)}")
    
    def _is_candle_already_pending(self, asset: str, new_candle: Dict[str, Any]) -> bool:
        """التحقق من وجود الشمعة في قائمة الانتظار"""
        try:
            if asset not in self.pending_transfers:
                return False
            
            new_timestamp = new_candle.get('timestamp')
            if not new_timestamp:
                return False
            
            for pending_candle in self.pending_transfers[asset]:
                if pending_candle.get('timestamp') == new_timestamp:
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"خطأ في التحقق من الشمعة المعلقة: {str(e)}")
            return False
    
    async def _process_pending_transfers(self):
        """معالجة الشموع المعلقة للنقل"""
        try:
            if not self.pending_transfers:
                return
            
            total_transferred = 0
            
            for asset, candles in list(self.pending_transfers.items()):
                if not candles:
                    continue
                
                # نقل الشموع في دفعات
                batch = candles[:self.batch_size]
                
                success = await self._transfer_candles_batch(asset, batch)
                
                if success:
                    # إزالة الشموع المنقولة من قائمة الانتظار
                    self.pending_transfers[asset] = candles[self.batch_size:]
                    
                    # إزالة القائمة إذا كانت فارغة
                    if not self.pending_transfers[asset]:
                        del self.pending_transfers[asset]
                    
                    total_transferred += len(batch)
                    self.transfer_stats['successful_transfers'] += len(batch)
                    
                    logger.debug(f"تم نقل {len(batch)} شمعة لـ {asset} إلى قاعدة البيانات")
                else:
                    self.transfer_stats['failed_transfers'] += len(batch)
                    logger.warning(f"فشل في نقل {len(batch)} شمعة لـ {asset}")
            
            if total_transferred > 0:
                self.transfer_stats['total_transferred'] += total_transferred
                logger.info(f"تم نقل {total_transferred} شمعة إلى قاعدة البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة النقل: {str(e)}")
    
    async def _transfer_candles_batch(self, asset: str, candles: List[Dict[str, Any]]) -> bool:
        """نقل دفعة من الشموع إلى قاعدة البيانات"""
        try:
            if not candles:
                return True
            
            # تحويل الشموع لتنسيق قاعدة البيانات
            db_candles = []
            for candle in candles:
                try:
                    # استخراج بيانات الشمعة
                    candle_info = candle.get('candle', {})
                    indicators = candle.get('indicators', {})
                    
                    db_candle = {
                        'asset': asset,
                        'timeframe': 60,  # دقيقة واحدة
                        'timestamp': candle_info.get('timestamp', datetime.now()),
                        'open_price': float(candle_info.get('open', 0)),
                        'high_price': float(candle_info.get('high', 0)),
                        'low_price': float(candle_info.get('low', 0)),
                        'close_price': float(candle_info.get('close', 0)),
                        'volume': float(candle_info.get('volume', 0)),
                        'indicators': indicators,
                        'analysis_timestamp': candle.get('timestamp', datetime.now().isoformat())
                    }
                    
                    db_candles.append(db_candle)
                    
                except Exception as e:
                    logger.debug(f"خطأ في تحويل شمعة لـ {asset}: {str(e)}")
                    continue
            
            if not db_candles:
                return False
            
            # نقل إلى قاعدة البيانات
            if len(db_candles) == 1:
                # شمعة واحدة
                success = self.historical_repo.store_candle_with_indicators(db_candles[0])
            else:
                # عدة شموع
                success = True
                for db_candle in db_candles:
                    if not self.historical_repo.store_candle_with_indicators(db_candle):
                        success = False
                        break
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في نقل دفعة الشموع لـ {asset}: {str(e)}")
            return False
    
    async def _cleanup_old_redis_candles(self):
        """تنظيف الشموع القديمة من Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return
            
            # البحث عن الشموع القديمة
            pattern = "closed_candle:*"
            keys = self.realtime_collector.redis_client.keys(pattern)
            
            cleaned_count = 0
            cutoff_time = datetime.now() - timedelta(seconds=self.redis_retention_time)
            
            for key in keys:
                try:
                    candle_data = self.realtime_collector.redis_client.get(key)
                    if candle_data:
                        candle = json.loads(candle_data)
                        
                        # التحقق من عمر الشمعة
                        timestamp_str = candle.get('timestamp')
                        if timestamp_str:
                            try:
                                candle_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                
                                if candle_time < cutoff_time:
                                    # حذف الشمعة القديمة
                                    self.realtime_collector.redis_client.delete(key)
                                    cleaned_count += 1
                                    
                            except Exception as e:
                                logger.debug(f"خطأ في تحليل وقت الشمعة: {str(e)}")
                                continue
                                
                except Exception as e:
                    logger.debug(f"خطأ في تنظيف شمعة من {key}: {str(e)}")
                    continue
            
            if cleaned_count > 0:
                self.transfer_stats['redis_cleanups'] += cleaned_count
                logger.debug(f"تم تنظيف {cleaned_count} شمعة قديمة من Redis")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف Redis: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def stop_transfer_system(self) -> bool:
        """إيقاف نظام النقل"""
        try:
            if not self.is_transferring:
                logger.warning("نظام النقل غير قيد التشغيل")
                return True
            
            logger.info("🛑 إيقاف نظام نقل الشموع المغلقة...")
            
            self.is_transferring = False
            
            # نقل الشموع المعلقة الأخيرة
            await self._process_pending_transfers()
            
            logger.info("✅ تم إيقاف نظام النقل بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام النقل: {str(e)}")
            return False
    
    @handle_async_errors(default_return=False, log_error=True)
    async def force_transfer_asset(self, asset: str) -> bool:
        """فرض نقل جميع الشموع المعلقة لأصل معين"""
        try:
            if asset not in self.pending_transfers:
                logger.info(f"لا توجد شموع معلقة لـ {asset}")
                return True
            
            candles = self.pending_transfers[asset]
            success = await self._transfer_candles_batch(asset, candles)
            
            if success:
                del self.pending_transfers[asset]
                logger.info(f"تم نقل {len(candles)} شمعة لـ {asset} بالقوة")
            else:
                logger.error(f"فشل في النقل القسري لـ {asset}")
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في النقل القسري لـ {asset}: {str(e)}")
            return False
    
    @handle_errors(default_return={}, log_error=True)
    def get_transfer_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النقل"""
        runtime = (datetime.now() - self.transfer_stats['start_time']).total_seconds() if self.transfer_stats['start_time'] else 0
        
        return {
            'is_transferring': self.is_transferring,
            'runtime_seconds': round(runtime, 1),
            'total_transferred': self.transfer_stats['total_transferred'],
            'successful_transfers': self.transfer_stats['successful_transfers'],
            'failed_transfers': self.transfer_stats['failed_transfers'],
            'errors': self.transfer_stats['errors'],
            'redis_cleanups': self.transfer_stats['redis_cleanups'],
            'pending_assets': len(self.pending_transfers),
            'pending_candles': sum(len(candles) for candles in self.pending_transfers.values()),
            'last_transfer_time': self.transfer_stats['last_transfer_time'].isoformat() if self.transfer_stats['last_transfer_time'] else None,
            'transfer_rate': round(self.transfer_stats['total_transferred'] / max(runtime, 1), 2),
            'success_rate': round((self.transfer_stats['successful_transfers'] / max(self.transfer_stats['total_transferred'], 1)) * 100, 2)
        }
    
    @handle_errors(default_return={}, log_error=True)
    def get_pending_transfers_summary(self) -> Dict[str, int]:
        """الحصول على ملخص الشموع المعلقة"""
        return {asset: len(candles) for asset, candles in self.pending_transfers.items()}
    
    @handle_async_errors(default_return=[], log_error=True)
    async def get_redis_closed_candles_count(self) -> int:
        """الحصول على عدد الشموع المغلقة في Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return 0
            
            pattern = "closed_candle:*"
            keys = self.realtime_collector.redis_client.keys(pattern)
            
            return len(keys)
            
        except Exception as e:
            logger.error(f"خطأ في عد الشموع في Redis: {str(e)}")
            return 0

# إنشاء مثيل عام للاستخدام
candle_transfer_manager = CandleTransferManager()
