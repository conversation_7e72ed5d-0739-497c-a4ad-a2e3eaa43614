"""
محلل أنماط الشموع - المرحلة الرابعة
Candlestick Pattern Analyzer for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("candlestick_pattern_analyzer")

class CandlestickPatternType(Enum):
    """أنواع أنماط الشموع"""
    # أنماط الانعكاس
    PIN_BAR = "PIN_BAR"                     # شمعة الدبوس
    DOJI = "DOJI"                           # شمعة الدوجي
    HAMMER = "HAMMER"                       # شمعة المطرقة
    HANGING_MAN = "HANGING_MAN"             # الرجل المعلق
    SHOOTING_STAR = "SHOOTING_STAR"         # النجم الساقط
    INVERTED_HAMMER = "INVERTED_HAMMER"     # المطرقة المقلوبة
    
    # أنماط الابتلاع
    BULLISH_ENGULFING = "BULLISH_ENGULFING" # الابتلاع الصاعد
    BEARISH_ENGULFING = "BEARISH_ENGULFING" # الابتلاع الهابط
    
    # أنماط الماروبوزو
    BULLISH_MARUBOZU = "BULLISH_MARUBOZU"   # ماروبوزو صاعد
    BEARISH_MARUBOZU = "BEARISH_MARUBOZU"   # ماروبوزو هابط
    
    # أنماط النجوم
    MORNING_STAR = "MORNING_STAR"           # نجمة الصباح
    EVENING_STAR = "EVENING_STAR"           # نجمة المساء
    
    # أنماط الهارامي
    BULLISH_HARAMI = "BULLISH_HARAMI"       # هارامي صاعد
    BEARISH_HARAMI = "BEARISH_HARAMI"       # هارامي هابط

class PatternSignal(Enum):
    """إشارة النمط"""
    STRONG_BULLISH = "STRONG_BULLISH"
    BULLISH = "BULLISH"
    NEUTRAL = "NEUTRAL"
    BEARISH = "BEARISH"
    STRONG_BEARISH = "STRONG_BEARISH"

class PatternReliability(Enum):
    """موثوقية النمط"""
    VERY_HIGH = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    VERY_LOW = 1

@dataclass
class CandlestickPattern:
    """نمط شمعة"""
    pattern_type: CandlestickPatternType
    signal: PatternSignal
    reliability: PatternReliability
    confidence: float  # 0-100
    
    # معلومات النمط
    candle_index: int  # موقع الشمعة في البيانات
    pattern_strength: float  # قوة النمط
    
    # تفاصيل الشمعة/الشموع
    candles_involved: List[Dict[str, Any]]
    
    # معايير التحليل
    body_size_ratio: float
    shadow_ratio: float
    volume_confirmation: bool
    
    # وصف النمط
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class PatternAnalysisResult:
    """نتيجة تحليل الأنماط"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # الأنماط المكتشفة
    detected_patterns: List[CandlestickPattern]
    
    # التقييم الإجمالي
    overall_signal: PatternSignal
    overall_confidence: float
    overall_reliability: PatternReliability
    
    # إحصائيات
    total_patterns: int
    bullish_patterns: int
    bearish_patterns: int
    high_reliability_patterns: int
    
    # توصيات
    is_actionable: bool
    recommended_action: str
    risk_assessment: str
    recommendations: List[str]

class CandlestickPatternAnalyzer:
    """محلل أنماط الشموع"""
    
    def __init__(self):
        self.pattern_weights = self._initialize_pattern_weights()
        self.reliability_thresholds = self._initialize_reliability_thresholds()
        self.min_confidence_threshold = 70.0
        self.pattern_history = {}  # تاريخ الأنماط لكل أصل
        
        logger.info("تم تهيئة محلل أنماط الشموع")

    def _initialize_pattern_weights(self) -> Dict[CandlestickPatternType, float]:
        """تهيئة أوزان الأنماط"""
        return {
            # أنماط الانعكاس القوية
            CandlestickPatternType.PIN_BAR: 0.85,
            CandlestickPatternType.HAMMER: 0.80,
            CandlestickPatternType.SHOOTING_STAR: 0.80,
            CandlestickPatternType.DOJI: 0.75,
            
            # أنماط الابتلاع
            CandlestickPatternType.BULLISH_ENGULFING: 0.90,
            CandlestickPatternType.BEARISH_ENGULFING: 0.90,
            
            # أنماط الماروبوزو
            CandlestickPatternType.BULLISH_MARUBOZU: 0.70,
            CandlestickPatternType.BEARISH_MARUBOZU: 0.70,
            
            # أنماط النجوم
            CandlestickPatternType.MORNING_STAR: 0.85,
            CandlestickPatternType.EVENING_STAR: 0.85,
            
            # أنماط الهارامي
            CandlestickPatternType.BULLISH_HARAMI: 0.65,
            CandlestickPatternType.BEARISH_HARAMI: 0.65,
            
            # أنماط أخرى
            CandlestickPatternType.HANGING_MAN: 0.75,
            CandlestickPatternType.INVERTED_HAMMER: 0.70
        }

    def _initialize_reliability_thresholds(self) -> Dict[str, float]:
        """تهيئة عتبات الموثوقية"""
        return {
            'body_size_min': 0.3,      # الحد الأدنى لحجم الجسم
            'shadow_ratio_min': 2.0,   # نسبة الظل للجسم
            'volume_increase': 1.2,    # زيادة الحجم المطلوبة
            'pattern_strength_min': 0.6 # الحد الأدنى لقوة النمط
        }

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_candlestick_patterns(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[PatternAnalysisResult]:
        """تحليل أنماط الشموع"""
        return await self.analyze_patterns(asset, candles_data)

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_patterns(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[PatternAnalysisResult]:
        """تحليل أنماط الشموع"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 5:
                logger.warning(f"بيانات غير كافية لتحليل الأنماط لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل أنماط الشموع لـ {asset} مع {len(candles_data)} شمعة")
            
            detected_patterns = []
            
            # تحليل الأنماط المختلفة
            for i in range(2, len(candles_data)):  # نبدأ من الشمعة الثالثة
                
                # 1. أنماط الشمعة الواحدة
                single_patterns = await self._analyze_single_candle_patterns(candles_data, i)
                detected_patterns.extend(single_patterns)
                
                # 2. أنماط الشمعتين
                if i >= 2:
                    double_patterns = await self._analyze_double_candle_patterns(candles_data, i)
                    detected_patterns.extend(double_patterns)
                
                # 3. أنماط الثلاث شموع
                if i >= 3:
                    triple_patterns = await self._analyze_triple_candle_patterns(candles_data, i)
                    detected_patterns.extend(triple_patterns)
            
            # تقييم الأنماط الإجمالي
            overall_assessment = self._evaluate_overall_patterns(detected_patterns)
            
            # حساب الإحصائيات
            stats = self._calculate_pattern_statistics(detected_patterns)
            
            # إنتاج التوصيات
            recommendations = self._generate_pattern_recommendations(detected_patterns, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = PatternAnalysisResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                detected_patterns=detected_patterns,
                overall_signal=overall_assessment['signal'],
                overall_confidence=overall_assessment['confidence'],
                overall_reliability=overall_assessment['reliability'],
                total_patterns=stats['total'],
                bullish_patterns=stats['bullish'],
                bearish_patterns=stats['bearish'],
                high_reliability_patterns=stats['high_reliability'],
                is_actionable=overall_assessment['is_actionable'],
                recommended_action=overall_assessment['action'],
                risk_assessment=overall_assessment['risk'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.pattern_history:
                self.pattern_history[asset] = []
            
            self.pattern_history[asset].append(result)
            
            # الاحتفاظ بآخر 30 تحليل فقط
            if len(self.pattern_history[asset]) > 30:
                self.pattern_history[asset] = self.pattern_history[asset][-30:]
            
            logger.info(f"تم إكمال تحليل الأنماط لـ {asset}: {len(detected_patterns)} نمط، الإشارة: {overall_assessment['signal'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الشموع لـ {asset}: {str(e)}")
            return None

    async def _analyze_single_candle_patterns(self, candles: List[Dict[str, Any]], index: int) -> List[CandlestickPattern]:
        """تحليل أنماط الشمعة الواحدة"""
        try:
            patterns = []
            candle = candles[index]
            
            # حساب خصائص الشمعة
            open_price = candle['open']
            high_price = candle['high']
            low_price = candle['low']
            close_price = candle['close']
            
            body_size = abs(close_price - open_price)
            total_range = high_price - low_price
            upper_shadow = high_price - max(open_price, close_price)
            lower_shadow = min(open_price, close_price) - low_price
            
            if total_range == 0:
                return patterns
            
            body_ratio = body_size / total_range
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range
            
            # 1. تحليل Doji
            doji_pattern = await self._detect_doji_pattern(candle, index, body_ratio)
            if doji_pattern:
                patterns.append(doji_pattern)
            
            # 2. تحليل Pin Bar
            pin_bar_pattern = await self._detect_pin_bar_pattern(candle, index, body_ratio, upper_shadow_ratio, lower_shadow_ratio)
            if pin_bar_pattern:
                patterns.append(pin_bar_pattern)
            
            # 3. تحليل Hammer
            hammer_pattern = await self._detect_hammer_pattern(candle, index, body_ratio, lower_shadow_ratio, upper_shadow_ratio)
            if hammer_pattern:
                patterns.append(hammer_pattern)
            
            # 4. تحليل Shooting Star
            shooting_star_pattern = await self._detect_shooting_star_pattern(candle, index, body_ratio, upper_shadow_ratio, lower_shadow_ratio)
            if shooting_star_pattern:
                patterns.append(shooting_star_pattern)
            
            # 5. تحليل Marubozu
            marubozu_pattern = await self._detect_marubozu_pattern(candle, index, body_ratio, upper_shadow_ratio, lower_shadow_ratio)
            if marubozu_pattern:
                patterns.append(marubozu_pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الشمعة الواحدة: {str(e)}")
            return []

    async def _detect_doji_pattern(self, candle: Dict[str, Any], index: int, body_ratio: float) -> Optional[CandlestickPattern]:
        """كشف نمط الدوجي"""
        try:
            # شروط الدوجي: جسم صغير جداً (أقل من 5% من المدى الكلي)
            if body_ratio <= 0.05:

                open_price = candle['open']
                close_price = candle['close']

                # تحديد قوة النمط
                pattern_strength = 1.0 - body_ratio  # كلما قل الجسم، زادت القوة

                # تحديد الإشارة (الدوجي عادة محايد)
                signal = PatternSignal.NEUTRAL

                # حساب الثقة
                confidence = min(95, 70 + (pattern_strength * 25))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.DOJI,
                    signal=signal,
                    reliability=PatternReliability.HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle],
                    body_size_ratio=body_ratio,
                    shadow_ratio=(candle['high'] - candle['low']) / abs(close_price - open_price) if abs(close_price - open_price) > 0 else 0,
                    volume_confirmation=self._check_volume_confirmation([candle]),
                    description="نمط الدوجي - تردد في السوق",
                    reasoning=f"جسم صغير جداً ({body_ratio:.3f}) يشير لتردد المتداولين",
                    supporting_data={
                        'body_ratio': body_ratio,
                        'open': open_price,
                        'close': close_price
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط الدوجي: {str(e)}")
            return None

    async def _detect_pin_bar_pattern(self, candle: Dict[str, Any], index: int, body_ratio: float, upper_shadow_ratio: float, lower_shadow_ratio: float) -> Optional[CandlestickPattern]:
        """كشف نمط Pin Bar"""
        try:
            # شروط Pin Bar: جسم صغير وظل طويل في اتجاه واحد
            if body_ratio <= 0.3:  # جسم صغير

                # Pin Bar صاعد (ظل سفلي طويل)
                if lower_shadow_ratio >= 0.6 and upper_shadow_ratio <= 0.2:
                    signal = PatternSignal.BULLISH
                    pattern_strength = lower_shadow_ratio + (1 - body_ratio)
                    description = "Pin Bar صاعد - رفض للأسعار المنخفضة"
                    reasoning = f"ظل سفلي طويل ({lower_shadow_ratio:.2f}) مع جسم صغير يشير لضغط شرائي"

                # Pin Bar هابط (ظل علوي طويل)
                elif upper_shadow_ratio >= 0.6 and lower_shadow_ratio <= 0.2:
                    signal = PatternSignal.BEARISH
                    pattern_strength = upper_shadow_ratio + (1 - body_ratio)
                    description = "Pin Bar هابط - رفض للأسعار المرتفعة"
                    reasoning = f"ظل علوي طويل ({upper_shadow_ratio:.2f}) مع جسم صغير يشير لضغط بيعي"

                else:
                    return None

                confidence = min(95, 60 + (pattern_strength * 30))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.PIN_BAR,
                    signal=signal,
                    reliability=PatternReliability.HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle],
                    body_size_ratio=body_ratio,
                    shadow_ratio=max(upper_shadow_ratio, lower_shadow_ratio),
                    volume_confirmation=self._check_volume_confirmation([candle]),
                    description=description,
                    reasoning=reasoning,
                    supporting_data={
                        'upper_shadow_ratio': upper_shadow_ratio,
                        'lower_shadow_ratio': lower_shadow_ratio,
                        'body_ratio': body_ratio
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط Pin Bar: {str(e)}")
            return None

    async def _detect_hammer_pattern(self, candle: Dict[str, Any], index: int, body_ratio: float, lower_shadow_ratio: float, upper_shadow_ratio: float) -> Optional[CandlestickPattern]:
        """كشف نمط المطرقة"""
        try:
            # شروط المطرقة: جسم صغير، ظل سفلي طويل، ظل علوي قصير أو معدوم
            if (body_ratio >= 0.1 and body_ratio <= 0.3 and  # جسم صغير لكن ليس صغير جداً
                lower_shadow_ratio >= 0.5 and  # ظل سفلي طويل
                upper_shadow_ratio <= 0.1):  # ظل علوي قصير

                pattern_strength = lower_shadow_ratio + (1 - body_ratio) + (1 - upper_shadow_ratio)
                confidence = min(90, 65 + (pattern_strength * 20))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.HAMMER,
                    signal=PatternSignal.BULLISH,
                    reliability=PatternReliability.HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle],
                    body_size_ratio=body_ratio,
                    shadow_ratio=lower_shadow_ratio,
                    volume_confirmation=self._check_volume_confirmation([candle]),
                    description="نمط المطرقة - إشارة انعكاس صاعدة",
                    reasoning=f"ظل سفلي طويل ({lower_shadow_ratio:.2f}) مع جسم صغير يشير لرفض الأسعار المنخفضة",
                    supporting_data={
                        'lower_shadow_ratio': lower_shadow_ratio,
                        'upper_shadow_ratio': upper_shadow_ratio,
                        'body_ratio': body_ratio
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط المطرقة: {str(e)}")
            return None

    async def _detect_shooting_star_pattern(self, candle: Dict[str, Any], index: int, body_ratio: float, upper_shadow_ratio: float, lower_shadow_ratio: float) -> Optional[CandlestickPattern]:
        """كشف نمط النجم الساقط"""
        try:
            # شروط النجم الساقط: جسم صغير، ظل علوي طويل، ظل سفلي قصير أو معدوم
            if (body_ratio >= 0.1 and body_ratio <= 0.3 and  # جسم صغير
                upper_shadow_ratio >= 0.5 and  # ظل علوي طويل
                lower_shadow_ratio <= 0.1):  # ظل سفلي قصير

                pattern_strength = upper_shadow_ratio + (1 - body_ratio) + (1 - lower_shadow_ratio)
                confidence = min(90, 65 + (pattern_strength * 20))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.SHOOTING_STAR,
                    signal=PatternSignal.BEARISH,
                    reliability=PatternReliability.HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle],
                    body_size_ratio=body_ratio,
                    shadow_ratio=upper_shadow_ratio,
                    volume_confirmation=self._check_volume_confirmation([candle]),
                    description="نمط النجم الساقط - إشارة انعكاس هابطة",
                    reasoning=f"ظل علوي طويل ({upper_shadow_ratio:.2f}) مع جسم صغير يشير لرفض الأسعار المرتفعة",
                    supporting_data={
                        'upper_shadow_ratio': upper_shadow_ratio,
                        'lower_shadow_ratio': lower_shadow_ratio,
                        'body_ratio': body_ratio
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط النجم الساقط: {str(e)}")
            return None

    async def _detect_marubozu_pattern(self, candle: Dict[str, Any], index: int, body_ratio: float, upper_shadow_ratio: float, lower_shadow_ratio: float) -> Optional[CandlestickPattern]:
        """كشف نمط الماروبوزو"""
        try:
            # شروط الماروبوزو: جسم كبير، ظلال قصيرة أو معدومة
            if (body_ratio >= 0.8 and  # جسم كبير
                upper_shadow_ratio <= 0.1 and  # ظل علوي قصير
                lower_shadow_ratio <= 0.1):  # ظل سفلي قصير

                open_price = candle['open']
                close_price = candle['close']

                # تحديد الاتجاه
                if close_price > open_price:
                    signal = PatternSignal.STRONG_BULLISH
                    pattern_type = CandlestickPatternType.BULLISH_MARUBOZU
                    description = "ماروبوزو صاعد - قوة شرائية قوية"
                    reasoning = "جسم كبير بدون ظلال يشير لهيمنة المشترين"
                else:
                    signal = PatternSignal.STRONG_BEARISH
                    pattern_type = CandlestickPatternType.BEARISH_MARUBOZU
                    description = "ماروبوزو هابط - قوة بيعية قوية"
                    reasoning = "جسم كبير بدون ظلال يشير لهيمنة البائعين"

                pattern_strength = body_ratio + (1 - upper_shadow_ratio) + (1 - lower_shadow_ratio)
                confidence = min(95, 70 + (pattern_strength * 20))

                return CandlestickPattern(
                    pattern_type=pattern_type,
                    signal=signal,
                    reliability=PatternReliability.MEDIUM,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle],
                    body_size_ratio=body_ratio,
                    shadow_ratio=max(upper_shadow_ratio, lower_shadow_ratio),
                    volume_confirmation=self._check_volume_confirmation([candle]),
                    description=description,
                    reasoning=reasoning,
                    supporting_data={
                        'body_ratio': body_ratio,
                        'direction': 'bullish' if close_price > open_price else 'bearish'
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط الماروبوزو: {str(e)}")
            return None

    async def _analyze_double_candle_patterns(self, candles: List[Dict[str, Any]], index: int) -> List[CandlestickPattern]:
        """تحليل أنماط الشمعتين"""
        try:
            patterns = []

            if index < 1:
                return patterns

            current_candle = candles[index]
            previous_candle = candles[index - 1]

            # 1. تحليل Engulfing
            engulfing_pattern = await self._detect_engulfing_pattern(previous_candle, current_candle, index)
            if engulfing_pattern:
                patterns.append(engulfing_pattern)

            # 2. تحليل Harami
            harami_pattern = await self._detect_harami_pattern(previous_candle, current_candle, index)
            if harami_pattern:
                patterns.append(harami_pattern)

            return patterns

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الشمعتين: {str(e)}")
            return []

    async def _detect_engulfing_pattern(self, prev_candle: Dict[str, Any], curr_candle: Dict[str, Any], index: int) -> Optional[CandlestickPattern]:
        """كشف نمط الابتلاع"""
        try:
            prev_open = prev_candle['open']
            prev_close = prev_candle['close']
            curr_open = curr_candle['open']
            curr_close = curr_candle['close']

            prev_body = abs(prev_close - prev_open)
            curr_body = abs(curr_close - curr_open)

            # الشمعة الحالية يجب أن تبتلع الشمعة السابقة
            if curr_body > prev_body * 1.2:  # الشمعة الحالية أكبر بـ 20% على الأقل

                # Bullish Engulfing
                if (prev_close < prev_open and  # الشمعة السابقة هابطة
                    curr_close > curr_open and  # الشمعة الحالية صاعدة
                    curr_open < prev_close and  # فتح الحالية أقل من إغلاق السابقة
                    curr_close > prev_open):    # إغلاق الحالية أعلى من فتح السابقة

                    signal = PatternSignal.STRONG_BULLISH
                    pattern_type = CandlestickPatternType.BULLISH_ENGULFING
                    description = "ابتلاع صاعد - انعكاس قوي للاتجاه"
                    reasoning = "شمعة صاعدة كبيرة تبتلع شمعة هابطة سابقة"

                # Bearish Engulfing
                elif (prev_close > prev_open and  # الشمعة السابقة صاعدة
                      curr_close < curr_open and  # الشمعة الحالية هابطة
                      curr_open > prev_close and  # فتح الحالية أعلى من إغلاق السابقة
                      curr_close < prev_open):    # إغلاق الحالية أقل من فتح السابقة

                    signal = PatternSignal.STRONG_BEARISH
                    pattern_type = CandlestickPatternType.BEARISH_ENGULFING
                    description = "ابتلاع هابط - انعكاس قوي للاتجاه"
                    reasoning = "شمعة هابطة كبيرة تبتلع شمعة صاعدة سابقة"

                else:
                    return None

                # حساب قوة النمط
                size_ratio = curr_body / prev_body
                pattern_strength = min(1.0, size_ratio / 2.0)
                confidence = min(95, 75 + (pattern_strength * 20))

                return CandlestickPattern(
                    pattern_type=pattern_type,
                    signal=signal,
                    reliability=PatternReliability.VERY_HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[prev_candle, curr_candle],
                    body_size_ratio=curr_body / (curr_candle['high'] - curr_candle['low']),
                    shadow_ratio=0,  # غير مهم في الابتلاع
                    volume_confirmation=self._check_volume_confirmation([prev_candle, curr_candle]),
                    description=description,
                    reasoning=reasoning,
                    supporting_data={
                        'size_ratio': size_ratio,
                        'prev_body': prev_body,
                        'curr_body': curr_body
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط الابتلاع: {str(e)}")
            return None

    async def _detect_harami_pattern(self, prev_candle: Dict[str, Any], curr_candle: Dict[str, Any], index: int) -> Optional[CandlestickPattern]:
        """كشف نمط الهارامي"""
        try:
            prev_open = prev_candle['open']
            prev_close = prev_candle['close']
            curr_open = curr_candle['open']
            curr_close = curr_candle['close']

            prev_body = abs(prev_close - prev_open)
            curr_body = abs(curr_close - curr_open)

            # الشمعة الحالية يجب أن تكون داخل جسم الشمعة السابقة
            if (curr_body < prev_body * 0.7 and  # الشمعة الحالية أصغر
                min(curr_open, curr_close) > min(prev_open, prev_close) and  # أدنى نقطة في الحالية أعلى من أدنى نقطة في السابقة
                max(curr_open, curr_close) < max(prev_open, prev_close)):    # أعلى نقطة في الحالية أقل من أعلى نقطة في السابقة

                # Bullish Harami
                if (prev_close < prev_open and  # الشمعة السابقة هابطة
                    curr_close > curr_open):    # الشمعة الحالية صاعدة

                    signal = PatternSignal.BULLISH
                    pattern_type = CandlestickPatternType.BULLISH_HARAMI
                    description = "هارامي صاعد - إشارة انعكاس محتملة"
                    reasoning = "شمعة صاعدة صغيرة داخل شمعة هابطة كبيرة"

                # Bearish Harami
                elif (prev_close > prev_open and  # الشمعة السابقة صاعدة
                      curr_close < curr_open):    # الشمعة الحالية هابطة

                    signal = PatternSignal.BEARISH
                    pattern_type = CandlestickPatternType.BEARISH_HARAMI
                    description = "هارامي هابط - إشارة انعكاس محتملة"
                    reasoning = "شمعة هابطة صغيرة داخل شمعة صاعدة كبيرة"

                else:
                    return None

                # حساب قوة النمط
                size_ratio = prev_body / curr_body
                pattern_strength = min(1.0, size_ratio / 3.0)
                confidence = min(85, 60 + (pattern_strength * 25))

                return CandlestickPattern(
                    pattern_type=pattern_type,
                    signal=signal,
                    reliability=PatternReliability.MEDIUM,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[prev_candle, curr_candle],
                    body_size_ratio=curr_body / (curr_candle['high'] - curr_candle['low']),
                    shadow_ratio=0,  # غير مهم في الهارامي
                    volume_confirmation=self._check_volume_confirmation([prev_candle, curr_candle]),
                    description=description,
                    reasoning=reasoning,
                    supporting_data={
                        'size_ratio': size_ratio,
                        'prev_body': prev_body,
                        'curr_body': curr_body
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط الهارامي: {str(e)}")
            return None

    async def _analyze_triple_candle_patterns(self, candles: List[Dict[str, Any]], index: int) -> List[CandlestickPattern]:
        """تحليل أنماط الثلاث شموع"""
        try:
            patterns = []

            if index < 2:
                return patterns

            candle1 = candles[index - 2]  # الشمعة الأولى
            candle2 = candles[index - 1]  # الشمعة الوسطى
            candle3 = candles[index]      # الشمعة الأخيرة

            # 1. تحليل Morning Star
            morning_star = await self._detect_morning_star_pattern(candle1, candle2, candle3, index)
            if morning_star:
                patterns.append(morning_star)

            # 2. تحليل Evening Star
            evening_star = await self._detect_evening_star_pattern(candle1, candle2, candle3, index)
            if evening_star:
                patterns.append(evening_star)

            return patterns

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الثلاث شموع: {str(e)}")
            return []

    async def _detect_morning_star_pattern(self, candle1: Dict[str, Any], candle2: Dict[str, Any], candle3: Dict[str, Any], index: int) -> Optional[CandlestickPattern]:
        """كشف نمط نجمة الصباح"""
        try:
            # شروط نجمة الصباح:
            # 1. شمعة هابطة كبيرة
            # 2. شمعة صغيرة (نجمة) مع فجوة سفلية
            # 3. شمعة صاعدة كبيرة

            body1 = abs(candle1['close'] - candle1['open'])
            body2 = abs(candle2['close'] - candle2['open'])
            body3 = abs(candle3['close'] - candle3['open'])

            range1 = candle1['high'] - candle1['low']
            range2 = candle2['high'] - candle2['low']
            range3 = candle3['high'] - candle3['low']

            # التحقق من الشروط
            if (candle1['close'] < candle1['open'] and  # الشمعة الأولى هابطة
                body1 / range1 >= 0.6 and  # جسم كبير
                body2 / range2 <= 0.3 and  # الشمعة الوسطى صغيرة
                candle3['close'] > candle3['open'] and  # الشمعة الثالثة صاعدة
                body3 / range3 >= 0.6 and  # جسم كبير
                candle2['high'] < candle1['close'] and  # فجوة سفلية
                candle3['close'] > (candle1['open'] + candle1['close']) / 2):  # الإغلاق يخترق منتصف الشمعة الأولى

                # حساب قوة النمط
                gap_size = candle1['close'] - candle2['high']
                penetration = candle3['close'] - (candle1['open'] + candle1['close']) / 2
                pattern_strength = (gap_size + penetration + body1 + body3) / (range1 + range2 + range3)

                confidence = min(95, 70 + (pattern_strength * 25))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.MORNING_STAR,
                    signal=PatternSignal.STRONG_BULLISH,
                    reliability=PatternReliability.VERY_HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle1, candle2, candle3],
                    body_size_ratio=(body1 + body3) / (range1 + range3),
                    shadow_ratio=0,  # غير مهم في النجوم
                    volume_confirmation=self._check_volume_confirmation([candle1, candle2, candle3]),
                    description="نجمة الصباح - انعكاس صاعد قوي",
                    reasoning="نمط ثلاث شموع يشير لانعكاس من هابط لصاعد",
                    supporting_data={
                        'gap_size': gap_size,
                        'penetration': penetration,
                        'pattern_strength': pattern_strength
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط نجمة الصباح: {str(e)}")
            return None

    async def _detect_evening_star_pattern(self, candle1: Dict[str, Any], candle2: Dict[str, Any], candle3: Dict[str, Any], index: int) -> Optional[CandlestickPattern]:
        """كشف نمط نجمة المساء"""
        try:
            # شروط نجمة المساء:
            # 1. شمعة صاعدة كبيرة
            # 2. شمعة صغيرة (نجمة) مع فجوة علوية
            # 3. شمعة هابطة كبيرة

            body1 = abs(candle1['close'] - candle1['open'])
            body2 = abs(candle2['close'] - candle2['open'])
            body3 = abs(candle3['close'] - candle3['open'])

            range1 = candle1['high'] - candle1['low']
            range2 = candle2['high'] - candle2['low']
            range3 = candle3['high'] - candle3['low']

            # التحقق من الشروط
            if (candle1['close'] > candle1['open'] and  # الشمعة الأولى صاعدة
                body1 / range1 >= 0.6 and  # جسم كبير
                body2 / range2 <= 0.3 and  # الشمعة الوسطى صغيرة
                candle3['close'] < candle3['open'] and  # الشمعة الثالثة هابطة
                body3 / range3 >= 0.6 and  # جسم كبير
                candle2['low'] > candle1['close'] and  # فجوة علوية
                candle3['close'] < (candle1['open'] + candle1['close']) / 2):  # الإغلاق يخترق منتصف الشمعة الأولى

                # حساب قوة النمط
                gap_size = candle2['low'] - candle1['close']
                penetration = (candle1['open'] + candle1['close']) / 2 - candle3['close']
                pattern_strength = (gap_size + penetration + body1 + body3) / (range1 + range2 + range3)

                confidence = min(95, 70 + (pattern_strength * 25))

                return CandlestickPattern(
                    pattern_type=CandlestickPatternType.EVENING_STAR,
                    signal=PatternSignal.STRONG_BEARISH,
                    reliability=PatternReliability.VERY_HIGH,
                    confidence=confidence,
                    candle_index=index,
                    pattern_strength=pattern_strength,
                    candles_involved=[candle1, candle2, candle3],
                    body_size_ratio=(body1 + body3) / (range1 + range3),
                    shadow_ratio=0,  # غير مهم في النجوم
                    volume_confirmation=self._check_volume_confirmation([candle1, candle2, candle3]),
                    description="نجمة المساء - انعكاس هابط قوي",
                    reasoning="نمط ثلاث شموع يشير لانعكاس من صاعد لهابط",
                    supporting_data={
                        'gap_size': gap_size,
                        'penetration': penetration,
                        'pattern_strength': pattern_strength
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف نمط نجمة المساء: {str(e)}")
            return None

    def _check_volume_confirmation(self, candles: List[Dict[str, Any]]) -> bool:
        """التحقق من تأكيد الحجم"""
        try:
            if not candles or len(candles) < 2:
                return False

            # فحص إذا كان الحجم متزايد
            volumes = [candle.get('volume', 0) for candle in candles]
            if not any(volumes):  # لا توجد بيانات حجم
                return False

            # التحقق من زيادة الحجم في الشمعة الأخيرة
            if len(volumes) >= 2:
                return volumes[-1] > volumes[-2] * 1.1  # زيادة 10% على الأقل

            return False

        except Exception as e:
            logger.error(f"خطأ في فحص تأكيد الحجم: {str(e)}")
            return False

    def _evaluate_overall_patterns(self, patterns: List[CandlestickPattern]) -> Dict[str, Any]:
        """تقييم الأنماط الإجمالي"""
        try:
            if not patterns:
                return {
                    'signal': PatternSignal.NEUTRAL,
                    'confidence': 0,
                    'reliability': PatternReliability.VERY_LOW,
                    'is_actionable': False,
                    'action': 'WAIT',
                    'risk': 'HIGH'
                }

            # حساب الأوزان المرجحة للإشارات
            bullish_weight = 0
            bearish_weight = 0
            total_weight = 0

            for pattern in patterns:
                weight = self.pattern_weights.get(pattern.pattern_type, 0.5)
                weighted_score = weight * pattern.confidence * pattern.reliability.value
                total_weight += weight

                if pattern.signal in [PatternSignal.BULLISH, PatternSignal.STRONG_BULLISH]:
                    bullish_weight += weighted_score
                elif pattern.signal in [PatternSignal.BEARISH, PatternSignal.STRONG_BEARISH]:
                    bearish_weight += weighted_score

            # تحديد الإشارة الإجمالية
            if bullish_weight > bearish_weight * 1.3:
                overall_signal = PatternSignal.BULLISH
                confidence_ratio = bullish_weight / (bullish_weight + bearish_weight)
            elif bearish_weight > bullish_weight * 1.3:
                overall_signal = PatternSignal.BEARISH
                confidence_ratio = bearish_weight / (bullish_weight + bearish_weight)
            else:
                overall_signal = PatternSignal.NEUTRAL
                confidence_ratio = 0.5

            # حساب الثقة الإجمالية
            overall_confidence = confidence_ratio * 100

            # تحديد الموثوقية الإجمالية
            high_reliability_count = len([p for p in patterns if p.reliability.value >= 4])
            if high_reliability_count >= 2:
                overall_reliability = PatternReliability.VERY_HIGH
            elif high_reliability_count >= 1:
                overall_reliability = PatternReliability.HIGH
            else:
                overall_reliability = PatternReliability.MEDIUM

            # تحديد قابلية التنفيذ
            is_actionable = (
                overall_confidence >= self.min_confidence_threshold and
                overall_reliability.value >= 3 and
                len(patterns) >= 1 and
                overall_signal != PatternSignal.NEUTRAL
            )

            # تحديد الإجراء المطلوب
            if is_actionable:
                if overall_signal in [PatternSignal.BULLISH, PatternSignal.STRONG_BULLISH]:
                    action = 'BUY'
                else:
                    action = 'SELL'
            else:
                action = 'WAIT'

            # تحديد مستوى المخاطر
            if overall_confidence >= 85 and overall_reliability.value >= 4:
                risk = 'LOW'
            elif overall_confidence >= 70 and overall_reliability.value >= 3:
                risk = 'MEDIUM'
            else:
                risk = 'HIGH'

            return {
                'signal': overall_signal,
                'confidence': round(overall_confidence, 2),
                'reliability': overall_reliability,
                'is_actionable': is_actionable,
                'action': action,
                'risk': risk
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم الأنماط الإجمالي: {str(e)}")
            return {
                'signal': PatternSignal.NEUTRAL,
                'confidence': 0,
                'reliability': PatternReliability.VERY_LOW,
                'is_actionable': False,
                'action': 'WAIT',
                'risk': 'HIGH'
            }

    def _calculate_pattern_statistics(self, patterns: List[CandlestickPattern]) -> Dict[str, int]:
        """حساب إحصائيات الأنماط"""
        try:
            total = len(patterns)
            bullish = len([p for p in patterns if p.signal in [PatternSignal.BULLISH, PatternSignal.STRONG_BULLISH]])
            bearish = len([p for p in patterns if p.signal in [PatternSignal.BEARISH, PatternSignal.STRONG_BEARISH]])
            high_reliability = len([p for p in patterns if p.reliability.value >= 4])

            return {
                'total': total,
                'bullish': bullish,
                'bearish': bearish,
                'high_reliability': high_reliability
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الأنماط: {str(e)}")
            return {'total': 0, 'bullish': 0, 'bearish': 0, 'high_reliability': 0}

    def _generate_pattern_recommendations(self, patterns: List[CandlestickPattern], overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات الأنماط"""
        try:
            recommendations = []

            if not patterns:
                recommendations.append("لا توجد أنماط شموع واضحة")
                return recommendations

            # توصيات بناء على عدد الأنماط
            if len(patterns) >= 3:
                recommendations.append("أنماط متعددة تؤكد الإشارة")
            elif len(patterns) == 1:
                recommendations.append("نمط واحد فقط - انتظار المزيد من التأكيد")

            # توصيات بناء على الموثوقية
            high_reliability_patterns = [p for p in patterns if p.reliability.value >= 4]
            if len(high_reliability_patterns) >= 2:
                recommendations.append("أنماط عالية الموثوقية - إشارة قوية")
            elif not high_reliability_patterns:
                recommendations.append("أنماط منخفضة الموثوقية - توخي الحذر")

            # توصيات بناء على التأكيد
            volume_confirmed = [p for p in patterns if p.volume_confirmation]
            if len(volume_confirmed) >= 2:
                recommendations.append("تأكيد قوي من الحجم")
            elif not volume_confirmed:
                recommendations.append("نقص في تأكيد الحجم")

            # توصيات بناء على نوع النمط
            engulfing_patterns = [p for p in patterns if 'ENGULFING' in p.pattern_type.value]
            star_patterns = [p for p in patterns if 'STAR' in p.pattern_type.value]

            if engulfing_patterns:
                recommendations.append("أنماط ابتلاع قوية - انعكاس محتمل")
            if star_patterns:
                recommendations.append("أنماط نجوم - إشارات انعكاس متقدمة")

            # توصيات بناء على المخاطر
            if overall_assessment['risk'] == 'HIGH':
                recommendations.append("مستوى مخاطر عالي - تقليل حجم الصفقة")
            elif overall_assessment['risk'] == 'LOW':
                recommendations.append("مستوى مخاطر منخفض - يمكن زيادة حجم الصفقة")

            return recommendations[:5]  # أقصى 5 توصيات

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات الأنماط: {str(e)}")
            return []

    def get_pattern_summary(self, analysis: PatternAnalysisResult) -> Dict[str, Any]:
        """الحصول على ملخص تحليل الأنماط"""
        if not analysis:
            return {}

        return {
            'asset': analysis.asset,
            'total_patterns': analysis.total_patterns,
            'overall_signal': analysis.overall_signal.value,
            'overall_confidence': analysis.overall_confidence,
            'overall_reliability': analysis.overall_reliability.value,
            'is_actionable': analysis.is_actionable,
            'recommended_action': analysis.recommended_action,
            'risk_assessment': analysis.risk_assessment,
            'processing_time_ms': analysis.processing_time_ms,
            'key_patterns': [
                {
                    'type': pattern.pattern_type.value,
                    'signal': pattern.signal.value,
                    'confidence': pattern.confidence,
                    'reliability': pattern.reliability.value,
                    'description': pattern.description
                }
                for pattern in analysis.detected_patterns[:3]  # أهم 3 أنماط
            ],
            'top_recommendations': analysis.recommendations[:3]
        }

    def is_pattern_actionable(self, analysis: PatternAnalysisResult, min_confidence: float = None) -> bool:
        """تحديد ما إذا كانت الأنماط قابلة للتنفيذ"""
        if not analysis:
            return False

        min_conf = min_confidence or self.min_confidence_threshold

        return (
            analysis.is_actionable and
            analysis.overall_confidence >= min_conf and
            analysis.overall_reliability.value >= 3 and
            analysis.total_patterns >= 1 and
            analysis.overall_signal != PatternSignal.NEUTRAL
        )

    def get_pattern_history(self, asset: str, limit: int = 10) -> List[PatternAnalysisResult]:
        """الحصول على تاريخ الأنماط لأصل معين"""
        if asset not in self.pattern_history:
            return []

        return self.pattern_history[asset][-limit:]

    def get_pattern_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات عامة للأنماط"""
        try:
            total_analyses = sum(len(history) for history in self.pattern_history.values())
            total_assets = len(self.pattern_history)

            if total_analyses == 0:
                return {
                    'total_analyses': 0,
                    'total_assets': 0,
                    'avg_patterns_per_analysis': 0,
                    'most_active_asset': None
                }

            # حساب متوسط الأنماط لكل تحليل
            total_patterns = sum(
                analysis.total_patterns
                for history in self.pattern_history.values()
                for analysis in history
            )
            avg_patterns = total_patterns / total_analyses

            # العثور على الأصل الأكثر نشاطاً
            most_active_asset = max(
                self.pattern_history.keys(),
                key=lambda asset: len(self.pattern_history[asset])
            ) if self.pattern_history else None

            return {
                'total_analyses': total_analyses,
                'total_assets': total_assets,
                'avg_patterns_per_analysis': round(avg_patterns, 2),
                'most_active_asset': most_active_asset,
                'supported_pattern_types': len(self.pattern_weights),
                'min_confidence_threshold': self.min_confidence_threshold
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الأنماط: {str(e)}")
            return {}

# إنشاء instance عام للاستخدام
candlestick_pattern_analyzer = CandlestickPatternAnalyzer()
