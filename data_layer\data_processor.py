"""
معالج البيانات لنظام السكالبينغ
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, DataError
from utils.helpers import validate_candle_data, get_current_timestamp

logger = scalping_logger.get_logger("data_processor")

@dataclass
class DataQualityReport:
    """تقرير جودة البيانات"""
    total_records: int
    valid_records: int
    invalid_records: int
    missing_values: int
    duplicate_records: int
    outliers: int
    quality_score: float
    issues: List[str]

class DataProcessor:
    """معالج البيانات المتقدم"""
    
    def __init__(self):
        self.outlier_threshold = 3.0  # Z-score threshold
        self.max_price_change = 0.1   # 10% maximum price change between candles
        
    @handle_errors(default_return=[], log_error=True)
    def process_candle_data(self, raw_candles: List[Dict[str, Any]], 
                           asset: str, timeframe: int) -> List[Dict[str, Any]]:
        """معالجة بيانات الشموع الخام"""
        try:
            if not raw_candles:
                return []
            
            logger.debug(f"بدء معالجة {len(raw_candles)} شمعة لـ {asset}")
            
            # تحويل إلى DataFrame للمعالجة
            df = pd.DataFrame(raw_candles)
            
            # التنظيف الأساسي
            df = self._basic_cleaning(df)
            
            # إضافة timestamps إذا لم تكن موجودة
            df = self._add_timestamps(df, timeframe)
            
            # إزالة التكرارات
            df = self._remove_duplicates(df)
            
            # ملء القيم المفقودة
            df = self._fill_missing_values(df)
            
            # إزالة القيم الشاذة
            df = self._remove_outliers(df)
            
            # التحقق من صحة البيانات
            df = self._validate_candle_logic(df)
            
            # ترتيب حسب الوقت
            if 'timestamp' in df.columns:
                df = df.sort_values('timestamp')
            
            # تحويل إلى قائمة من القواميس
            processed_candles = df.to_dict('records')
            
            logger.debug(f"تم معالجة {len(processed_candles)} شمعة صالحة من أصل {len(raw_candles)}")
            
            return processed_candles
            
        except Exception as e:
            logger.error(f"خطأ في معالجة بيانات الشموع لـ {asset}: {str(e)}")
            return []
    
    def _basic_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """التنظيف الأساسي للبيانات"""
        # تحويل الأعمدة المطلوبة إلى أرقام
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # إزالة الصفوف التي تحتوي على قيم غير صالحة في الأسعار
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                df = df[df[col] > 0]  # الأسعار يجب أن تكون موجبة
        
        return df
    
    def _add_timestamps(self, df: pd.DataFrame, timeframe: int) -> pd.DataFrame:
        """إضافة timestamps إذا لم تكن موجودة"""
        if 'timestamp' not in df.columns:
            # إنشاء timestamps بناءً على الفهرس والإطار الزمني
            current_time = get_current_timestamp()
            timestamps = []
            
            for i in range(len(df)):
                timestamp = current_time - timedelta(seconds=timeframe * (len(df) - i - 1))
                timestamps.append(timestamp)
            
            df['timestamp'] = timestamps
        else:
            # تحويل إلى datetime إذا لم يكن كذلك
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        return df
    
    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """إزالة التكرارات"""
        initial_count = len(df)
        
        # إزالة التكرارات بناءً على timestamp
        if 'timestamp' in df.columns:
            df = df.drop_duplicates(subset=['timestamp'], keep='last')
        
        # إزالة التكرارات الكاملة
        df = df.drop_duplicates()
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.debug(f"تم إزالة {removed_count} سجل مكرر")
        
        return df
    
    def _fill_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """ملء القيم المفقودة"""
        # ملء القيم المفقودة في الأسعار بالاستيفاء الخطي
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            if col in df.columns:
                # استخدام الاستيفاء الخطي للقيم المفقودة
                df[col] = df[col].interpolate(method='linear')
                
                # ملء القيم المفقودة في البداية والنهاية
                df[col] = df[col].fillna(method='bfill').fillna(method='ffill')
        
        # ملء الحجم بالصفر إذا كان مفقود
        if 'volume' in df.columns:
            df['volume'] = df['volume'].fillna(0)
        
        return df
    
    def _remove_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """إزالة القيم الشاذة"""
        if len(df) < 10:  # لا نحتاج لإزالة القيم الشاذة مع البيانات القليلة
            return df
        
        initial_count = len(df)
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            if col in df.columns:
                # حساب Z-score
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                
                # إزالة القيم التي تتجاوز العتبة
                df = df[z_scores <= self.outlier_threshold]
        
        # إزالة الشموع مع تغييرات سعرية غير منطقية
        if 'close' in df.columns and len(df) > 1:
            df = df.reset_index(drop=True)
            price_changes = df['close'].pct_change().abs()
            df = df[price_changes <= self.max_price_change]
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.debug(f"تم إزالة {removed_count} قيمة شاذة")
        
        return df
    
    def _validate_candle_logic(self, df: pd.DataFrame) -> pd.DataFrame:
        """التحقق من منطقية بيانات الشموع"""
        price_columns = ['open', 'high', 'low', 'close']
        
        # التحقق من وجود جميع الأعمدة المطلوبة
        if not all(col in df.columns for col in price_columns):
            return df
        
        initial_count = len(df)
        
        # التحقق من أن high >= max(open, close) و low <= min(open, close)
        valid_high = df['high'] >= df[['open', 'close']].max(axis=1)
        valid_low = df['low'] <= df[['open', 'close']].min(axis=1)
        
        # الاحتفاظ بالشموع الصالحة فقط
        df = df[valid_high & valid_low]
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.debug(f"تم إزالة {removed_count} شمعة غير منطقية")
        
        return df
    
    @handle_errors(default_return=DataQualityReport(0, 0, 0, 0, 0, 0, 0, []), log_error=True)
    def assess_data_quality(self, candles: List[Dict[str, Any]]) -> DataQualityReport:
        """تقييم جودة البيانات"""
        try:
            if not candles:
                return DataQualityReport(0, 0, 0, 0, 0, 0, 0, ["لا توجد بيانات"])
            
            df = pd.DataFrame(candles)
            total_records = len(df)
            issues = []
            
            # عد القيم المفقودة
            missing_values = df.isnull().sum().sum()
            if missing_values > 0:
                issues.append(f"{missing_values} قيمة مفقودة")
            
            # عد التكرارات
            if 'timestamp' in df.columns:
                duplicate_records = df.duplicated(subset=['timestamp']).sum()
            else:
                duplicate_records = df.duplicated().sum()
            
            if duplicate_records > 0:
                issues.append(f"{duplicate_records} سجل مكرر")
            
            # عد القيم الشاذة
            outliers = 0
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns and len(df) > 10:
                    z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                    outliers += (z_scores > self.outlier_threshold).sum()
            
            if outliers > 0:
                issues.append(f"{outliers} قيمة شاذة")
            
            # التحقق من منطقية الشموع
            invalid_candles = 0
            if all(col in df.columns for col in price_columns):
                valid_high = df['high'] >= df[['open', 'close']].max(axis=1)
                valid_low = df['low'] <= df[['open', 'close']].min(axis=1)
                invalid_candles = (~(valid_high & valid_low)).sum()
            
            if invalid_candles > 0:
                issues.append(f"{invalid_candles} شمعة غير منطقية")
            
            # حساب السجلات الصالحة
            valid_records = total_records - missing_values - duplicate_records - outliers - invalid_candles
            invalid_records = total_records - valid_records
            
            # حساب نقاط الجودة
            quality_score = (valid_records / total_records) * 100 if total_records > 0 else 0
            
            return DataQualityReport(
                total_records=total_records,
                valid_records=max(0, valid_records),
                invalid_records=invalid_records,
                missing_values=missing_values,
                duplicate_records=duplicate_records,
                outliers=outliers,
                quality_score=round(quality_score, 2),
                issues=issues
            )
            
        except Exception as e:
            logger.error(f"خطأ في تقييم جودة البيانات: {str(e)}")
            return DataQualityReport(0, 0, 0, 0, 0, 0, 0, [f"خطأ في التقييم: {str(e)}"])
    
    @handle_errors(default_return=[], log_error=True)
    def resample_data(self, candles: List[Dict[str, Any]], 
                     from_timeframe: int, to_timeframe: int) -> List[Dict[str, Any]]:
        """إعادة تجميع البيانات لإطار زمني مختلف"""
        try:
            if not candles or to_timeframe <= from_timeframe:
                return candles
            
            df = pd.DataFrame(candles)
            
            if 'timestamp' not in df.columns:
                logger.error("لا يمكن إعادة التجميع بدون timestamps")
                return []
            
            # تحويل timestamp إلى index
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # تحديد فترة إعادة التجميع
            resample_period = f"{to_timeframe}S"
            
            # إعادة التجميع
            resampled = df.resample(resample_period).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            # تحويل إلى قائمة من القواميس
            resampled.reset_index(inplace=True)
            result = resampled.to_dict('records')
            
            logger.debug(f"تم إعادة تجميع {len(candles)} شمعة إلى {len(result)} شمعة")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في إعادة تجميع البيانات: {str(e)}")
            return []
    
    @handle_errors(default_return=[], log_error=True)
    def calculate_derived_features(self, candles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """حساب الميزات المشتقة"""
        try:
            if not candles:
                return []
            
            df = pd.DataFrame(candles)
            price_columns = ['open', 'high', 'low', 'close']
            
            if not all(col in df.columns for col in price_columns):
                return candles
            
            # حساب الميزات المشتقة
            
            # 1. نطاق الشمعة
            df['range'] = df['high'] - df['low']
            df['range_percentage'] = (df['range'] / df['close']) * 100
            
            # 2. جسم الشمعة
            df['body'] = abs(df['close'] - df['open'])
            df['body_percentage'] = (df['body'] / df['range']) * 100
            
            # 3. الظلال
            df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
            df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
            
            # 4. نوع الشمعة
            df['candle_type'] = df.apply(lambda row: 
                'bullish' if row['close'] > row['open'] else 
                'bearish' if row['close'] < row['open'] else 'doji', axis=1)
            
            # 5. التغيير السعري
            df['price_change'] = df['close'] - df['open']
            df['price_change_percentage'] = (df['price_change'] / df['open']) * 100
            
            # 6. متوسط السعر
            df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
            df['weighted_price'] = (df['high'] + df['low'] + df['close'] * 2) / 4
            
            # تحويل إلى قائمة من القواميس
            result = df.to_dict('records')
            
            logger.debug(f"تم حساب الميزات المشتقة لـ {len(result)} شمعة")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في حساب الميزات المشتقة: {str(e)}")
            return candles
    
    @handle_errors(default_return={}, log_error=True)
    def get_data_statistics(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """الحصول على إحصائيات البيانات"""
        try:
            if not candles:
                return {}
            
            df = pd.DataFrame(candles)
            
            stats = {
                'count': len(df),
                'time_range': {},
                'price_statistics': {},
                'volume_statistics': {},
                'data_quality': {}
            }
            
            # إحصائيات الوقت
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                stats['time_range'] = {
                    'start': df['timestamp'].min().isoformat(),
                    'end': df['timestamp'].max().isoformat(),
                    'duration_hours': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 3600
                }
            
            # إحصائيات الأسعار
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in df.columns:
                    stats['price_statistics'][col] = {
                        'min': float(df[col].min()),
                        'max': float(df[col].max()),
                        'mean': float(df[col].mean()),
                        'std': float(df[col].std())
                    }
            
            # إحصائيات الحجم
            if 'volume' in df.columns:
                stats['volume_statistics'] = {
                    'total': float(df['volume'].sum()),
                    'average': float(df['volume'].mean()),
                    'max': float(df['volume'].max())
                }
            
            # جودة البيانات
            quality_report = self.assess_data_quality(candles)
            stats['data_quality'] = {
                'quality_score': quality_report.quality_score,
                'valid_records': quality_report.valid_records,
                'issues_count': len(quality_report.issues)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات البيانات: {str(e)}")
            return {}

# إنشاء instance عام
data_processor = DataProcessor()
