"""
مدير نظام FIFO للبيانات - إدارة حد 10,080 شمعة لكل زوج
"""

import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict

from config.currency_pairs import CURRENCY_PAIRS_70
from database.repository import historical_data_repo
from database.models import HistoricalData
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, DataError

logger = scalping_logger.get_logger("fifo_manager")

@dataclass
class FIFOStats:
    """إحصائيات نظام FIFO لزوج واحد"""
    asset: str
    current_count: int = 0
    max_limit: int = 10080
    deleted_count: int = 0
    added_count: int = 0
    last_cleanup: Optional[datetime] = None
    oldest_timestamp: Optional[datetime] = None
    newest_timestamp: Optional[datetime] = None
    
    @property
    def is_at_limit(self) -> bool:
        """هل وصل الزوج للحد الأقصى"""
        return self.current_count >= self.max_limit
    
    @property
    def available_space(self) -> int:
        """المساحة المتاحة"""
        return max(0, self.max_limit - self.current_count)
    
    @property
    def usage_percentage(self) -> float:
        """نسبة الاستخدام"""
        return (self.current_count / self.max_limit) * 100

class FIFODataManager:
    """مدير نظام FIFO للبيانات التاريخية"""
    
    def __init__(self, max_candles_per_asset: int = 10080, timeframe: int = 60):
        self.max_candles_per_asset = max_candles_per_asset
        self.timeframe = timeframe
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        
        # إحصائيات لكل زوج
        self.fifo_stats: Dict[str, FIFOStats] = {}
        
        # إحصائيات النظام العامة
        self.system_stats = {
            'total_cleanups': 0,
            'total_deleted': 0,
            'total_added': 0,
            'last_full_scan': None,
            'errors': 0,
            'start_time': datetime.now()
        }
        
        # إعدادات التشغيل
        self.auto_cleanup_enabled = True
        self.cleanup_interval = 300  # 5 دقائق
        self.batch_size = 1000  # حجم دفعة الحذف
        
        # تهيئة إحصائيات الأزواج
        self._initialize_stats()
        
        logger.info(f"تم تهيئة مدير FIFO للـ{len(self.currency_pairs)} زوج عملة")
        logger.info(f"الحد الأقصى: {self.max_candles_per_asset} شمعة لكل زوج")

    def _initialize_stats(self):
        """تهيئة إحصائيات الأزواج"""
        for asset in self.currency_pairs:
            self.fifo_stats[asset] = FIFOStats(
                asset=asset,
                max_limit=self.max_candles_per_asset
            )

    def initialize_all_pairs(self):
        """تهيئة جميع الأزواج (للتوافق مع النظام الرئيسي)"""
        try:
            logger.info(f"تهيئة نظام FIFO لجميع الأزواج الـ{len(self.currency_pairs)}")

            # تحديث إحصائيات جميع الأزواج
            for asset in self.currency_pairs:
                self.update_asset_stats(asset)

            # بدء التنظيف التلقائي
            if self.auto_cleanup_enabled:
                asyncio.create_task(self.start_auto_cleanup())

            logger.info("✅ تم تهيئة نظام FIFO بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تهيئة جميع الأزواج: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def update_asset_stats(self, asset: str) -> bool:
        """تحديث إحصائيات زوج معين"""
        try:
            if asset not in self.fifo_stats:
                self.fifo_stats[asset] = FIFOStats(
                    asset=asset,
                    max_limit=self.max_candles_per_asset
                )
            
            # عد الشموع الحالية
            current_count = historical_data_repo.count(
                asset=asset,
                timeframe=self.timeframe
            )
            
            # الحصول على أقدم وأحدث timestamp
            oldest = historical_data_repo.get_oldest_timestamp(asset, self.timeframe)
            newest = historical_data_repo.get_latest_timestamp(asset, self.timeframe)
            
            # تحديث الإحصائيات
            stats = self.fifo_stats[asset]
            stats.current_count = current_count
            stats.oldest_timestamp = oldest
            stats.newest_timestamp = newest
            
            logger.debug(f"تحديث إحصائيات {asset}: {current_count}/{self.max_candles_per_asset} شمعة")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات {asset}: {str(e)}")
            self.system_stats['errors'] += 1
            return False

    @handle_errors(default_return=0, log_error=True)
    def cleanup_asset_data(self, asset: str, force: bool = False) -> int:
        """تنظيف بيانات زوج معين وإرجاع عدد الشموع المحذوفة"""
        try:
            # تحديث الإحصائيات أولاً
            if not self.update_asset_stats(asset):
                return 0
            
            stats = self.fifo_stats[asset]
            
            # التحقق من الحاجة للتنظيف
            if not force and not stats.is_at_limit:
                logger.debug(f"{asset}: لا يحتاج تنظيف ({stats.current_count}/{stats.max_limit})")
                return 0
            
            # حساب عدد الشموع المطلوب حذفها
            excess_count = stats.current_count - self.max_candles_per_asset
            if excess_count <= 0 and not force:
                return 0
            
            # إضافة هامش أمان (حذف 5% إضافية لتجنب التنظيف المتكرر)
            safety_margin = int(self.max_candles_per_asset * 0.05)
            delete_count = max(excess_count, safety_margin) if not force else excess_count
            
            logger.info(f"🧹 تنظيف {asset}: حذف {delete_count} شمعة قديمة")
            
            # تنفيذ الحذف
            deleted_count = self._delete_oldest_candles(asset, delete_count)
            
            if deleted_count > 0:
                # تحديث الإحصائيات
                stats.deleted_count += deleted_count
                stats.last_cleanup = datetime.now()
                stats.current_count -= deleted_count
                
                self.system_stats['total_deleted'] += deleted_count
                self.system_stats['total_cleanups'] += 1
                
                logger.info(f"✅ تم حذف {deleted_count} شمعة من {asset}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف {asset}: {str(e)}")
            self.system_stats['errors'] += 1
            return 0

    @handle_errors(default_return=0, log_error=True)
    def _delete_oldest_candles(self, asset: str, count: int) -> int:
        """حذف أقدم الشموع لزوج معين"""
        try:
            # استخدام النظام المحسن في repository
            success = historical_data_repo.maintain_fifo_limit(
                asset=asset,
                max_candles=self.max_candles_per_asset - count,
                timeframe=self.timeframe
            )
            
            if success:
                # إعادة حساب العدد الفعلي المحذوف
                new_count = historical_data_repo.count(
                    asset=asset,
                    timeframe=self.timeframe
                )
                
                old_count = self.fifo_stats[asset].current_count
                actual_deleted = old_count - new_count
                
                return max(0, actual_deleted)
            
            return 0
            
        except Exception as e:
            logger.error(f"خطأ في حذف الشموع القديمة لـ {asset}: {str(e)}")
            return 0

    @handle_errors(default_return={}, log_error=True)
    def cleanup_all_assets(self, force: bool = False) -> Dict[str, int]:
        """تنظيف جميع الأزواج وإرجاع تقرير بعدد الشموع المحذوفة"""
        try:
            logger.info(f"🧹 بدء تنظيف شامل لـ {len(self.currency_pairs)} زوج")
            
            cleanup_results = {}
            total_deleted = 0
            
            for asset in self.currency_pairs:
                try:
                    deleted_count = self.cleanup_asset_data(asset, force)
                    cleanup_results[asset] = deleted_count
                    total_deleted += deleted_count
                    
                    # تأخير قصير لتجنب إرهاق قاعدة البيانات
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"خطأ في تنظيف {asset}: {str(e)}")
                    cleanup_results[asset] = 0
            
            # تحديث وقت آخر فحص شامل
            self.system_stats['last_full_scan'] = datetime.now()
            
            logger.info(f"✅ انتهى التنظيف الشامل: حذف {total_deleted} شمعة إجمالية")
            
            return cleanup_results
            
        except Exception as e:
            logger.error(f"خطأ في التنظيف الشامل: {str(e)}")
            return {}

    @handle_async_errors(default_return=False, log_error=True)
    async def start_auto_cleanup(self) -> bool:
        """بدء التنظيف التلقائي"""
        try:
            if not self.auto_cleanup_enabled:
                logger.warning("التنظيف التلقائي معطل")
                return False
            
            logger.info(f"🔄 بدء التنظيف التلقائي كل {self.cleanup_interval} ثانية")
            
            # بدء حلقة التنظيف التلقائي
            asyncio.create_task(self._auto_cleanup_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء التنظيف التلقائي: {str(e)}")
            return False

    async def _auto_cleanup_loop(self):
        """حلقة التنظيف التلقائي"""
        try:
            while self.auto_cleanup_enabled:
                try:
                    # تنظيف الأزواج التي تحتاج تنظيف فقط
                    assets_needing_cleanup = self._get_assets_needing_cleanup()
                    
                    if assets_needing_cleanup:
                        logger.info(f"تنظيف تلقائي لـ {len(assets_needing_cleanup)} زوج")
                        
                        for asset in assets_needing_cleanup:
                            self.cleanup_asset_data(asset)
                            await asyncio.sleep(0.1)  # تأخير قصير
                    
                    # انتظار حتى الدورة التالية
                    await asyncio.sleep(self.cleanup_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في حلقة التنظيف التلقائي: {str(e)}")
                    await asyncio.sleep(60)  # انتظار أطول عند الخطأ
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة التنظيف التلقائي: {str(e)}")

    def _get_assets_needing_cleanup(self) -> List[str]:
        """الحصول على الأزواج التي تحتاج تنظيف"""
        needing_cleanup = []
        
        for asset in self.currency_pairs:
            try:
                # تحديث الإحصائيات
                self.update_asset_stats(asset)
                
                stats = self.fifo_stats[asset]
                
                # التحقق من الحاجة للتنظيف
                if stats.is_at_limit:
                    needing_cleanup.append(asset)
                    
            except Exception as e:
                logger.debug(f"خطأ في فحص {asset}: {str(e)}")
                continue
        
        return needing_cleanup

    def stop_auto_cleanup(self):
        """إيقاف التنظيف التلقائي"""
        self.auto_cleanup_enabled = False
        logger.info("تم إيقاف التنظيف التلقائي")

    @handle_errors(default_return={}, log_error=True)
    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام الشاملة"""
        try:
            # حساب الإحصائيات العامة
            total_candles = sum(stats.current_count for stats in self.fifo_stats.values())
            total_capacity = len(self.currency_pairs) * self.max_candles_per_asset

            assets_at_limit = sum(1 for stats in self.fifo_stats.values() if stats.is_at_limit)
            assets_near_limit = sum(1 for stats in self.fifo_stats.values()
                                  if stats.usage_percentage >= 90 and not stats.is_at_limit)

            runtime = (datetime.now() - self.system_stats['start_time']).total_seconds()

            return {
                'system_overview': {
                    'total_assets': len(self.currency_pairs),
                    'total_candles': total_candles,
                    'total_capacity': total_capacity,
                    'usage_percentage': (total_candles / total_capacity) * 100,
                    'assets_at_limit': assets_at_limit,
                    'assets_near_limit': assets_near_limit,
                    'runtime_hours': round(runtime / 3600, 2)
                },
                'cleanup_stats': {
                    'total_cleanups': self.system_stats['total_cleanups'],
                    'total_deleted': self.system_stats['total_deleted'],
                    'total_added': self.system_stats['total_added'],
                    'errors': self.system_stats['errors'],
                    'last_full_scan': self.system_stats['last_full_scan'].isoformat() if self.system_stats['last_full_scan'] else None,
                    'auto_cleanup_enabled': self.auto_cleanup_enabled,
                    'cleanup_interval': self.cleanup_interval
                },
                'performance': {
                    'avg_cleanup_rate': round(self.system_stats['total_cleanups'] / max(runtime, 1), 4),
                    'avg_deletion_rate': round(self.system_stats['total_deleted'] / max(runtime, 1), 2),
                    'error_rate': round(self.system_stats['errors'] / max(runtime, 1), 4)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات النظام: {str(e)}")
            return {}

    @handle_errors(default_return=[], log_error=True)
    def get_assets_status_report(self, sort_by: str = 'usage') -> List[Dict[str, Any]]:
        """تقرير مفصل عن حالة جميع الأزواج"""
        try:
            report = []

            for asset, stats in self.fifo_stats.items():
                # تحديث الإحصائيات
                self.update_asset_stats(asset)

                asset_report = {
                    'asset': asset,
                    'current_count': stats.current_count,
                    'max_limit': stats.max_limit,
                    'usage_percentage': round(stats.usage_percentage, 2),
                    'available_space': stats.available_space,
                    'is_at_limit': stats.is_at_limit,
                    'deleted_count': stats.deleted_count,
                    'added_count': stats.added_count,
                    'last_cleanup': stats.last_cleanup.isoformat() if stats.last_cleanup else None,
                    'oldest_timestamp': stats.oldest_timestamp.isoformat() if stats.oldest_timestamp else None,
                    'newest_timestamp': stats.newest_timestamp.isoformat() if stats.newest_timestamp else None,
                    'status': self._get_asset_status(stats)
                }

                report.append(asset_report)

            # ترتيب التقرير
            if sort_by == 'usage':
                report.sort(key=lambda x: x['usage_percentage'], reverse=True)
            elif sort_by == 'count':
                report.sort(key=lambda x: x['current_count'], reverse=True)
            elif sort_by == 'asset':
                report.sort(key=lambda x: x['asset'])

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الأزواج: {str(e)}")
            return []

    def _get_asset_status(self, stats: FIFOStats) -> str:
        """تحديد حالة الزوج"""
        if stats.usage_percentage >= 100:
            return "FULL"
        elif stats.usage_percentage >= 95:
            return "CRITICAL"
        elif stats.usage_percentage >= 90:
            return "WARNING"
        elif stats.usage_percentage >= 75:
            return "HIGH"
        elif stats.usage_percentage >= 50:
            return "MEDIUM"
        elif stats.usage_percentage >= 25:
            return "LOW"
        else:
            return "EMPTY"

    @handle_errors(default_return=False, log_error=True)
    def add_new_candles(self, asset: str, candles_count: int) -> bool:
        """تسجيل إضافة شموع جديدة وتنفيذ FIFO إذا لزم الأمر"""
        try:
            if asset not in self.fifo_stats:
                self.fifo_stats[asset] = FIFOStats(
                    asset=asset,
                    max_limit=self.max_candles_per_asset
                )

            stats = self.fifo_stats[asset]

            # تحديث عداد الإضافة
            stats.added_count += candles_count
            self.system_stats['total_added'] += candles_count

            # تحديث الإحصائيات
            self.update_asset_stats(asset)

            # تنفيذ FIFO إذا تجاوز الحد
            if stats.is_at_limit:
                deleted_count = self.cleanup_asset_data(asset)
                logger.debug(f"FIFO تلقائي لـ {asset}: أضيف {candles_count}, حذف {deleted_count}")

            return True

        except Exception as e:
            logger.error(f"خطأ في تسجيل الشموع الجديدة لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def validate_data_integrity(self, asset: str = None) -> bool:
        """التحقق من تكامل البيانات"""
        try:
            assets_to_check = [asset] if asset else self.currency_pairs

            integrity_issues = []

            for check_asset in assets_to_check:
                # تحديث الإحصائيات
                self.update_asset_stats(check_asset)
                stats = self.fifo_stats[check_asset]

                # فحص التكامل
                if stats.current_count > self.max_candles_per_asset:
                    integrity_issues.append(f"{check_asset}: تجاوز الحد ({stats.current_count}/{self.max_candles_per_asset})")

                # فحص الترتيب الزمني
                if not self._check_chronological_order(check_asset):
                    integrity_issues.append(f"{check_asset}: خلل في الترتيب الزمني")

            if integrity_issues:
                logger.warning(f"مشاكل تكامل البيانات: {len(integrity_issues)}")
                for issue in integrity_issues:
                    logger.warning(f"  - {issue}")
                return False

            logger.info("تم التحقق من تكامل البيانات - لا توجد مشاكل")
            return True

        except Exception as e:
            logger.error(f"خطأ في التحقق من تكامل البيانات: {str(e)}")
            return False

    def _check_chronological_order(self, asset: str) -> bool:
        """فحص الترتيب الزمني للشموع"""
        try:
            # فحص عينة من الشموع للتأكد من الترتيب
            sample_candles = historical_data_repo.get_candles_range(
                asset=asset,
                timeframe=self.timeframe,
                limit=100
            )

            if len(sample_candles) < 2:
                return True

            # التحقق من الترتيب
            for i in range(1, len(sample_candles)):
                if sample_candles[i]['timestamp'] <= sample_candles[i-1]['timestamp']:
                    return False

            return True

        except Exception as e:
            logger.debug(f"خطأ في فحص الترتيب الزمني لـ {asset}: {str(e)}")
            return True  # افتراض أن الترتيب صحيح عند الخطأ

# إنشاء مثيل عام للاستخدام
fifo_manager = FIFODataManager()
