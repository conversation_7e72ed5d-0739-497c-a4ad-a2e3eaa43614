#!/usr/bin/env python3
"""
محرك تقييم الإشارات النهائية - Final Signal Evaluation Engine
يقوم بتقييم وترجيح الإشارات النهائية قبل التنفيذ
"""

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from config.trading_config import TradingConfig
from data_layer.quadruple_convergence_system import ConvergenceResult, quadruple_convergence_system

logger = scalping_logger.get_logger("final_signal_evaluation")

class SignalQuality(Enum):
    """جودة الإشارة"""
    EXCELLENT = "ممتاز"
    GOOD = "جيد"
    AVERAGE = "متوسط"
    POOR = "ضعيف"
    REJECTED = "مرفوض"

class MarketCondition(Enum):
    """حالة السوق"""
    TRENDING = "اتجاهي"
    RANGING = "محدود النطاق"
    VOLATILE = "متقلب"
    CALM = "هادئ"
    UNCERTAIN = "غير مؤكد"

@dataclass
class SignalEvaluation:
    """تقييم الإشارة النهائية"""
    currency_pair: str
    original_signal: str
    final_signal: str
    quality_score: float  # 0.0 - 1.0
    quality_grade: SignalQuality
    confidence_score: float  # 0.0 - 1.0
    risk_score: float  # 0.0 - 1.0
    market_condition: MarketCondition
    execution_priority: int  # 1-5 (1 = أعلى أولوية)
    recommended_amount: float
    recommended_timeframe: int
    stop_conditions: List[str]
    evaluation_details: Dict[str, Any]
    timestamp: datetime

@dataclass
class EvaluationCriteria:
    """معايير التقييم"""
    min_confidence_threshold: float = 0.70
    min_convergence_threshold: float = 0.75
    max_risk_threshold: float = 0.60
    quality_weights: Dict[str, float] = None
    market_condition_multipliers: Dict[str, float] = None
    
    def __post_init__(self):
        if self.quality_weights is None:
            self.quality_weights = {
                'convergence_score': 0.30,
                'confidence_level': 0.25,
                'risk_assessment': 0.20,
                'market_condition': 0.15,
                'historical_performance': 0.10
            }
        
        if self.market_condition_multipliers is None:
            self.market_condition_multipliers = {
                'TRENDING': 1.2,
                'CALM': 1.1,
                'RANGING': 0.9,
                'VOLATILE': 0.7,
                'UNCERTAIN': 0.5
            }

class FinalSignalEvaluationEngine:
    """محرك تقييم الإشارات النهائية"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logger
        self.criteria = EvaluationCriteria()
        
        # إحصائيات التقييم
        self.evaluation_stats = {
            'total_evaluations': 0,
            'excellent_signals': 0,
            'good_signals': 0,
            'average_signals': 0,
            'poor_signals': 0,
            'rejected_signals': 0,
            'execution_success_rate': 0.0,
            'quality_distribution': {},
            'market_condition_performance': {}
        }
        
        # تاريخ الأداء
        self.performance_history = []
        
        self.logger.info("تم تهيئة محرك تقييم الإشارات النهائية")

    async def evaluate_signal(self, currency_pair: str, convergence_result: ConvergenceResult) -> SignalEvaluation:
        """تقييم إشارة نهائية"""
        try:
            self.evaluation_stats['total_evaluations'] += 1
            evaluation_start = datetime.now()
            
            self.logger.info(f"بدء تقييم الإشارة النهائية لـ {currency_pair}")
            
            # التقييم الأساسي
            basic_evaluation = await self._perform_basic_evaluation(convergence_result)
            
            # تقييم حالة السوق
            market_evaluation = await self._evaluate_market_condition(currency_pair)
            
            # تقييم الأداء التاريخي
            historical_evaluation = await self._evaluate_historical_performance(currency_pair, convergence_result.final_signal)
            
            # حساب النقاط النهائية
            final_evaluation = await self._calculate_final_evaluation(
                currency_pair, convergence_result, basic_evaluation, 
                market_evaluation, historical_evaluation
            )
            
            # تحديث الإحصائيات
            self._update_evaluation_statistics(final_evaluation)
            
            evaluation_duration = (datetime.now() - evaluation_start).total_seconds()
            self.logger.info(f"اكتمل تقييم الإشارة لـ {currency_pair} في {evaluation_duration:.2f} ثانية")
            
            return final_evaluation
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم الإشارة لـ {currency_pair}: {str(e)}")
            raise

    async def _perform_basic_evaluation(self, convergence_result: ConvergenceResult) -> Dict[str, Any]:
        """التقييم الأساسي للإشارة"""
        try:
            evaluation = {
                'convergence_quality': 0.0,
                'confidence_quality': 0.0,
                'risk_quality': 0.0,
                'layer_consistency': 0.0
            }
            
            # تقييم جودة التقارب
            convergence_score = convergence_result.convergence_score
            if convergence_score >= 0.9:
                evaluation['convergence_quality'] = 1.0
            elif convergence_score >= 0.8:
                evaluation['convergence_quality'] = 0.8
            elif convergence_score >= 0.7:
                evaluation['convergence_quality'] = 0.6
            else:
                evaluation['convergence_quality'] = 0.3
            
            # تقييم جودة الثقة
            confidence_score = convergence_result.overall_confidence
            if confidence_score >= 0.85:
                evaluation['confidence_quality'] = 1.0
            elif confidence_score >= 0.75:
                evaluation['confidence_quality'] = 0.8
            elif confidence_score >= 0.65:
                evaluation['confidence_quality'] = 0.6
            else:
                evaluation['confidence_quality'] = 0.3
            
            # تقييم جودة المخاطر (عكسي - كلما قل الخطر كانت الجودة أعلى)
            risk_score = convergence_result.risk_assessment.get('risk_score', 1.0)
            evaluation['risk_quality'] = max(0.0, 1.0 - risk_score)
            
            # تقييم اتساق الطبقات
            layer_signals = convergence_result.layer_signals
            if layer_signals:
                signal_types = [signal.signal_type for signal in layer_signals]
                dominant_signal = convergence_result.final_signal
                
                if dominant_signal != 'NEUTRAL':
                    matching_signals = signal_types.count(dominant_signal)
                    evaluation['layer_consistency'] = matching_signals / len(signal_types)
                else:
                    evaluation['layer_consistency'] = 0.5
            
            return evaluation
            
        except Exception as e:
            self.logger.error(f"خطأ في التقييم الأساسي: {str(e)}")
            return {'convergence_quality': 0.0, 'confidence_quality': 0.0, 'risk_quality': 0.0, 'layer_consistency': 0.0}

    async def _evaluate_market_condition(self, currency_pair: str) -> Dict[str, Any]:
        """تقييم حالة السوق"""
        try:
            # جلب البيانات الحديثة
            from database.repository import historical_data_repo
            recent_candles = historical_data_repo.get_latest_candles(currency_pair, 50)
            
            if not recent_candles or len(recent_candles) < 20:
                return {
                    'market_condition': MarketCondition.UNCERTAIN,
                    'condition_strength': 0.0,
                    'volatility_level': 0.0,
                    'trend_strength': 0.0
                }
            
            # تحويل البيانات للتحليل - التعامل مع النوعين المختلفين
            prices = []
            highs = []
            lows = []

            for candle in recent_candles:
                if isinstance(candle, dict):
                    prices.append(float(candle['close']))
                    highs.append(float(candle['high']))
                    lows.append(float(candle['low']))
                else:
                    prices.append(float(candle.close))
                    highs.append(float(candle.high))
                    lows.append(float(candle.low))
            
            # حساب التقلبات
            price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            volatility = np.mean(price_changes) * 100
            
            # حساب قوة الاتجاه
            price_diff = prices[-1] - prices[0]
            trend_strength = abs(price_diff) / prices[0] * 100
            
            # تحديد حالة السوق
            market_condition = MarketCondition.UNCERTAIN
            condition_strength = 0.0
            
            if volatility > 0.5:  # تقلبات عالية
                market_condition = MarketCondition.VOLATILE
                condition_strength = min(1.0, volatility / 1.0)
            elif trend_strength > 0.3:  # اتجاه قوي
                market_condition = MarketCondition.TRENDING
                condition_strength = min(1.0, trend_strength / 0.5)
            elif volatility < 0.1:  # هدوء
                market_condition = MarketCondition.CALM
                condition_strength = max(0.5, 1.0 - volatility * 10)
            else:  # نطاق محدود
                market_condition = MarketCondition.RANGING
                condition_strength = 0.6
            
            return {
                'market_condition': market_condition,
                'condition_strength': condition_strength,
                'volatility_level': volatility,
                'trend_strength': trend_strength,
                'price_range': {
                    'high': max(highs),
                    'low': min(lows),
                    'current': prices[-1]
                }
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم حالة السوق: {str(e)}")
            return {
                'market_condition': MarketCondition.UNCERTAIN,
                'condition_strength': 0.0,
                'volatility_level': 0.0,
                'trend_strength': 0.0
            }

    async def _evaluate_historical_performance(self, currency_pair: str, signal_type: str) -> Dict[str, Any]:
        """تقييم الأداء التاريخي"""
        try:
            # البحث في تاريخ الأداء
            relevant_history = [
                record for record in self.performance_history
                if record.get('currency_pair') == currency_pair and 
                   record.get('signal_type') == signal_type
            ]
            
            if not relevant_history:
                return {
                    'historical_success_rate': 0.5,  # افتراضي
                    'average_performance': 0.5,
                    'sample_size': 0,
                    'confidence_in_history': 0.0
                }
            
            # حساب معدل النجاح
            successful_trades = sum(1 for record in relevant_history if record.get('was_successful', False))
            success_rate = successful_trades / len(relevant_history)
            
            # حساب متوسط الأداء
            performance_scores = [record.get('performance_score', 0.5) for record in relevant_history]
            average_performance = np.mean(performance_scores)
            
            # حساب الثقة في التاريخ (بناءً على حجم العينة)
            sample_size = len(relevant_history)
            confidence_in_history = min(1.0, sample_size / 20)  # ثقة كاملة عند 20 عينة أو أكثر
            
            return {
                'historical_success_rate': success_rate,
                'average_performance': average_performance,
                'sample_size': sample_size,
                'confidence_in_history': confidence_in_history,
                'recent_trend': self._calculate_recent_trend(relevant_history[-10:])  # آخر 10 صفقات
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم الأداء التاريخي: {str(e)}")
            return {
                'historical_success_rate': 0.5,
                'average_performance': 0.5,
                'sample_size': 0,
                'confidence_in_history': 0.0
            }

    def _calculate_recent_trend(self, recent_records: List[Dict]) -> str:
        """حساب الاتجاه الحديث للأداء"""
        if len(recent_records) < 3:
            return "غير محدد"
        
        recent_scores = [record.get('performance_score', 0.5) for record in recent_records]
        
        # حساب الاتجاه
        first_half = np.mean(recent_scores[:len(recent_scores)//2])
        second_half = np.mean(recent_scores[len(recent_scores)//2:])
        
        if second_half > first_half + 0.1:
            return "تحسن"
        elif second_half < first_half - 0.1:
            return "تراجع"
        else:
            return "مستقر"

    async def _calculate_final_evaluation(self, currency_pair: str, convergence_result: ConvergenceResult,
                                        basic_evaluation: Dict, market_evaluation: Dict,
                                        historical_evaluation: Dict) -> SignalEvaluation:
        """حساب التقييم النهائي"""
        try:
            # حساب النقاط المرجحة
            quality_score = 0.0
            weights = self.criteria.quality_weights

            # نقاط التقارب
            quality_score += basic_evaluation['convergence_quality'] * weights['convergence_score']

            # نقاط الثقة
            quality_score += basic_evaluation['confidence_quality'] * weights['confidence_level']

            # نقاط المخاطر
            quality_score += basic_evaluation['risk_quality'] * weights['risk_assessment']

            # نقاط حالة السوق
            market_condition = market_evaluation['market_condition']
            market_multiplier = self.criteria.market_condition_multipliers.get(market_condition.name, 1.0)
            market_score = market_evaluation['condition_strength'] * market_multiplier
            quality_score += market_score * weights['market_condition']

            # نقاط الأداء التاريخي
            historical_score = historical_evaluation['average_performance'] * historical_evaluation['confidence_in_history']
            quality_score += historical_score * weights['historical_performance']

            # تحديد درجة الجودة
            quality_grade = self._determine_quality_grade(quality_score)

            # حساب نقاط الثقة النهائية
            confidence_score = self._calculate_confidence_score(
                convergence_result, basic_evaluation, market_evaluation, historical_evaluation
            )

            # حساب نقاط المخاطر النهائية
            risk_score = self._calculate_risk_score(
                convergence_result, market_evaluation, quality_score
            )

            # تحديد الإشارة النهائية
            final_signal = self._determine_final_signal(
                convergence_result, quality_grade, confidence_score, risk_score
            )

            # تحديد أولوية التنفيذ
            execution_priority = self._calculate_execution_priority(quality_score, confidence_score, risk_score)

            # تحديد المبلغ والإطار الزمني المقترح
            recommended_amount, recommended_timeframe = self._calculate_execution_parameters(
                quality_score, confidence_score, risk_score, market_condition
            )

            # تحديد شروط الإيقاف
            stop_conditions = self._generate_stop_conditions(
                quality_grade, market_condition, risk_score
            )

            # تجميع تفاصيل التقييم
            evaluation_details = {
                'basic_evaluation': basic_evaluation,
                'market_evaluation': market_evaluation,
                'historical_evaluation': historical_evaluation,
                'quality_breakdown': {
                    'convergence_contribution': basic_evaluation['convergence_quality'] * weights['convergence_score'],
                    'confidence_contribution': basic_evaluation['confidence_quality'] * weights['confidence_level'],
                    'risk_contribution': basic_evaluation['risk_quality'] * weights['risk_assessment'],
                    'market_contribution': market_score * weights['market_condition'],
                    'historical_contribution': historical_score * weights['historical_performance']
                },
                'decision_factors': {
                    'quality_threshold_met': quality_score >= 0.6,
                    'confidence_threshold_met': confidence_score >= self.criteria.min_confidence_threshold,
                    'risk_threshold_met': risk_score <= self.criteria.max_risk_threshold,
                    'market_condition_favorable': market_multiplier >= 1.0
                }
            }

            return SignalEvaluation(
                currency_pair=currency_pair,
                original_signal=convergence_result.final_signal,
                final_signal=final_signal,
                quality_score=quality_score,
                quality_grade=quality_grade,
                confidence_score=confidence_score,
                risk_score=risk_score,
                market_condition=market_condition,
                execution_priority=execution_priority,
                recommended_amount=recommended_amount,
                recommended_timeframe=recommended_timeframe,
                stop_conditions=stop_conditions,
                evaluation_details=evaluation_details,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"خطأ في حساب التقييم النهائي: {str(e)}")
            raise

    def _determine_quality_grade(self, quality_score: float) -> SignalQuality:
        """تحديد درجة جودة الإشارة"""
        if quality_score >= 0.85:
            return SignalQuality.EXCELLENT
        elif quality_score >= 0.75:
            return SignalQuality.GOOD
        elif quality_score >= 0.60:
            return SignalQuality.AVERAGE
        elif quality_score >= 0.40:
            return SignalQuality.POOR
        else:
            return SignalQuality.REJECTED

    def _calculate_confidence_score(self, convergence_result: ConvergenceResult,
                                   basic_evaluation: Dict, market_evaluation: Dict,
                                   historical_evaluation: Dict) -> float:
        """حساب نقاط الثقة النهائية"""
        try:
            # الثقة الأساسية من التقارب
            base_confidence = convergence_result.overall_confidence

            # تعديل بناءً على اتساق الطبقات
            consistency_bonus = basic_evaluation['layer_consistency'] * 0.1

            # تعديل بناءً على حالة السوق
            market_condition = market_evaluation['market_condition']
            if market_condition in [MarketCondition.TRENDING, MarketCondition.CALM]:
                market_bonus = 0.05
            elif market_condition == MarketCondition.VOLATILE:
                market_bonus = -0.1
            else:
                market_bonus = 0.0

            # تعديل بناءً على الأداء التاريخي
            historical_bonus = (historical_evaluation['historical_success_rate'] - 0.5) * 0.2

            final_confidence = base_confidence + consistency_bonus + market_bonus + historical_bonus
            return max(0.0, min(1.0, final_confidence))

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط الثقة: {str(e)}")
            return 0.5

    def _calculate_risk_score(self, convergence_result: ConvergenceResult,
                            market_evaluation: Dict, quality_score: float) -> float:
        """حساب نقاط المخاطر النهائية"""
        try:
            # المخاطر الأساسية من التقارب
            base_risk = convergence_result.risk_assessment.get('risk_score', 0.5)

            # تعديل بناءً على حالة السوق
            market_condition = market_evaluation['market_condition']
            if market_condition == MarketCondition.VOLATILE:
                market_risk_addition = 0.2
            elif market_condition == MarketCondition.UNCERTAIN:
                market_risk_addition = 0.3
            else:
                market_risk_addition = 0.0

            # تعديل بناءً على جودة الإشارة (جودة أعلى = مخاطر أقل)
            quality_risk_reduction = (quality_score - 0.5) * 0.2

            final_risk = base_risk + market_risk_addition - quality_risk_reduction
            return max(0.0, min(1.0, final_risk))

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط المخاطر: {str(e)}")
            return 0.8

    def _determine_final_signal(self, convergence_result: ConvergenceResult,
                              quality_grade: SignalQuality, confidence_score: float,
                              risk_score: float) -> str:
        """تحديد الإشارة النهائية"""
        try:
            original_signal = convergence_result.final_signal

            # رفض الإشارات ضعيفة الجودة
            if quality_grade == SignalQuality.REJECTED:
                return 'NEUTRAL'

            # رفض الإشارات منخفضة الثقة
            if confidence_score < self.criteria.min_confidence_threshold:
                return 'NEUTRAL'

            # رفض الإشارات عالية المخاطر
            if risk_score > self.criteria.max_risk_threshold:
                return 'NEUTRAL'

            # قبول الإشارة الأصلية إذا استوفت المعايير
            return original_signal

        except Exception as e:
            self.logger.error(f"خطأ في تحديد الإشارة النهائية: {str(e)}")
            return 'NEUTRAL'

    def _calculate_execution_priority(self, quality_score: float, confidence_score: float,
                                    risk_score: float) -> int:
        """حساب أولوية التنفيذ (1-5)"""
        try:
            # حساب النقاط المجمعة
            combined_score = (quality_score + confidence_score + (1 - risk_score)) / 3

            if combined_score >= 0.9:
                return 1  # أولوية عالية جداً
            elif combined_score >= 0.8:
                return 2  # أولوية عالية
            elif combined_score >= 0.7:
                return 3  # أولوية متوسطة
            elif combined_score >= 0.6:
                return 4  # أولوية منخفضة
            else:
                return 5  # أولوية منخفضة جداً

        except Exception as e:
            self.logger.error(f"خطأ في حساب أولوية التنفيذ: {str(e)}")
            return 5

    def _calculate_execution_parameters(self, quality_score: float, confidence_score: float,
                                      risk_score: float, market_condition: MarketCondition) -> Tuple[float, int]:
        """حساب معاملات التنفيذ (المبلغ والإطار الزمني)"""
        try:
            # حساب المبلغ المقترح (نسبة من الرصيد)
            base_amount = 0.02  # 2% أساسي

            # تعديل بناءً على الجودة والثقة
            quality_multiplier = 0.5 + (quality_score * 0.5)  # 0.5 - 1.0
            confidence_multiplier = 0.5 + (confidence_score * 0.5)  # 0.5 - 1.0
            risk_multiplier = 1.5 - risk_score  # 0.5 - 1.5

            recommended_amount = base_amount * quality_multiplier * confidence_multiplier * risk_multiplier
            recommended_amount = max(0.005, min(0.05, recommended_amount))  # حدود 0.5% - 5%

            # تحديد الإطار الزمني
            if market_condition == MarketCondition.TRENDING and quality_score > 0.8:
                recommended_timeframe = 60  # دقيقة واحدة للاتجاهات القوية
            elif market_condition == MarketCondition.CALM and confidence_score > 0.8:
                recommended_timeframe = 300  # 5 دقائق للأسواق الهادئة
            elif market_condition == MarketCondition.VOLATILE:
                recommended_timeframe = 900  # 15 دقيقة للأسواق المتقلبة
            else:
                recommended_timeframe = 300  # 5 دقائق افتراضي

            return recommended_amount, recommended_timeframe

        except Exception as e:
            self.logger.error(f"خطأ في حساب معاملات التنفيذ: {str(e)}")
            return 0.01, 300

    def _generate_stop_conditions(self, quality_grade: SignalQuality,
                                market_condition: MarketCondition, risk_score: float) -> List[str]:
        """توليد شروط الإيقاف"""
        try:
            conditions = []

            # شروط أساسية
            conditions.append("انخفاض الثقة تحت 60%")
            conditions.append("ارتفاع المخاطر فوق 70%")

            # شروط بناءً على الجودة
            if quality_grade in [SignalQuality.POOR, SignalQuality.AVERAGE]:
                conditions.append("تغيير درجة الجودة للأسوأ")

            # شروط بناءً على حالة السوق
            if market_condition == MarketCondition.VOLATILE:
                conditions.append("زيادة التقلبات فوق 1%")
            elif market_condition == MarketCondition.TRENDING:
                conditions.append("كسر الاتجاه الحالي")

            # شروط بناءً على المخاطر
            if risk_score > 0.5:
                conditions.append("ظهور إشارات تضارب جديدة")

            conditions.append("انقضاء 30 دقيقة بدون تنفيذ")

            return conditions

        except Exception as e:
            self.logger.error(f"خطأ في توليد شروط الإيقاف: {str(e)}")
            return ["خطأ في النظام"]

    def _update_evaluation_statistics(self, evaluation: SignalEvaluation):
        """تحديث إحصائيات التقييم"""
        try:
            # تحديث توزيع الجودة
            quality_name = evaluation.quality_grade.value
            if quality_name not in self.evaluation_stats['quality_distribution']:
                self.evaluation_stats['quality_distribution'][quality_name] = 0
            self.evaluation_stats['quality_distribution'][quality_name] += 1

            # تحديث عدادات الجودة
            if evaluation.quality_grade == SignalQuality.EXCELLENT:
                self.evaluation_stats['excellent_signals'] += 1
            elif evaluation.quality_grade == SignalQuality.GOOD:
                self.evaluation_stats['good_signals'] += 1
            elif evaluation.quality_grade == SignalQuality.AVERAGE:
                self.evaluation_stats['average_signals'] += 1
            elif evaluation.quality_grade == SignalQuality.POOR:
                self.evaluation_stats['poor_signals'] += 1
            else:
                self.evaluation_stats['rejected_signals'] += 1

            # تحديث أداء حالة السوق
            market_name = evaluation.market_condition.value
            if market_name not in self.evaluation_stats['market_condition_performance']:
                self.evaluation_stats['market_condition_performance'][market_name] = {
                    'count': 0, 'avg_quality': 0.0, 'avg_confidence': 0.0
                }

            market_stats = self.evaluation_stats['market_condition_performance'][market_name]
            market_stats['count'] += 1

            # تحديث المتوسطات
            current_avg_quality = market_stats['avg_quality']
            current_avg_confidence = market_stats['avg_confidence']
            count = market_stats['count']

            market_stats['avg_quality'] = ((current_avg_quality * (count - 1)) + evaluation.quality_score) / count
            market_stats['avg_confidence'] = ((current_avg_confidence * (count - 1)) + evaluation.confidence_score) / count

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات التقييم: {str(e)}")

    def get_evaluation_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التقييم"""
        try:
            total = self.evaluation_stats['total_evaluations']
            if total == 0:
                return self.evaluation_stats

            # حساب النسب المئوية
            quality_percentages = {}
            for quality, count in self.evaluation_stats['quality_distribution'].items():
                quality_percentages[quality] = (count / total) * 100

            return {
                'total_evaluations': total,
                'quality_distribution': self.evaluation_stats['quality_distribution'],
                'quality_percentages': quality_percentages,
                'excellent_rate': (self.evaluation_stats['excellent_signals'] / total) * 100,
                'good_rate': (self.evaluation_stats['good_signals'] / total) * 100,
                'average_rate': (self.evaluation_stats['average_signals'] / total) * 100,
                'poor_rate': (self.evaluation_stats['poor_signals'] / total) * 100,
                'rejection_rate': (self.evaluation_stats['rejected_signals'] / total) * 100,
                'market_condition_performance': self.evaluation_stats['market_condition_performance'],
                'execution_success_rate': self.evaluation_stats['execution_success_rate']
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات التقييم: {str(e)}")
            return self.evaluation_stats

    async def update_performance_history(self, currency_pair: str, signal_type: str,
                                       was_successful: bool, performance_score: float):
        """تحديث تاريخ الأداء"""
        try:
            performance_record = {
                'currency_pair': currency_pair,
                'signal_type': signal_type,
                'was_successful': was_successful,
                'performance_score': performance_score,
                'timestamp': datetime.now().isoformat()
            }

            self.performance_history.append(performance_record)

            # الاحتفاظ بآخر 1000 سجل فقط
            if len(self.performance_history) > 1000:
                self.performance_history = self.performance_history[-1000:]

            # تحديث معدل النجاح العام
            successful_count = sum(1 for record in self.performance_history if record['was_successful'])
            self.evaluation_stats['execution_success_rate'] = (successful_count / len(self.performance_history)) * 100

        except Exception as e:
            self.logger.error(f"خطأ في تحديث تاريخ الأداء: {str(e)}")

# إنشاء مثيل عام للمحرك
final_signal_evaluation_engine = FinalSignalEvaluationEngine()
