"""
نظام حل الفجوات والتكامل - معالجة انقطاع البيانات وضمان التكامل
"""

import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict
import threading

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from database.repository import historical_data_repo
from database.models import HistoricalData
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, DataError

logger = scalping_logger.get_logger("gap_resolver")

@dataclass
class GapInfo:
    """معلومات الفجوة في البيانات"""
    asset: str
    start_time: datetime
    end_time: datetime
    expected_candles: int
    missing_candles: int
    gap_duration_minutes: int
    severity: str = "LOW"  # LOW, MEDIUM, HIGH, CRITICAL
    
    @property
    def gap_percentage(self) -> float:
        """نسبة الفجوة"""
        if self.expected_candles == 0:
            return 0.0
        return (self.missing_candles / self.expected_candles) * 100
    
    def __post_init__(self):
        """تحديد شدة الفجوة"""
        if self.gap_percentage >= 50:
            self.severity = "CRITICAL"
        elif self.gap_percentage >= 25:
            self.severity = "HIGH"
        elif self.gap_percentage >= 10:
            self.severity = "MEDIUM"
        else:
            self.severity = "LOW"

@dataclass
class IntegrityReport:
    """تقرير تكامل البيانات"""
    asset: str
    total_candles: int
    expected_candles: int
    missing_candles: int
    gaps_found: List[GapInfo]
    integrity_score: float = 0.0
    last_check: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """حساب نقاط التكامل"""
        if self.expected_candles > 0:
            self.integrity_score = ((self.expected_candles - self.missing_candles) / self.expected_candles) * 100
        else:
            self.integrity_score = 100.0

class GapResolver:
    """نظام حل الفجوات والتكامل"""
    
    def __init__(self, timeframe: int = 60, max_gap_hours: int = 24):
        self.timeframe = timeframe
        self.max_gap_hours = max_gap_hours
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.config = TradingConfig()
        
        # إحصائيات النظام
        self.system_stats = {
            'gaps_detected': 0,
            'gaps_resolved': 0,
            'integrity_checks': 0,
            'reconnections': 0,
            'errors': 0,
            'start_time': datetime.now()
        }
        
        # تقارير التكامل
        self.integrity_reports: Dict[str, IntegrityReport] = {}
        
        # إعدادات التشغيل
        self.auto_gap_detection = True
        self.auto_gap_filling = True
        self.integrity_check_interval = 3600  # ساعة واحدة
        self.max_concurrent_fills = 5
        
        # قفل للعمليات المتزامنة
        self._lock = threading.Lock()
        
        # قائمة الفجوات المكتشفة
        self.detected_gaps: Dict[str, List[GapInfo]] = defaultdict(list)
        
        logger.info(f"تم تهيئة نظام حل الفجوات للـ{len(self.currency_pairs)} زوج")
        logger.info(f"الإطار الزمني: {self.timeframe} ثانية، الحد الأقصى للفجوة: {self.max_gap_hours} ساعة")

    @handle_errors(default_return=[], log_error=True)
    def detect_gaps_for_asset(self, asset: str, start_date: datetime = None, end_date: datetime = None) -> List[GapInfo]:
        """اكتشاف الفجوات في البيانات لزوج معين"""
        try:
            if not start_date:
                start_date = datetime.now() - timedelta(days=7)  # آخر أسبوع
            if not end_date:
                end_date = datetime.now()
            
            logger.debug(f"🔍 فحص الفجوات في {asset} من {start_date} إلى {end_date}")
            
            # جلب البيانات الموجودة
            existing_candles = historical_data_repo.get_candles_in_range(
                asset=asset,
                timeframe=self.timeframe,
                start_time=start_date,
                end_time=end_date
            )
            
            if not existing_candles:
                # لا توجد بيانات - فجوة كاملة
                expected_candles = int((end_date - start_date).total_seconds() / self.timeframe)
                gap = GapInfo(
                    asset=asset,
                    start_time=start_date,
                    end_time=end_date,
                    expected_candles=expected_candles,
                    missing_candles=expected_candles,
                    gap_duration_minutes=int((end_date - start_date).total_seconds() / 60)
                )
                return [gap]
            
            # تحليل الفجوات
            gaps = []
            candle_times = []

            # تحويل timestamps إلى datetime objects
            for candle in existing_candles:
                timestamp = candle['timestamp']
                if isinstance(timestamp, str):
                    # تحويل string إلى datetime
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                elif not isinstance(timestamp, datetime):
                    # إذا كان نوع آخر، تجاهل هذه الشمعة
                    continue
                candle_times.append(timestamp)

            candle_times.sort()
            
            # فحص الفجوات بين الشموع
            for i in range(len(candle_times) - 1):
                current_time = candle_times[i]
                next_time = candle_times[i + 1]
                
                expected_next = current_time + timedelta(seconds=self.timeframe)
                
                if next_time > expected_next:
                    # فجوة مكتشفة
                    gap_duration = (next_time - expected_next).total_seconds()
                    missing_candles = int(gap_duration / self.timeframe)
                    
                    if missing_candles > 0:
                        gap = GapInfo(
                            asset=asset,
                            start_time=expected_next,
                            end_time=next_time,
                            expected_candles=missing_candles,
                            missing_candles=missing_candles,
                            gap_duration_minutes=int(gap_duration / 60)
                        )
                        gaps.append(gap)
            
            # فحص الفجوة في البداية
            if candle_times and candle_times[0] > start_date:
                gap_duration = (candle_times[0] - start_date).total_seconds()
                missing_candles = int(gap_duration / self.timeframe)
                
                if missing_candles > 0:
                    gap = GapInfo(
                        asset=asset,
                        start_time=start_date,
                        end_time=candle_times[0],
                        expected_candles=missing_candles,
                        missing_candles=missing_candles,
                        gap_duration_minutes=int(gap_duration / 60)
                    )
                    gaps.insert(0, gap)
            
            # فحص الفجوة في النهاية
            if candle_times and candle_times[-1] < end_date:
                gap_duration = (end_date - candle_times[-1]).total_seconds()
                missing_candles = int(gap_duration / self.timeframe)
                
                if missing_candles > 0:
                    gap = GapInfo(
                        asset=asset,
                        start_time=candle_times[-1] + timedelta(seconds=self.timeframe),
                        end_time=end_date,
                        expected_candles=missing_candles,
                        missing_candles=missing_candles,
                        gap_duration_minutes=int(gap_duration / 60)
                    )
                    gaps.append(gap)
            
            if gaps:
                logger.info(f"🔍 اكتشاف {len(gaps)} فجوة في {asset}")
                for gap in gaps:
                    logger.debug(f"  - فجوة {gap.severity}: {gap.missing_candles} شمعة ({gap.gap_duration_minutes} دقيقة)")
            
            self.system_stats['gaps_detected'] += len(gaps)
            return gaps
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف الفجوات لـ {asset}: {str(e)}")
            self.system_stats['errors'] += 1
            return []

    @handle_async_errors(default_return=False, log_error=True)
    async def fill_gap_async(self, gap: GapInfo) -> bool:
        """ملء فجوة معينة بشكل غير متزامن"""
        try:
            logger.info(f"🔧 ملء فجوة في {gap.asset}: {gap.missing_candles} شمعة")
            
            # استيراد مجمع البيانات
            from .historical_collector import async_historical_collector
            
            # حساب الفترة الزمنية المطلوبة
            duration_hours = gap.gap_duration_minutes / 60
            
            # جمع البيانات للفترة المفقودة
            results = async_historical_collector.collect_and_calculate_indicators(
                assets=[gap.asset],
                timeframe=self.timeframe,
                duration_hours=min(duration_hours, self.max_gap_hours)
            )
            
            if gap.asset in results and results[gap.asset].get('success', False):
                logger.info(f"✅ تم ملء فجوة {gap.asset} بنجاح")
                self.system_stats['gaps_resolved'] += 1
                return True
            else:
                logger.warning(f"⚠️ فشل في ملء فجوة {gap.asset}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في ملء فجوة {gap.asset}: {str(e)}")
            self.system_stats['errors'] += 1
            return False

    @handle_errors(default_return={}, log_error=True)
    def scan_all_assets_for_gaps(self, days_back: int = 7) -> Dict[str, List[GapInfo]]:
        """فحص جميع الأزواج للبحث عن فجوات"""
        try:
            logger.info(f"🔍 فحص شامل للفجوات في {len(self.currency_pairs)} زوج (آخر {days_back} أيام)")
            
            start_date = datetime.now() - timedelta(days=days_back)
            end_date = datetime.now()
            
            all_gaps = {}
            total_gaps = 0
            
            for asset in self.currency_pairs:
                try:
                    gaps = self.detect_gaps_for_asset(asset, start_date, end_date)
                    if gaps:
                        all_gaps[asset] = gaps
                        total_gaps += len(gaps)
                        
                        # حفظ الفجوات المكتشفة
                        with self._lock:
                            self.detected_gaps[asset] = gaps
                    
                    # تأخير قصير لتجنب إرهاق قاعدة البيانات
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"خطأ في فحص {asset}: {str(e)}")
                    continue
            
            logger.info(f"📊 نتائج الفحص الشامل: {total_gaps} فجوة في {len(all_gaps)} زوج")
            
            return all_gaps
            
        except Exception as e:
            logger.error(f"خطأ في الفحص الشامل للفجوات: {str(e)}")
            return {}

    @handle_errors(default_return=None, log_error=True)
    def generate_integrity_report(self, asset: str, days_back: int = 30) -> Optional[IntegrityReport]:
        """إنتاج تقرير تكامل البيانات لزوج معين"""
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            end_date = datetime.now()
            
            # حساب العدد المتوقع للشموع
            expected_candles = int((end_date - start_date).total_seconds() / self.timeframe)
            
            # عد الشموع الموجودة
            actual_candles = historical_data_repo.count_candles_in_range(
                asset=asset,
                timeframe=self.timeframe,
                start_time=start_date,
                end_time=end_date
            )
            
            # اكتشاف الفجوات
            gaps = self.detect_gaps_for_asset(asset, start_date, end_date)
            missing_candles = sum(gap.missing_candles for gap in gaps)
            
            # إنشاء التقرير
            report = IntegrityReport(
                asset=asset,
                total_candles=actual_candles,
                expected_candles=expected_candles,
                missing_candles=missing_candles,
                gaps_found=gaps
            )
            
            # حفظ التقرير
            with self._lock:
                self.integrity_reports[asset] = report
            
            self.system_stats['integrity_checks'] += 1
            
            logger.debug(f"📋 تقرير تكامل {asset}: {report.integrity_score:.1f}% ({len(gaps)} فجوة)")
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنتاج تقرير التكامل لـ {asset}: {str(e)}")
            return None

    @handle_async_errors(default_return=0, log_error=True)
    async def auto_fill_detected_gaps(self, max_concurrent: int = None) -> int:
        """ملء الفجوات المكتشفة تلقائياً"""
        try:
            if not max_concurrent:
                max_concurrent = self.max_concurrent_fills

            # جمع جميع الفجوات القابلة للملء
            fillable_gaps = []

            with self._lock:
                for asset, gaps in self.detected_gaps.items():
                    for gap in gaps:
                        # ملء الفجوات الصغيرة والمتوسطة فقط
                        if gap.severity in ["LOW", "MEDIUM"] and gap.gap_duration_minutes <= (self.max_gap_hours * 60):
                            fillable_gaps.append(gap)

            if not fillable_gaps:
                logger.info("لا توجد فجوات قابلة للملء")
                return 0

            logger.info(f"🔧 بدء ملء {len(fillable_gaps)} فجوة تلقائياً")

            # ملء الفجوات بشكل متوازي
            semaphore = asyncio.Semaphore(max_concurrent)

            async def fill_with_semaphore(gap):
                async with semaphore:
                    return await self.fill_gap_async(gap)

            # تنفيذ الملء المتوازي
            tasks = [fill_with_semaphore(gap) for gap in fillable_gaps]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # حساب النتائج
            successful_fills = sum(1 for result in results if result is True)

            logger.info(f"✅ تم ملء {successful_fills}/{len(fillable_gaps)} فجوة بنجاح")

            return successful_fills

        except Exception as e:
            logger.error(f"خطأ في الملء التلقائي للفجوات: {str(e)}")
            return 0

    @handle_errors(default_return=False, log_error=True)
    def handle_connection_loss(self, asset: str = None) -> bool:
        """معالجة انقطاع الاتصال"""
        try:
            logger.warning(f"🔌 معالجة انقطاع الاتصال{' لـ ' + asset if asset else ''}")

            # تسجيل انقطاع الاتصال
            self.system_stats['reconnections'] += 1

            # إذا كان لزوج معين، فحص الفجوات الحديثة
            if asset:
                recent_gaps = self.detect_gaps_for_asset(
                    asset,
                    datetime.now() - timedelta(hours=1),
                    datetime.now()
                )

                if recent_gaps:
                    logger.warning(f"⚠️ اكتشاف {len(recent_gaps)} فجوة حديثة في {asset}")

                    # حفظ الفجوات للملء اللاحق
                    with self._lock:
                        self.detected_gaps[asset].extend(recent_gaps)

            # محاولة إعادة الاتصال
            return self._attempt_reconnection()

        except Exception as e:
            logger.error(f"خطأ في معالجة انقطاع الاتصال: {str(e)}")
            return False

    def _attempt_reconnection(self) -> bool:
        """محاولة إعادة الاتصال"""
        try:
            # استيراد مجمع البيانات للاختبار
            from .historical_collector import async_historical_collector

            # اختبار الاتصال بجلب بيانات بسيطة
            test_asset = self.currency_pairs[0]

            results = async_historical_collector.collect_and_calculate_indicators(
                assets=[test_asset],
                timeframe=self.timeframe,
                duration_hours=1
            )

            if test_asset in results and results[test_asset].get('success', False):
                logger.info("✅ تم إعادة الاتصال بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في إعادة الاتصال")
                return False

        except Exception as e:
            logger.error(f"خطأ في محاولة إعادة الاتصال: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_continuous_monitoring(self) -> bool:
        """بدء المراقبة المستمرة للتكامل"""
        try:
            if not self.auto_gap_detection:
                logger.warning("المراقبة التلقائية معطلة")
                return False

            logger.info(f"🔄 بدء المراقبة المستمرة كل {self.integrity_check_interval} ثانية")

            # بدء حلقة المراقبة
            asyncio.create_task(self._monitoring_loop())

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء المراقبة المستمرة: {str(e)}")
            return False

    async def _monitoring_loop(self):
        """حلقة المراقبة المستمرة"""
        try:
            while self.auto_gap_detection:
                try:
                    logger.info("🔍 بدء دورة فحص التكامل")

                    # فحص الفجوات
                    gaps = self.scan_all_assets_for_gaps(days_back=1)  # آخر يوم فقط

                    if gaps:
                        total_gaps = sum(len(asset_gaps) for asset_gaps in gaps.values())
                        logger.info(f"🔍 اكتشاف {total_gaps} فجوة جديدة")

                        # ملء الفجوات تلقائياً إذا كان مفعل
                        if self.auto_gap_filling:
                            filled = await self.auto_fill_detected_gaps()
                            logger.info(f"🔧 تم ملء {filled} فجوة تلقائياً")

                    # إنتاج تقارير التكامل لعينة من الأزواج
                    sample_assets = self.currency_pairs[:10]  # أول 10 أزواج
                    for asset in sample_assets:
                        self.generate_integrity_report(asset, days_back=7)

                    # انتظار حتى الدورة التالية
                    await asyncio.sleep(self.integrity_check_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة المراقبة: {str(e)}")
                    await asyncio.sleep(300)  # انتظار 5 دقائق عند الخطأ

        except Exception as e:
            logger.error(f"خطأ في حلقة المراقبة: {str(e)}")

    def stop_monitoring(self):
        """إيقاف المراقبة المستمرة"""
        self.auto_gap_detection = False
        logger.info("تم إيقاف المراقبة المستمرة")

    @handle_errors(default_return={}, log_error=True)
    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        try:
            runtime = (datetime.now() - self.system_stats['start_time']).total_seconds()

            # حساب إحصائيات الفجوات
            total_detected_gaps = sum(len(gaps) for gaps in self.detected_gaps.values())
            assets_with_gaps = len([asset for asset, gaps in self.detected_gaps.items() if gaps])

            # حساب إحصائيات التكامل
            integrity_scores = [report.integrity_score for report in self.integrity_reports.values()]
            avg_integrity = sum(integrity_scores) / len(integrity_scores) if integrity_scores else 100.0

            return {
                'system_overview': {
                    'runtime_hours': round(runtime / 3600, 2),
                    'monitored_assets': len(self.currency_pairs),
                    'auto_gap_detection': self.auto_gap_detection,
                    'auto_gap_filling': self.auto_gap_filling,
                    'check_interval_minutes': self.integrity_check_interval / 60
                },
                'gap_statistics': {
                    'total_gaps_detected': self.system_stats['gaps_detected'],
                    'total_gaps_resolved': self.system_stats['gaps_resolved'],
                    'current_detected_gaps': total_detected_gaps,
                    'assets_with_gaps': assets_with_gaps,
                    'resolution_rate': round((self.system_stats['gaps_resolved'] / max(self.system_stats['gaps_detected'], 1)) * 100, 2)
                },
                'integrity_statistics': {
                    'integrity_checks_performed': self.system_stats['integrity_checks'],
                    'average_integrity_score': round(avg_integrity, 2),
                    'reports_generated': len(self.integrity_reports)
                },
                'connection_statistics': {
                    'reconnection_attempts': self.system_stats['reconnections'],
                    'total_errors': self.system_stats['errors'],
                    'error_rate': round(self.system_stats['errors'] / max(runtime, 1), 4)
                },
                'performance': {
                    'gaps_per_hour': round(self.system_stats['gaps_detected'] / max(runtime / 3600, 1), 2),
                    'checks_per_hour': round(self.system_stats['integrity_checks'] / max(runtime / 3600, 1), 2),
                    'resolution_per_hour': round(self.system_stats['gaps_resolved'] / max(runtime / 3600, 1), 2)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات النظام: {str(e)}")
            return {}

    @handle_errors(default_return=[], log_error=True)
    def get_critical_gaps_report(self) -> List[Dict[str, Any]]:
        """تقرير الفجوات الحرجة التي تحتاج تدخل فوري"""
        try:
            critical_gaps = []

            with self._lock:
                for asset, gaps in self.detected_gaps.items():
                    for gap in gaps:
                        if gap.severity in ["HIGH", "CRITICAL"]:
                            critical_gaps.append({
                                'asset': gap.asset,
                                'severity': gap.severity,
                                'missing_candles': gap.missing_candles,
                                'gap_percentage': round(gap.gap_percentage, 2),
                                'duration_hours': round(gap.gap_duration_minutes / 60, 2),
                                'start_time': gap.start_time.isoformat(),
                                'end_time': gap.end_time.isoformat()
                            })

            # ترتيب حسب الشدة
            critical_gaps.sort(key=lambda x: (x['severity'] == 'CRITICAL', x['gap_percentage']), reverse=True)

            return critical_gaps

        except Exception as e:
            logger.error(f"خطأ في إنتاج تقرير الفجوات الحرجة: {str(e)}")
            return []

# إنشاء مثيل عام للاستخدام
gap_resolver = GapResolver()
