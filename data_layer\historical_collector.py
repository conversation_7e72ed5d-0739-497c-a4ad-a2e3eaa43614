"""
مجمع البيانات التاريخية المتوازي لنظام السكالبينغ
"""

import time
import asyncio
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from BinaryOptionsToolsV2.pocketoption import PocketOption, PocketOptionAsync

from config.trading_config import TradingConfig, default_trading_config
from config.currency_pairs import CURRENCY_PAIRS_70
from database.repository import historical_data_repo
from database.models import HistoricalData
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, ConnectionError, DataError
from utils.helpers import validate_candle_data, clean_candle_data, get_current_timestamp

logger = scalping_logger.get_logger("historical_collector")

class HistoricalDataCollector:
    """مجمع البيانات التاريخية"""
    
    def __init__(self, config: TradingConfig = None):
        self.config = config or default_trading_config
        self.api = None
        self.is_connected = False
        self.collection_stats = {
            'total_collected': 0,
            'successful_collections': 0,
            'failed_collections': 0,
            'last_collection_time': None
        }
    
    @handle_errors(raise_on_error=True)
    def initialize_connection(self):
        """تهيئة الاتصال مع Pocket Option"""
        try:
            if not self.config.pocket_option_ssid:
                raise ConnectionError("SSID غير موجود في الإعدادات")
            
            self.api = PocketOption(self.config.pocket_option_ssid)
            time.sleep(5)  # انتظار تأسيس الاتصال
            
            # اختبار الاتصال
            balance = self.api.balance()
            if balance is None:
                raise ConnectionError("فشل في الحصول على الرصيد")
            
            self.is_connected = True
            logger.info("تم تأسيس اتصال مجمع البيانات التاريخية")
            
        except Exception as e:
            self.is_connected = False
            logger.error(f"فشل في تهيئة اتصال البيانات التاريخية: {str(e)}")
            raise ConnectionError(f"فشل الاتصال: {str(e)}")
    
    @handle_errors(default_return=[], log_error=True)
    def collect_historical_data(self, asset: str, timeframe: int, duration_hours: int = 24) -> List[Dict[str, Any]]:
        """جمع البيانات التاريخية لأصل معين"""
        try:
            if not self.is_connected:
                self.initialize_connection()
            
            # حساب المدة بالثواني
            duration_seconds = duration_hours * 3600
            
            logger.info(f"بدء جمع البيانات التاريخية لـ {asset} - إطار زمني: {timeframe}s، مدة: {duration_hours}h")
            
            # جلب البيانات من API
            candles = self.api.get_candles(asset, timeframe, duration_seconds)
            
            if not candles:
                logger.warning(f"لم يتم جلب أي بيانات تاريخية لـ {asset}")
                self.collection_stats['failed_collections'] += 1
                return []
            
            # تنظيف البيانات
            cleaned_candles = clean_candle_data(candles)
            
            # إضافة timestamps إذا لم تكن موجودة
            current_time = get_current_timestamp()
            for i, candle in enumerate(cleaned_candles):
                if 'timestamp' not in candle:
                    candle['timestamp'] = current_time - timedelta(seconds=timeframe * (len(cleaned_candles) - i - 1))
            
            # تحديث الإحصائيات
            self.collection_stats['total_collected'] += len(cleaned_candles)
            self.collection_stats['successful_collections'] += 1
            self.collection_stats['last_collection_time'] = current_time
            
            logger.info(f"تم جمع {len(cleaned_candles)} شمعة تاريخية لـ {asset}")
            return cleaned_candles
            
        except Exception as e:
            logger.error(f"خطأ في جمع البيانات التاريخية لـ {asset}: {str(e)}")
            self.collection_stats['failed_collections'] += 1
            return []
    
    @handle_errors(default_return=False, log_error=True)
    def collect_and_store_data(self, asset: str, timeframe: int, duration_hours: int = 24) -> bool:
        """جمع وحفظ البيانات التاريخية"""
        try:
            # جمع البيانات
            candles = self.collect_historical_data(asset, timeframe, duration_hours)
            
            if not candles:
                return False
            
            # حفظ في قاعدة البيانات
            return self.store_candles_to_database(asset, candles, timeframe)
            
        except Exception as e:
            logger.error(f"خطأ في جمع وحفظ البيانات لـ {asset}: {str(e)}")
            return False
    
    @handle_errors(default_return=False, log_error=True)
    def store_candles_to_database(self, asset: str, candles: List[Dict[str, Any]], timeframe: int) -> bool:
        """حفظ الشموع في قاعدة البيانات"""
        try:
            if not candles:
                return False
            
            # التحقق من البيانات الموجودة لتجنب التكرار
            existing_timestamps = self.get_existing_timestamps(asset, timeframe)
            
            # فلترة البيانات الجديدة فقط
            new_candles = []
            for candle in candles:
                if validate_candle_data(candle):
                    # استخدام time إذا كان موجود، وإلا timestamp
                    candle_timestamp = candle.get('time', candle.get('timestamp'))
                    if isinstance(candle_timestamp, (int, float)):
                        candle_timestamp = datetime.fromtimestamp(candle_timestamp)

                    if candle_timestamp not in existing_timestamps:
                        new_candles.append(candle)
            
            if not new_candles:
                logger.info(f"لا توجد بيانات جديدة لحفظها لـ {asset}")
                return True
            
            # تحويل لتنسيق قاعدة البيانات
            db_candles = []
            for candle in new_candles:
                try:
                    # تحضير البيانات للنموذج
                    time_value = candle.get('time', candle.get('timestamp'))

                    # تحويل timestamp إلى datetime
                    if isinstance(time_value, str):
                        # إذا كان string، حاول تحويله إلى رقم أولاً
                        try:
                            time_value = float(time_value)
                        except ValueError:
                            # إذا فشل، حاول تحليله كـ ISO format
                            time_value = datetime.fromisoformat(time_value.replace('Z', '+00:00'))

                    if isinstance(time_value, (int, float)):
                        timestamp = datetime.fromtimestamp(time_value)
                    else:
                        timestamp = time_value

                    candle_for_db = {
                        'timestamp': timestamp,
                        'open': float(candle['open']),
                        'high': float(candle['high']),
                        'low': float(candle['low']),
                        'close': float(candle['close']),
                        'volume': float(candle.get('volume', 0))
                    }

                    db_candle = HistoricalData.from_candle_dict(candle_for_db, asset, timeframe)
                    db_candles.append(db_candle.to_dict())
                except Exception as e:
                    logger.debug(f"تجاهل شمعة غير صالحة: {str(e)}")
                    continue
            
            # حفظ في قاعدة البيانات باستخدام النظام المحسن
            success = historical_data_repo.store_candles_optimized(asset, new_candles, timeframe)
            
            if success:
                logger.info(f"تم حفظ {len(db_candles)} شمعة جديدة لـ {asset} في قاعدة البيانات")
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الشموع لـ {asset}: {str(e)}")
            return False
    
    @handle_errors(default_return=set(), log_error=True)
    def get_existing_timestamps(self, asset: str, timeframe: int) -> set:
        """الحصول على timestamps الموجودة في قاعدة البيانات"""
        try:
            # جلب آخر 1000 شمعة لتجنب التكرار
            existing_candles = historical_data_repo.get_latest_candles(asset, timeframe, 1000)
            return {candle.timestamp for candle in existing_candles}
        except Exception as e:
            logger.error(f"خطأ في جلب timestamps الموجودة لـ {asset}: {str(e)}")
            return set()
    
    @handle_errors(default_return={}, log_error=True)
    def collect_multiple_assets(self, assets: List[str], timeframe: int = 60, duration_hours: int = 24) -> Dict[str, bool]:
        """جمع البيانات التاريخية لعدة أصول"""
        results = {}

        logger.info(f"بدء جمع البيانات التاريخية لـ {len(assets)} أصل")

        for i, asset in enumerate(assets):
            try:
                logger.info(f"معالجة الأصل {i+1}/{len(assets)}: {asset}")

                success = self.collect_and_store_data(asset, timeframe, duration_hours)
                results[asset] = success

                # تأخير قصير بين الطلبات لتجنب إرهاق الخادم
                if i < len(assets) - 1:  # ليس الأصل الأخير
                    time.sleep(0.5)  # تقليل التأخير للمجمع المتزامن

            except Exception as e:
                logger.error(f"خطأ في معالجة {asset}: {str(e)}")
                results[asset] = False

        # إحصائيات النتائج
        successful = sum(1 for success in results.values() if success)
        logger.info(f"تم جمع البيانات بنجاح لـ {successful}/{len(assets)} أصل")

        return results

    @handle_errors(default_return={}, log_error=True)
    def collect_and_calculate_indicators(self, assets: List[str], timeframe: int = 60, duration_hours: int = 168) -> Dict[str, Dict[str, Any]]:
        """جمع البيانات التاريخية وحساب المؤشرات لجميع الأصول"""
        from indicators.ema_indicator import EMAIndicator
        from indicators.sma_indicator import SMAIndicator
        from indicators.rsi_indicator import RSI5, RSI14
        from indicators.macd_indicator import MACD
        from indicators.momentum_indicator import Momentum
        from indicators.bollinger_bands_indicator import BollingerBands
        from indicators.atr_indicator import ATR5, ATR14
        from indicators.heiken_ashi_indicator import HeikenAshi
        from indicators.zscore_indicator import ZScore
        from indicators.stochastic_indicator import Stochastic
        from indicators.williams_r_indicator import WilliamsR
        from database.repository import technical_indicator_repo

        # إنشاء جميع المؤشرات المطلوبة (15 مؤشر)
        indicators = {
            'EMA_5': EMAIndicator(5),
            'EMA_10': EMAIndicator(10),
            'EMA_21': EMAIndicator(21),
            'SMA_10': SMAIndicator(10),
            'RSI_5': RSI5(),
            'RSI_14': RSI14(),
            'MACD': MACD(),
            'Momentum': Momentum(),
            'BollingerBands': BollingerBands(),
            'ATR_5': ATR5(),
            'ATR_14': ATR14(),
            'HeikenAshi': HeikenAshi(),
            'ZScore': ZScore(),
            'Stochastic': Stochastic(),
            'WilliamsR': WilliamsR()
        }

        results = {}
        total_indicators_calculated = 0

        logger.info(f"بدء جمع البيانات وحساب المؤشرات لـ {len(assets)} أصل مع {len(indicators)} مؤشر")

        for i, asset in enumerate(assets):
            try:
                logger.info(f"معالجة الأصل {i+1}/{len(assets)}: {asset}")

                # جمع البيانات التاريخية أولاً
                data_success = self.collect_and_store_data(asset, timeframe, duration_hours)

                if not data_success:
                    logger.warning(f"فشل في جمع البيانات لـ {asset}")
                    results[asset] = {
                        'data_collection': False,
                        'indicators_calculated': 0,
                        'indicators_stored': 0,
                        'success': False
                    }
                    continue

                # جلب البيانات المخزنة لحساب المؤشرات
                candles = historical_data_repo.get_candles(asset, timeframe, limit=10080)  # 10,080 شمعة

                if len(candles) < 50:  # الحد الأدنى لحساب المؤشرات
                    logger.warning(f"بيانات غير كافية لحساب المؤشرات لـ {asset}: {len(candles)} شمعة")
                    results[asset] = {
                        'data_collection': True,
                        'indicators_calculated': 0,
                        'indicators_stored': 0,
                        'success': False,
                        'candles_count': len(candles)
                    }
                    continue

                # تحويل البيانات إلى التنسيق المطلوب
                candle_data = []
                for candle in sorted(candles, key=lambda x: x.timestamp):
                    candle_data.append({
                        'timestamp': candle.timestamp,
                        'open': float(candle.open_price),
                        'high': float(candle.high_price),
                        'low': float(candle.low_price),
                        'close': float(candle.close_price),
                        'volume': candle.volume or 0
                    })

                indicators_calculated = 0
                indicators_stored = 0

                # حساب وتخزين كل مؤشر
                for indicator_name, indicator in indicators.items():
                    try:
                        logger.debug(f"حساب {indicator_name} لـ {asset}")

                        # حساب المؤشر مع التحقق من صحة البيانات
                        result = indicator.calculate_with_validation(candle_data)

                        if result and result.get('values'):
                            indicators_calculated += 1

                            # تخزين قيم المؤشر في قاعدة البيانات
                            for j, value in enumerate(result['values']):
                                if value is not None and j < len(candle_data):
                                    candle_timestamp = candle_data[j]['timestamp']

                                    # إنشاء سجل المؤشر
                                    indicator_record = {
                                        'asset': asset,
                                        'timestamp': candle_timestamp,
                                        'timeframe': timeframe,
                                        'indicator_name': indicator_name,
                                        'indicator_value': float(value),
                                        'indicator_signal': result.get('signal', 'NEUTRAL'),
                                        'parameters': {
                                            'period': getattr(indicator, 'period', None),
                                            'calculation_time_ms': result.get('calculation_time_ms', 0)
                                        },
                                        'confidence_score': result.get('confidence_score', 0),
                                        'calculation_time_ms': result.get('calculation_time_ms', 0)
                                    }

                                    # حفظ في قاعدة البيانات
                                    saved_indicator = technical_indicator_repo.create(**indicator_record)
                                    if saved_indicator:
                                        indicators_stored += 1

                            total_indicators_calculated += 1
                            logger.debug(f"تم حساب وتخزين {indicator_name} لـ {asset}: {len(result['values'])} قيمة")

                    except Exception as e:
                        logger.error(f"خطأ في حساب {indicator_name} لـ {asset}: {str(e)}")
                        continue

                results[asset] = {
                    'data_collection': True,
                    'indicators_calculated': indicators_calculated,
                    'indicators_stored': indicators_stored,
                    'candles_count': len(candle_data),
                    'success': indicators_calculated > 0
                }

                logger.info(f"اكتمل معالجة {asset}: {indicators_calculated}/{len(indicators)} مؤشر، {indicators_stored} قيمة مخزنة")

                # تأخير بين الأصول
                if i < len(assets) - 1:
                    time.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في معالجة {asset}: {str(e)}")
                results[asset] = {
                    'data_collection': False,
                    'indicators_calculated': 0,
                    'indicators_stored': 0,
                    'success': False,
                    'error': str(e)
                }

        # إحصائيات النتائج
        successful_assets = sum(1 for result in results.values() if result['success'])
        total_indicators_stored = sum(result.get('indicators_stored', 0) for result in results.values())

        logger.info(f"اكتمل معالجة جميع الأصول:")
        logger.info(f"- الأصول الناجحة: {successful_assets}/{len(assets)}")
        logger.info(f"- إجمالي المؤشرات المحسوبة: {total_indicators_calculated}")
        logger.info(f"- إجمالي القيم المخزنة: {total_indicators_stored}")

        return results
    
    @handle_errors(default_return=None, log_error=True)
    def get_data_gap_analysis(self, asset: str, timeframe: int) -> Optional[Dict[str, Any]]:
        """تحليل الفجوات في البيانات"""
        try:
            # جلب البيانات الموجودة
            existing_candles = historical_data_repo.get_candles(asset, timeframe, limit=10000)
            
            if not existing_candles:
                return {
                    'has_data': False,
                    'total_candles': 0,
                    'gaps': [],
                    'coverage_percentage': 0
                }
            
            # ترتيب البيانات حسب الوقت
            sorted_candles = sorted(existing_candles, key=lambda x: x.timestamp)
            
            # البحث عن الفجوات
            gaps = []
            expected_interval = timedelta(seconds=timeframe)
            
            for i in range(1, len(sorted_candles)):
                current_time = sorted_candles[i].timestamp
                previous_time = sorted_candles[i-1].timestamp
                actual_interval = current_time - previous_time
                
                if actual_interval > expected_interval * 1.5:  # فجوة أكبر من 1.5 ضعف الفترة المتوقعة
                    gap_duration = actual_interval.total_seconds()
                    missing_candles = int(gap_duration / timeframe) - 1
                    
                    gaps.append({
                        'start_time': previous_time,
                        'end_time': current_time,
                        'duration_seconds': gap_duration,
                        'missing_candles': missing_candles
                    })
            
            # حساب نسبة التغطية
            if sorted_candles:
                total_duration = (sorted_candles[-1].timestamp - sorted_candles[0].timestamp).total_seconds()
                expected_candles = int(total_duration / timeframe)
                coverage_percentage = (len(sorted_candles) / expected_candles) * 100 if expected_candles > 0 else 0
            else:
                coverage_percentage = 0
            
            return {
                'has_data': True,
                'total_candles': len(sorted_candles),
                'first_candle_time': sorted_candles[0].timestamp if sorted_candles else None,
                'last_candle_time': sorted_candles[-1].timestamp if sorted_candles else None,
                'gaps': gaps,
                'total_gaps': len(gaps),
                'coverage_percentage': round(coverage_percentage, 2)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل فجوات البيانات لـ {asset}: {str(e)}")
            return None
    
    @handle_errors(default_return=False, log_error=True)
    def fill_data_gaps(self, asset: str, timeframe: int, max_gap_hours: int = 24) -> bool:
        """ملء الفجوات في البيانات"""
        try:
            # تحليل الفجوات
            gap_analysis = self.get_data_gap_analysis(asset, timeframe)
            
            if not gap_analysis or not gap_analysis['gaps']:
                logger.info(f"لا توجد فجوات في البيانات لـ {asset}")
                return True
            
            logger.info(f"وجدت {len(gap_analysis['gaps'])} فجوة في بيانات {asset}")
            
            filled_gaps = 0
            for gap in gap_analysis['gaps']:
                gap_duration_hours = gap['duration_seconds'] / 3600
                
                # تجاهل الفجوات الكبيرة جداً
                if gap_duration_hours > max_gap_hours:
                    logger.warning(f"تجاهل فجوة كبيرة: {gap_duration_hours:.1f} ساعة")
                    continue
                
                # محاولة ملء الفجوة
                try:
                    gap_candles = self.api.get_candles(asset, timeframe, int(gap['duration_seconds']))
                    
                    if gap_candles:
                        # فلترة البيانات للفترة المطلوبة
                        filtered_candles = []
                        for candle in gap_candles:
                            candle_time = candle.get('timestamp', datetime.now())
                            if gap['start_time'] <= candle_time <= gap['end_time']:
                                filtered_candles.append(candle)
                        
                        if filtered_candles:
                            success = self.store_candles_to_database(asset, filtered_candles, timeframe)
                            if success:
                                filled_gaps += 1
                                logger.info(f"تم ملء فجوة: {len(filtered_candles)} شمعة")
                    
                    # تأخير بين الطلبات
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"خطأ في ملء فجوة: {str(e)}")
                    continue
            
            logger.info(f"تم ملء {filled_gaps}/{len(gap_analysis['gaps'])} فجوة لـ {asset}")
            return filled_gaps > 0
            
        except Exception as e:
            logger.error(f"خطأ في ملء فجوات البيانات لـ {asset}: {str(e)}")
            return False
    
    def get_collection_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الجمع"""
        return self.collection_stats.copy()
    
    def reset_statistics(self):
        """إعادة تعيين الإحصائيات"""
        self.collection_stats = {
            'total_collected': 0,
            'successful_collections': 0,
            'failed_collections': 0,
            'last_collection_time': None
        }
        logger.info("تم إعادة تعيين إحصائيات الجمع")

class AsyncParallelHistoricalCollector:
    """مجمع البيانات التاريخية المتوازي غير المتزامن للـ70 زوج عملة"""

    def __init__(self, config: TradingConfig = None, max_concurrent: int = 25):
        self.config = config or default_trading_config
        self.max_concurrent = max_concurrent
        self.currency_pairs = CURRENCY_PAIRS_70.copy()  # جميع الأزواج الـ70
        self.collection_results = {}
        self.collection_stats = {
            'total_pairs': len(self.currency_pairs),
            'successful_pairs': 0,
            'failed_pairs': 0,
            'total_candles_collected': 0,
            'start_time': None,
            'end_time': None,
            'duration_seconds': 0
        }
        logger.info(f"تم إنشاء مجمع البيانات المتوازي غير المتزامن لـ {len(self.currency_pairs)} زوج عملة")

    @handle_errors(default_return=False, log_error=True)
    async def collect_single_pair_data_async(self, pair: str, timeframe: int = 60, candles_count: int = 10080) -> Dict[str, Any]:
        """جمع البيانات لزوج واحد بشكل غير متزامن"""
        try:
            logger.info(f"🔄 بدء جمع البيانات غير المتزامن لـ {pair}")

            # التحقق من وجود SSID
            if not self.config.pocket_option_ssid:
                raise ConnectionError("SSID غير موجود في الإعدادات")

            # إنشاء عميل غير متزامن
            api = PocketOptionAsync(self.config.pocket_option_ssid)
            await asyncio.sleep(3)  # انتظار تأسيس الاتصال

            # حساب المدة المطلوبة بالساعات
            duration_hours = (candles_count * timeframe) // 3600
            if duration_hours < 1:
                duration_hours = 1

            logger.info(f"📊 جلب {candles_count} شمعة لـ {pair} (مدة: {duration_hours} ساعة)")

            # جمع البيانات التاريخية
            candles = await api.get_candles(pair, timeframe, duration_hours * 3600)

            if not candles or len(candles) == 0:
                logger.warning(f"⚠️  لم يتم جلب أي بيانات لـ {pair}")
                return {
                    'pair': pair,
                    'success': False,
                    'candles_count': 0,
                    'error': 'لم يتم جلب أي بيانات من API'
                }

            # تنظيف وتحضير البيانات
            cleaned_candles = []
            current_time = datetime.now()

            for i, candle in enumerate(candles):
                try:
                    # إضافة timestamp إذا لم يكن موجوداً
                    if 'timestamp' not in candle or candle['timestamp'] is None:
                        candle['timestamp'] = current_time - timedelta(seconds=timeframe * (len(candles) - i - 1))

                    # التحقق من صحة البيانات
                    if all(key in candle for key in ['open', 'high', 'low', 'close']):
                        cleaned_candles.append(candle)
                except Exception as e:
                    logger.debug(f"تجاهل شمعة غير صالحة لـ {pair}: {str(e)}")
                    continue

            if not cleaned_candles:
                return {
                    'pair': pair,
                    'success': False,
                    'candles_count': 0,
                    'error': 'جميع الشموع غير صالحة'
                }

            # حفظ البيانات في قاعدة البيانات
            success = await self._store_candles_async(pair, cleaned_candles, timeframe)

            result = {
                'pair': pair,
                'success': success,
                'candles_count': len(cleaned_candles),
                'timeframe': timeframe,
                'collection_time': datetime.now()
            }

            if success:
                logger.info(f"✅ تم جمع وحفظ {len(cleaned_candles)} شمعة لـ {pair}")
            else:
                result['error'] = 'فشل في حفظ البيانات'
                logger.error(f"❌ فشل في حفظ البيانات لـ {pair}")

            return result

        except Exception as e:
            error_msg = f"خطأ في جمع البيانات لـ {pair}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'pair': pair,
                'success': False,
                'candles_count': 0,
                'error': error_msg
            }

    async def _store_candles_async(self, pair: str, candles: List[Dict[str, Any]], timeframe: int) -> bool:
        """حفظ الشموع في قاعدة البيانات بشكل غير متزامن"""
        try:
            if not candles:
                return False

            # التحقق من البيانات الموجودة لتجنب التكرار
            existing_timestamps = set()
            try:
                existing_candles = historical_data_repo.get_latest_candles(pair, timeframe, 1000)
                existing_timestamps = {candle.timestamp for candle in existing_candles}
            except Exception as e:
                logger.debug(f"تعذر جلب البيانات الموجودة لـ {pair}: {str(e)}")

            # فلترة البيانات الجديدة فقط
            new_candles = []
            for candle in candles:
                try:
                    candle_timestamp = candle['timestamp']
                    if isinstance(candle_timestamp, str):
                        candle_timestamp = datetime.fromisoformat(candle_timestamp.replace('Z', '+00:00'))

                    if candle_timestamp not in existing_timestamps:
                        new_candles.append(candle)
                except Exception as e:
                    logger.debug(f"تجاهل شمعة غير صالحة: {str(e)}")
                    continue

            if not new_candles:
                logger.info(f"لا توجد بيانات جديدة لحفظها لـ {pair}")
                return True

            # تحويل لتنسيق قاعدة البيانات
            db_candles = []
            for candle in new_candles:
                try:
                    # تحويل أسماء المفاتيح لتتوافق مع نموذج قاعدة البيانات
                    normalized_candle = {
                        'timestamp': candle['timestamp'],
                        'open': candle.get('open', candle.get('open_price', 0)),
                        'high': candle.get('high', candle.get('high_price', 0)),
                        'low': candle.get('low', candle.get('low_price', 0)),
                        'close': candle.get('close', candle.get('close_price', 0)),
                        'volume': candle.get('volume', 0)
                    }

                    db_candle = HistoricalData.from_candle_dict(normalized_candle, pair, timeframe)
                    db_candles.append(db_candle.to_dict())
                except Exception as e:
                    logger.debug(f"تعذر تحويل شمعة لـ {pair}: {str(e)}")
                    continue

            if not db_candles:
                return False

            # حفظ في قاعدة البيانات باستخدام النظام المحسن
            success = historical_data_repo.store_candles_optimized(pair, new_candles, timeframe)

            if success:
                logger.info(f"💾 تم حفظ {len(new_candles)} شمعة جديدة لـ {pair}")

                # صيانة حد FIFO (10,080 شمعة)
                await asyncio.sleep(0.1)  # تأخير قصير
                fifo_success = historical_data_repo.maintain_fifo_limit(pair, 10080, timeframe)
                if fifo_success:
                    logger.debug(f"🔄 تم تطبيق صيانة FIFO لـ {pair}")

            return success

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الشموع لـ {pair}: {str(e)}")
            return False

    @handle_errors(default_return={}, log_error=True)
    async def collect_all_pairs_async(self, timeframe: int = 60, candles_count: int = 10080) -> Dict[str, Any]:
        """جمع البيانات لجميع الأزواج الـ70 بشكل غير متزامن ومتوازي"""
        try:
            logger.info("🚀 بدء جمع البيانات غير المتزامن والمتوازي")
            logger.info(f"📊 الإعدادات:")
            logger.info(f"   - عدد الأزواج: {len(self.currency_pairs)}")
            logger.info(f"   - الأزواج: {', '.join(self.currency_pairs[:10])}{'...' if len(self.currency_pairs) > 10 else ''}")
            logger.info(f"   - الإطار الزمني: {timeframe} ثانية")
            logger.info(f"   - عدد الشموع المطلوبة: {candles_count}")
            logger.info(f"   - الحد الأقصى للمهام المتزامنة: {self.max_concurrent}")

            self.collection_stats['start_time'] = datetime.now()

            # إنشاء semaphore للتحكم في عدد المهام المتوازية غير المتزامنة
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def collect_with_semaphore(pair):
                async with semaphore:
                    return await self.collect_single_pair_data_async(pair, timeframe, candles_count)

            # إنشاء مهام لجميع الأزواج
            tasks = [collect_with_semaphore(pair) for pair in self.currency_pairs]

            # تنفيذ المهام مع تتبع التقدم
            completed_count = 0
            total_count = len(tasks)

            for coro in asyncio.as_completed(tasks):
                try:
                    result = await coro
                    pair = result['pair']
                    self.collection_results[pair] = result

                    # تحديث الإحصائيات
                    if result['success']:
                        self.collection_stats['successful_pairs'] += 1
                        self.collection_stats['total_candles_collected'] += result['candles_count']
                        status_icon = "✅"
                    else:
                        self.collection_stats['failed_pairs'] += 1
                        status_icon = "❌"

                    completed_count += 1
                    progress_percent = (completed_count / total_count) * 100

                    logger.info(f"📈 التقدم: {completed_count}/{total_count} ({progress_percent:.1f}%) - {pair}: {status_icon}")

                except Exception as e:
                    logger.error(f"❌ خطأ في معالجة مهمة: {str(e)}")
                    self.collection_stats['failed_pairs'] += 1

            # إنهاء الإحصائيات
            self.collection_stats['end_time'] = datetime.now()
            self.collection_stats['duration_seconds'] = (
                self.collection_stats['end_time'] - self.collection_stats['start_time']
            ).total_seconds()

            # طباعة الملخص النهائي
            self._print_collection_summary()

            return self.collection_results

        except Exception as e:
            logger.error(f"❌ خطأ في الجمع غير المتزامن: {str(e)}")
            return {}

    def collect_all_pairs_parallel(self, timeframe: int = 60, candles_count: int = 10080) -> Dict[str, Any]:
        """دالة wrapper للتشغيل المتزامن للجمع غير المتزامن"""
        try:
            # تشغيل الدالة غير المتزامنة
            return asyncio.run(self.collect_all_pairs_async(timeframe, candles_count))
        except Exception as e:
            logger.error(f"❌ خطأ في wrapper المتزامن: {str(e)}")
            return {}

    def _print_collection_summary(self):
        """طباعة ملخص عملية الجمع"""
        stats = self.collection_stats

        logger.info("=" * 60)
        logger.info("ملخص عملية جمع البيانات المتوازية")
        logger.info("=" * 60)
        logger.info(f"إجمالي الأزواج: {stats['total_pairs']}")
        logger.info(f"نجح: {stats['successful_pairs']}")
        logger.info(f"فشل: {stats['failed_pairs']}")
        logger.info(f"نسبة النجاح: {(stats['successful_pairs']/stats['total_pairs']*100):.1f}%")
        logger.info(f"إجمالي الشموع المجمعة: {stats['total_candles_collected']:,}")
        logger.info(f"مدة العملية: {stats['duration_seconds']:.1f} ثانية")
        logger.info(f"متوسط الوقت لكل زوج: {stats['duration_seconds']/stats['total_pairs']:.1f} ثانية")

        # طباعة الأزواج الفاشلة
        failed_pairs = [pair for pair, result in self.collection_results.items() if not result['success']]
        if failed_pairs:
            logger.warning(f"الأزواج الفاشلة ({len(failed_pairs)}): {', '.join(failed_pairs)}")

        logger.info("=" * 60)

    def get_collection_report(self) -> Dict[str, Any]:
        """الحصول على تقرير مفصل لعملية الجمع"""
        return {
            'statistics': self.collection_stats.copy(),
            'results': self.collection_results.copy(),
            'successful_pairs': [pair for pair, result in self.collection_results.items() if result['success']],
            'failed_pairs': [pair for pair, result in self.collection_results.items() if not result['success']],
            'total_candles_by_pair': {pair: result['candles_count'] for pair, result in self.collection_results.items()}
        }

    @handle_errors(default_return=False, log_error=True)
    def retry_failed_pairs(self, timeframe: int = 60, candles_count: int = 10080) -> bool:
        """إعادة محاولة جمع البيانات للأزواج الفاشلة"""
        try:
            failed_pairs = [pair for pair, result in self.collection_results.items() if not result['success']]

            if not failed_pairs:
                logger.info("لا توجد أزواج فاشلة لإعادة المحاولة")
                return True

            logger.info(f"إعادة محاولة جمع البيانات لـ {len(failed_pairs)} زوج فاشل")

            retry_results = {}
            for pair in failed_pairs:
                logger.info(f"إعادة محاولة: {pair}")
                result = self.collect_single_pair_data(pair, timeframe, candles_count)
                retry_results[pair] = result

                # تحديث النتائج الأصلية
                if result['success']:
                    self.collection_results[pair] = result
                    self.collection_stats['successful_pairs'] += 1
                    self.collection_stats['failed_pairs'] -= 1
                    self.collection_stats['total_candles_collected'] += result['candles_count']

                # تأخير بين المحاولات
                time.sleep(2)

            successful_retries = sum(1 for result in retry_results.values() if result['success'])
            logger.info(f"نجحت إعادة المحاولة لـ {successful_retries}/{len(failed_pairs)} زوج")

            return successful_retries > 0

        except Exception as e:
            logger.error(f"خطأ في إعادة المحاولة: {str(e)}")
            return False

# إنشاء instances عامة
historical_collector = HistoricalDataCollector()
parallel_collector = AsyncParallelHistoricalCollector()

class AsyncHistoricalCollectorWrapper:
    """Wrapper لجعل المجمع غير المتزامن يعمل مع الواجهة المتزامنة"""

    def __init__(self):
        self.async_collector = parallel_collector

    def collect_and_calculate_indicators(self, assets: List[str], timeframe: int = 60, duration_hours: int = 24) -> Dict[str, Any]:
        """دالة wrapper لجمع البيانات وحساب المؤشرات بشكل غير متزامن"""
        try:
            logger.info(f"🚀 بدء جمع البيانات غير المتزامن لـ {len(assets)} أصل")

            # حساب عدد الشموع المطلوبة
            candles_count = min(10080, (duration_hours * 3600) // timeframe)

            # تشغيل المجمع غير المتزامن
            results = self.async_collector.collect_all_pairs_parallel(timeframe, candles_count)

            if not results:
                logger.error("❌ فشل في جمع البيانات")
                return {}

            # فلترة النتائج للأصول المطلوبة فقط
            filtered_results = {}
            for asset in assets:
                if asset in self.async_collector.collection_results:
                    filtered_results[asset] = self.async_collector.collection_results[asset]

            logger.info(f"✅ تم جمع البيانات بنجاح لـ {len(filtered_results)} أصل")
            return filtered_results

        except Exception as e:
            logger.error(f"❌ خطأ في wrapper جمع البيانات: {str(e)}")
            return {}

    def collect_multiple_assets(self, assets: List[str], timeframe: int = 60, duration_hours: int = 24) -> Dict[str, bool]:
        """دالة wrapper لجمع البيانات لعدة أصول"""
        try:
            results = self.collect_and_calculate_indicators(assets, timeframe, duration_hours)

            # تحويل النتائج إلى تنسيق bool
            bool_results = {}
            for asset in assets:
                bool_results[asset] = asset in results and results[asset].get('success', False)

            return bool_results

        except Exception as e:
            logger.error(f"❌ خطأ في wrapper جمع الأصول المتعددة: {str(e)}")
            return {asset: False for asset in assets}

    async def collect_single_pair_data_async(self, asset: str, timeframe: int = 60, duration_hours: int = 24) -> Dict[str, Any]:
        """جمع البيانات لزوج واحد بشكل غير متزامن"""
        try:
            logger.info(f"🔄 بدء جمع البيانات غير المتزامن لـ {asset}")

            # حساب عدد الشموع المطلوبة
            candles_count = min(10080, (duration_hours * 3600) // timeframe)

            # جمع البيانات للزوج الواحد
            result = await self.async_collector.collect_single_pair_data_async(asset, timeframe, candles_count)

            if result and result.get('success', False):
                logger.info(f"✅ تم جمع البيانات بنجاح لـ {asset}")
                return result
            else:
                logger.warning(f"⚠️ فشل في جمع البيانات لـ {asset}")
                return {'success': False, 'asset': asset}

        except Exception as e:
            logger.error(f"❌ خطأ في جمع البيانات غير المتزامن لـ {asset}: {str(e)}")
            return {'success': False, 'asset': asset, 'error': str(e)}

# إنشاء wrapper للتوافق مع الكود الحالي
async_historical_collector = AsyncHistoricalCollectorWrapper()

# تكامل مع نظام FIFO
def integrate_fifo_with_collectors():
    """تكامل نظام FIFO مع مجمعات البيانات"""
    try:
        # استيراد مدير FIFO (تأخير الاستيراد لتجنب circular import)
        from .fifo_manager import fifo_manager

        # إضافة callback لتسجيل الشموع الجديدة
        def on_candles_added(asset: str, count: int):
            """callback عند إضافة شموع جديدة"""
            fifo_manager.add_new_candles(asset, count)

        # ربط callback مع المجمعات
        historical_collector.on_candles_added = on_candles_added
        parallel_collector.on_candles_added = on_candles_added

        logger.info("تم تكامل نظام FIFO مع مجمعات البيانات")
        return True

    except Exception as e:
        logger.error(f"خطأ في تكامل FIFO: {str(e)}")
        return False

# تفعيل التكامل
integrate_fifo_with_collectors()
