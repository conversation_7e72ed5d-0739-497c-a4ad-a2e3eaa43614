"""
فلتر الاحتمالات التاريخية - المرحلة الرابعة
Historical Probability Filter for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("historical_probability_filter")

class SignalType(Enum):
    """نوع الإشارة"""
    BUY = "BUY"
    SELL = "SELL"
    NEUTRAL = "NEUTRAL"

class OutcomeType(Enum):
    """نوع النتيجة"""
    WIN = "WIN"
    LOSS = "LOSS"
    DRAW = "DRAW"

class ProbabilityLevel(Enum):
    """مستوى الاحتمالية"""
    VERY_HIGH = "VERY_HIGH"      # 85%+
    HIGH = "HIGH"                # 75-84%
    MODERATE = "MODERATE"        # 65-74%
    LOW = "LOW"                  # 55-64%
    VERY_LOW = "VERY_LOW"        # <55%

@dataclass
class HistoricalSignal:
    """إشارة تاريخية"""
    timestamp: datetime
    signal_type: SignalType
    signal_strength: float
    confidence: float
    
    # معايير الإشارة
    technical_score: float
    behavioral_score: float
    market_conditions: Dict[str, Any]
    
    # النتيجة
    outcome: Optional[OutcomeType] = None
    profit_loss: Optional[float] = None
    duration_minutes: Optional[int] = None
    
    # معلومات إضافية
    metadata: Dict[str, Any] = None

@dataclass
class ProbabilityAnalysis:
    """تحليل الاحتمالية"""
    signal_type: SignalType
    success_probability: float
    confidence_level: ProbabilityLevel
    
    # إحصائيات تاريخية
    total_signals: int
    successful_signals: int
    failed_signals: int
    average_profit: float
    average_loss: float
    
    # تحليل الظروف
    similar_conditions_count: int
    condition_match_score: float
    
    # توصيات
    is_recommended: bool
    risk_reward_ratio: float
    expected_value: float
    
    # وصف التحليل
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class ProbabilityFilterResult:
    """نتيجة فلتر الاحتمالية"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # تحليل الاحتمالية
    probability_analysis: ProbabilityAnalysis
    
    # مقارنة مع الظروف المشابهة
    similar_scenarios: List[HistoricalSignal]
    
    # التقييم الإجمالي
    overall_probability: float
    recommendation_strength: float
    filter_passed: bool
    
    # توصيات
    recommended_action: str
    position_size_multiplier: float
    risk_assessment: str
    recommendations: List[str]

class HistoricalProbabilityFilter:
    """فلتر الاحتمالات التاريخية"""
    
    def __init__(self):
        self.historical_signals = {}  # تخزين الإشارات التاريخية لكل أصل
        self.min_historical_samples = 50  # الحد الأدنى للعينات التاريخية
        self.similarity_threshold = 0.7  # عتبة التشابه
        self.min_success_rate = 0.65  # الحد الأدنى لمعدل النجاح
        self.lookback_days = 30  # عدد الأيام للبحث في التاريخ
        
        logger.info("تم تهيئة فلتر الاحتمالات التاريخية")

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_historical_probability(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[ProbabilityFilterResult]:
        """تحليل الاحتمالات التاريخية"""
        # تحويل البيانات للتنسيق المطلوب
        current_signal = {'signal_type': 'NEUTRAL', 'strength': 0.5}
        market_conditions = {'volatility': 0.5, 'trend': 'NEUTRAL'}
        return await self.analyze_signal_probability(asset, current_signal, market_conditions)

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_signal_probability(self, asset: str, current_signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> Optional[ProbabilityFilterResult]:
        """تحليل احتمالية نجاح الإشارة"""
        start_time = time.time()
        
        try:
            logger.info(f"بدء تحليل احتمالية الإشارة لـ {asset}")
            
            # 1. البحث عن الإشارات المشابهة
            similar_signals = await self._find_similar_signals(asset, current_signal, market_conditions)
            
            if len(similar_signals) < self.min_historical_samples:
                logger.warning(f"عدد العينات التاريخية غير كافي لـ {asset}: {len(similar_signals)}")
                return None
            
            # 2. تحليل الاحتمالية
            probability_analysis = await self._analyze_probability(current_signal, similar_signals, market_conditions)
            
            # 3. تقييم التوصية
            recommendation_assessment = self._evaluate_recommendation(probability_analysis)
            
            # 4. إنتاج التوصيات
            recommendations = self._generate_probability_recommendations(probability_analysis, recommendation_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = ProbabilityFilterResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                probability_analysis=probability_analysis,
                similar_scenarios=similar_signals[:10],  # أفضل 10 سيناريوهات مشابهة
                overall_probability=probability_analysis.success_probability,
                recommendation_strength=recommendation_assessment['strength'],
                filter_passed=recommendation_assessment['passed'],
                recommended_action=recommendation_assessment['action'],
                position_size_multiplier=recommendation_assessment['position_multiplier'],
                risk_assessment=recommendation_assessment['risk'],
                recommendations=recommendations
            )
            
            logger.info(f"تم إكمال تحليل الاحتمالية لـ {asset}: {probability_analysis.success_probability:.1f}% نجاح")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل احتمالية الإشارة لـ {asset}: {str(e)}")
            return None

    async def _find_similar_signals(self, asset: str, current_signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> List[HistoricalSignal]:
        """البحث عن الإشارات المشابهة"""
        try:
            if asset not in self.historical_signals:
                return []
            
            historical_data = self.historical_signals[asset]
            similar_signals = []
            
            # تحديد النطاق الزمني للبحث
            cutoff_date = datetime.now() - timedelta(days=self.lookback_days)
            
            for historical_signal in historical_data:
                if historical_signal.timestamp < cutoff_date:
                    continue
                
                # حساب درجة التشابه
                similarity_score = self._calculate_similarity(current_signal, historical_signal, market_conditions)
                
                if similarity_score >= self.similarity_threshold:
                    similar_signals.append(historical_signal)
            
            # ترتيب حسب درجة التشابه
            similar_signals.sort(key=lambda x: self._calculate_similarity(current_signal, x, market_conditions), reverse=True)
            
            return similar_signals
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن الإشارات المشابهة: {str(e)}")
            return []

    def _calculate_similarity(self, current_signal: Dict[str, Any], historical_signal: HistoricalSignal, market_conditions: Dict[str, Any]) -> float:
        """حساب درجة التشابه بين الإشارات"""
        try:
            similarity_score = 0.0
            total_weight = 0.0
            
            # 1. تشابه نوع الإشارة (وزن 30%)
            signal_weight = 0.3
            if current_signal.get('signal_type') == historical_signal.signal_type.value:
                similarity_score += signal_weight
            total_weight += signal_weight
            
            # 2. تشابه قوة الإشارة (وزن 25%)
            strength_weight = 0.25
            current_strength = current_signal.get('signal_strength', 0)
            historical_strength = historical_signal.signal_strength
            
            if current_strength > 0 and historical_strength > 0:
                strength_diff = abs(current_strength - historical_strength) / max(current_strength, historical_strength)
                strength_similarity = max(0, 1 - strength_diff)
                similarity_score += strength_similarity * strength_weight
            total_weight += strength_weight
            
            # 3. تشابه الثقة (وزن 20%)
            confidence_weight = 0.2
            current_confidence = current_signal.get('confidence', 0)
            historical_confidence = historical_signal.confidence
            
            if current_confidence > 0 and historical_confidence > 0:
                confidence_diff = abs(current_confidence - historical_confidence) / max(current_confidence, historical_confidence)
                confidence_similarity = max(0, 1 - confidence_diff)
                similarity_score += confidence_similarity * confidence_weight
            total_weight += confidence_weight
            
            # 4. تشابه ظروف السوق (وزن 25%)
            market_weight = 0.25
            market_similarity = self._calculate_market_conditions_similarity(market_conditions, historical_signal.market_conditions)
            similarity_score += market_similarity * market_weight
            total_weight += market_weight
            
            # تطبيع النتيجة
            if total_weight > 0:
                return similarity_score / total_weight
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"خطأ في حساب درجة التشابه: {str(e)}")
            return 0.0

    def _calculate_market_conditions_similarity(self, current_conditions: Dict[str, Any], historical_conditions: Dict[str, Any]) -> float:
        """حساب تشابه ظروف السوق"""
        try:
            if not current_conditions or not historical_conditions:
                return 0.5

            similarity_factors = []

            # مقارنة التقلبات
            current_volatility = current_conditions.get('volatility', 0)
            historical_volatility = historical_conditions.get('volatility', 0)
            if current_volatility > 0 and historical_volatility > 0:
                volatility_diff = abs(current_volatility - historical_volatility) / max(current_volatility, historical_volatility)
                volatility_similarity = max(0, 1 - volatility_diff)
                similarity_factors.append(volatility_similarity)

            # مقارنة الاتجاه
            current_trend = current_conditions.get('trend_direction', 'NEUTRAL')
            historical_trend = historical_conditions.get('trend_direction', 'NEUTRAL')
            if current_trend == historical_trend:
                similarity_factors.append(1.0)
            else:
                similarity_factors.append(0.3)

            # مقارنة الحجم
            current_volume = current_conditions.get('volume_ratio', 1.0)
            historical_volume = historical_conditions.get('volume_ratio', 1.0)
            if current_volume > 0 and historical_volume > 0:
                volume_diff = abs(current_volume - historical_volume) / max(current_volume, historical_volume)
                volume_similarity = max(0, 1 - volume_diff)
                similarity_factors.append(volume_similarity)

            # مقارنة وقت التداول
            current_hour = current_conditions.get('trading_hour', 12)
            historical_hour = historical_conditions.get('trading_hour', 12)
            hour_diff = abs(current_hour - historical_hour)
            if hour_diff <= 2:
                similarity_factors.append(1.0)
            elif hour_diff <= 4:
                similarity_factors.append(0.7)
            else:
                similarity_factors.append(0.3)

            return statistics.mean(similarity_factors) if similarity_factors else 0.5

        except Exception as e:
            logger.error(f"خطأ في حساب تشابه ظروف السوق: {str(e)}")
            return 0.5

    async def _analyze_probability(self, current_signal: Dict[str, Any], similar_signals: List[HistoricalSignal], market_conditions: Dict[str, Any]) -> ProbabilityAnalysis:
        """تحليل الاحتمالية"""
        try:
            signal_type = SignalType(current_signal.get('signal_type', 'NEUTRAL'))

            # حساب الإحصائيات الأساسية
            total_signals = len(similar_signals)
            successful_signals = len([s for s in similar_signals if s.outcome == OutcomeType.WIN])
            failed_signals = len([s for s in similar_signals if s.outcome == OutcomeType.LOSS])

            # حساب معدل النجاح
            if total_signals > 0:
                success_probability = successful_signals / total_signals
            else:
                success_probability = 0.5

            # حساب متوسط الربح والخسارة
            profits = [s.profit_loss for s in similar_signals if s.outcome == OutcomeType.WIN and s.profit_loss is not None]
            losses = [abs(s.profit_loss) for s in similar_signals if s.outcome == OutcomeType.LOSS and s.profit_loss is not None]

            average_profit = statistics.mean(profits) if profits else 0
            average_loss = statistics.mean(losses) if losses else 0

            # تحديد مستوى الثقة
            confidence_level = self._determine_confidence_level(success_probability)

            # حساب نسبة المخاطر والعوائد
            if average_loss > 0:
                risk_reward_ratio = average_profit / average_loss
            else:
                risk_reward_ratio = 0

            # حساب القيمة المتوقعة
            expected_value = (success_probability * average_profit) - ((1 - success_probability) * average_loss)

            # تحديد التوصية
            is_recommended = (
                success_probability >= self.min_success_rate and
                expected_value > 0 and
                total_signals >= self.min_historical_samples
            )

            # حساب درجة تطابق الظروف
            condition_scores = []
            for signal in similar_signals:
                score = self._calculate_market_conditions_similarity(market_conditions, signal.market_conditions)
                condition_scores.append(score)

            condition_match_score = statistics.mean(condition_scores) if condition_scores else 0.5

            return ProbabilityAnalysis(
                signal_type=signal_type,
                success_probability=success_probability * 100,  # تحويل إلى نسبة مئوية
                confidence_level=confidence_level,
                total_signals=total_signals,
                successful_signals=successful_signals,
                failed_signals=failed_signals,
                average_profit=average_profit,
                average_loss=average_loss,
                similar_conditions_count=len([s for s in similar_signals if self._calculate_market_conditions_similarity(market_conditions, s.market_conditions) >= 0.8]),
                condition_match_score=condition_match_score,
                is_recommended=is_recommended,
                risk_reward_ratio=risk_reward_ratio,
                expected_value=expected_value,
                description=f"تحليل احتمالية {signal_type.value}: {success_probability*100:.1f}% نجاح",
                reasoning=f"بناء على {total_signals} إشارة مشابهة، {successful_signals} نجحت و {failed_signals} فشلت",
                supporting_data={
                    'lookback_days': self.lookback_days,
                    'similarity_threshold': self.similarity_threshold,
                    'condition_match_score': condition_match_score
                }
            )

        except Exception as e:
            logger.error(f"خطأ في تحليل الاحتمالية: {str(e)}")
            return ProbabilityAnalysis(
                signal_type=SignalType.NEUTRAL,
                success_probability=50.0,
                confidence_level=ProbabilityLevel.LOW,
                total_signals=0,
                successful_signals=0,
                failed_signals=0,
                average_profit=0,
                average_loss=0,
                similar_conditions_count=0,
                condition_match_score=0,
                is_recommended=False,
                risk_reward_ratio=0,
                expected_value=0,
                description="خطأ في التحليل",
                reasoning="فشل في تحليل البيانات التاريخية",
                supporting_data={}
            )

    def _determine_confidence_level(self, success_probability: float) -> ProbabilityLevel:
        """تحديد مستوى الثقة"""
        try:
            probability_percent = success_probability * 100

            if probability_percent >= 85:
                return ProbabilityLevel.VERY_HIGH
            elif probability_percent >= 75:
                return ProbabilityLevel.HIGH
            elif probability_percent >= 65:
                return ProbabilityLevel.MODERATE
            elif probability_percent >= 55:
                return ProbabilityLevel.LOW
            else:
                return ProbabilityLevel.VERY_LOW

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى الثقة: {str(e)}")
            return ProbabilityLevel.LOW

    def _evaluate_recommendation(self, probability_analysis: ProbabilityAnalysis) -> Dict[str, Any]:
        """تقييم التوصية"""
        try:
            success_rate = probability_analysis.success_probability
            confidence_level = probability_analysis.confidence_level
            expected_value = probability_analysis.expected_value
            risk_reward_ratio = probability_analysis.risk_reward_ratio

            # تحديد قوة التوصية
            strength_factors = []

            # عامل معدل النجاح
            if success_rate >= 85:
                strength_factors.append(1.0)
            elif success_rate >= 75:
                strength_factors.append(0.8)
            elif success_rate >= 65:
                strength_factors.append(0.6)
            else:
                strength_factors.append(0.3)

            # عامل القيمة المتوقعة
            if expected_value > 0.5:
                strength_factors.append(1.0)
            elif expected_value > 0.2:
                strength_factors.append(0.7)
            elif expected_value > 0:
                strength_factors.append(0.5)
            else:
                strength_factors.append(0.2)

            # عامل نسبة المخاطر والعوائد
            if risk_reward_ratio >= 2.0:
                strength_factors.append(1.0)
            elif risk_reward_ratio >= 1.5:
                strength_factors.append(0.8)
            elif risk_reward_ratio >= 1.0:
                strength_factors.append(0.6)
            else:
                strength_factors.append(0.3)

            # عامل عدد العينات
            sample_count = probability_analysis.total_signals
            if sample_count >= 100:
                strength_factors.append(1.0)
            elif sample_count >= 75:
                strength_factors.append(0.8)
            elif sample_count >= 50:
                strength_factors.append(0.6)
            else:
                strength_factors.append(0.4)

            recommendation_strength = statistics.mean(strength_factors)

            # تحديد اجتياز الفلتر
            filter_passed = (
                probability_analysis.is_recommended and
                recommendation_strength >= 0.6 and
                confidence_level.value in ['HIGH', 'VERY_HIGH']
            )

            # تحديد الإجراء
            if filter_passed:
                if probability_analysis.signal_type == SignalType.BUY:
                    action = 'BUY'
                elif probability_analysis.signal_type == SignalType.SELL:
                    action = 'SELL'
                else:
                    action = 'WAIT'
            else:
                action = 'WAIT'

            # تحديد مضاعف حجم الصفقة
            if recommendation_strength >= 0.9:
                position_multiplier = 1.5
            elif recommendation_strength >= 0.8:
                position_multiplier = 1.2
            elif recommendation_strength >= 0.7:
                position_multiplier = 1.0
            else:
                position_multiplier = 0.8

            # تقييم المخاطر
            if confidence_level == ProbabilityLevel.VERY_HIGH and risk_reward_ratio >= 2.0:
                risk = 'LOW'
            elif confidence_level == ProbabilityLevel.HIGH and risk_reward_ratio >= 1.5:
                risk = 'MEDIUM'
            else:
                risk = 'HIGH'

            return {
                'strength': recommendation_strength,
                'passed': filter_passed,
                'action': action,
                'position_multiplier': position_multiplier,
                'risk': risk
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم التوصية: {str(e)}")
            return {
                'strength': 0.3,
                'passed': False,
                'action': 'WAIT',
                'position_multiplier': 0.5,
                'risk': 'HIGH'
            }

    def _generate_probability_recommendations(self, probability_analysis: ProbabilityAnalysis, recommendation_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات الاحتمالية"""
        try:
            recommendations = []

            success_rate = probability_analysis.success_probability
            confidence_level = probability_analysis.confidence_level

            # توصيات حسب معدل النجاح
            if success_rate >= 85:
                recommendations.append("معدل نجاح ممتاز - فرصة عالية الجودة")
            elif success_rate >= 75:
                recommendations.append("معدل نجاح جيد جداً - فرصة موثوقة")
            elif success_rate >= 65:
                recommendations.append("معدل نجاح مقبول - فرصة معتدلة")
            else:
                recommendations.append("معدل نجاح منخفض - تجنب هذه الإشارة")

            # توصيات حسب مستوى الثقة
            if confidence_level == ProbabilityLevel.VERY_HIGH:
                recommendations.append("ثقة عالية جداً في التحليل التاريخي")
            elif confidence_level == ProbabilityLevel.HIGH:
                recommendations.append("ثقة عالية في التحليل التاريخي")
            elif confidence_level == ProbabilityLevel.MODERATE:
                recommendations.append("ثقة معتدلة - تحتاج تأكيد إضافي")
            else:
                recommendations.append("ثقة منخفضة - تجنب التداول")

            # توصيات حسب القيمة المتوقعة
            if probability_analysis.expected_value > 0.5:
                recommendations.append("قيمة متوقعة ممتازة - عائد مرتفع متوقع")
            elif probability_analysis.expected_value > 0.2:
                recommendations.append("قيمة متوقعة جيدة - عائد إيجابي متوقع")
            elif probability_analysis.expected_value > 0:
                recommendations.append("قيمة متوقعة إيجابية بسيطة")
            else:
                recommendations.append("قيمة متوقعة سلبية - تجنب التداول")

            # توصيات حسب نسبة المخاطر والعوائد
            if probability_analysis.risk_reward_ratio >= 2.0:
                recommendations.append("نسبة مخاطر/عوائد ممتازة (1:2 أو أفضل)")
            elif probability_analysis.risk_reward_ratio >= 1.5:
                recommendations.append("نسبة مخاطر/عوائد جيدة")
            elif probability_analysis.risk_reward_ratio >= 1.0:
                recommendations.append("نسبة مخاطر/عوائد متوازنة")
            else:
                recommendations.append("نسبة مخاطر/عوائد ضعيفة")

            # توصيات حسب عدد العينات
            if probability_analysis.total_signals >= 100:
                recommendations.append("عدد كبير من العينات التاريخية - تحليل موثوق")
            elif probability_analysis.total_signals >= 50:
                recommendations.append("عدد كافي من العينات التاريخية")
            else:
                recommendations.append("عدد محدود من العينات - تحليل أقل موثوقية")

            # توصية الإجراء النهائي
            if recommendation_assessment['passed']:
                recommendations.append(f"✅ يُنصح بالتداول - {recommendation_assessment['action']}")
                recommendations.append(f"مضاعف حجم الصفقة: {recommendation_assessment['position_multiplier']:.1f}x")
            else:
                recommendations.append("❌ لا يُنصح بالتداول - انتظار فرصة أفضل")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات الاحتمالية: {str(e)}")
            return ["خطأ في إنتاج التوصيات"]

    def add_historical_signal(self, asset: str, signal: HistoricalSignal):
        """إضافة إشارة تاريخية"""
        try:
            if asset not in self.historical_signals:
                self.historical_signals[asset] = []

            self.historical_signals[asset].append(signal)

            # الاحتفاظ بآخر 1000 إشارة فقط لكل أصل
            if len(self.historical_signals[asset]) > 1000:
                self.historical_signals[asset] = self.historical_signals[asset][-1000:]

            logger.debug(f"تم إضافة إشارة تاريخية لـ {asset}")

        except Exception as e:
            logger.error(f"خطأ في إضافة الإشارة التاريخية لـ {asset}: {str(e)}")

    def get_success_rate_summary(self, asset: str, days: int = 7) -> Optional[Dict[str, Any]]:
        """الحصول على ملخص معدل النجاح"""
        try:
            if asset not in self.historical_signals:
                return None

            cutoff_date = datetime.now() - timedelta(days=days)
            recent_signals = [s for s in self.historical_signals[asset] if s.timestamp >= cutoff_date and s.outcome is not None]

            if not recent_signals:
                return None

            total = len(recent_signals)
            wins = len([s for s in recent_signals if s.outcome == OutcomeType.WIN])
            losses = len([s for s in recent_signals if s.outcome == OutcomeType.LOSS])

            buy_signals = [s for s in recent_signals if s.signal_type == SignalType.BUY]
            sell_signals = [s for s in recent_signals if s.signal_type == SignalType.SELL]

            buy_wins = len([s for s in buy_signals if s.outcome == OutcomeType.WIN])
            sell_wins = len([s for s in sell_signals if s.outcome == OutcomeType.WIN])

            return {
                'asset': asset,
                'period_days': days,
                'total_signals': total,
                'wins': wins,
                'losses': losses,
                'success_rate': (wins / total * 100) if total > 0 else 0,
                'buy_success_rate': (buy_wins / len(buy_signals) * 100) if buy_signals else 0,
                'sell_success_rate': (sell_wins / len(sell_signals) * 100) if sell_signals else 0
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص معدل النجاح لـ {asset}: {str(e)}")
            return None

# إنشاء instance عام للاستخدام
historical_probability_filter = HistoricalProbabilityFilter()

if __name__ == "__main__":
    # اختبار سريع
    import asyncio

    async def test_filter():
        # إضافة بعض الإشارات التاريخية للاختبار
        test_signals = []
        for i in range(60):
            signal = HistoricalSignal(
                timestamp=datetime.now() - timedelta(days=i),
                signal_type=SignalType.BUY if i % 2 == 0 else SignalType.SELL,
                signal_strength=0.7 + (i % 3) * 0.1,
                confidence=70 + (i % 20),
                technical_score=0.6 + (i % 4) * 0.1,
                behavioral_score=0.5 + (i % 5) * 0.1,
                market_conditions={
                    'volatility': 0.02 + (i % 3) * 0.01,
                    'trend_direction': 'UPTREND' if i % 3 == 0 else 'DOWNTREND',
                    'volume_ratio': 1.0 + (i % 2) * 0.5,
                    'trading_hour': 10 + (i % 8)
                },
                outcome=OutcomeType.WIN if i % 3 != 0 else OutcomeType.LOSS,  # 67% نجاح
                profit_loss=0.5 if i % 3 != 0 else -0.3,
                duration_minutes=60
            )
            test_signals.append(signal)

        # إضافة الإشارات للفلتر
        for signal in test_signals:
            historical_probability_filter.add_historical_signal("TEST_PAIR", signal)

        # اختبار إشارة جديدة
        current_signal = {
            'signal_type': 'BUY',
            'signal_strength': 0.8,
            'confidence': 75
        }

        market_conditions = {
            'volatility': 0.025,
            'trend_direction': 'UPTREND',
            'volume_ratio': 1.2,
            'trading_hour': 12
        }

        # تشغيل التحليل
        result = await historical_probability_filter.analyze_signal_probability("TEST_PAIR", current_signal, market_conditions)

        if result:
            print(f"تحليل فلتر الاحتمالات:")
            print(f"احتمالية النجاح: {result.overall_probability:.1f}%")
            print(f"اجتاز الفلتر: {result.filter_passed}")
            print(f"الإجراء المقترح: {result.recommended_action}")
            print(f"مضاعف حجم الصفقة: {result.position_size_multiplier:.1f}x")
            print(f"تقييم المخاطر: {result.risk_assessment}")

            print(f"\nتفاصيل التحليل:")
            analysis = result.probability_analysis
            print(f"إجمالي الإشارات المشابهة: {analysis.total_signals}")
            print(f"الإشارات الناجحة: {analysis.successful_signals}")
            print(f"متوسط الربح: {analysis.average_profit:.3f}")
            print(f"متوسط الخسارة: {analysis.average_loss:.3f}")
            print(f"نسبة المخاطر/العوائد: {analysis.risk_reward_ratio:.2f}")
            print(f"القيمة المتوقعة: {analysis.expected_value:.3f}")
        else:
            print("فشل في التحليل")

    # تشغيل الاختبار
    asyncio.run(test_filter())
