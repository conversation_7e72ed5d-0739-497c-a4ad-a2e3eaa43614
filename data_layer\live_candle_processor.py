"""
معالج الشموع المباشرة - نظام متكامل للتعامل مع الشموع الحالية والمغلقة
Live Candle Processor - Integrated system for current and closed candles
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from config.trading_config import default_trading_config
from config.currency_pairs import CURRENCY_PAIRS_70
from data_layer.realtime_collector import realtime_collector
from data_layer.realtime_indicator_analyzer import realtime_indicator_analyzer
from data_layer.profit_balance_manager import profit_balance_manager
from database.repository import historical_data_repo
from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("live_candle_processor")

class LiveCandleProcessor:
    """معالج الشموع المباشرة مع التكامل الكامل"""
    
    def __init__(self, config=None):
        self.config = config or default_trading_config
        self.realtime_collector = realtime_collector
        self.indicator_analyzer = realtime_indicator_analyzer
        self.profit_balance_manager = profit_balance_manager
        self.historical_repo = historical_data_repo
        
        # إعدادات المعالجة
        self.is_processing = False
        self.processing_interval = 1.0  # ثانية واحدة
        self.candle_duration = 60  # دقيقة واحدة
        
        # تتبع الشموع الحالية
        self.current_candles = {}  # {asset: candle_data}
        self.last_candle_timestamps = {}  # {asset: timestamp}
        
        # إحصائيات المعالجة
        self.processing_stats = {
            'processed_candles': 0,
            'stored_candles': 0,
            'calculated_indicators': 0,
            'errors': 0,
            'start_time': None
        }
        
        logger.info("تم تهيئة معالج الشموع المباشرة")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_system(self) -> bool:
        """تهيئة النظام الكامل"""
        try:
            logger.info("🔧 تهيئة نظام معالجة الشموع المباشرة...")
            
            # تهيئة جامع البيانات المباشرة
            if not await self.realtime_collector.initialize_async_connection():
                logger.error("فشل في تهيئة جامع البيانات المباشرة")
                return False
            
            # تهيئة محلل المؤشرات
            if not await self.indicator_analyzer.initialize_system():
                logger.error("فشل في تهيئة محلل المؤشرات")
                return False

            # تهيئة مدير الأرباح والرصيد
            if not await self.profit_balance_manager.initialize_system():
                logger.error("فشل في تهيئة مدير الأرباح والرصيد")
                return False
            
            # تحميل آخر الشموع المخزنة
            await self._load_last_stored_candles()
            
            logger.info("✅ تم تهيئة نظام معالجة الشموع المباشرة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة النظام: {str(e)}")
            return False
    
    async def _load_last_stored_candles(self):
        """تحميل آخر الشموع المخزنة لكل أصل"""
        try:
            for asset in CURRENCY_PAIRS_70:
                # جلب آخر timestamp مخزن
                last_timestamp = self.historical_repo.get_latest_timestamp(asset, 60)
                if last_timestamp:
                    self.last_candle_timestamps[asset] = last_timestamp
                    logger.debug(f"آخر شمعة مخزنة لـ {asset}: {last_timestamp}")
            
            logger.info(f"تم تحميل آخر الشموع لـ {len(self.last_candle_timestamps)} أصل")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل آخر الشموع: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def start_live_processing(self, assets: List[str] = None) -> bool:
        """بدء معالجة الشموع المباشرة"""
        try:
            if self.is_processing:
                logger.warning("المعالجة المباشرة قيد التشغيل بالفعل")
                return True
            
            assets = assets or CURRENCY_PAIRS_70
            logger.info(f"🚀 بدء معالجة الشموع المباشرة لـ {len(assets)} أصل")
            
            self.is_processing = True
            self.processing_stats['start_time'] = datetime.now()
            
            # بدء البث المباشر
            streaming_success = await self.realtime_collector.start_streaming(assets)
            
            if not any(streaming_success.values()):
                logger.error("فشل في بدء البث المباشر لجميع الأصول")
                self.is_processing = False
                return False
            
            # بدء مراقبة الأرباح والرصيد
            await self.profit_balance_manager.start_monitoring(assets)

            # بدء حلقة المعالجة
            asyncio.create_task(self._processing_loop(assets))
            
            logger.info(f"✅ تم بدء معالجة الشموع المباشرة لـ {sum(streaming_success.values())} أصل")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء المعالجة المباشرة: {str(e)}")
            self.is_processing = False
            return False
    
    async def _processing_loop(self, assets: List[str]):
        """حلقة معالجة الشموع المباشرة"""
        try:
            logger.info("🔄 بدء حلقة معالجة الشموع المباشرة")
            
            while self.is_processing:
                start_time = time.time()
                
                # معالجة كل أصل
                for asset in assets:
                    try:
                        await self._process_asset_candle(asset)
                    except Exception as e:
                        logger.error(f"خطأ في معالجة {asset}: {str(e)}")
                        self.processing_stats['errors'] += 1
                
                # انتظار حتى الدورة التالية
                processing_time = time.time() - start_time
                sleep_time = max(0, self.processing_interval - processing_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
        except Exception as e:
            logger.error(f"خطأ في حلقة المعالجة: {str(e)}")
        finally:
            logger.info("انتهت حلقة معالجة الشموع المباشرة")
    
    async def _process_asset_candle(self, asset: str):
        """معالجة شمعة أصل واحد"""
        try:
            # جلب الشمعة الحالية من البث المباشر
            current_candle = await self._get_live_candle(asset)
            
            if not current_candle:
                return
            
            candle_timestamp = current_candle.get('timestamp', datetime.now())
            
            # التحقق من وجود شمعة جديدة مغلقة
            if await self._is_new_closed_candle(asset, candle_timestamp):
                # معالجة الشمعة المغلقة
                await self._process_closed_candle(asset, current_candle)
            
            # معالجة الشمعة الحالية (البث المباشر)
            await self._process_current_candle(asset, current_candle)
            
            self.processing_stats['processed_candles'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالجة شمعة {asset}: {str(e)}")
    
    async def _get_live_candle(self, asset: str) -> Optional[Dict[str, Any]]:
        """جلب الشمعة المباشرة من Redis أو المنصة"""
        try:
            # جلب من Redis أولاً
            if self.realtime_collector.redis_client:
                redis_key = f"live_data:{asset}"
                candle_data = self.realtime_collector.redis_client.get(redis_key)
                
                if candle_data:
                    data = json.loads(candle_data)
                    return data.get('candle', data)
            
            # إذا لم توجد، جلب من المنصة مباشرة
            candles = await self.realtime_collector.get_latest_candles_async(asset, 60, 1)
            
            if candles:
                return candles[0]
            
            return None
            
        except Exception as e:
            logger.debug(f"خطأ في جلب الشمعة المباشرة لـ {asset}: {str(e)}")
            return None
    
    async def _is_new_closed_candle(self, asset: str, current_timestamp: datetime) -> bool:
        """التحقق من وجود شمعة جديدة مغلقة"""
        try:
            last_stored = self.last_candle_timestamps.get(asset)
            
            if not last_stored:
                return True
            
            # التحقق من الفرق الزمني (دقيقة واحدة)
            time_diff = (current_timestamp - last_stored).total_seconds()
            
            return time_diff >= self.candle_duration
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الشمعة المغلقة لـ {asset}: {str(e)}")
            return False
    
    async def _process_closed_candle(self, asset: str, candle_data: Dict[str, Any]):
        """معالجة الشمعة المغلقة - تحليل وتخزين في Redis للنقل اللاحق"""
        try:
            # تحليل الشمعة المغلقة مع المؤشرات
            analysis_result = await self.indicator_analyzer.analyze_current_candle(
                asset, candle_data, is_closed=True
            )

            if analysis_result:
                # تخزين الشمعة المغلقة في Redis للنقل اللاحق
                await self._store_closed_candle_in_redis(asset, analysis_result)

                # تحديث آخر timestamp مخزن
                self.last_candle_timestamps[asset] = candle_data.get('timestamp', datetime.now())
                self.processing_stats['stored_candles'] += 1
                self.processing_stats['calculated_indicators'] += len(analysis_result.get('indicators', {}))

                logger.debug(f"تم تخزين شمعة مغلقة لـ {asset} في Redis مع {len(analysis_result.get('indicators', {}))} مؤشر")

        except Exception as e:
            logger.error(f"خطأ في معالجة الشمعة المغلقة لـ {asset}: {str(e)}")

    async def _store_closed_candle_in_redis(self, asset: str, analysis_result: Dict[str, Any]):
        """تخزين الشمعة المغلقة في Redis للنقل اللاحق"""
        try:
            if not self.realtime_collector.redis_client:
                return

            # إنشاء مفتاح فريد للشمعة المغلقة
            timestamp = analysis_result['candle'].get('timestamp', datetime.now())
            if isinstance(timestamp, str):
                timestamp_str = timestamp
            else:
                timestamp_str = timestamp.isoformat()

            redis_key = f"closed_candle:{asset}:{timestamp_str}"

            # إعداد بيانات الشمعة للتخزين
            closed_candle_data = {
                'asset': asset,
                'candle': analysis_result['candle'],
                'indicators': analysis_result['indicators'],
                'timestamp': analysis_result['timestamp'],
                'analysis_time': analysis_result['analysis_time'],
                'is_closed': True,
                'stored_at': datetime.now().isoformat()
            }

            # تخزين في Redis مع انتهاء صلاحية (10 دقائق)
            self.realtime_collector.redis_client.setex(
                redis_key,
                600,  # 10 دقائق
                json.dumps(closed_candle_data, default=str)
            )

            logger.debug(f"تم تخزين الشمعة المغلقة في Redis: {redis_key}")

        except Exception as e:
            logger.error(f"خطأ في تخزين الشمعة المغلقة في Redis لـ {asset}: {str(e)}")
    
    async def _process_current_candle(self, asset: str, candle_data: Dict[str, Any]):
        """معالجة الشمعة الحالية - تحليل وتخزين في Redis"""
        try:
            # تحليل الشمعة الحالية مع المؤشرات
            analysis_result = await self.indicator_analyzer.analyze_current_candle(
                asset, candle_data, is_closed=False
            )
            
            if analysis_result:
                # تحديث الشمعة الحالية في الذاكرة
                self.current_candles[asset] = analysis_result
                
                logger.debug(f"تم تحديث الشمعة الحالية لـ {asset} مع {len(analysis_result.get('indicators', {}))} مؤشر")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الشمعة الحالية لـ {asset}: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def stop_live_processing(self) -> bool:
        """إيقاف معالجة الشموع المباشرة"""
        try:
            if not self.is_processing:
                logger.warning("المعالجة المباشرة غير قيد التشغيل")
                return True
            
            logger.info("🛑 إيقاف معالجة الشموع المباشرة...")
            
            self.is_processing = False
            
            # إيقاف مراقبة الأرباح والرصيد
            await self.profit_balance_manager.stop_monitoring()

            # إيقاف البث المباشر
            await self.realtime_collector.stop_streaming()
            
            logger.info("✅ تم إيقاف معالجة الشموع المباشرة")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف المعالجة المباشرة: {str(e)}")
            return False
    
    def get_current_candle(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على الشمعة الحالية لأصل معين"""
        return self.current_candles.get(asset)
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المعالجة"""
        runtime = (datetime.now() - self.processing_stats['start_time']).total_seconds() if self.processing_stats['start_time'] else 0
        
        return {
            'is_processing': self.is_processing,
            'runtime_seconds': round(runtime, 1),
            'processed_candles': self.processing_stats['processed_candles'],
            'stored_candles': self.processing_stats['stored_candles'],
            'calculated_indicators': self.processing_stats['calculated_indicators'],
            'errors': self.processing_stats['errors'],
            'current_candles_count': len(self.current_candles),
            'processing_rate': round(self.processing_stats['processed_candles'] / max(runtime, 1), 2)
        }
    
    def get_all_current_candles(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على جميع الشموع الحالية"""
        return self.current_candles.copy()

    def get_current_profit_rates(self) -> Dict[str, float]:
        """الحصول على نسب الأرباح الحالية لجميع الأصول"""
        return self.profit_balance_manager.get_all_profit_rates()

    def get_current_account_balance(self) -> Dict[str, Any]:
        """الحصول على رصيد الحساب الحالي"""
        return self.profit_balance_manager.get_account_balance()

    def get_profit_rate_for_asset(self, asset: str) -> Optional[float]:
        """الحصول على نسبة الربح لأصل معين"""
        return self.profit_balance_manager.get_profit_rate(asset)

    def get_comprehensive_market_data(self) -> Dict[str, Any]:
        """الحصول على بيانات السوق الشاملة (شموع + مؤشرات + أرباح + رصيد)"""
        return {
            'current_candles': self.get_all_current_candles(),
            'profit_rates': self.get_current_profit_rates(),
            'account_balance': self.get_current_account_balance(),
            'processing_stats': self.get_processing_statistics(),
            'profit_balance_stats': self.profit_balance_manager.get_system_statistics(),
            'timestamp': datetime.now().isoformat()
        }

# إنشاء مثيل عام للاستخدام
live_candle_processor = LiveCandleProcessor()
