"""
مقيم حالة السوق لنظام السكالبينغ
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from database.repository import historical_data_repo, market_condition_repo
from database.models import MarketCondition
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, DataError
from utils.helpers import calculate_volatility, get_current_timestamp

logger = scalping_logger.get_logger("market_evaluator")

@dataclass
class MarketAnalysis:
    """نتيجة تحليل السوق"""
    market_state: str  # TRENDING, RANGING, VOLATILE, CALM
    trend_direction: str  # UP, DOWN, SIDEWAYS
    trend_strength: float  # 0-100
    volatility_level: str  # LOW, MEDIUM, HIGH, EXTREME
    volatility_value: float
    support_level: Optional[float]
    resistance_level: Optional[float]
    current_price: float
    is_trading_recommended: bool
    confidence: float  # 0-100
    analysis_details: Dict[str, Any]

class MarketEvaluator:
    """مقيم حالة السوق"""
    
    def __init__(self):
        self.volatility_thresholds = {
            'low': 0.01,      # 1%
            'medium': 0.03,   # 3%
            'high': 0.05,     # 5%
            'extreme': 0.10   # 10%
        }
        
        self.trend_strength_thresholds = {
            'weak': 30,
            'moderate': 60,
            'strong': 80
        }
    
    @handle_errors(default_return=None, log_error=True)
    def evaluate_market_condition(self, asset: str, timeframe: int = 60, lookback_periods: int = 100) -> Optional[MarketAnalysis]:
        """تقييم حالة السوق لأصل معين"""
        try:
            # جلب البيانات التاريخية
            candles = historical_data_repo.get_latest_candles(asset, timeframe, lookback_periods)
            
            if len(candles) < 20:  # الحد الأدنى للتحليل
                logger.warning(f"بيانات غير كافية لتحليل {asset}: {len(candles)} شمعة")
                return None
            
            # ترتيب البيانات حسب الوقت
            sorted_candles = sorted(candles, key=lambda x: x.timestamp)
            
            # استخراج الأسعار
            prices = [float(candle.close_price) for candle in sorted_candles]
            highs = [float(candle.high_price) for candle in sorted_candles]
            lows = [float(candle.low_price) for candle in sorted_candles]
            opens = [float(candle.open_price) for candle in sorted_candles]
            
            current_price = prices[-1]
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_trend(prices)
            
            # تحليل التقلب
            volatility_analysis = self._analyze_volatility(prices, highs, lows)
            
            # تحليل الدعم والمقاومة
            support_resistance = self._find_support_resistance(highs, lows, prices)
            
            # تحليل حالة السوق
            market_state = self._determine_market_state(prices, volatility_analysis['value'])
            
            # تقييم إمكانية التداول
            trading_recommendation = self._evaluate_trading_conditions(
                trend_analysis, volatility_analysis, market_state
            )
            
            # حساب الثقة الإجمالية
            confidence = self._calculate_confidence(
                trend_analysis, volatility_analysis, len(sorted_candles)
            )
            
            # تفاصيل التحليل
            analysis_details = {
                'data_points': len(sorted_candles),
                'price_range': {
                    'min': min(lows),
                    'max': max(highs),
                    'current': current_price
                },
                'trend_details': trend_analysis,
                'volatility_details': volatility_analysis,
                'support_resistance': support_resistance,
                'analysis_timestamp': get_current_timestamp().isoformat()
            }
            
            # إنشاء نتيجة التحليل
            analysis = MarketAnalysis(
                market_state=market_state,
                trend_direction=trend_analysis['direction'],
                trend_strength=trend_analysis['strength'],
                volatility_level=volatility_analysis['level'],
                volatility_value=volatility_analysis['value'],
                support_level=support_resistance.get('support'),
                resistance_level=support_resistance.get('resistance'),
                current_price=current_price,
                is_trading_recommended=trading_recommendation,
                confidence=confidence,
                analysis_details=analysis_details
            )
            
            logger.debug(f"تحليل السوق لـ {asset}: {market_state}, اتجاه: {trend_analysis['direction']}, تقلب: {volatility_analysis['level']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تقييم حالة السوق لـ {asset}: {str(e)}")
            return None
    
    def _analyze_trend(self, prices: List[float]) -> Dict[str, Any]:
        """تحليل الاتجاه"""
        if len(prices) < 10:
            return {'direction': 'SIDEWAYS', 'strength': 0, 'slope': 0}
        
        # حساب المتوسطات المتحركة
        short_ma = np.mean(prices[-10:])  # آخر 10 فترات
        long_ma = np.mean(prices[-20:])   # آخر 20 فترة
        
        # حساب الميل
        x = np.arange(len(prices))
        slope = np.polyfit(x, prices, 1)[0]
        
        # تحديد الاتجاه
        if short_ma > long_ma * 1.001:  # 0.1% فرق على الأقل
            direction = 'UP'
        elif short_ma < long_ma * 0.999:
            direction = 'DOWN'
        else:
            direction = 'SIDEWAYS'
        
        # حساب قوة الاتجاه
        price_range = max(prices) - min(prices)
        if price_range > 0:
            strength = min(100, abs(slope) / price_range * 10000)  # تطبيع إلى 0-100
        else:
            strength = 0
        
        return {
            'direction': direction,
            'strength': round(strength, 2),
            'slope': slope,
            'short_ma': short_ma,
            'long_ma': long_ma
        }
    
    def _analyze_volatility(self, prices: List[float], highs: List[float], lows: List[float]) -> Dict[str, Any]:
        """تحليل التقلب"""
        # حساب التقلب بطرق متعددة
        
        # 1. التقلب المعياري للعوائد
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                return_rate = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(return_rate)
        
        std_volatility = np.std(returns) if returns else 0
        
        # 2. متوسط المدى الحقيقي (ATR)
        true_ranges = []
        for i in range(1, len(prices)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - prices[i-1])
            tr3 = abs(lows[i] - prices[i-1])
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        atr = np.mean(true_ranges[-14:]) if len(true_ranges) >= 14 else np.mean(true_ranges)
        atr_percentage = (atr / prices[-1]) * 100 if prices[-1] != 0 else 0
        
        # 3. تقلب الأسعار العالية والمنخفضة
        hl_volatility = np.std([(h - l) / l for h, l in zip(highs, lows) if l != 0])
        
        # حساب التقلب المركب
        composite_volatility = (std_volatility + atr_percentage/100 + hl_volatility) / 3
        
        # تحديد مستوى التقلب
        if composite_volatility <= self.volatility_thresholds['low']:
            level = 'LOW'
        elif composite_volatility <= self.volatility_thresholds['medium']:
            level = 'MEDIUM'
        elif composite_volatility <= self.volatility_thresholds['high']:
            level = 'HIGH'
        else:
            level = 'EXTREME'
        
        return {
            'level': level,
            'value': round(composite_volatility, 6),
            'std_volatility': round(std_volatility, 6),
            'atr': round(atr, 6),
            'atr_percentage': round(atr_percentage, 4),
            'hl_volatility': round(hl_volatility, 6)
        }
    
    def _find_support_resistance(self, highs: List[float], lows: List[float], prices: List[float]) -> Dict[str, Optional[float]]:
        """العثور على مستويات الدعم والمقاومة"""
        try:
            # البحث عن القمم والقيعان المحلية
            peaks = []
            troughs = []
            
            # تحديد القمم (مقاومة محتملة)
            for i in range(2, len(highs) - 2):
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    peaks.append(highs[i])
            
            # تحديد القيعان (دعم محتمل)
            for i in range(2, len(lows) - 2):
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    troughs.append(lows[i])
            
            # العثور على أقوى مستويات الدعم والمقاومة
            current_price = prices[-1]
            
            # مقاومة: أقرب قمة أعلى من السعر الحالي
            resistance_candidates = [p for p in peaks if p > current_price]
            resistance = min(resistance_candidates) if resistance_candidates else None
            
            # دعم: أقرب قاع أقل من السعر الحالي
            support_candidates = [t for t in troughs if t < current_price]
            support = max(support_candidates) if support_candidates else None
            
            return {
                'support': support,
                'resistance': resistance,
                'peaks_count': len(peaks),
                'troughs_count': len(troughs)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحديد الدعم والمقاومة: {str(e)}")
            return {'support': None, 'resistance': None}
    
    def _determine_market_state(self, prices: List[float], volatility: float) -> str:
        """تحديد حالة السوق"""
        # تحليل الاتجاه العام
        trend_analysis = self._analyze_trend(prices)
        
        # تحديد الحالة بناءً على الاتجاه والتقلب
        if volatility >= self.volatility_thresholds['high']:
            return 'VOLATILE'
        elif volatility <= self.volatility_thresholds['low']:
            return 'CALM'
        elif trend_analysis['strength'] >= self.trend_strength_thresholds['moderate']:
            return 'TRENDING'
        else:
            return 'RANGING'
    
    def _evaluate_trading_conditions(self, trend_analysis: Dict, volatility_analysis: Dict, market_state: str) -> bool:
        """تقييم ظروف التداول"""
        # شروط التداول المفضلة
        good_conditions = []
        
        # 1. التقلب المناسب (ليس منخفض جداً أو عالي جداً)
        volatility_ok = volatility_analysis['level'] in ['MEDIUM', 'HIGH']
        good_conditions.append(volatility_ok)
        
        # 2. اتجاه واضح أو تداول جانبي مستقر
        trend_ok = (trend_analysis['strength'] >= 30 or 
                   (market_state == 'RANGING' and volatility_analysis['level'] != 'EXTREME'))
        good_conditions.append(trend_ok)
        
        # 3. تجنب التقلبات الشديدة
        not_too_volatile = volatility_analysis['level'] != 'EXTREME'
        good_conditions.append(not_too_volatile)
        
        # 4. تجنب السوق الهادئ جداً
        not_too_calm = volatility_analysis['level'] != 'LOW'
        good_conditions.append(not_too_calm)
        
        # يجب تحقق معظم الشروط
        return sum(good_conditions) >= 3
    
    def _calculate_confidence(self, trend_analysis: Dict, volatility_analysis: Dict, data_points: int) -> float:
        """حساب مستوى الثقة في التحليل"""
        confidence_factors = []
        
        # 1. كمية البيانات
        data_confidence = min(100, (data_points / 100) * 100)
        confidence_factors.append(data_confidence)
        
        # 2. وضوح الاتجاه
        trend_confidence = trend_analysis['strength']
        confidence_factors.append(trend_confidence)
        
        # 3. استقرار التقلب
        volatility_stability = 100 - min(100, volatility_analysis['value'] * 1000)
        confidence_factors.append(max(0, volatility_stability))
        
        # المتوسط المرجح
        weights = [0.3, 0.4, 0.3]  # أهمية أكبر للاتجاه
        weighted_confidence = sum(cf * w for cf, w in zip(confidence_factors, weights))
        
        return round(weighted_confidence, 2)
    
    @handle_errors(default_return=False, log_error=True)
    def store_market_analysis(self, asset: str, analysis: MarketAnalysis, timeframe: int = 60) -> bool:
        """حفظ تحليل السوق في قاعدة البيانات"""
        try:
            market_condition = MarketCondition(
                asset=asset,
                timestamp=get_current_timestamp(),
                timeframe=timeframe,
                market_state=analysis.market_state,
                trend_direction=analysis.trend_direction,
                trend_strength=analysis.trend_strength,
                volatility_level=analysis.volatility_level,
                volatility_value=analysis.volatility_value,
                support_level=analysis.support_level,
                resistance_level=analysis.resistance_level,
                current_price=analysis.current_price,
                is_trading_recommended=analysis.is_trading_recommended,
                analysis_confidence=analysis.confidence,
                analysis_details=analysis.analysis_details
            )
            
            created_condition = market_condition_repo.create(**market_condition.to_dict())
            return created_condition is not None
            
        except Exception as e:
            logger.error(f"خطأ في حفظ تحليل السوق لـ {asset}: {str(e)}")
            return False
    
    @handle_errors(default_return={}, log_error=True)
    def get_market_summary(self, assets: List[str], timeframe: int = 60) -> Dict[str, Any]:
        """الحصول على ملخص حالة السوق لعدة أصول"""
        summary = {
            'total_assets': len(assets),
            'analyzed_assets': 0,
            'trading_recommended': 0,
            'market_states': {},
            'volatility_levels': {},
            'trend_directions': {},
            'average_confidence': 0,
            'asset_analyses': {}
        }
        
        total_confidence = 0
        
        for asset in assets:
            analysis = self.evaluate_market_condition(asset, timeframe)
            
            if analysis:
                summary['analyzed_assets'] += 1
                summary['asset_analyses'][asset] = analysis
                
                if analysis.is_trading_recommended:
                    summary['trading_recommended'] += 1
                
                # تجميع الإحصائيات
                summary['market_states'][analysis.market_state] = summary['market_states'].get(analysis.market_state, 0) + 1
                summary['volatility_levels'][analysis.volatility_level] = summary['volatility_levels'].get(analysis.volatility_level, 0) + 1
                summary['trend_directions'][analysis.trend_direction] = summary['trend_directions'].get(analysis.trend_direction, 0) + 1
                
                total_confidence += analysis.confidence
        
        if summary['analyzed_assets'] > 0:
            summary['average_confidence'] = round(total_confidence / summary['analyzed_assets'], 2)
        
        return summary

# إنشاء instance عام
market_evaluator = MarketEvaluator()
