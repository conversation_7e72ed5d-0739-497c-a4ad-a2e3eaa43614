"""
كاشف الزخم والتسارع - المرحلة الرابعة
Momentum and Acceleration Detector for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("momentum_acceleration_detector")

class MomentumDirection(Enum):
    """اتجاه الزخم"""
    STRONG_BULLISH = "STRONG_BULLISH"
    BULLISH = "BULLISH"
    NEUTRAL = "NEUTRAL"
    BEARISH = "BEARISH"
    STRONG_BEARISH = "STRONG_BEARISH"

class AccelerationType(Enum):
    """نوع التسارع"""
    ACCELERATING = "ACCELERATING"      # تسارع متزايد
    DECELERATING = "DECELERATING"      # تسارع متناقص
    CONSTANT = "CONSTANT"              # سرعة ثابتة
    REVERSING = "REVERSING"            # انعكاس الاتجاه

class MomentumStrength(Enum):
    """قوة الزخم"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class MomentumSignal:
    """إشارة الزخم"""
    direction: MomentumDirection
    strength: MomentumStrength
    confidence: float  # 0-100
    
    # قياسات الزخم
    momentum_value: float
    momentum_change: float
    velocity: float  # السرعة
    acceleration: float  # التسارع
    
    # معلومات التحليل
    timeframe_analyzed: int  # عدد الشموع المحللة
    price_change_percent: float
    volume_support: bool
    
    # وصف الإشارة
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class AccelerationAnalysis:
    """تحليل التسارع"""
    acceleration_type: AccelerationType
    acceleration_value: float
    acceleration_change: float
    
    # تحليل الاتجاه
    trend_acceleration: float
    trend_consistency: float
    
    # قوة التسارع
    acceleration_strength: float
    confidence: float
    
    # معلومات إضافية
    description: str
    supporting_data: Dict[str, Any]

@dataclass
class MomentumAnalysisResult:
    """نتيجة تحليل الزخم والتسارع"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # إشارات الزخم
    momentum_signals: List[MomentumSignal]
    
    # تحليل التسارع
    acceleration_analysis: AccelerationAnalysis
    
    # التقييم الإجمالي
    overall_momentum: MomentumDirection
    overall_strength: MomentumStrength
    overall_confidence: float
    
    # توصيات
    is_actionable: bool
    recommended_action: str
    risk_level: str
    recommendations: List[str]

class MomentumAccelerationDetector:
    """كاشف الزخم والتسارع"""
    
    def __init__(self):
        self.momentum_periods = [5, 10, 20]  # فترات تحليل الزخم
        self.acceleration_periods = [3, 5, 10]  # فترات تحليل التسارع
        self.min_confidence_threshold = 65.0
        self.momentum_history = {}  # تاريخ الزخم لكل أصل
        
        logger.info("تم تهيئة كاشف الزخم والتسارع")

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_momentum_acceleration(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[MomentumAnalysisResult]:
        """تحليل الزخم والتسارع"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 20:
                logger.warning(f"بيانات غير كافية لتحليل الزخم لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل الزخم والتسارع لـ {asset} مع {len(candles_data)} شمعة")
            
            # 1. تحليل الزخم بفترات مختلفة
            momentum_signals = []
            for period in self.momentum_periods:
                signal = await self._analyze_momentum_period(candles_data, period)
                if signal:
                    momentum_signals.append(signal)
            
            # 2. تحليل التسارع
            acceleration_analysis = await self._analyze_acceleration(candles_data)
            
            # 3. التقييم الإجمالي
            overall_assessment = self._evaluate_overall_momentum(momentum_signals, acceleration_analysis)
            
            # 4. إنتاج التوصيات
            recommendations = self._generate_momentum_recommendations(momentum_signals, acceleration_analysis, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = MomentumAnalysisResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                momentum_signals=momentum_signals,
                acceleration_analysis=acceleration_analysis,
                overall_momentum=overall_assessment['momentum'],
                overall_strength=overall_assessment['strength'],
                overall_confidence=overall_assessment['confidence'],
                is_actionable=overall_assessment['is_actionable'],
                recommended_action=overall_assessment['action'],
                risk_level=overall_assessment['risk'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.momentum_history:
                self.momentum_history[asset] = []
            
            self.momentum_history[asset].append(result)
            
            # الاحتفاظ بآخر 20 تحليل فقط
            if len(self.momentum_history[asset]) > 20:
                self.momentum_history[asset] = self.momentum_history[asset][-20:]
            
            logger.info(f"تم إكمال تحليل الزخم لـ {asset}: {overall_assessment['momentum'].value}, القوة: {overall_assessment['strength'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الزخم والتسارع لـ {asset}: {str(e)}")
            return None

    async def _analyze_momentum_period(self, candles: List[Dict[str, Any]], period: int) -> Optional[MomentumSignal]:
        """تحليل الزخم لفترة محددة"""
        try:
            if len(candles) < period + 5:
                return None
            
            # حساب التغيرات السعرية
            recent_candles = candles[-period:]
            prices = [candle['close'] for candle in recent_candles]
            
            # حساب الزخم (Rate of Change)
            start_price = prices[0]
            end_price = prices[-1]
            momentum_value = ((end_price - start_price) / start_price) * 100
            
            # حساب السرعة (متوسط التغيير)
            price_changes = []
            for i in range(1, len(prices)):
                change = ((prices[i] - prices[i-1]) / prices[i-1]) * 100
                price_changes.append(change)
            
            velocity = statistics.mean(price_changes) if price_changes else 0
            
            # حساب التسارع (تغيير السرعة)
            if len(price_changes) >= 3:
                first_half = price_changes[:len(price_changes)//2]
                second_half = price_changes[len(price_changes)//2:]
                
                first_velocity = statistics.mean(first_half)
                second_velocity = statistics.mean(second_half)
                acceleration = second_velocity - first_velocity
            else:
                acceleration = 0
            
            # تحديد اتجاه الزخم
            direction = self._determine_momentum_direction(momentum_value, velocity)
            
            # تحديد قوة الزخم
            strength = self._determine_momentum_strength(abs(momentum_value), abs(velocity), abs(acceleration))
            
            # حساب الثقة
            confidence = self._calculate_momentum_confidence(momentum_value, velocity, acceleration, period)
            
            # فحص دعم الحجم
            volume_support = self._check_volume_support(recent_candles)
            
            # تحديد التغيير في الزخم
            momentum_change = 0
            if len(candles) >= period * 2:
                prev_period_candles = candles[-(period*2):-period]
                prev_prices = [candle['close'] for candle in prev_period_candles]
                prev_momentum = ((prev_prices[-1] - prev_prices[0]) / prev_prices[0]) * 100
                momentum_change = momentum_value - prev_momentum
            
            return MomentumSignal(
                direction=direction,
                strength=strength,
                confidence=confidence,
                momentum_value=momentum_value,
                momentum_change=momentum_change,
                velocity=velocity,
                acceleration=acceleration,
                timeframe_analyzed=period,
                price_change_percent=momentum_value,
                volume_support=volume_support,
                description=f"زخم {period} شموع: {direction.value}",
                reasoning=f"تغيير سعري {momentum_value:.2f}% بسرعة {velocity:.3f}% وتسارع {acceleration:.3f}%",
                supporting_data={
                    'period': period,
                    'start_price': start_price,
                    'end_price': end_price,
                    'price_changes': price_changes[-5:]  # آخر 5 تغييرات
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل زخم الفترة {period}: {str(e)}")
            return None

    async def _analyze_acceleration(self, candles: List[Dict[str, Any]]) -> AccelerationAnalysis:
        """تحليل التسارع"""
        try:
            if len(candles) < 15:
                return AccelerationAnalysis(
                    acceleration_type=AccelerationType.CONSTANT,
                    acceleration_value=0,
                    acceleration_change=0,
                    trend_acceleration=0,
                    trend_consistency=0,
                    acceleration_strength=0,
                    confidence=0,
                    description="بيانات غير كافية لتحليل التسارع",
                    supporting_data={}
                )
            
            # حساب السرعات لفترات مختلفة
            velocities = []
            for period in self.acceleration_periods:
                if len(candles) >= period + 2:
                    recent_candles = candles[-period:]
                    prices = [candle['close'] for candle in recent_candles]
                    
                    # حساب متوسط السرعة للفترة
                    changes = []
                    for i in range(1, len(prices)):
                        change = ((prices[i] - prices[i-1]) / prices[i-1]) * 100
                        changes.append(change)
                    
                    avg_velocity = statistics.mean(changes) if changes else 0
                    velocities.append(avg_velocity)
            
            # حساب التسارع (تغيير السرعة)
            if len(velocities) >= 2:
                acceleration_value = velocities[-1] - velocities[0]  # التسارع الإجمالي
                
                # حساب تغيير التسارع
                if len(velocities) >= 3:
                    recent_acceleration = velocities[-1] - velocities[-2]
                    prev_acceleration = velocities[-2] - velocities[-3]
                    acceleration_change = recent_acceleration - prev_acceleration
                else:
                    acceleration_change = 0
            else:
                acceleration_value = 0
                acceleration_change = 0
            
            # تحديد نوع التسارع
            acceleration_type = self._determine_acceleration_type(acceleration_value, acceleration_change)
            
            # حساب اتساق الاتجاه
            trend_consistency = self._calculate_trend_consistency(candles[-10:])
            
            # حساب تسارع الاتجاه
            trend_acceleration = self._calculate_trend_acceleration(candles[-15:])
            
            # حساب قوة التسارع
            acceleration_strength = min(1.0, abs(acceleration_value) / 0.5)  # تطبيع إلى 0-1
            
            # حساب الثقة
            confidence = self._calculate_acceleration_confidence(acceleration_value, trend_consistency, acceleration_strength)
            
            return AccelerationAnalysis(
                acceleration_type=acceleration_type,
                acceleration_value=acceleration_value,
                acceleration_change=acceleration_change,
                trend_acceleration=trend_acceleration,
                trend_consistency=trend_consistency,
                acceleration_strength=acceleration_strength,
                confidence=confidence,
                description=f"تسارع {acceleration_type.value}: {acceleration_value:.3f}%",
                supporting_data={
                    'velocities': velocities,
                    'periods_analyzed': self.acceleration_periods
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التسارع: {str(e)}")
            return AccelerationAnalysis(
                acceleration_type=AccelerationType.CONSTANT,
                acceleration_value=0,
                acceleration_change=0,
                trend_acceleration=0,
                trend_consistency=0,
                acceleration_strength=0,
                confidence=0,
                description="خطأ في تحليل التسارع",
                supporting_data={}
            )

    def _determine_momentum_direction(self, momentum_value: float, velocity: float) -> MomentumDirection:
        """تحديد اتجاه الزخم"""
        try:
            # تحديد الاتجاه بناء على قيمة الزخم والسرعة
            if momentum_value > 2.0 and velocity > 0.5:
                return MomentumDirection.STRONG_BULLISH
            elif momentum_value > 0.5 and velocity > 0.1:
                return MomentumDirection.BULLISH
            elif momentum_value < -2.0 and velocity < -0.5:
                return MomentumDirection.STRONG_BEARISH
            elif momentum_value < -0.5 and velocity < -0.1:
                return MomentumDirection.BEARISH
            else:
                return MomentumDirection.NEUTRAL

        except Exception as e:
            logger.error(f"خطأ في تحديد اتجاه الزخم: {str(e)}")
            return MomentumDirection.NEUTRAL

    def _determine_momentum_strength(self, momentum_abs: float, velocity_abs: float, acceleration_abs: float) -> MomentumStrength:
        """تحديد قوة الزخم"""
        try:
            # حساب نقاط القوة المركبة
            momentum_score = min(5, momentum_abs / 0.5)  # كل 0.5% = نقطة
            velocity_score = min(5, velocity_abs / 0.1)  # كل 0.1% = نقطة
            acceleration_score = min(5, acceleration_abs / 0.05)  # كل 0.05% = نقطة

            total_score = (momentum_score + velocity_score + acceleration_score) / 3

            if total_score >= 4.0:
                return MomentumStrength.VERY_STRONG
            elif total_score >= 3.0:
                return MomentumStrength.STRONG
            elif total_score >= 2.0:
                return MomentumStrength.MODERATE
            elif total_score >= 1.0:
                return MomentumStrength.WEAK
            else:
                return MomentumStrength.VERY_WEAK

        except Exception as e:
            logger.error(f"خطأ في تحديد قوة الزخم: {str(e)}")
            return MomentumStrength.WEAK

    def _calculate_momentum_confidence(self, momentum: float, velocity: float, acceleration: float, period: int) -> float:
        """حساب ثقة الزخم"""
        try:
            # النقاط الأساسية من قوة الإشارة
            base_confidence = min(50, abs(momentum) * 10)

            # مكافأة للسرعة المتسقة
            velocity_bonus = min(20, abs(velocity) * 50)

            # مكافأة للتسارع الإيجابي
            acceleration_bonus = min(15, abs(acceleration) * 100)

            # مكافأة للفترة الأطول (أكثر موثوقية)
            period_bonus = min(10, period / 2)

            # خصم للتضارب في الإشارات
            conflict_penalty = 0
            if (momentum > 0 and velocity < 0) or (momentum < 0 and velocity > 0):
                conflict_penalty = 10

            total_confidence = base_confidence + velocity_bonus + acceleration_bonus + period_bonus - conflict_penalty

            return min(95, max(20, total_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة الزخم: {str(e)}")
            return 50

    def _check_volume_support(self, candles: List[Dict[str, Any]]) -> bool:
        """فحص دعم الحجم للزخم"""
        try:
            if len(candles) < 3:
                return False

            volumes = [candle.get('volume', 0) for candle in candles]
            if not any(volumes):  # لا توجد بيانات حجم
                return False

            # فحص إذا كان الحجم متزايد مع الزخم
            recent_volume = statistics.mean(volumes[-3:])
            earlier_volume = statistics.mean(volumes[:-3]) if len(volumes) > 3 else recent_volume

            return recent_volume > earlier_volume * 1.1  # زيادة 10% على الأقل

        except Exception as e:
            logger.error(f"خطأ في فحص دعم الحجم: {str(e)}")
            return False

    def _determine_acceleration_type(self, acceleration_value: float, acceleration_change: float) -> AccelerationType:
        """تحديد نوع التسارع"""
        try:
            if abs(acceleration_value) < 0.01:  # تسارع ضعيف جداً
                return AccelerationType.CONSTANT
            elif acceleration_change > 0.02:  # تسارع متزايد
                return AccelerationType.ACCELERATING
            elif acceleration_change < -0.02:  # تسارع متناقص
                return AccelerationType.DECELERATING
            elif (acceleration_value > 0.05 and acceleration_change < -0.05) or (acceleration_value < -0.05 and acceleration_change > 0.05):
                return AccelerationType.REVERSING
            else:
                return AccelerationType.CONSTANT

        except Exception as e:
            logger.error(f"خطأ في تحديد نوع التسارع: {str(e)}")
            return AccelerationType.CONSTANT

    def _calculate_trend_consistency(self, candles: List[Dict[str, Any]]) -> float:
        """حساب اتساق الاتجاه"""
        try:
            if len(candles) < 5:
                return 0.5

            prices = [candle['close'] for candle in candles]

            # حساب التغييرات
            changes = []
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                changes.append(1 if change > 0 else -1 if change < 0 else 0)

            if not changes:
                return 0.5

            # حساب نسبة الاتساق
            positive_changes = changes.count(1)
            negative_changes = changes.count(-1)
            total_changes = len(changes)

            if total_changes == 0:
                return 0.5

            # أعلى نسبة من الاتجاه الواحد
            max_consistency = max(positive_changes, negative_changes) / total_changes

            return max_consistency

        except Exception as e:
            logger.error(f"خطأ في حساب اتساق الاتجاه: {str(e)}")
            return 0.5

    def _calculate_trend_acceleration(self, candles: List[Dict[str, Any]]) -> float:
        """حساب تسارع الاتجاه"""
        try:
            if len(candles) < 10:
                return 0

            prices = [candle['close'] for candle in candles]

            # تقسيم إلى نصفين
            mid_point = len(prices) // 2
            first_half = prices[:mid_point]
            second_half = prices[mid_point:]

            # حساب متوسط التغيير لكل نصف
            first_avg_change = (first_half[-1] - first_half[0]) / len(first_half)
            second_avg_change = (second_half[-1] - second_half[0]) / len(second_half)

            # التسارع = الفرق في متوسط التغيير
            trend_acceleration = second_avg_change - first_avg_change

            return trend_acceleration

        except Exception as e:
            logger.error(f"خطأ في حساب تسارع الاتجاه: {str(e)}")
            return 0

    def _calculate_acceleration_confidence(self, acceleration_value: float, trend_consistency: float, acceleration_strength: float) -> float:
        """حساب ثقة التسارع"""
        try:
            # النقاط الأساسية من قوة التسارع
            base_confidence = acceleration_strength * 40

            # مكافأة لاتساق الاتجاه
            consistency_bonus = trend_consistency * 30

            # مكافأة لقوة التسارع
            strength_bonus = min(20, abs(acceleration_value) * 100)

            total_confidence = base_confidence + consistency_bonus + strength_bonus

            return min(95, max(30, total_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة التسارع: {str(e)}")
            return 50

    def _evaluate_overall_momentum(self, momentum_signals: List[MomentumSignal], acceleration_analysis: AccelerationAnalysis) -> Dict[str, Any]:
        """تقييم الزخم الإجمالي"""
        try:
            if not momentum_signals:
                return {
                    'momentum': MomentumDirection.NEUTRAL,
                    'strength': MomentumStrength.VERY_WEAK,
                    'confidence': 0,
                    'is_actionable': False,
                    'action': 'WAIT',
                    'risk': 'HIGH'
                }

            # حساب الأوزان المرجحة
            bullish_weight = 0
            bearish_weight = 0
            total_confidence = 0

            for signal in momentum_signals:
                weight = signal.timeframe_analyzed / 20  # وزن بناء على الفترة
                weighted_score = weight * signal.confidence

                if signal.direction in [MomentumDirection.BULLISH, MomentumDirection.STRONG_BULLISH]:
                    bullish_weight += weighted_score
                elif signal.direction in [MomentumDirection.BEARISH, MomentumDirection.STRONG_BEARISH]:
                    bearish_weight += weighted_score

                total_confidence += signal.confidence

            # تحديد الاتجاه الإجمالي
            if bullish_weight > bearish_weight * 1.2:
                overall_momentum = MomentumDirection.BULLISH
                confidence_ratio = bullish_weight / (bullish_weight + bearish_weight)
            elif bearish_weight > bullish_weight * 1.2:
                overall_momentum = MomentumDirection.BEARISH
                confidence_ratio = bearish_weight / (bullish_weight + bearish_weight)
            else:
                overall_momentum = MomentumDirection.NEUTRAL
                confidence_ratio = 0.5

            # حساب الثقة الإجمالية
            avg_confidence = total_confidence / len(momentum_signals)
            overall_confidence = avg_confidence * confidence_ratio

            # تحديد القوة الإجمالية
            strong_signals = [s for s in momentum_signals if s.strength.value >= 4]
            if len(strong_signals) >= 2:
                overall_strength = MomentumStrength.VERY_STRONG
            elif len(strong_signals) >= 1:
                overall_strength = MomentumStrength.STRONG
            else:
                avg_strength = statistics.mean([s.strength.value for s in momentum_signals])
                if avg_strength >= 3:
                    overall_strength = MomentumStrength.MODERATE
                elif avg_strength >= 2:
                    overall_strength = MomentumStrength.WEAK
                else:
                    overall_strength = MomentumStrength.VERY_WEAK

            # تعديل بناء على التسارع
            if acceleration_analysis.acceleration_type == AccelerationType.ACCELERATING:
                overall_confidence += 10
            elif acceleration_analysis.acceleration_type == AccelerationType.REVERSING:
                overall_confidence -= 15

            overall_confidence = min(95, max(20, overall_confidence))

            # تحديد قابلية التنفيذ
            is_actionable = (
                overall_confidence >= self.min_confidence_threshold and
                overall_strength.value >= 3 and
                len(momentum_signals) >= 2 and
                overall_momentum != MomentumDirection.NEUTRAL
            )

            # تحديد الإجراء المطلوب
            if is_actionable:
                if overall_momentum in [MomentumDirection.BULLISH, MomentumDirection.STRONG_BULLISH]:
                    action = 'BUY'
                else:
                    action = 'SELL'
            else:
                action = 'WAIT'

            # تحديد مستوى المخاطر
            if overall_confidence >= 80 and overall_strength.value >= 4:
                risk = 'LOW'
            elif overall_confidence >= 65 and overall_strength.value >= 3:
                risk = 'MEDIUM'
            else:
                risk = 'HIGH'

            return {
                'momentum': overall_momentum,
                'strength': overall_strength,
                'confidence': round(overall_confidence, 2),
                'is_actionable': is_actionable,
                'action': action,
                'risk': risk
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم الزخم الإجمالي: {str(e)}")
            return {
                'momentum': MomentumDirection.NEUTRAL,
                'strength': MomentumStrength.VERY_WEAK,
                'confidence': 0,
                'is_actionable': False,
                'action': 'WAIT',
                'risk': 'HIGH'
            }

    def _generate_momentum_recommendations(self, momentum_signals: List[MomentumSignal], acceleration_analysis: AccelerationAnalysis, overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات الزخم"""
        try:
            recommendations = []

            if not momentum_signals:
                recommendations.append("لا توجد إشارات زخم واضحة")
                return recommendations

            # توصيات بناء على عدد الإشارات
            if len(momentum_signals) >= 3:
                recommendations.append("إشارات زخم متعددة تؤكد الاتجاه")
            elif len(momentum_signals) == 1:
                recommendations.append("إشارة زخم واحدة - انتظار المزيد من التأكيد")

            # توصيات بناء على القوة
            strong_signals = [s for s in momentum_signals if s.strength.value >= 4]
            if len(strong_signals) >= 2:
                recommendations.append("زخم قوي متعدد الفترات - إشارة موثوقة")
            elif not strong_signals:
                recommendations.append("زخم ضعيف - توخي الحذر")

            # توصيات بناء على التسارع
            if acceleration_analysis.acceleration_type == AccelerationType.ACCELERATING:
                recommendations.append("تسارع متزايد - قوة الاتجاه تتزايد")
            elif acceleration_analysis.acceleration_type == AccelerationType.DECELERATING:
                recommendations.append("تسارع متناقص - قوة الاتجاه تتراجع")
            elif acceleration_analysis.acceleration_type == AccelerationType.REVERSING:
                recommendations.append("انعكاس في التسارع - احتمال تغيير الاتجاه")

            # توصيات بناء على دعم الحجم
            volume_supported = [s for s in momentum_signals if s.volume_support]
            if len(volume_supported) >= 2:
                recommendations.append("دعم قوي من الحجم")
            elif not volume_supported:
                recommendations.append("نقص في دعم الحجم")

            # توصيات بناء على المخاطر
            if overall_assessment['risk'] == 'HIGH':
                recommendations.append("مستوى مخاطر عالي - تقليل حجم الصفقة")
            elif overall_assessment['risk'] == 'LOW':
                recommendations.append("مستوى مخاطر منخفض - يمكن زيادة حجم الصفقة")

            return recommendations[:5]  # أقصى 5 توصيات

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات الزخم: {str(e)}")
            return []

    def get_momentum_summary(self, analysis: MomentumAnalysisResult) -> Dict[str, Any]:
        """الحصول على ملخص تحليل الزخم"""
        if not analysis:
            return {}

        return {
            'asset': analysis.asset,
            'overall_momentum': analysis.overall_momentum.value,
            'overall_strength': analysis.overall_strength.value,
            'overall_confidence': analysis.overall_confidence,
            'is_actionable': analysis.is_actionable,
            'recommended_action': analysis.recommended_action,
            'risk_level': analysis.risk_level,
            'processing_time_ms': analysis.processing_time_ms,
            'signals_count': len(analysis.momentum_signals),
            'acceleration_type': analysis.acceleration_analysis.acceleration_type.value,
            'acceleration_strength': analysis.acceleration_analysis.acceleration_strength,
            'key_signals': [
                {
                    'period': signal.timeframe_analyzed,
                    'direction': signal.direction.value,
                    'strength': signal.strength.value,
                    'confidence': signal.confidence,
                    'momentum_value': signal.momentum_value,
                    'velocity': signal.velocity,
                    'acceleration': signal.acceleration
                }
                for signal in analysis.momentum_signals
            ],
            'top_recommendations': analysis.recommendations[:3]
        }

    def is_momentum_actionable(self, analysis: MomentumAnalysisResult, min_confidence: float = None) -> bool:
        """تحديد ما إذا كان الزخم قابل للتنفيذ"""
        if not analysis:
            return False

        min_conf = min_confidence or self.min_confidence_threshold

        return (
            analysis.is_actionable and
            analysis.overall_confidence >= min_conf and
            analysis.overall_strength.value >= 3 and
            len(analysis.momentum_signals) >= 2 and
            analysis.overall_momentum != MomentumDirection.NEUTRAL
        )

    def get_momentum_history(self, asset: str, limit: int = 10) -> List[MomentumAnalysisResult]:
        """الحصول على تاريخ الزخم لأصل معين"""
        if asset not in self.momentum_history:
            return []

        return self.momentum_history[asset][-limit:]

    def get_momentum_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات عامة للزخم"""
        try:
            total_analyses = sum(len(history) for history in self.momentum_history.values())
            total_assets = len(self.momentum_history)

            if total_analyses == 0:
                return {
                    'total_analyses': 0,
                    'total_assets': 0,
                    'avg_signals_per_analysis': 0,
                    'most_active_asset': None
                }

            # حساب متوسط الإشارات لكل تحليل
            total_signals = sum(
                len(analysis.momentum_signals)
                for history in self.momentum_history.values()
                for analysis in history
            )
            avg_signals = total_signals / total_analyses

            # العثور على الأصل الأكثر نشاطاً
            most_active_asset = max(
                self.momentum_history.keys(),
                key=lambda asset: len(self.momentum_history[asset])
            ) if self.momentum_history else None

            return {
                'total_analyses': total_analyses,
                'total_assets': total_assets,
                'avg_signals_per_analysis': round(avg_signals, 2),
                'most_active_asset': most_active_asset,
                'momentum_periods': self.momentum_periods,
                'acceleration_periods': self.acceleration_periods,
                'min_confidence_threshold': self.min_confidence_threshold
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الزخم: {str(e)}")
            return {}

# إنشاء instance عام للاستخدام
momentum_acceleration_detector = MomentumAccelerationDetector()
