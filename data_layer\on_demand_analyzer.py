"""
نظام التحليل عند الطلب - المرحلة الرابعة
On-Demand Analysis System for Phase 4 Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70
from database.connection_manager import db_manager

# استيراد أنظمة التحليل المطورة
from data_layer.technical_signals_engine import technical_signals_engine, CombinedSignal
from data_layer.signal_quality_evaluator import signal_quality_evaluator, QualityMetrics

logger = scalping_logger.get_logger("on_demand_analyzer")

@dataclass
class AnalysisRequest:
    """طلب تحليل"""
    asset: str
    timeframe: int = 60  # بالثواني
    candles_count: int = 100  # عدد الشموع المطلوبة
    include_quality_evaluation: bool = True
    include_recommendations: bool = True
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class AnalysisResult:
    """نتيجة التحليل"""
    asset: str
    request_timestamp: datetime
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # بيانات السوق
    current_price: float
    price_change_24h: float
    volatility: float
    
    # نتائج التحليل الفني
    technical_signal: Optional[CombinedSignal]
    signal_summary: Dict[str, Any]
    
    # تقييم الجودة
    quality_metrics: Optional[QualityMetrics]
    quality_summary: Dict[str, Any]
    
    # التوصيات
    is_tradeable: bool
    recommended_action: str  # BUY, SELL, WAIT
    confidence_level: float
    risk_level: str
    recommendations: List[str]
    
    # معلومات إضافية
    data_sources: Dict[str, Any]
    analysis_details: Dict[str, Any]

class OnDemandAnalyzer:
    """نظام التحليل عند الطلب"""
    
    def __init__(self):
        self.redis_client = None
        self.db_manager = None
        self.analysis_cache = {}  # تخزين مؤقت للتحليلات الحديثة
        self.cache_duration = 300  # 5 دقائق

        logger.info("تم تهيئة نظام التحليل عند الطلب")

    async def initialize_connections(self):
        """تهيئة الاتصالات مع قواعد البيانات"""
        try:
            # تهيئة اتصال Redis
            import redis.asyncio as redis
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                decode_responses=True
            )
            
            # تهيئة اتصال PostgreSQL
            self.db_manager = db_manager
            if not self.db_manager._is_initialized:
                self.db_manager.initialize()
            
            logger.info("تم تهيئة الاتصالات مع قواعد البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة الاتصالات: {str(e)}")
            raise

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_asset(self, request: AnalysisRequest) -> Optional[AnalysisResult]:
        """تحليل أصل محدد عند الطلب"""
        start_time = time.time()
        
        try:
            # التحقق من صحة الطلب
            if not self._validate_request(request):
                return None
            
            # التحقق من التخزين المؤقت
            cached_result = self._get_cached_analysis(request.asset)
            if cached_result:
                logger.info(f"تم استرجاع تحليل {request.asset} من التخزين المؤقت")
                return cached_result
            
            logger.info(f"بدء تحليل {request.asset} عند الطلب...")
            
            # 1. جلب البيانات من قواعد البيانات
            market_data = await self._fetch_market_data(request)
            if not market_data:
                logger.error(f"فشل في جلب بيانات {request.asset}")
                return None
            
            # 2. التحليل الفني مع التقاطعات
            technical_signal = await self._perform_technical_analysis(request, market_data)

            # 2.1 التحليل الشامل مع التقاطعات (إذا كان متاحاً)
            enhanced_analysis = None
            try:
                enhanced_analysis = await technical_signals_engine.analyze_with_crossovers(request.asset, market_data.get('candles', []))
            except Exception as e:
                logger.warning(f"لم يتم تحليل التقاطعات لـ {request.asset}: {str(e)}")
            
            # 3. تقييم الجودة
            quality_metrics = None
            if request.include_quality_evaluation and technical_signal:
                quality_metrics = await self._evaluate_signal_quality(technical_signal, market_data)
            
            # 4. إنتاج التوصيات
            recommendations = []
            if request.include_recommendations:
                recommendations = self._generate_recommendations(technical_signal, quality_metrics, market_data)
            
            # 5. تحديد قابلية التداول والإجراء المطلوب
            is_tradeable, recommended_action, confidence_level, risk_level = self._determine_trading_decision(
                technical_signal, quality_metrics, enhanced_analysis
            )
            
            # إنشاء النتيجة النهائية
            processing_time = (time.time() - start_time) * 1000  # بالميلي ثانية
            
            result = AnalysisResult(
                asset=request.asset,
                request_timestamp=request.timestamp,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                
                # بيانات السوق
                current_price=market_data.get('current_price', 0),
                price_change_24h=market_data.get('price_change_24h', 0),
                volatility=market_data.get('volatility', 0),
                
                # نتائج التحليل الفني
                technical_signal=technical_signal,
                signal_summary=technical_signals_engine.get_signal_summary(technical_signal) if technical_signal else {},
                
                # تقييم الجودة
                quality_metrics=quality_metrics,
                quality_summary=signal_quality_evaluator.get_quality_summary(quality_metrics) if quality_metrics else {},
                
                # التوصيات
                is_tradeable=is_tradeable,
                recommended_action=recommended_action,
                confidence_level=confidence_level,
                risk_level=risk_level,
                recommendations=recommendations,
                
                # معلومات إضافية
                data_sources=market_data.get('sources', {}),
                analysis_details={
                    'candles_analyzed': len(market_data.get('candles', [])),
                    'indicators_used': len(technical_signals_engine.indicators),
                    'quality_factors': len(signal_quality_evaluator.quality_weights) if quality_metrics else 0,
                    'crossover_analysis_enabled': enhanced_analysis is not None,
                    'crossover_count': enhanced_analysis.get('crossover_analysis', {}).get('total_crossovers', 0) if enhanced_analysis else 0,
                    'analysis_version': '4.1_with_crossovers'
                }
            )
            
            # حفظ في التخزين المؤقت
            self._cache_analysis(request.asset, result)
            
            logger.info(f"تم إكمال تحليل {request.asset} في {processing_time:.1f}ms - النتيجة: {recommended_action}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل {request.asset}: {str(e)}")
            return None

    def _validate_request(self, request: AnalysisRequest) -> bool:
        """التحقق من صحة طلب التحليل"""
        if not request.asset:
            logger.error("اسم الأصل مطلوب")
            return False
        
        if request.asset not in CURRENCY_PAIRS_70:
            logger.error(f"الأصل {request.asset} غير مدعوم")
            return False
        
        if request.candles_count < 30:
            logger.error("عدد الشموع يجب أن يكون 30 على الأقل")
            return False
        
        return True

    async def _fetch_market_data(self, request: AnalysisRequest) -> Optional[Dict[str, Any]]:
        """جلب بيانات السوق من قواعد البيانات"""
        try:
            market_data = {
                'candles': [],
                'current_price': 0,
                'price_change_24h': 0,
                'volatility': 0,
                'sources': {'redis': False, 'postgresql': False}
            }
            
            # 1. محاولة جلب البيانات من Redis أولاً (البيانات الحديثة)
            redis_candles = await self._fetch_from_redis(request.asset, request.candles_count)
            if redis_candles:
                market_data['candles'].extend(redis_candles)
                market_data['sources']['redis'] = True
                logger.info(f"تم جلب {len(redis_candles)} شمعة من Redis لـ {request.asset}")
            
            # 2. إذا لم تكن البيانات كافية، جلب من PostgreSQL
            if len(market_data['candles']) < request.candles_count:
                needed_candles = request.candles_count - len(market_data['candles'])
                pg_candles = await self._fetch_from_postgresql(request.asset, needed_candles)
                if pg_candles:
                    # دمج البيانات (PostgreSQL أولاً، ثم Redis)
                    market_data['candles'] = pg_candles + market_data['candles']
                    market_data['sources']['postgresql'] = True
                    logger.info(f"تم جلب {len(pg_candles)} شمعة من PostgreSQL لـ {request.asset}")
            
            # 3. حساب المعلومات الإضافية
            if market_data['candles']:
                market_data['current_price'] = market_data['candles'][-1]['close']
                market_data['price_change_24h'] = self._calculate_price_change_24h(market_data['candles'])
                market_data['volatility'] = self._calculate_volatility(market_data['candles'])
            
            logger.info(f"تم جلب {len(market_data['candles'])} شمعة إجمالية لـ {request.asset}")
            
            return market_data if market_data['candles'] else None
            
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات {request.asset}: {str(e)}")
            return None

    async def _fetch_from_redis(self, asset: str, count: int) -> List[Dict[str, Any]]:
        """جلب البيانات من Redis"""
        try:
            if not self.redis_client:
                return []
            
            # جلب البيانات من Redis
            redis_key = f"candles:{asset}:60"  # مفتاح الشموع الدقيقة
            candles_data = await self.redis_client.lrange(redis_key, -count, -1)
            
            candles = []
            for candle_str in candles_data:
                try:
                    import json
                    candle = json.loads(candle_str)
                    candles.append(candle)
                except Exception as e:
                    logger.warning(f"خطأ في تحليل شمعة من Redis: {str(e)}")
                    continue
            
            return candles
            
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات من Redis لـ {asset}: {str(e)}")
            return []

    async def _fetch_from_postgresql(self, asset: str, count: int) -> List[Dict[str, Any]]:
        """جلب البيانات من PostgreSQL"""
        try:
            if not self.db_manager:
                return []

            # استعلام PostgreSQL
            from database.models import HistoricalData

            with self.db_manager.get_session() as session:
                query = session.query(HistoricalData).filter(
                    HistoricalData.asset == asset
                ).order_by(HistoricalData.timestamp.desc()).limit(count)

                results = query.all()

                candles = []
                for row in reversed(results):  # ترتيب تصاعدي حسب الوقت
                    candle = {
                        'timestamp': row.timestamp,
                        'open': float(row.open_price),
                        'high': float(row.high_price),
                        'low': float(row.low_price),
                        'close': float(row.close_price),
                        'volume': float(row.volume) if row.volume else 0
                    }
                    candles.append(candle)

                return candles

        except Exception as e:
            logger.error(f"خطأ في جلب البيانات من PostgreSQL لـ {asset}: {str(e)}")
            return []

    async def _perform_technical_analysis(self, request: AnalysisRequest, market_data: Dict[str, Any]) -> Optional[CombinedSignal]:
        """تنفيذ التحليل الفني"""
        try:
            candles = market_data.get('candles', [])
            if len(candles) < 30:
                logger.warning(f"بيانات غير كافية للتحليل الفني لـ {request.asset}")
                return None
            
            # تحليل باستخدام محرك الإشارات الفنية
            signal = await technical_signals_engine.analyze_all_indicators(request.asset, candles)
            
            return signal
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الفني لـ {request.asset}: {str(e)}")
            return None

    async def _evaluate_signal_quality(self, signal: CombinedSignal, market_data: Dict[str, Any]) -> Optional[QualityMetrics]:
        """تقييم جودة الإشارة"""
        try:
            # تقييم باستخدام نظام تقييم الجودة
            quality = await signal_quality_evaluator.evaluate_signal_quality(signal, market_data)
            
            return quality
            
        except Exception as e:
            logger.error(f"خطأ في تقييم جودة الإشارة: {str(e)}")
            return None

    def _generate_recommendations(self, signal: Optional[CombinedSignal], quality: Optional[QualityMetrics], market_data: Dict[str, Any]) -> List[str]:
        """إنتاج التوصيات"""
        recommendations = []
        
        try:
            # توصيات من تقييم الجودة
            if quality and quality.recommendations:
                recommendations.extend(quality.recommendations[:3])  # أهم 3 توصيات
            
            # توصيات إضافية بناء على ظروف السوق
            volatility = market_data.get('volatility', 0)
            if volatility > 0.003:
                recommendations.append("تقلبات عالية - تقليل حجم الصفقة")
            elif volatility < 0.0005:
                recommendations.append("تقلبات منخفضة - قد لا تكون مناسبة للسكالبينغ")
            
            # توصيات بناء على الإشارة
            if signal:
                if len(signal.supporting_signals) >= 8:
                    recommendations.append("إجماع قوي بين المؤشرات - إشارة موثوقة")
                elif len(signal.conflicting_signals) > len(signal.supporting_signals):
                    recommendations.append("تضارب في الإشارات - انتظار وضوح أكبر")
            
            return recommendations[:5]  # أقصى 5 توصيات
            
        except Exception as e:
            logger.error(f"خطأ في إنتاج التوصيات: {str(e)}")
            return []

    def _determine_trading_decision(self, signal: Optional[CombinedSignal], quality: Optional[QualityMetrics], enhanced_analysis: Optional[Dict[str, Any]] = None) -> Tuple[bool, str, float, str]:
        """تحديد قرار التداول"""
        try:
            # القيم الافتراضية
            is_tradeable = False
            recommended_action = "WAIT"
            confidence_level = 0.0
            risk_level = "HIGH"

            if not signal:
                return is_tradeable, recommended_action, confidence_level, risk_level

            # استخدام التحليل المحسن إذا كان متاحاً
            if enhanced_analysis and enhanced_analysis.get('combined_assessment'):
                combined_assessment = enhanced_analysis['combined_assessment']

                # استخدام التقييم المدمج
                is_tradeable = combined_assessment.get('is_tradeable', False)
                recommended_action = combined_assessment.get('recommended_action', 'WAIT')
                confidence_level = combined_assessment.get('overall_confidence', 0.0)
                risk_level = combined_assessment.get('risk_level', 'HIGH')

                logger.info(f"استخدام التحليل المحسن: {recommended_action} (ثقة: {confidence_level}%)")

            else:
                # التقييم التقليدي
                if quality:
                    is_tradeable = signal_quality_evaluator.is_signal_tradeable(quality)
                    risk_level = quality.risk_level
                    confidence_level = signal.confidence_score * quality.confidence_adjustment
                else:
                    # تقييم أساسي بدون نظام الجودة
                    is_tradeable = (signal.confidence_score >= 70 and
                                  len(signal.supporting_signals) >= 3 and
                                  len(signal.conflicting_signals) <= len(signal.supporting_signals))
                    confidence_level = signal.confidence_score
                    risk_level = "MEDIUM"

                # تحديد الإجراء المطلوب
                if is_tradeable:
                    if signal.direction.value == "BULLISH":
                        recommended_action = "BUY"
                    elif signal.direction.value == "BEARISH":
                        recommended_action = "SELL"
                    else:
                        recommended_action = "WAIT"
                        is_tradeable = False

            return is_tradeable, recommended_action, confidence_level, risk_level
            
        except Exception as e:
            logger.error(f"خطأ في تحديد قرار التداول: {str(e)}")
            return False, "WAIT", 0.0, "HIGH"

    def _calculate_price_change_24h(self, candles: List[Dict[str, Any]]) -> float:
        """حساب تغيير السعر خلال 24 ساعة"""
        try:
            if len(candles) < 1440:  # 24 ساعة = 1440 دقيقة
                return 0.0

            current_price = candles[-1]['close']
            price_24h_ago = candles[-1440]['close']

            change_percent = ((current_price - price_24h_ago) / price_24h_ago) * 100
            return round(change_percent, 4)

        except Exception as e:
            logger.error(f"خطأ في حساب تغيير السعر 24 ساعة: {str(e)}")
            return 0.0

    def _calculate_volatility(self, candles: List[Dict[str, Any]]) -> float:
        """حساب التقلبات"""
        try:
            if len(candles) < 20:
                return 0.0

            # حساب التقلبات باستخدام آخر 20 شمعة
            recent_candles = candles[-20:]
            price_changes = []

            for i in range(1, len(recent_candles)):
                change = abs(recent_candles[i]['close'] - recent_candles[i-1]['close'])
                price_changes.append(change)

            if price_changes:
                import statistics
                volatility = statistics.stdev(price_changes)
                return round(volatility, 6)

            return 0.0

        except Exception as e:
            logger.error(f"خطأ في حساب التقلبات: {str(e)}")
            return 0.0

    def _get_cached_analysis(self, asset: str) -> Optional[AnalysisResult]:
        """الحصول على تحليل من التخزين المؤقت"""
        try:
            if asset in self.analysis_cache:
                cached_data = self.analysis_cache[asset]
                cache_time = cached_data['timestamp']

                # التحقق من انتهاء صلاحية التخزين المؤقت
                if (datetime.now() - cache_time).total_seconds() < self.cache_duration:
                    return cached_data['result']
                else:
                    # حذف التخزين المؤقت المنتهي الصلاحية
                    del self.analysis_cache[asset]

            return None

        except Exception as e:
            logger.error(f"خطأ في استرجاع التحليل المؤقت لـ {asset}: {str(e)}")
            return None

    def _cache_analysis(self, asset: str, result: AnalysisResult):
        """حفظ التحليل في التخزين المؤقت"""
        try:
            self.analysis_cache[asset] = {
                'timestamp': datetime.now(),
                'result': result
            }

            # تنظيف التخزين المؤقت القديم
            self._cleanup_cache()

        except Exception as e:
            logger.error(f"خطأ في حفظ التحليل المؤقت لـ {asset}: {str(e)}")

    def _cleanup_cache(self):
        """تنظيف التخزين المؤقت من البيانات القديمة"""
        try:
            current_time = datetime.now()
            expired_assets = []

            for asset, cached_data in self.analysis_cache.items():
                cache_time = cached_data['timestamp']
                if (current_time - cache_time).total_seconds() >= self.cache_duration:
                    expired_assets.append(asset)

            for asset in expired_assets:
                del self.analysis_cache[asset]

            if expired_assets:
                logger.info(f"تم تنظيف {len(expired_assets)} تحليل منتهي الصلاحية من التخزين المؤقت")

        except Exception as e:
            logger.error(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")

    async def get_analysis_summary(self, asset: str) -> Dict[str, Any]:
        """الحصول على ملخص سريع للتحليل"""
        try:
            # إنشاء طلب تحليل مبسط
            request = AnalysisRequest(
                asset=asset,
                candles_count=50,  # عدد أقل للسرعة
                include_quality_evaluation=False,
                include_recommendations=False
            )

            # تنفيذ التحليل
            result = await self.analyze_asset(request)

            if not result:
                return {'error': f'فشل في تحليل {asset}'}

            return {
                'asset': result.asset,
                'current_price': result.current_price,
                'recommended_action': result.recommended_action,
                'confidence_level': round(result.confidence_level, 1),
                'risk_level': result.risk_level,
                'is_tradeable': result.is_tradeable,
                'processing_time_ms': round(result.processing_time_ms, 1),
                'analysis_timestamp': result.analysis_timestamp.isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص التحليل لـ {asset}: {str(e)}")
            return {'error': str(e)}

    async def batch_analyze_assets(self, assets: List[str], quick_mode: bool = True) -> Dict[str, Dict[str, Any]]:
        """تحليل مجموعة من الأصول"""
        try:
            results = {}

            for asset in assets:
                try:
                    if quick_mode:
                        # تحليل سريع
                        summary = await self.get_analysis_summary(asset)
                        results[asset] = summary
                    else:
                        # تحليل كامل
                        request = AnalysisRequest(asset=asset)
                        result = await self.analyze_asset(request)

                        if result:
                            results[asset] = {
                                'recommended_action': result.recommended_action,
                                'confidence_level': result.confidence_level,
                                'risk_level': result.risk_level,
                                'is_tradeable': result.is_tradeable,
                                'recommendations': result.recommendations[:3]
                            }
                        else:
                            results[asset] = {'error': 'فشل في التحليل'}

                    # تأخير قصير بين التحليلات
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.error(f"خطأ في تحليل {asset}: {str(e)}")
                    results[asset] = {'error': str(e)}

            logger.info(f"تم تحليل {len(results)} أصل من أصل {len(assets)}")
            return results

        except Exception as e:
            logger.error(f"خطأ في التحليل المجمع: {str(e)}")
            return {}

    def get_cache_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        try:
            current_time = datetime.now()
            active_cache = 0
            expired_cache = 0

            for cached_data in self.analysis_cache.values():
                cache_time = cached_data['timestamp']
                if (current_time - cache_time).total_seconds() < self.cache_duration:
                    active_cache += 1
                else:
                    expired_cache += 1

            return {
                'total_cached': len(self.analysis_cache),
                'active_cache': active_cache,
                'expired_cache': expired_cache,
                'cache_duration_seconds': self.cache_duration,
                'cache_hit_rate': 'غير متاح'  # يمكن تطويره لاحقاً
            }

        except Exception as e:
            logger.error(f"خطأ في إحصائيات التخزين المؤقت: {str(e)}")
            return {}

    async def close_connections(self):
        """إغلاق الاتصالات"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                logger.info("تم إغلاق اتصال Redis")

            if self.db_manager:
                # لا حاجة لإغلاق db_manager لأنه يدير الاتصالات تلقائياً
                logger.info("تم إنهاء استخدام PostgreSQL")

        except Exception as e:
            logger.error(f"خطأ في إغلاق الاتصالات: {str(e)}")

# إنشاء instance عام للاستخدام
on_demand_analyzer = OnDemandAnalyzer()
