"""
نظام التحقق من صحة أزواج العملات وتوافرها في المنصة
"""

import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from loguru import logger

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import POCKET_OPTION_CONFIG
from BinaryOptionsToolsV2 import pocketoption as PocketOption


class PairValidator:
    """فئة للتحقق من صحة أزواج العملات في المنصة"""
    
    def __init__(self, ssid: str = None):
        """
        تهيئة نظام التحقق من الأزواج
        
        Args:
            ssid: معرف الجلسة للاتصال بالمنصة
        """
        self.ssid = ssid or POCKET_OPTION_CONFIG.get("ssid")
        self.api = None
        self.available_assets = {}
        self.validation_results = {}
        
        logger.info("تم تهيئة نظام التحقق من أزواج العملات")
    
    def connect_to_platform(self) -> bool:
        """
        الاتصال بمنصة Pocket Option
        
        Returns:
            bool: True إذا نجح الاتصال، False إذا فشل
        """
        try:
            logger.info("محاولة الاتصال بمنصة Pocket Option...")
            
            if not self.ssid:
                logger.error("معرف الجلسة (SSID) غير متوفر")
                return False
            
            self.api = PocketOption(self.ssid)
            time.sleep(3)  # انتظار لضمان الاتصال
            
            # اختبار الاتصال بجلب الرصيد
            balance = self.api.balance()
            logger.success(f"تم الاتصال بنجاح - الرصيد: ${balance}")
            
            return True
            
        except Exception as e:
            logger.error(f"فشل الاتصال بالمنصة: {e}")
            return False
    
    def get_available_assets(self) -> Dict[str, float]:
        """
        جلب جميع الأصول المتاحة من المنصة
        
        Returns:
            Dict[str, float]: قاموس الأصول مع نسب أرباحها
        """
        try:
            logger.info("جلب قائمة الأصول المتاحة من المنصة...")
            
            if not self.api:
                logger.error("لم يتم الاتصال بالمنصة بعد")
                return {}
            
            # جلب جميع نسب الأرباح (تحتوي على جميع الأصول المتاحة)
            payouts = self.api.payout()
            
            if not payouts:
                logger.warning("لم يتم العثور على أصول متاحة")
                return {}
            
            self.available_assets = payouts
            logger.success(f"تم جلب {len(payouts)} أصل متاح من المنصة")
            
            return payouts
            
        except Exception as e:
            logger.error(f"خطأ في جلب الأصول المتاحة: {e}")
            return {}
    
    def validate_single_pair(self, pair: str) -> Dict:
        """
        التحقق من صحة زوج واحد
        
        Args:
            pair: اسم الزوج للتحقق منه
            
        Returns:
            Dict: نتيجة التحقق
        """
        result = {
            "pair": pair,
            "available": False,
            "exact_match": False,
            "similar_matches": [],
            "payout": None,
            "status": "not_found"
        }
        
        try:
            # البحث عن تطابق تام
            if pair in self.available_assets:
                result.update({
                    "available": True,
                    "exact_match": True,
                    "payout": self.available_assets[pair],
                    "status": "exact_match"
                })
                logger.debug(f"✅ {pair}: تطابق تام - نسبة الربح: {result['payout']}%")
                return result
            
            # البحث عن تطابقات مشابهة
            similar_pairs = []
            pair_base = pair.replace("_otc", "").upper()
            
            for available_pair in self.available_assets.keys():
                available_base = available_pair.replace("_otc", "").upper()
                
                # تطابق الأساس
                if pair_base == available_base:
                    similar_pairs.append({
                        "name": available_pair,
                        "payout": self.available_assets[available_pair],
                        "match_type": "base_match"
                    })
                # تطابق جزئي
                elif pair_base in available_base or available_base in pair_base:
                    similar_pairs.append({
                        "name": available_pair,
                        "payout": self.available_assets[available_pair],
                        "match_type": "partial_match"
                    })
            
            if similar_pairs:
                result.update({
                    "similar_matches": similar_pairs,
                    "status": "similar_found"
                })
                logger.debug(f"⚠️ {pair}: لم يوجد تطابق تام، وجد {len(similar_pairs)} تطابق مشابه")
            else:
                logger.debug(f"❌ {pair}: غير متوفر في المنصة")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الزوج {pair}: {e}")
            result["status"] = "error"
            result["error"] = str(e)
            return result
    
    def validate_all_pairs(self) -> Dict:
        """
        التحقق من صحة جميع الأزواج الـ70
        
        Returns:
            Dict: نتائج التحقق الشاملة
        """
        logger.info("بدء التحقق من صحة جميع الأزواج الـ70...")
        
        validation_summary = {
            "total_pairs": len(CURRENCY_PAIRS_70),
            "exact_matches": 0,
            "similar_matches": 0,
            "not_found": 0,
            "errors": 0,
            "validation_time": datetime.now().isoformat(),
            "results": {}
        }
        
        for i, pair in enumerate(CURRENCY_PAIRS_70, 1):
            logger.info(f"التحقق من الزوج {i}/{len(CURRENCY_PAIRS_70)}: {pair}")
            
            result = self.validate_single_pair(pair)
            validation_summary["results"][pair] = result
            
            # تحديث الإحصائيات
            if result["status"] == "exact_match":
                validation_summary["exact_matches"] += 1
            elif result["status"] == "similar_found":
                validation_summary["similar_matches"] += 1
            elif result["status"] == "not_found":
                validation_summary["not_found"] += 1
            elif result["status"] == "error":
                validation_summary["errors"] += 1
            
            # توقف قصير لتجنب الحمل الزائد
            time.sleep(0.1)
        
        self.validation_results = validation_summary
        
        logger.success(f"انتهى التحقق من الأزواج:")
        logger.info(f"  - تطابق تام: {validation_summary['exact_matches']}")
        logger.info(f"  - تطابق مشابه: {validation_summary['similar_matches']}")
        logger.info(f"  - غير موجود: {validation_summary['not_found']}")
        logger.info(f"  - أخطاء: {validation_summary['errors']}")
        
        return validation_summary

    def generate_report(self, save_to_file: bool = True) -> str:
        """
        إنشاء تقرير مفصل عن نتائج التحقق

        Args:
            save_to_file: حفظ التقرير في ملف

        Returns:
            str: التقرير النصي
        """
        if not self.validation_results:
            return "لا توجد نتائج تحقق متاحة"

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("تقرير التحقق من صحة أزواج العملات")
        report_lines.append("=" * 80)
        report_lines.append(f"تاريخ التحقق: {self.validation_results['validation_time']}")
        report_lines.append(f"إجمالي الأزواج: {self.validation_results['total_pairs']}")
        report_lines.append("")

        # ملخص النتائج
        report_lines.append("📊 ملخص النتائج:")
        report_lines.append(f"✅ تطابق تام: {self.validation_results['exact_matches']}")
        report_lines.append(f"⚠️ تطابق مشابه: {self.validation_results['similar_matches']}")
        report_lines.append(f"❌ غير موجود: {self.validation_results['not_found']}")
        report_lines.append(f"💥 أخطاء: {self.validation_results['errors']}")
        report_lines.append("")

        # الأزواج ذات التطابق التام
        exact_matches = [pair for pair, result in self.validation_results['results'].items()
                        if result['status'] == 'exact_match']
        if exact_matches:
            report_lines.append("✅ الأزواج المتاحة بتطابق تام:")
            for pair in exact_matches:
                payout = self.validation_results['results'][pair]['payout']
                report_lines.append(f"   {pair}: {payout}%")
            report_lines.append("")

        # الأزواج ذات التطابق المشابه
        similar_matches = [pair for pair, result in self.validation_results['results'].items()
                          if result['status'] == 'similar_found']
        if similar_matches:
            report_lines.append("⚠️ الأزواج ذات التطابق المشابه:")
            for pair in similar_matches:
                result = self.validation_results['results'][pair]
                report_lines.append(f"   {pair}:")
                for match in result['similar_matches']:
                    report_lines.append(f"     - {match['name']}: {match['payout']}% ({match['match_type']})")
            report_lines.append("")

        # الأزواج غير الموجودة
        not_found = [pair for pair, result in self.validation_results['results'].items()
                    if result['status'] == 'not_found']
        if not_found:
            report_lines.append("❌ الأزواج غير الموجودة:")
            for pair in not_found:
                report_lines.append(f"   {pair}")
            report_lines.append("")

        # الأخطاء
        errors = [pair for pair, result in self.validation_results['results'].items()
                 if result['status'] == 'error']
        if errors:
            report_lines.append("💥 أخطاء في التحقق:")
            for pair in errors:
                error_msg = self.validation_results['results'][pair].get('error', 'خطأ غير محدد')
                report_lines.append(f"   {pair}: {error_msg}")
            report_lines.append("")

        report_lines.append("=" * 80)

        report_text = "\n".join(report_lines)

        if save_to_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/pair_validation_report_{timestamp}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                logger.info(f"تم حفظ التقرير في: {filename}")
            except Exception as e:
                logger.error(f"خطأ في حفظ التقرير: {e}")

        return report_text

    def save_results_json(self, filename: str = None) -> bool:
        """
        حفظ نتائج التحقق في ملف JSON

        Args:
            filename: اسم الملف (اختياري)

        Returns:
            bool: True إذا نجح الحفظ
        """
        if not self.validation_results:
            logger.error("لا توجد نتائج للحفظ")
            return False

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/pair_validation_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.validation_results, f, ensure_ascii=False, indent=2)

            logger.success(f"تم حفظ النتائج في: {filename}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ النتائج: {e}")
            return False

    def get_recommended_pairs(self) -> List[str]:
        """
        الحصول على قائمة الأزواج الموصى بها (المتاحة فعلياً)

        Returns:
            List[str]: قائمة الأزواج الموصى بها
        """
        if not self.validation_results:
            logger.warning("لا توجد نتائج تحقق متاحة")
            return []

        recommended = []

        # إضافة الأزواج ذات التطابق التام
        for pair, result in self.validation_results['results'].items():
            if result['status'] == 'exact_match':
                recommended.append(pair)

        # إضافة أفضل تطابق من الأزواج المشابهة
        for pair, result in self.validation_results['results'].items():
            if result['status'] == 'similar_found' and result['similar_matches']:
                # اختيار أفضل تطابق (أعلى نسبة ربح)
                best_match = max(result['similar_matches'], key=lambda x: x['payout'])
                if best_match['match_type'] == 'base_match':  # تفضيل التطابق الأساسي
                    recommended.append(best_match['name'])

        logger.info(f"تم العثور على {len(recommended)} زوج موصى به")
        return recommended

    def run_full_validation(self) -> bool:
        """
        تشغيل عملية التحقق الكاملة

        Returns:
            bool: True إذا نجحت العملية
        """
        try:
            logger.info("🚀 بدء عملية التحقق الكاملة من أزواج العملات")

            # 1. الاتصال بالمنصة
            if not self.connect_to_platform():
                return False

            # 2. جلب الأصول المتاحة
            if not self.get_available_assets():
                return False

            # 3. التحقق من جميع الأزواج
            self.validate_all_pairs()

            # 4. إنشاء التقرير
            report = self.generate_report(save_to_file=True)
            print(report)

            # 5. حفظ النتائج
            self.save_results_json()

            # 6. عرض الأزواج الموصى بها
            recommended = self.get_recommended_pairs()
            logger.info(f"الأزواج الموصى بها: {len(recommended)} زوج")

            logger.success("✅ انتهت عملية التحقق بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في عملية التحقق: {e}")
            return False


def main():
    """دالة رئيسية لاختبار النظام"""
    validator = PairValidator()
    success = validator.run_full_validation()

    if success:
        print("\n🎉 تم التحقق من الأزواج بنجاح!")
    else:
        print("\n❌ فشل في التحقق من الأزواج")


# إنشاء instance عام
pair_validator = PairValidator()

if __name__ == "__main__":
    main()
