"""
نظام مراقبة وتحديث نسب الأرباح لأزواج العملات
"""

import time
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from loguru import logger

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import POCKET_OPTION_CONFIG
from BinaryOptionsToolsV2 import pocketoption as PocketOption


@dataclass
class PayoutRecord:
    """سجل نسبة الربح لزوج معين"""
    pair: str
    payout: float
    timestamp: str
    is_available: bool = True
    
    def to_dict(self) -> Dict:
        return asdict(self)


class PayoutMonitor:
    """نظام مراقبة نسب الأرباح"""
    
    def __init__(self, ssid: str = None, update_interval: int = 300):
        """
        تهيئة نظام مراقبة نسب الأرباح
        
        Args:
            ssid: معرف الجلسة
            update_interval: فترة التحديث بالثواني (افتراضي: 5 دقائق)
        """
        self.ssid = ssid or POCKET_OPTION_CONFIG.get("ssid")
        self.update_interval = update_interval
        self.api = None
        
        # بيانات نسب الأرباح
        self.current_payouts: Dict[str, PayoutRecord] = {}
        self.payout_history: List[Dict] = []
        self.last_update: Optional[datetime] = None
        
        # إعدادات التنبيهات
        self.alert_threshold = 5.0  # تنبيه عند تغيير أكثر من 5%
        self.min_payout_threshold = 70.0  # الحد الأدنى لنسبة الربح المقبولة
        
        logger.info("تم تهيئة نظام مراقبة نسب الأرباح")
    
    def connect_to_platform(self) -> bool:
        """الاتصال بالمنصة"""
        try:
            logger.info("الاتصال بمنصة Pocket Option...")
            
            if not self.ssid:
                logger.error("معرف الجلسة غير متوفر")
                return False
            
            self.api = PocketOption(self.ssid)
            time.sleep(3)
            
            # اختبار الاتصال
            balance = self.api.balance()
            logger.success(f"تم الاتصال بنجاح - الرصيد: ${balance}")
            
            return True
            
        except Exception as e:
            logger.error(f"فشل الاتصال: {e}")
            return False
    
    def fetch_current_payouts(self) -> Dict[str, float]:
        """جلب نسب الأرباح الحالية"""
        try:
            logger.info("جلب نسب الأرباح الحالية...")
            
            if not self.api:
                logger.error("لم يتم الاتصال بالمنصة")
                return {}
            
            # جلب جميع نسب الأرباح
            all_payouts = self.api.payout()
            
            if not all_payouts:
                logger.warning("لم يتم العثور على نسب أرباح")
                return {}
            
            logger.success(f"تم جلب نسب الأرباح لـ {len(all_payouts)} أصل")
            return all_payouts
            
        except Exception as e:
            logger.error(f"خطأ في جلب نسب الأرباح: {e}")
            return {}
    
    def update_payout_records(self, new_payouts: Dict[str, float]) -> List[str]:
        """
        تحديث سجلات نسب الأرباح
        
        Args:
            new_payouts: نسب الأرباح الجديدة
            
        Returns:
            List[str]: قائمة التنبيهات
        """
        alerts = []
        timestamp = datetime.now().isoformat()
        
        # تحديث الأزواج الموجودة في قائمتنا
        for pair in CURRENCY_PAIRS_70:
            old_record = self.current_payouts.get(pair)
            new_payout = new_payouts.get(pair)
            
            if new_payout is not None:
                # الزوج متاح
                new_record = PayoutRecord(
                    pair=pair,
                    payout=new_payout,
                    timestamp=timestamp,
                    is_available=True
                )
                
                # التحقق من التغييرات الكبيرة
                if old_record and old_record.is_available:
                    change = abs(new_payout - old_record.payout)
                    change_percent = (change / old_record.payout) * 100
                    
                    if change_percent >= self.alert_threshold:
                        direction = "زيادة" if new_payout > old_record.payout else "انخفاض"
                        alert = f"تغيير كبير في {pair}: {direction} من {old_record.payout}% إلى {new_payout}% ({change_percent:.1f}%)"
                        alerts.append(alert)
                        logger.warning(alert)
                
                # التحقق من الحد الأدنى
                if new_payout < self.min_payout_threshold:
                    alert = f"نسبة ربح منخفضة في {pair}: {new_payout}%"
                    alerts.append(alert)
                    logger.warning(alert)
                
                self.current_payouts[pair] = new_record
                
            else:
                # الزوج غير متاح
                if old_record and old_record.is_available:
                    alert = f"الزوج {pair} لم يعد متاحاً"
                    alerts.append(alert)
                    logger.warning(alert)
                
                new_record = PayoutRecord(
                    pair=pair,
                    payout=0.0,
                    timestamp=timestamp,
                    is_available=False
                )
                self.current_payouts[pair] = new_record
        
        # حفظ في السجل التاريخي
        history_entry = {
            "timestamp": timestamp,
            "payouts": {pair: record.to_dict() for pair, record in self.current_payouts.items()},
            "alerts": alerts,
            "total_available": sum(1 for record in self.current_payouts.values() if record.is_available),
            "average_payout": self._calculate_average_payout()
        }
        self.payout_history.append(history_entry)
        
        # الاحتفاظ بآخر 100 سجل فقط
        if len(self.payout_history) > 100:
            self.payout_history = self.payout_history[-100:]
        
        self.last_update = datetime.now()
        
        return alerts
    
    def _calculate_average_payout(self) -> float:
        """حساب متوسط نسب الأرباح للأزواج المتاحة"""
        available_payouts = [
            record.payout for record in self.current_payouts.values() 
            if record.is_available and record.payout > 0
        ]
        
        if not available_payouts:
            return 0.0
        
        return sum(available_payouts) / len(available_payouts)
    
    def get_payout_summary(self) -> Dict:
        """الحصول على ملخص نسب الأرباح"""
        available_pairs = [
            record for record in self.current_payouts.values() 
            if record.is_available
        ]
        
        if not available_pairs:
            return {
                "total_pairs": len(CURRENCY_PAIRS_70),
                "available_pairs": 0,
                "unavailable_pairs": len(CURRENCY_PAIRS_70),
                "average_payout": 0.0,
                "highest_payout": None,
                "lowest_payout": None,
                "last_update": self.last_update.isoformat() if self.last_update else None
            }
        
        payouts = [record.payout for record in available_pairs]
        highest = max(available_pairs, key=lambda x: x.payout)
        lowest = min(available_pairs, key=lambda x: x.payout)
        
        return {
            "total_pairs": len(CURRENCY_PAIRS_70),
            "available_pairs": len(available_pairs),
            "unavailable_pairs": len(CURRENCY_PAIRS_70) - len(available_pairs),
            "average_payout": sum(payouts) / len(payouts),
            "highest_payout": {"pair": highest.pair, "payout": highest.payout},
            "lowest_payout": {"pair": lowest.pair, "payout": lowest.payout},
            "last_update": self.last_update.isoformat() if self.last_update else None
        }

    def get_best_pairs(self, top_n: int = 10) -> List[Dict]:
        """الحصول على أفضل الأزواج حسب نسبة الربح"""
        available_pairs = [
            record for record in self.current_payouts.values()
            if record.is_available and record.payout > 0
        ]

        # ترتيب حسب نسبة الربح
        sorted_pairs = sorted(available_pairs, key=lambda x: x.payout, reverse=True)

        return [
            {"pair": record.pair, "payout": record.payout, "timestamp": record.timestamp}
            for record in sorted_pairs[:top_n]
        ]

    def save_data(self, filename: str = None) -> bool:
        """حفظ البيانات في ملف JSON"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/payout_data_{timestamp}.json"

        try:
            data = {
                "current_payouts": {pair: record.to_dict() for pair, record in self.current_payouts.items()},
                "payout_history": self.payout_history,
                "summary": self.get_payout_summary(),
                "last_update": self.last_update.isoformat() if self.last_update else None
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.success(f"تم حفظ البيانات في: {filename}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ البيانات: {e}")
            return False

    def run_single_update(self) -> bool:
        """تشغيل تحديث واحد لنسب الأرباح"""
        try:
            logger.info("🔄 تشغيل تحديث واحد لنسب الأرباح")

            # الاتصال بالمنصة
            if not self.connect_to_platform():
                return False

            # جلب نسب الأرباح
            new_payouts = self.fetch_current_payouts()
            if not new_payouts:
                return False

            # تحديث السجلات
            alerts = self.update_payout_records(new_payouts)

            # عرض النتائج
            summary = self.get_payout_summary()
            print(f"\n📊 ملخص نسب الأرباح:")
            print(f"الأزواج المتاحة: {summary['available_pairs']}/{summary['total_pairs']}")
            print(f"متوسط نسبة الربح: {summary['average_payout']:.2f}%")

            if alerts:
                print(f"\n🚨 التنبيهات ({len(alerts)}):")
                for alert in alerts:
                    print(f"  - {alert}")

            # حفظ البيانات
            self.save_data()

            logger.success("تم التحديث بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في التحديث: {e}")
            return False


def main():
    """دالة رئيسية لاختبار النظام"""
    monitor = PayoutMonitor()
    success = monitor.run_single_update()

    if success:
        print("\n🎉 تم تحديث نسب الأرباح بنجاح!")
    else:
        print("\n❌ فشل في تحديث نسب الأرباح")


# إنشاء instance عام
payout_monitor = PayoutMonitor()

if __name__ == "__main__":
    main()
