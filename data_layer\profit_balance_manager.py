"""
مدير نسب الأرباح ورصيد الحساب - نظام تخزين وتحديث مستمر
Profit Rates and Account Balance Manager - Storage and Continuous Update System
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from config.trading_config import default_trading_config
from config.currency_pairs import CURRENCY_PAIRS_70
from data_layer.realtime_collector import realtime_collector
from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors, handle_errors

logger = scalping_logger.get_logger("profit_balance_manager")

class ProfitBalanceManager:
    """مدير نسب الأرباح ورصيد الحساب مع التحديث المستمر"""
    
    def __init__(self, config=None):
        self.config = config or default_trading_config
        self.realtime_collector = realtime_collector
        
        # إعدادات النظام
        self.is_monitoring = False
        self.update_interval = 5.0  # تحديث كل 5 ثوان
        self.balance_update_interval = 10.0  # تحديث الرصيد كل 10 ثوان
        
        # بيانات نسب الأرباح
        self.profit_rates = {}  # {asset: profit_rate}
        self.last_profit_update = {}  # {asset: timestamp}
        
        # بيانات رصيد الحساب
        self.account_balance = {
            'current_balance': 0.0,
            'initial_balance': 0.0,
            'total_profit': 0.0,
            'total_loss': 0.0,
            'last_update': None,
            'currency': 'USD'
        }
        
        # إحصائيات النظام
        self.stats = {
            'profit_updates': 0,
            'balance_updates': 0,
            'errors': 0,
            'start_time': None,
            'last_successful_update': None
        }
        
        logger.info("تم تهيئة مدير نسب الأرباح ورصيد الحساب")

    def _format_asset_name(self, asset: str) -> str:
        """تحويل اسم الأصل إلى التنسيق الصحيح للمنصة"""
        try:
            # إذا كان الأصل يحتوي على _otc بالفعل، أرجعه كما هو
            if "_otc" in asset.lower():
                return asset

            # إضافة _otc للأصول العادية
            return f"{asset}_otc"

        except Exception as e:
            logger.debug(f"خطأ في تنسيق اسم الأصل {asset}: {str(e)}")
            return asset
    
    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_system(self) -> bool:
        """تهيئة النظام"""
        try:
            logger.info("🔧 تهيئة نظام إدارة الأرباح والرصيد...")
            
            # التأكد من اتصال Redis
            if not self.realtime_collector.redis_client:
                await self.realtime_collector.initialize_redis()
            
            if not self.realtime_collector.redis_client:
                logger.error("فشل في الاتصال بـ Redis")
                return False
            
            # تحميل البيانات المحفوظة
            await self._load_saved_data()
            
            # تحديث الرصيد الأولي
            await self._update_account_balance()
            
            logger.info("✅ تم تهيئة نظام إدارة الأرباح والرصيد بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة النظام: {str(e)}")
            return False
    
    async def _load_saved_data(self):
        """تحميل البيانات المحفوظة من Redis"""
        try:
            # تحميل نسب الأرباح
            profit_data = self.realtime_collector.redis_client.get("profit_rates:all")
            if profit_data:
                self.profit_rates = json.loads(profit_data)
                logger.info(f"تم تحميل نسب الأرباح لـ {len(self.profit_rates)} أصل")
            
            # تحميل رصيد الحساب
            balance_data = self.realtime_collector.redis_client.get("account_balance")
            if balance_data:
                self.account_balance.update(json.loads(balance_data))
                logger.info(f"تم تحميل رصيد الحساب: ${self.account_balance['current_balance']}")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def start_monitoring(self, assets: List[str] = None) -> bool:
        """بدء مراقبة وتحديث نسب الأرباح والرصيد"""
        try:
            if self.is_monitoring:
                logger.warning("نظام المراقبة قيد التشغيل بالفعل")
                return True
            
            assets = assets or CURRENCY_PAIRS_70
            logger.info(f"🚀 بدء مراقبة نسب الأرباح والرصيد لـ {len(assets)} أصل")
            
            self.is_monitoring = True
            self.stats['start_time'] = datetime.now()
            
            # بدء حلقة المراقبة
            asyncio.create_task(self._monitoring_loop(assets))
            
            logger.info("✅ تم بدء نظام المراقبة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء المراقبة: {str(e)}")
            self.is_monitoring = False
            return False
    
    async def _monitoring_loop(self, assets: List[str]):
        """حلقة مراقبة وتحديث البيانات"""
        try:
            logger.info("🔄 بدء حلقة مراقبة الأرباح والرصيد")
            
            last_balance_update = time.time()
            
            while self.is_monitoring:
                start_time = time.time()
                
                try:
                    # تحديث نسب الأرباح
                    await self._update_profit_rates(assets)
                    
                    # تحديث رصيد الحساب (كل 10 ثوان)
                    if time.time() - last_balance_update >= self.balance_update_interval:
                        await self._update_account_balance()
                        last_balance_update = time.time()
                    
                    # تحديث الإحصائيات
                    self.stats['last_successful_update'] = datetime.now()
                    
                except Exception as e:
                    logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                    self.stats['errors'] += 1
                
                # انتظار حتى الدورة التالية
                processing_time = time.time() - start_time
                sleep_time = max(0, self.update_interval - processing_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
        except Exception as e:
            logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
        finally:
            logger.info("انتهت حلقة مراقبة الأرباح والرصيد")
    
    async def _update_profit_rates(self, assets: List[str]):
        """تحديث نسب الأرباح لجميع الأصول"""
        try:
            updated_count = 0

            # محاولة جلب جميع نسب الأرباح مرة واحدة (أسرع)
            bulk_rates = await self._get_all_profit_rates_bulk(assets)

            if bulk_rates:
                # تحديث من البيانات المجمعة
                for asset, profit_rate in bulk_rates.items():
                    if profit_rate is not None:
                        old_rate = self.profit_rates.get(asset, 0)
                        self.profit_rates[asset] = profit_rate
                        self.last_profit_update[asset] = datetime.now().isoformat()

                        # تخزين في Redis
                        await self._store_profit_rate_in_redis(asset, profit_rate)

                        updated_count += 1

                        # تسجيل التغييرات المهمة
                        if abs(profit_rate - old_rate) > 1.0:  # تغيير أكثر من 1%
                            logger.info(f"تغيير نسبة الربح لـ {asset}: {old_rate:.1f}% → {profit_rate:.1f}%")
            else:
                # الطريقة التقليدية - أصل بأصل
                for asset in assets:
                    try:
                        # جلب نسبة الربح من المنصة
                        profit_rate = await self._get_asset_profit_rate(asset)

                        if profit_rate is not None:
                            # تحديث البيانات
                            old_rate = self.profit_rates.get(asset, 0)
                            self.profit_rates[asset] = profit_rate
                            self.last_profit_update[asset] = datetime.now().isoformat()

                            # تخزين في Redis
                            await self._store_profit_rate_in_redis(asset, profit_rate)

                            updated_count += 1

                            # تسجيل التغييرات المهمة
                            if abs(profit_rate - old_rate) > 1.0:  # تغيير أكثر من 1%
                                logger.info(f"تغيير نسبة الربح لـ {asset}: {old_rate:.1f}% → {profit_rate:.1f}%")

                    except Exception as e:
                        logger.debug(f"خطأ في تحديث نسبة الربح لـ {asset}: {str(e)}")
                        continue

            if updated_count > 0:
                # تخزين جميع النسب
                await self._store_all_profit_rates()
                self.stats['profit_updates'] += updated_count

                logger.debug(f"تم تحديث نسب الأرباح لـ {updated_count}/{len(assets)} أصل")

        except Exception as e:
            logger.error(f"خطأ في تحديث نسب الأرباح: {str(e)}")

    async def _get_all_profit_rates_bulk(self, assets: List[str]) -> Dict[str, float]:
        """جلب نسب الأرباح لجميع الأصول مرة واحدة (أسرع)"""
        try:
            # التأكد من الاتصال
            if not self.realtime_collector.is_connected:
                await self.realtime_collector.initialize_async_connection()

            # التحقق من وجود API (متزامن أو غير متزامن)
            api_to_use = None
            if hasattr(self.realtime_collector, 'api') and self.realtime_collector.api:
                api_to_use = self.realtime_collector.api
            elif hasattr(self.realtime_collector, 'async_api') and self.realtime_collector.async_api:
                api_to_use = self.realtime_collector.async_api

            if not api_to_use:
                logger.debug("لا يوجد API متاح لجلب نسب الأرباح المجمعة")
                return {}

            profit_rates = {}

            try:
                # الطريقة الأولى: جلب جميع نسب الأرباح مرة واحدة باستخدام api.payout()
                if hasattr(api_to_use, 'payout'):
                    if api_to_use == self.realtime_collector.async_api:
                        # API غير متزامن
                        all_payouts = await api_to_use.payout()
                    else:
                        # API متزامن
                        all_payouts = api_to_use.payout()
                else:
                    logger.debug("API لا يحتوي على دالة payout")
                    return {}

                if all_payouts and isinstance(all_payouts, dict):
                    # تصفية الأصول المطلوبة فقط
                    for asset in assets:
                        # جرب الأصل كما هو
                        if asset in all_payouts:
                            profit_rates[asset] = float(all_payouts[asset])
                        else:
                            # جرب مع _otc
                            formatted_asset = self._format_asset_name(asset)
                            if formatted_asset in all_payouts:
                                profit_rates[asset] = float(all_payouts[formatted_asset])
                            # جرب بدون _otc إذا كان موجود
                            elif "_otc" in asset:
                                clean_asset = asset.replace("_otc", "")
                                if clean_asset in all_payouts:
                                    profit_rates[asset] = float(all_payouts[clean_asset])

                    logger.debug(f"تم جلب نسب الأرباح لـ {len(profit_rates)} أصل من إجمالي {len(assets)}")
                    return profit_rates

                # الطريقة الثانية: جلب نسب أرباح عدة أصول معاً
                elif hasattr(api_to_use, 'payout') and len(assets) > 1:
                    # تحويل أسماء الأصول إلى التنسيق الصحيح
                    formatted_assets = [self._format_asset_name(asset) for asset in assets]

                    # جرب تمرير قائمة الأصول المنسقة
                    if api_to_use == self.realtime_collector.async_api:
                        # API غير متزامن
                        payouts_list = await api_to_use.payout(formatted_assets)
                    else:
                        # API متزامن
                        payouts_list = api_to_use.payout(formatted_assets)

                    if payouts_list and isinstance(payouts_list, list) and len(payouts_list) == len(assets):
                        for asset, payout in zip(assets, payouts_list):
                            if payout is not None:
                                profit_rates[asset] = float(payout)

                        return profit_rates

                return {}

            except Exception as api_error:
                logger.debug(f"خطأ في جلب نسب الأرباح المجمعة: {str(api_error)}")
                return {}

        except Exception as e:
            logger.debug(f"خطأ في جلب نسب الأرباح المجمعة: {str(e)}")
            return {}
    
    async def _get_asset_profit_rate(self, asset: str) -> Optional[float]:
        """جلب نسبة الربح لأصل معين من المنصة"""
        try:
            # التأكد من الاتصال
            if not self.realtime_collector.is_connected:
                await self.realtime_collector.initialize_async_connection()

            # التحقق من وجود API (متزامن أو غير متزامن)
            api_to_use = None
            if hasattr(self.realtime_collector, 'api') and self.realtime_collector.api:
                api_to_use = self.realtime_collector.api
            elif hasattr(self.realtime_collector, 'async_api') and self.realtime_collector.async_api:
                api_to_use = self.realtime_collector.async_api

            if not api_to_use:
                logger.debug(f"لا يوجد API متاح لجلب نسبة الربح لـ {asset}")
                return None

            # تحويل اسم الأصل إلى التنسيق الصحيح
            formatted_asset = self._format_asset_name(asset)

            # جلب نسبة الربح للأصل المحدد باستخدام api.payout()
            try:
                # استخدام api.payout() لأصل واحد
                if hasattr(api_to_use, 'payout'):
                    if api_to_use == self.realtime_collector.async_api:
                        # API غير متزامن
                        profit_rate = await api_to_use.payout(formatted_asset)
                    else:
                        # API متزامن
                        profit_rate = api_to_use.payout(formatted_asset)
                else:
                    logger.debug(f"API لا يحتوي على دالة payout لـ {asset}")
                    return None

                if profit_rate is not None:
                    return float(profit_rate)

                # إذا فشل مع _otc، جرب بدونها
                if "_otc" in formatted_asset:
                    original_asset = asset.replace("_otc", "")
                    if api_to_use == self.realtime_collector.async_api:
                        # API غير متزامن
                        profit_rate = await api_to_use.payout(original_asset)
                    else:
                        # API متزامن
                        profit_rate = api_to_use.payout(original_asset)

                    if profit_rate is not None:
                        return float(profit_rate)

                return None

            except Exception as api_error:
                logger.debug(f"خطأ في API لجلب نسبة الربح لـ {asset}: {str(api_error)}")
                return None

        except Exception as e:
            logger.debug(f"خطأ في جلب نسبة الربح لـ {asset}: {str(e)}")
            return None
    
    async def _update_account_balance(self):
        """تحديث رصيد الحساب"""
        try:
            # التأكد من الاتصال
            if not self.realtime_collector.is_connected:
                await self.realtime_collector.initialize_async_connection()

            # التحقق من وجود API (متزامن أو غير متزامن)
            api_to_use = None
            if hasattr(self.realtime_collector, 'api') and self.realtime_collector.api:
                api_to_use = self.realtime_collector.api
            elif hasattr(self.realtime_collector, 'async_api') and self.realtime_collector.async_api:
                api_to_use = self.realtime_collector.async_api

            if not api_to_use:
                logger.warning("لا يوجد API متاح لتحديث الرصيد")
                return

            # جلب الرصيد الحالي باستخدام api.balance()
            logger.debug(f"محاولة جلب الرصيد من API...")
            logger.debug(f"حالة الاتصال: {self.realtime_collector.is_connected}")
            logger.debug(f"API متاح: {api_to_use is not None}")
            logger.debug(f"نوع API: {'async' if api_to_use == self.realtime_collector.async_api else 'sync'}")

            # استخدام API المناسب
            if hasattr(api_to_use, 'balance'):
                if api_to_use == self.realtime_collector.async_api:
                    # API غير متزامن
                    current_balance = await api_to_use.balance()
                else:
                    # API متزامن
                    current_balance = api_to_use.balance()
            else:
                logger.warning("API لا يحتوي على دالة balance")
                return

            logger.debug(f"الرصيد المجلوب من API: {current_balance}")

            if current_balance is not None:
                old_balance = self.account_balance['current_balance']
                
                # تحديث البيانات
                self.account_balance['current_balance'] = float(current_balance)
                self.account_balance['last_update'] = datetime.now().isoformat()
                
                # حساب الربح/الخسارة إذا كان هناك رصيد أولي
                if self.account_balance['initial_balance'] == 0:
                    self.account_balance['initial_balance'] = float(current_balance)
                else:
                    balance_change = current_balance - self.account_balance['initial_balance']
                    if balance_change > 0:
                        self.account_balance['total_profit'] = balance_change
                        self.account_balance['total_loss'] = 0.0
                    else:
                        self.account_balance['total_loss'] = abs(balance_change)
                        self.account_balance['total_profit'] = 0.0
                
                # تخزين في Redis
                await self._store_balance_in_redis()
                
                self.stats['balance_updates'] += 1
                
                # تسجيل التغييرات المهمة
                if abs(current_balance - old_balance) > 1.0:  # تغيير أكثر من $1
                    logger.info(f"تغيير رصيد الحساب: ${old_balance:.2f} → ${current_balance:.2f}")

                logger.debug(f"تم تحديث رصيد الحساب بنجاح: ${current_balance:.2f}")
            else:
                logger.warning("لم يتم الحصول على رصيد من API")

        except Exception as e:
            logger.error(f"خطأ في تحديث رصيد الحساب: {str(e)}")
            import traceback
            logger.debug(f"تفاصيل الخطأ: {traceback.format_exc()}")
    
    async def _store_profit_rate_in_redis(self, asset: str, profit_rate: float):
        """تخزين نسبة الربح لأصل واحد في Redis"""
        try:
            profit_data = {
                'asset': asset,
                'profit_rate': profit_rate,
                'timestamp': datetime.now().isoformat(),
                'last_update': self.last_profit_update.get(asset)
            }
            
            # تخزين نسبة الربح للأصل
            key = f"profit_rate:{asset}"
            self.realtime_collector.redis_client.setex(
                key, 
                3600,  # ساعة واحدة
                json.dumps(profit_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"خطأ في تخزين نسبة الربح في Redis لـ {asset}: {str(e)}")
    
    async def _store_all_profit_rates(self):
        """تخزين جميع نسب الأرباح في Redis"""
        try:
            all_rates_data = {
                'profit_rates': self.profit_rates,
                'last_updates': self.last_profit_update,
                'total_assets': len(self.profit_rates),
                'last_full_update': datetime.now().isoformat()
            }
            
            # تخزين جميع النسب
            self.realtime_collector.redis_client.setex(
                "profit_rates:all",
                3600,  # ساعة واحدة
                json.dumps(all_rates_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"خطأ في تخزين جميع نسب الأرباح: {str(e)}")
    
    async def _store_balance_in_redis(self):
        """تخزين رصيد الحساب في Redis"""
        try:
            # تخزين رصيد الحساب
            self.realtime_collector.redis_client.setex(
                "account_balance",
                3600,  # ساعة واحدة
                json.dumps(self.account_balance, default=str)
            )
            
            # تخزين سجل الرصيد
            balance_history_key = f"balance_history:{datetime.now().strftime('%Y-%m-%d')}"
            balance_entry = {
                'timestamp': datetime.now().isoformat(),
                'balance': self.account_balance['current_balance'],
                'profit': self.account_balance['total_profit'],
                'loss': self.account_balance['total_loss']
            }
            
            self.realtime_collector.redis_client.lpush(
                balance_history_key,
                json.dumps(balance_entry, default=str)
            )
            
            # الاحتفاظ بآخر 100 سجل فقط
            self.realtime_collector.redis_client.ltrim(balance_history_key, 0, 99)
            
        except Exception as e:
            logger.error(f"خطأ في تخزين رصيد الحساب في Redis: {str(e)}")
    
    @handle_async_errors(default_return=False, log_error=True)
    async def stop_monitoring(self) -> bool:
        """إيقاف نظام المراقبة"""
        try:
            if not self.is_monitoring:
                logger.warning("نظام المراقبة غير قيد التشغيل")
                return True
            
            logger.info("🛑 إيقاف نظام مراقبة الأرباح والرصيد...")
            
            self.is_monitoring = False
            
            # حفظ البيانات النهائية
            await self._store_all_profit_rates()
            await self._store_balance_in_redis()
            
            logger.info("✅ تم إيقاف نظام المراقبة بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف المراقبة: {str(e)}")
            return False
    
    @handle_errors(default_return=None, log_error=True)
    def get_profit_rate(self, asset: str) -> Optional[float]:
        """الحصول على نسبة الربح لأصل معين"""
        return self.profit_rates.get(asset)
    
    @handle_errors(default_return={}, log_error=True)
    def get_all_profit_rates(self) -> Dict[str, float]:
        """الحصول على جميع نسب الأرباح"""
        return self.profit_rates.copy()
    
    @handle_errors(default_return={}, log_error=True)
    def get_account_balance(self) -> Dict[str, Any]:
        """الحصول على رصيد الحساب"""
        return self.account_balance.copy()
    
    @handle_errors(default_return={}, log_error=True)
    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        runtime = (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0
        
        return {
            'is_monitoring': self.is_monitoring,
            'runtime_seconds': round(runtime, 1),
            'profit_updates': self.stats['profit_updates'],
            'balance_updates': self.stats['balance_updates'],
            'errors': self.stats['errors'],
            'tracked_assets': len(self.profit_rates),
            'current_balance': self.account_balance['current_balance'],
            'total_profit': self.account_balance['total_profit'],
            'total_loss': self.account_balance['total_loss'],
            'last_successful_update': self.stats['last_successful_update'].isoformat() if self.stats['last_successful_update'] else None,
            'update_rate': round(self.stats['profit_updates'] / max(runtime, 1), 2)
        }
    
    @handle_async_errors(default_return={}, log_error=True)
    async def get_profit_rates_from_redis(self) -> Dict[str, Any]:
        """جلب نسب الأرباح من Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return {}
            
            data = self.realtime_collector.redis_client.get("profit_rates:all")
            if data:
                return json.loads(data)
            
            return {}
            
        except Exception as e:
            logger.error(f"خطأ في جلب نسب الأرباح من Redis: {str(e)}")
            return {}
    
    @handle_async_errors(default_return={}, log_error=True)
    async def get_balance_from_redis(self) -> Dict[str, Any]:
        """جلب رصيد الحساب من Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return {}
            
            data = self.realtime_collector.redis_client.get("account_balance")
            if data:
                return json.loads(data)
            
            return {}
            
        except Exception as e:
            logger.error(f"خطأ في جلب رصيد الحساب من Redis: {str(e)}")
            return {}

# إنشاء مثيل عام للاستخدام
profit_balance_manager = ProfitBalanceManager()
