#!/usr/bin/env python3
"""
نظام التقارب الرباعي - Quadruple Convergence System
يدمج الطبقات الأربع: التحليل الفني + السلوكي + الكمي + الذكاء الاصطناعي
"""

import asyncio
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict

from utils.logger import scalping_logger
from config.trading_config import TradingConfig

# استيراد طبقات التحليل
from data_layer.technical_signals_engine import TechnicalSignalsEngine
from data_layer.candlestick_pattern_analyzer import CandlestickPatternAnalyzer
from data_layer.momentum_acceleration_detector import MomentumAccelerationDetector
from data_layer.realtime_behavior_analyzer import RealtimeBehaviorAnalyzer
from data_layer.reverse_pressure_analyzer import ReversePressureAnalyzer

# استيراد طبقة التحليل الكمي
from data_layer.zscore_deviation_analyzer import ZScoreDeviationAnalyzer
from data_layer.historical_probability_filter import HistoricalProbabilityFilter
from data_layer.sharpe_ratio_analyzer import SharpeRatioAnalyzer
from data_layer.atr_volatility_analyzer import ATRVolatilityAnalyzer

# استيراد طبقة الذكاء الاصطناعي
from ai_models.unified_prediction_interface import UnifiedPredictionInterface
from ai_models.market_state_classifier import MarketStateClassifier
from ai_models.confidence_evaluation_engine import ConfidenceEvaluationEngine
from ai_models.reinforcement_learning_system import ReinforcementLearningSystem

logger = scalping_logger.get_logger("quadruple_convergence")

@dataclass
class LayerSignal:
    """إشارة من طبقة واحدة"""
    layer_name: str
    signal_type: str  # 'CALL', 'PUT', 'NEUTRAL'
    confidence: float  # 0.0 - 1.0
    strength: float   # 0.0 - 1.0
    details: Dict[str, Any]
    timestamp: datetime

@dataclass
class ConvergenceResult:
    """نتيجة التقارب الرباعي"""
    final_signal: str  # 'CALL', 'PUT', 'NEUTRAL'
    overall_confidence: float  # 0.0 - 1.0
    convergence_score: float   # 0.0 - 1.0 (مدى التوافق بين الطبقات)
    layer_signals: List[LayerSignal]
    risk_assessment: Dict[str, Any]
    execution_recommendation: Dict[str, Any]
    timestamp: datetime

class QuadrupleConvergenceSystem:
    """نظام التقارب الرباعي المتكامل"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logger
        
        # تهيئة طبقات التحليل
        self._initialize_analysis_layers()
        
        # إعدادات التقارب
        self.convergence_settings = {
            'minimum_confidence_threshold': 0.65,
            'minimum_convergence_score': 0.70,
            'layer_weights': {
                'technical': 0.30,
                'behavioral': 0.25,
                'quantitative': 0.25,
                'ai': 0.20
            },
            'signal_timeout_seconds': 30
        }
        
        # إحصائيات النظام
        self.stats = {
            'total_analyses': 0,
            'convergent_signals': 0,
            'divergent_signals': 0,
            'neutral_signals': 0,
            'layer_performance': {
                'technical': {'correct': 0, 'total': 0},
                'behavioral': {'correct': 0, 'total': 0},
                'quantitative': {'correct': 0, 'total': 0},
                'ai': {'correct': 0, 'total': 0}
            }
        }
        
        self.logger.info("تم تهيئة نظام التقارب الرباعي")

    def _initialize_analysis_layers(self):
        """تهيئة طبقات التحليل الأربع"""
        try:
            # الطبقة الفنية
            self.technical_engine = TechnicalSignalsEngine()
            self.pattern_analyzer = CandlestickPatternAnalyzer()
            
            # الطبقة السلوكية
            self.momentum_detector = MomentumAccelerationDetector()
            self.behavior_analyzer = RealtimeBehaviorAnalyzer()
            self.pressure_analyzer = ReversePressureAnalyzer()
            
            # الطبقة الكمية
            self.zscore_analyzer = ZScoreDeviationAnalyzer()
            self.probability_filter = HistoricalProbabilityFilter()
            self.sharpe_analyzer = SharpeRatioAnalyzer()
            self.volatility_analyzer = ATRVolatilityAnalyzer()
            
            # طبقة الذكاء الاصطناعي
            self.ai_predictor = UnifiedPredictionInterface()
            self.market_classifier = MarketStateClassifier()
            self.confidence_engine = ConfidenceEvaluationEngine()
            self.rl_system = ReinforcementLearningSystem()
            
            self.logger.info("تم تهيئة جميع طبقات التحليل بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة طبقات التحليل: {str(e)}")
            raise

    async def analyze_convergence(self, currency_pair: str, timeframe: int = 60) -> ConvergenceResult:
        """تحليل التقارب الرباعي لزوج عملة"""
        try:
            self.stats['total_analyses'] += 1
            analysis_start = datetime.now()
            
            self.logger.info(f"بدء تحليل التقارب الرباعي لـ {currency_pair}")
            
            # تحليل متوازي للطبقات الأربع
            layer_tasks = [
                self._analyze_technical_layer(currency_pair, timeframe),
                self._analyze_behavioral_layer(currency_pair, timeframe),
                self._analyze_quantitative_layer(currency_pair, timeframe),
                self._analyze_ai_layer(currency_pair, timeframe)
            ]
            
            # تنفيذ التحليلات بشكل متوازي
            layer_results = await asyncio.gather(*layer_tasks, return_exceptions=True)
            
            # معالجة النتائج
            valid_signals = []
            for i, result in enumerate(layer_results):
                if isinstance(result, Exception):
                    layer_name = ['technical', 'behavioral', 'quantitative', 'ai'][i]
                    self.logger.error(f"خطأ في طبقة {layer_name}: {str(result)}")
                else:
                    valid_signals.append(result)
            
            if len(valid_signals) < 2:
                self.logger.warning(f"عدد الإشارات الصالحة قليل: {len(valid_signals)}")
                return self._create_neutral_result(currency_pair, valid_signals)
            
            # حساب التقارب
            convergence_result = self._calculate_convergence(valid_signals, currency_pair)
            
            # تحديث الإحصائيات
            self._update_statistics(convergence_result)
            
            analysis_duration = (datetime.now() - analysis_start).total_seconds()
            self.logger.info(f"اكتمل تحليل التقارب لـ {currency_pair} في {analysis_duration:.2f} ثانية")
            
            return convergence_result
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل التقارب لـ {currency_pair}: {str(e)}")
            raise

    async def _analyze_technical_layer(self, currency_pair: str, timeframe: int) -> LayerSignal:
        """تحليل الطبقة الفنية"""
        try:
            # جلب البيانات من قاعدة البيانات
            from database.repository import historical_data_repo
            candles_data = historical_data_repo.get_latest_candles(currency_pair, 100)

            if not candles_data or len(candles_data) < 30:
                return LayerSignal(
                    layer_name='technical',
                    signal_type='NEUTRAL',
                    confidence=0.0,
                    strength=0.0,
                    details={'error': 'بيانات غير كافية'},
                    timestamp=datetime.now()
                )

            # تحليل المؤشرات الفنية
            technical_signals = await self.technical_engine.analyze_all_indicators(currency_pair, candles_data)

            # تحليل أنماط الشموع
            pattern_analysis = await self.pattern_analyzer.analyze_candlestick_patterns(currency_pair, candles_data)
            
            # دمج النتائج
            signal_strength = 0.0
            signal_direction = 'NEUTRAL'
            confidence = 0.0

            if technical_signals and pattern_analysis:
                # حساب قوة الإشارة من المؤشرات
                tech_score = technical_signals.overall_signal_strength if hasattr(technical_signals, 'overall_signal_strength') else 0.0
                tech_direction = technical_signals.dominant_signal if hasattr(technical_signals, 'dominant_signal') else 'NEUTRAL'

                # حساب قوة الإشارة من الأنماط
                pattern_score = pattern_analysis.get('pattern_strength', 0.0) if isinstance(pattern_analysis, dict) else 0.0
                pattern_direction = pattern_analysis.get('pattern_signal', 'NEUTRAL') if isinstance(pattern_analysis, dict) else 'NEUTRAL'

                # دمج النتائج
                if tech_direction == pattern_direction and tech_direction != 'NEUTRAL':
                    signal_direction = tech_direction
                    signal_strength = (tech_score + pattern_score) / 2
                    confidence = min(0.95, signal_strength * 1.2)
                else:
                    # تضارب في الإشارات
                    signal_strength = abs(tech_score - pattern_score) / 2
                    confidence = signal_strength * 0.6

            details = {
                'technical_signals': technical_signals.__dict__ if hasattr(technical_signals, '__dict__') else str(technical_signals),
                'pattern_analysis': pattern_analysis,
                'analysis_method': 'combined_technical_pattern'
            }
            
            return LayerSignal(
                layer_name='technical',
                signal_type=signal_direction,
                confidence=confidence,
                strength=signal_strength,
                details=details,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الطبقة الفنية: {str(e)}")
            raise

    async def _analyze_behavioral_layer(self, currency_pair: str, timeframe: int) -> LayerSignal:
        """تحليل الطبقة السلوكية"""
        try:
            # جلب البيانات من قاعدة البيانات
            from database.repository import historical_data_repo
            candles_data = historical_data_repo.get_latest_candles(currency_pair, 100)

            if not candles_data or len(candles_data) < 20:
                return LayerSignal(
                    layer_name='behavioral',
                    signal_type='NEUTRAL',
                    confidence=0.0,
                    strength=0.0,
                    details={'error': 'بيانات غير كافية'},
                    timestamp=datetime.now()
                )

            # تحليل الزخم والتسارع
            momentum_analysis = await self.momentum_detector.analyze_momentum_acceleration(currency_pair, candles_data)

            # تحليل السلوك اللحظي
            behavior_analysis = await self.behavior_analyzer.analyze_price_behavior(currency_pair, candles_data)

            # تحليل الضغط العكسي
            pressure_analysis = await self.pressure_analyzer.analyze_pressure_patterns(currency_pair, candles_data)
            
            # دمج التحليلات السلوكية
            signal_components = []

            if momentum_analysis:
                # استخراج الإشارة من نتيجة تحليل الزخم
                momentum_signal = 'NEUTRAL'
                momentum_strength = 0.0

                if hasattr(momentum_analysis, 'dominant_signal'):
                    momentum_signal = momentum_analysis.dominant_signal
                    momentum_strength = momentum_analysis.signal_strength if hasattr(momentum_analysis, 'signal_strength') else 0.0

                signal_components.append((momentum_signal, momentum_strength, 0.4))

            if behavior_analysis:
                behavior_signal = behavior_analysis.get('behavior_signal', 'NEUTRAL') if isinstance(behavior_analysis, dict) else 'NEUTRAL'
                behavior_strength = behavior_analysis.get('behavior_strength', 0.0) if isinstance(behavior_analysis, dict) else 0.0
                signal_components.append((behavior_signal, behavior_strength, 0.35))

            if pressure_analysis:
                pressure_signal = pressure_analysis.get('pressure_signal', 'NEUTRAL') if isinstance(pressure_analysis, dict) else 'NEUTRAL'
                pressure_strength = pressure_analysis.get('pressure_strength', 0.0) if isinstance(pressure_analysis, dict) else 0.0
                signal_components.append((pressure_signal, pressure_strength, 0.25))
            
            # حساب الإشارة المجمعة
            final_signal, final_strength, final_confidence = self._combine_behavioral_signals(signal_components)
            
            details = {
                'momentum_analysis': momentum_analysis,
                'behavior_analysis': behavior_analysis,
                'pressure_analysis': pressure_analysis,
                'signal_components': signal_components
            }
            
            return LayerSignal(
                layer_name='behavioral',
                signal_type=final_signal,
                confidence=final_confidence,
                strength=final_strength,
                details=details,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الطبقة السلوكية: {str(e)}")
            raise

    def _combine_behavioral_signals(self, signal_components: List[Tuple[str, float, float]]) -> Tuple[str, float, float]:
        """دمج الإشارات السلوكية"""
        if not signal_components:
            return 'NEUTRAL', 0.0, 0.0
        
        call_score = 0.0
        put_score = 0.0
        total_weight = 0.0
        
        for signal, strength, weight in signal_components:
            if signal == 'CALL':
                call_score += strength * weight
            elif signal == 'PUT':
                put_score += strength * weight
            total_weight += weight
        
        if total_weight > 0:
            call_score /= total_weight
            put_score /= total_weight
        
        # تحديد الإشارة النهائية
        if call_score > put_score and call_score > 0.5:
            return 'CALL', call_score, min(0.9, call_score * 1.1)
        elif put_score > call_score and put_score > 0.5:
            return 'PUT', put_score, min(0.9, put_score * 1.1)
        else:
            return 'NEUTRAL', max(call_score, put_score), max(call_score, put_score) * 0.5

    async def _analyze_quantitative_layer(self, currency_pair: str, timeframe: int) -> LayerSignal:
        """تحليل الطبقة الكمية"""
        try:
            # جلب البيانات من قاعدة البيانات
            from database.repository import historical_data_repo
            candles_data = historical_data_repo.get_latest_candles(currency_pair, 100)

            if not candles_data or len(candles_data) < 30:
                return LayerSignal(
                    layer_name='quantitative',
                    signal_type='NEUTRAL',
                    confidence=0.0,
                    strength=0.0,
                    details={'error': 'بيانات غير كافية'},
                    timestamp=datetime.now()
                )

            # تحليل Z-Score
            zscore_analysis = await self.zscore_analyzer.analyze_zscore_deviation(currency_pair, candles_data)

            # فلتر الاحتمالات التاريخية
            probability_analysis = await self.probability_filter.analyze_historical_probability(currency_pair, candles_data)

            # تحليل نسبة شارب
            sharpe_analysis = await self.sharpe_analyzer.calculate_sharpe_ratio(currency_pair, candles_data)

            # تحليل التقلبات
            volatility_analysis = await self.volatility_analyzer.analyze_atr_volatility(currency_pair, candles_data)
            
            # دمج التحليلات الكمية
            quantitative_score = 0.0
            signal_direction = 'NEUTRAL'
            confidence_factors = []
            
            # معالجة Z-Score
            if zscore_analysis:
                zscore_signal = zscore_analysis.get('deviation_signal', 'NEUTRAL')
                zscore_strength = zscore_analysis.get('deviation_strength', 0.0)
                if zscore_signal != 'NEUTRAL':
                    quantitative_score += zscore_strength * 0.3
                    confidence_factors.append(zscore_strength)
                    if signal_direction == 'NEUTRAL':
                        signal_direction = zscore_signal
            
            # معالجة الاحتمالات التاريخية
            if probability_analysis:
                prob_signal = probability_analysis.get('probability_signal', 'NEUTRAL')
                prob_confidence = probability_analysis.get('historical_confidence', 0.0)
                if prob_signal != 'NEUTRAL':
                    quantitative_score += prob_confidence * 0.25
                    confidence_factors.append(prob_confidence)
                    if signal_direction == 'NEUTRAL' or signal_direction == prob_signal:
                        signal_direction = prob_signal
            
            # معالجة نسبة شارب
            if sharpe_analysis:
                sharpe_quality = sharpe_analysis.get('signal_quality', 0.0)
                quantitative_score += sharpe_quality * 0.2
                confidence_factors.append(sharpe_quality)
            
            # معالجة التقلبات
            if volatility_analysis:
                volatility_suitability = volatility_analysis.get('scalping_suitability', 0.0)
                quantitative_score += volatility_suitability * 0.25
                confidence_factors.append(volatility_suitability)
            
            # حساب الثقة النهائية
            final_confidence = np.mean(confidence_factors) if confidence_factors else 0.0
            
            details = {
                'zscore_analysis': zscore_analysis,
                'probability_analysis': probability_analysis,
                'sharpe_analysis': sharpe_analysis,
                'volatility_analysis': volatility_analysis,
                'quantitative_score': quantitative_score
            }
            
            return LayerSignal(
                layer_name='quantitative',
                signal_type=signal_direction,
                confidence=final_confidence,
                strength=quantitative_score,
                details=details,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الطبقة الكمية: {str(e)}")
            raise

    async def _analyze_ai_layer(self, currency_pair: str, timeframe: int) -> LayerSignal:
        """تحليل طبقة الذكاء الاصطناعي"""
        try:
            # جلب البيانات من قاعدة البيانات
            from database.repository import historical_data_repo
            candles_data = historical_data_repo.get_latest_candles(currency_pair, 100)

            if not candles_data or len(candles_data) < 30:
                return LayerSignal(
                    layer_name='ai',
                    signal_type='NEUTRAL',
                    confidence=0.0,
                    strength=0.0,
                    details={'error': 'بيانات غير كافية'},
                    timestamp=datetime.now()
                )

            # التنبؤ الموحد
            ai_prediction = await self.ai_predictor.predict_direction(currency_pair, candles_data)

            # تصنيف حالة السوق
            market_state = await self.market_classifier.classify_market_condition(currency_pair, candles_data)

            # تقييم الثقة
            confidence_evaluation = await self.confidence_engine.evaluate_confidence(
                currency_pair, candles_data, ai_prediction
            )

            # نظام التعلم التعزيزي
            rl_recommendation = await self.rl_system.recommend_action(currency_pair, candles_data)

            # دمج نتائج الذكاء الاصطناعي
            ai_signal = 'NEUTRAL'
            ai_confidence = 0.0
            ai_strength = 0.0

            if ai_prediction:
                predicted_direction = ai_prediction.get('predicted_direction', 'NEUTRAL')
                prediction_confidence = ai_prediction.get('confidence', 0.0)

                if predicted_direction != 'NEUTRAL' and prediction_confidence > 0.6:
                    ai_signal = predicted_direction
                    ai_confidence = prediction_confidence
                    ai_strength = prediction_confidence

            # تعديل بناءً على حالة السوق
            if market_state:
                market_condition = market_state.get('market_state', 'UNKNOWN')
                state_confidence = market_state.get('confidence', 0.0)

                # تقليل الثقة في الأسواق المتقلبة أو المحاصرة
                if market_condition in ['VOLATILE', 'TRAP']:
                    ai_confidence *= 0.7
                elif market_condition == 'TRENDING':
                    ai_confidence *= 1.1

            # تعديل بناءً على تقييم الثقة
            if confidence_evaluation:
                confidence_score = confidence_evaluation.get('overall_confidence', 0.0)
                ai_confidence = (ai_confidence + confidence_score) / 2

            # تعديل بناءً على التعلم التعزيزي
            if rl_recommendation:
                rl_action = rl_recommendation.get('recommended_action', 'HOLD')
                rl_confidence = rl_recommendation.get('action_confidence', 0.0)

                if rl_action in ['BUY', 'SELL'] and rl_confidence > 0.7:
                    rl_signal = 'CALL' if rl_action == 'BUY' else 'PUT'
                    if ai_signal == rl_signal:
                        ai_confidence *= 1.2  # تعزيز الثقة عند التوافق
                    elif ai_signal != 'NEUTRAL':
                        ai_confidence *= 0.8  # تقليل الثقة عند التضارب

            # تحديد النهائي
            ai_confidence = min(0.95, ai_confidence)

            details = {
                'ai_prediction': ai_prediction,
                'market_state': market_state,
                'confidence_evaluation': confidence_evaluation,
                'rl_recommendation': rl_recommendation,
                'final_processing': {
                    'signal': ai_signal,
                    'confidence': ai_confidence,
                    'strength': ai_strength
                }
            }

            return LayerSignal(
                layer_name='ai',
                signal_type=ai_signal,
                confidence=ai_confidence,
                strength=ai_strength,
                details=details,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"خطأ في تحليل طبقة الذكاء الاصطناعي: {str(e)}")
            raise

    def _calculate_convergence(self, layer_signals: List[LayerSignal], currency_pair: str) -> ConvergenceResult:
        """حساب التقارب بين الطبقات"""
        try:
            # تجميع الإشارات حسب النوع
            call_signals = [s for s in layer_signals if s.signal_type == 'CALL']
            put_signals = [s for s in layer_signals if s.signal_type == 'PUT']
            neutral_signals = [s for s in layer_signals if s.signal_type == 'NEUTRAL']

            # حساب النقاط المرجحة
            call_score = sum(s.confidence * self.convergence_settings['layer_weights'].get(s.layer_name, 0.25)
                           for s in call_signals)
            put_score = sum(s.confidence * self.convergence_settings['layer_weights'].get(s.layer_name, 0.25)
                          for s in put_signals)

            # حساب درجة التقارب
            total_signals = len(layer_signals)
            convergence_score = 0.0

            if total_signals > 0:
                # نسبة الإشارات المتفقة
                max_agreement = max(len(call_signals), len(put_signals), len(neutral_signals))
                convergence_score = max_agreement / total_signals

                # تعديل بناءً على قوة الإشارات
                if call_score > put_score:
                    convergence_score *= (call_score / max(call_score + put_score, 0.1))
                elif put_score > call_score:
                    convergence_score *= (put_score / max(call_score + put_score, 0.1))

            # تحديد الإشارة النهائية
            final_signal = 'NEUTRAL'
            overall_confidence = 0.0

            if call_score > put_score and call_score > 0.5:
                final_signal = 'CALL'
                overall_confidence = call_score
            elif put_score > call_score and put_score > 0.5:
                final_signal = 'PUT'
                overall_confidence = put_score
            else:
                final_signal = 'NEUTRAL'
                overall_confidence = max(call_score, put_score) * 0.5

            # تقييم المخاطر
            risk_assessment = self._assess_risk(layer_signals, convergence_score, overall_confidence)

            # توصية التنفيذ
            execution_recommendation = self._generate_execution_recommendation(
                final_signal, overall_confidence, convergence_score, risk_assessment
            )

            return ConvergenceResult(
                final_signal=final_signal,
                overall_confidence=overall_confidence,
                convergence_score=convergence_score,
                layer_signals=layer_signals,
                risk_assessment=risk_assessment,
                execution_recommendation=execution_recommendation,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"خطأ في حساب التقارب: {str(e)}")
            raise

    def _assess_risk(self, layer_signals: List[LayerSignal], convergence_score: float,
                    overall_confidence: float) -> Dict[str, Any]:
        """تقييم المخاطر"""
        try:
            risk_factors = []
            risk_score = 0.0

            # تقييم التضارب بين الطبقات
            signal_types = [s.signal_type for s in layer_signals]
            unique_signals = set(signal_types)

            if len(unique_signals) > 2:
                risk_factors.append("تضارب عالي بين الطبقات")
                risk_score += 0.3
            elif len(unique_signals) == 2 and 'NEUTRAL' not in unique_signals:
                risk_factors.append("تضارب متوسط بين الطبقات")
                risk_score += 0.2

            # تقييم انخفاض الثقة
            if overall_confidence < self.convergence_settings['minimum_confidence_threshold']:
                risk_factors.append("انخفاض مستوى الثقة")
                risk_score += 0.25

            # تقييم انخفاض التقارب
            if convergence_score < self.convergence_settings['minimum_convergence_score']:
                risk_factors.append("انخفاض درجة التقارب")
                risk_score += 0.2

            # تقييم جودة البيانات من كل طبقة
            low_quality_layers = [s.layer_name for s in layer_signals if s.confidence < 0.5]
            if low_quality_layers:
                risk_factors.append(f"جودة منخفضة في الطبقات: {', '.join(low_quality_layers)}")
                risk_score += len(low_quality_layers) * 0.1

            # تصنيف المخاطر
            if risk_score <= 0.2:
                risk_level = "منخفض"
            elif risk_score <= 0.5:
                risk_level = "متوسط"
            else:
                risk_level = "عالي"

            return {
                'risk_score': min(1.0, risk_score),
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'layer_quality': {s.layer_name: s.confidence for s in layer_signals},
                'signal_distribution': {
                    'CALL': len([s for s in layer_signals if s.signal_type == 'CALL']),
                    'PUT': len([s for s in layer_signals if s.signal_type == 'PUT']),
                    'NEUTRAL': len([s for s in layer_signals if s.signal_type == 'NEUTRAL'])
                }
            }

        except Exception as e:
            self.logger.error(f"خطأ في تقييم المخاطر: {str(e)}")
            return {'risk_score': 1.0, 'risk_level': 'عالي', 'risk_factors': ['خطأ في التقييم']}

    def _generate_execution_recommendation(self, final_signal: str, overall_confidence: float,
                                         convergence_score: float, risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """توليد توصية التنفيذ"""
        try:
            recommendation = {
                'should_execute': False,
                'execution_strength': 0.0,
                'recommended_amount': 0.0,
                'recommended_timeframe': 60,
                'stop_conditions': [],
                'reasoning': []
            }

            # شروط التنفيذ الأساسية
            min_confidence = self.convergence_settings['minimum_confidence_threshold']
            min_convergence = self.convergence_settings['minimum_convergence_score']

            if (final_signal != 'NEUTRAL' and
                overall_confidence >= min_confidence and
                convergence_score >= min_convergence and
                risk_assessment['risk_level'] != 'عالي'):

                recommendation['should_execute'] = True
                recommendation['reasoning'].append("استوفيت شروط التنفيذ الأساسية")

                # حساب قوة التنفيذ
                execution_strength = (overall_confidence + convergence_score) / 2
                execution_strength *= (1 - risk_assessment['risk_score'])
                recommendation['execution_strength'] = execution_strength

                # تحديد المبلغ المقترح (نسبة من الرصيد)
                base_amount = 0.02  # 2% من الرصيد
                if execution_strength > 0.8:
                    recommended_amount = base_amount * 1.5
                    recommendation['reasoning'].append("إشارة قوية - زيادة المبلغ")
                elif execution_strength > 0.7:
                    recommended_amount = base_amount
                    recommendation['reasoning'].append("إشارة متوسطة - مبلغ عادي")
                else:
                    recommended_amount = base_amount * 0.5
                    recommendation['reasoning'].append("إشارة ضعيفة - تقليل المبلغ")

                recommendation['recommended_amount'] = recommended_amount

                # تحديد الإطار الزمني
                if convergence_score > 0.85:
                    recommendation['recommended_timeframe'] = 60  # دقيقة واحدة
                elif convergence_score > 0.75:
                    recommendation['recommended_timeframe'] = 300  # 5 دقائق
                else:
                    recommendation['recommended_timeframe'] = 900  # 15 دقيقة

                # شروط الإيقاف
                recommendation['stop_conditions'] = [
                    f"انخفاض الثقة تحت {min_confidence}",
                    f"انخفاض التقارب تحت {min_convergence}",
                    "ارتفاع مستوى المخاطر إلى عالي",
                    "تغيير الإشارة النهائية"
                ]

            else:
                # أسباب عدم التنفيذ
                if final_signal == 'NEUTRAL':
                    recommendation['reasoning'].append("إشارة محايدة")
                if overall_confidence < min_confidence:
                    recommendation['reasoning'].append(f"الثقة منخفضة ({overall_confidence:.2f} < {min_confidence})")
                if convergence_score < min_convergence:
                    recommendation['reasoning'].append(f"التقارب منخفض ({convergence_score:.2f} < {min_convergence})")
                if risk_assessment['risk_level'] == 'عالي':
                    recommendation['reasoning'].append("مستوى المخاطر عالي")

            return recommendation

        except Exception as e:
            self.logger.error(f"خطأ في توليد توصية التنفيذ: {str(e)}")
            return {'should_execute': False, 'reasoning': ['خطأ في التوليد']}

    def _create_neutral_result(self, currency_pair: str, valid_signals: List[LayerSignal]) -> ConvergenceResult:
        """إنشاء نتيجة محايدة عند عدم توفر إشارات كافية"""
        return ConvergenceResult(
            final_signal='NEUTRAL',
            overall_confidence=0.0,
            convergence_score=0.0,
            layer_signals=valid_signals,
            risk_assessment={'risk_score': 1.0, 'risk_level': 'عالي', 'risk_factors': ['بيانات غير كافية']},
            execution_recommendation={'should_execute': False, 'reasoning': ['بيانات غير كافية للتحليل']},
            timestamp=datetime.now()
        )

    def _update_statistics(self, result: ConvergenceResult):
        """تحديث إحصائيات النظام"""
        try:
            if result.final_signal == 'NEUTRAL':
                self.stats['neutral_signals'] += 1
            elif result.convergence_score >= self.convergence_settings['minimum_convergence_score']:
                self.stats['convergent_signals'] += 1
            else:
                self.stats['divergent_signals'] += 1

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        try:
            total_analyses = self.stats['total_analyses']
            if total_analyses == 0:
                return self.stats

            # حساب النسب المئوية
            convergence_rate = (self.stats['convergent_signals'] / total_analyses) * 100
            divergence_rate = (self.stats['divergent_signals'] / total_analyses) * 100
            neutral_rate = (self.stats['neutral_signals'] / total_analyses) * 100

            # حساب أداء الطبقات
            layer_performance = {}
            for layer, perf in self.stats['layer_performance'].items():
                if perf['total'] > 0:
                    accuracy = (perf['correct'] / perf['total']) * 100
                    layer_performance[layer] = {
                        'accuracy': accuracy,
                        'total_predictions': perf['total'],
                        'correct_predictions': perf['correct']
                    }
                else:
                    layer_performance[layer] = {
                        'accuracy': 0.0,
                        'total_predictions': 0,
                        'correct_predictions': 0
                    }

            return {
                'total_analyses': total_analyses,
                'signal_distribution': {
                    'convergent_signals': self.stats['convergent_signals'],
                    'divergent_signals': self.stats['divergent_signals'],
                    'neutral_signals': self.stats['neutral_signals']
                },
                'signal_rates': {
                    'convergence_rate': convergence_rate,
                    'divergence_rate': divergence_rate,
                    'neutral_rate': neutral_rate
                },
                'layer_performance': layer_performance,
                'system_settings': self.convergence_settings
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return self.stats

    async def update_layer_performance(self, layer_name: str, was_correct: bool):
        """تحديث أداء طبقة معينة"""
        try:
            if layer_name in self.stats['layer_performance']:
                self.stats['layer_performance'][layer_name]['total'] += 1
                if was_correct:
                    self.stats['layer_performance'][layer_name]['correct'] += 1

        except Exception as e:
            self.logger.error(f"خطأ في تحديث أداء الطبقة {layer_name}: {str(e)}")

# إنشاء مثيل عام للنظام
quadruple_convergence_system = QuadrupleConvergenceSystem()
