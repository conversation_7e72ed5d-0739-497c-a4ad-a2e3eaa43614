"""
محلل السلوك اللحظي - المرحلة الرابعة
Realtime Behavior Analyzer for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("realtime_behavior_analyzer")

class MarketBehavior(Enum):
    """سلوك السوق"""
    AGGRESSIVE_BUYING = "AGGRESSIVE_BUYING"      # شراء عدواني
    MODERATE_BUYING = "MODERATE_BUYING"          # شراء معتدل
    CAUTIOUS_BUYING = "CAUTIOUS_BUYING"          # شراء حذر
    NEUTRAL = "NEUTRAL"                          # محايد
    CAUTIOUS_SELLING = "CAUTIOUS_SELLING"        # بيع حذر
    MODERATE_SELLING = "MODERATE_SELLING"        # بيع معتدل
    AGGRESSIVE_SELLING = "AGGRESSIVE_SELLING"    # بيع عدواني

class TradingIntention(Enum):
    """نية التداول"""
    STRONG_ACCUMULATION = "STRONG_ACCUMULATION"  # تجميع قوي
    ACCUMULATION = "ACCUMULATION"                # تجميع
    DISTRIBUTION = "DISTRIBUTION"                # توزيع
    STRONG_DISTRIBUTION = "STRONG_DISTRIBUTION"  # توزيع قوي
    CONSOLIDATION = "CONSOLIDATION"              # توطيد
    BREAKOUT_PREPARATION = "BREAKOUT_PREPARATION" # تحضير للاختراق

class PriceAction(Enum):
    """حركة السعر"""
    STRONG_REJECTION = "STRONG_REJECTION"        # رفض قوي
    WEAK_REJECTION = "WEAK_REJECTION"            # رفض ضعيف
    ACCEPTANCE = "ACCEPTANCE"                    # قبول
    TESTING = "TESTING"                          # اختبار
    BREAKTHROUGH = "BREAKTHROUGH"                # اختراق

@dataclass
class BehaviorSignal:
    """إشارة السلوك"""
    behavior_type: MarketBehavior
    intention: TradingIntention
    price_action: PriceAction
    confidence: float  # 0-100
    
    # قياسات السلوك
    aggression_level: float  # مستوى العدوانية
    volume_intensity: float  # كثافة الحجم
    price_velocity: float    # سرعة السعر
    
    # تحليل النوايا
    buying_pressure: float   # ضغط الشراء
    selling_pressure: float  # ضغط البيع
    market_sentiment: float  # معنويات السوق
    
    # معلومات التحليل
    timeframe_seconds: int
    candles_analyzed: int
    
    # وصف السلوك
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class RealtimeBehaviorAnalysis:
    """تحليل السلوك اللحظي"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # إشارات السلوك
    behavior_signals: List[BehaviorSignal]
    
    # التقييم الإجمالي
    overall_behavior: MarketBehavior
    dominant_intention: TradingIntention
    market_state: str
    
    # مقاييس السلوك
    overall_aggression: float
    market_pressure_ratio: float  # نسبة ضغط الشراء/البيع
    sentiment_score: float
    
    # توصيات
    is_actionable: bool
    recommended_action: str
    timing_quality: str
    recommendations: List[str]

class RealtimeBehaviorAnalyzer:
    """محلل السلوك اللحظي"""
    
    def __init__(self):
        self.analysis_timeframes = [30, 60, 120]  # ثواني
        self.behavior_weights = self._initialize_behavior_weights()
        self.min_confidence_threshold = 70.0
        self.behavior_history = {}  # تاريخ السلوك لكل أصل
        
        logger.info("تم تهيئة محلل السلوك اللحظي")

    def _initialize_behavior_weights(self) -> Dict[MarketBehavior, float]:
        """تهيئة أوزان السلوك"""
        return {
            MarketBehavior.AGGRESSIVE_BUYING: 1.0,
            MarketBehavior.MODERATE_BUYING: 0.7,
            MarketBehavior.CAUTIOUS_BUYING: 0.4,
            MarketBehavior.NEUTRAL: 0.0,
            MarketBehavior.CAUTIOUS_SELLING: -0.4,
            MarketBehavior.MODERATE_SELLING: -0.7,
            MarketBehavior.AGGRESSIVE_SELLING: -1.0
        }

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_price_behavior(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[RealtimeBehaviorAnalysis]:
        """تحليل السلوك السعري"""
        return await self.analyze_realtime_behavior(asset, candles_data)

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_realtime_behavior(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[RealtimeBehaviorAnalysis]:
        """تحليل السلوك اللحظي"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 10:
                logger.warning(f"بيانات غير كافية لتحليل السلوك لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل السلوك اللحظي لـ {asset} مع {len(candles_data)} شمعة")
            
            # 1. تحليل السلوك بفترات زمنية مختلفة
            behavior_signals = []
            for timeframe in self.analysis_timeframes:
                signal = await self._analyze_behavior_timeframe(candles_data, timeframe)
                if signal:
                    behavior_signals.append(signal)
            
            # 2. تحليل النوايا والضغوط
            market_pressures = await self._analyze_market_pressures(candles_data)
            
            # 3. تحليل حركة السعر
            price_action_analysis = await self._analyze_price_action(candles_data)
            
            # 4. التقييم الإجمالي
            overall_assessment = self._evaluate_overall_behavior(behavior_signals, market_pressures, price_action_analysis)
            
            # 5. إنتاج التوصيات
            recommendations = self._generate_behavior_recommendations(behavior_signals, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = RealtimeBehaviorAnalysis(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                behavior_signals=behavior_signals,
                overall_behavior=overall_assessment['behavior'],
                dominant_intention=overall_assessment['intention'],
                market_state=overall_assessment['market_state'],
                overall_aggression=overall_assessment['aggression'],
                market_pressure_ratio=overall_assessment['pressure_ratio'],
                sentiment_score=overall_assessment['sentiment'],
                is_actionable=overall_assessment['is_actionable'],
                recommended_action=overall_assessment['action'],
                timing_quality=overall_assessment['timing'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.behavior_history:
                self.behavior_history[asset] = []
            
            self.behavior_history[asset].append(result)
            
            # الاحتفاظ بآخر 15 تحليل فقط
            if len(self.behavior_history[asset]) > 15:
                self.behavior_history[asset] = self.behavior_history[asset][-15:]
            
            logger.info(f"تم إكمال تحليل السلوك لـ {asset}: {overall_assessment['behavior'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل السلوك اللحظي لـ {asset}: {str(e)}")
            return None

    async def _analyze_behavior_timeframe(self, candles: List[Dict[str, Any]], timeframe_seconds: int) -> Optional[BehaviorSignal]:
        """تحليل السلوك لفترة زمنية محددة"""
        try:
            # تحديد عدد الشموع المطلوبة (افتراض شمعة كل دقيقة)
            candles_needed = max(3, timeframe_seconds // 60)
            
            if len(candles) < candles_needed:
                return None
            
            recent_candles = candles[-candles_needed:]
            
            # حساب مقاييس السلوك
            aggression_level = await self._calculate_aggression_level(recent_candles)
            volume_intensity = await self._calculate_volume_intensity(recent_candles)
            price_velocity = await self._calculate_price_velocity(recent_candles)
            
            # حساب الضغوط
            buying_pressure = await self._calculate_buying_pressure(recent_candles)
            selling_pressure = await self._calculate_selling_pressure(recent_candles)
            
            # تحديد السلوك
            behavior_type = self._determine_market_behavior(aggression_level, buying_pressure, selling_pressure)
            
            # تحديد النية
            intention = self._determine_trading_intention(buying_pressure, selling_pressure, volume_intensity)
            
            # تحديد حركة السعر
            price_action = self._determine_price_action(recent_candles, aggression_level)
            
            # حساب معنويات السوق
            market_sentiment = (buying_pressure - selling_pressure) / (buying_pressure + selling_pressure) if (buying_pressure + selling_pressure) > 0 else 0
            
            # حساب الثقة
            confidence = self._calculate_behavior_confidence(aggression_level, volume_intensity, buying_pressure, selling_pressure)
            
            return BehaviorSignal(
                behavior_type=behavior_type,
                intention=intention,
                price_action=price_action,
                confidence=confidence,
                aggression_level=aggression_level,
                volume_intensity=volume_intensity,
                price_velocity=price_velocity,
                buying_pressure=buying_pressure,
                selling_pressure=selling_pressure,
                market_sentiment=market_sentiment,
                timeframe_seconds=timeframe_seconds,
                candles_analyzed=len(recent_candles),
                description=f"سلوك {timeframe_seconds}ث: {behavior_type.value}",
                reasoning=f"عدوانية {aggression_level:.2f}, ضغط شراء {buying_pressure:.2f}, ضغط بيع {selling_pressure:.2f}",
                supporting_data={
                    'timeframe': timeframe_seconds,
                    'candles_count': len(recent_candles),
                    'price_range': recent_candles[-1]['high'] - recent_candles[-1]['low']
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل سلوك الفترة {timeframe_seconds}: {str(e)}")
            return None

    async def _calculate_aggression_level(self, candles: List[Dict[str, Any]]) -> float:
        """حساب مستوى العدوانية"""
        try:
            if len(candles) < 2:
                return 0.5
            
            # حساب التغييرات السعرية السريعة
            price_changes = []
            for i in range(1, len(candles)):
                change = abs(candles[i]['close'] - candles[i-1]['close'])
                price_range = candles[i]['high'] - candles[i]['low']
                if price_range > 0:
                    aggression = change / price_range
                    price_changes.append(aggression)
            
            if not price_changes:
                return 0.5
            
            # متوسط العدوانية
            avg_aggression = statistics.mean(price_changes)
            
            # تطبيع إلى 0-1
            normalized_aggression = min(1.0, avg_aggression * 2)
            
            return normalized_aggression
            
        except Exception as e:
            logger.error(f"خطأ في حساب مستوى العدوانية: {str(e)}")
            return 0.5

    async def _calculate_volume_intensity(self, candles: List[Dict[str, Any]]) -> float:
        """حساب كثافة الحجم"""
        try:
            volumes = [candle.get('volume', 0) for candle in candles]
            if not any(volumes) or len(volumes) < 2:
                return 0.5
            
            # مقارنة الحجم الحالي مع المتوسط
            current_volume = volumes[-1]
            avg_volume = statistics.mean(volumes[:-1]) if len(volumes) > 1 else current_volume
            
            if avg_volume == 0:
                return 0.5
            
            # نسبة الكثافة
            intensity_ratio = current_volume / avg_volume
            
            # تطبيع إلى 0-1
            normalized_intensity = min(1.0, intensity_ratio / 3.0)  # 3x = كثافة عالية
            
            return normalized_intensity
            
        except Exception as e:
            logger.error(f"خطأ في حساب كثافة الحجم: {str(e)}")
            return 0.5

    async def _calculate_price_velocity(self, candles: List[Dict[str, Any]]) -> float:
        """حساب سرعة السعر"""
        try:
            if len(candles) < 3:
                return 0
            
            # حساب التغييرات السعرية
            price_changes = []
            for i in range(1, len(candles)):
                change = candles[i]['close'] - candles[i-1]['close']
                price_changes.append(abs(change))
            
            if not price_changes:
                return 0
            
            # متوسط السرعة
            avg_velocity = statistics.mean(price_changes)
            
            # تطبيع بناء على متوسط السعر
            avg_price = statistics.mean([candle['close'] for candle in candles])
            normalized_velocity = (avg_velocity / avg_price) * 1000  # تحويل إلى نقاط أساس
            
            return min(10.0, normalized_velocity)  # حد أقصى 10 نقاط أساس
            
        except Exception as e:
            logger.error(f"خطأ في حساب سرعة السعر: {str(e)}")
            return 0

    async def _calculate_buying_pressure(self, candles: List[Dict[str, Any]]) -> float:
        """حساب ضغط الشراء"""
        try:
            if not candles:
                return 0.5

            buying_pressure = 0
            total_pressure = 0

            for candle in candles:
                open_price = candle['open']
                high_price = candle['high']
                low_price = candle['low']
                close_price = candle['close']

                # حساب ضغط الشراء بناء على موقع الإغلاق
                price_range = high_price - low_price
                if price_range > 0:
                    # كلما كان الإغلاق أقرب للقمة، زاد ضغط الشراء
                    buy_pressure = (close_price - low_price) / price_range
                    buying_pressure += buy_pressure
                    total_pressure += 1

            if total_pressure == 0:
                return 0.5

            avg_buying_pressure = buying_pressure / total_pressure
            return avg_buying_pressure

        except Exception as e:
            logger.error(f"خطأ في حساب ضغط الشراء: {str(e)}")
            return 0.5

    async def _calculate_selling_pressure(self, candles: List[Dict[str, Any]]) -> float:
        """حساب ضغط البيع"""
        try:
            if not candles:
                return 0.5

            selling_pressure = 0
            total_pressure = 0

            for candle in candles:
                open_price = candle['open']
                high_price = candle['high']
                low_price = candle['low']
                close_price = candle['close']

                # حساب ضغط البيع بناء على موقع الإغلاق
                price_range = high_price - low_price
                if price_range > 0:
                    # كلما كان الإغلاق أقرب للقاع، زاد ضغط البيع
                    sell_pressure = (high_price - close_price) / price_range
                    selling_pressure += sell_pressure
                    total_pressure += 1

            if total_pressure == 0:
                return 0.5

            avg_selling_pressure = selling_pressure / total_pressure
            return avg_selling_pressure

        except Exception as e:
            logger.error(f"خطأ في حساب ضغط البيع: {str(e)}")
            return 0.5

    def _determine_market_behavior(self, aggression: float, buying_pressure: float, selling_pressure: float) -> MarketBehavior:
        """تحديد سلوك السوق"""
        try:
            pressure_diff = buying_pressure - selling_pressure

            # سلوك شرائي
            if pressure_diff > 0.3:
                if aggression > 0.7:
                    return MarketBehavior.AGGRESSIVE_BUYING
                elif aggression > 0.5:
                    return MarketBehavior.MODERATE_BUYING
                else:
                    return MarketBehavior.CAUTIOUS_BUYING

            # سلوك بيعي
            elif pressure_diff < -0.3:
                if aggression > 0.7:
                    return MarketBehavior.AGGRESSIVE_SELLING
                elif aggression > 0.5:
                    return MarketBehavior.MODERATE_SELLING
                else:
                    return MarketBehavior.CAUTIOUS_SELLING

            # سلوك محايد
            else:
                return MarketBehavior.NEUTRAL

        except Exception as e:
            logger.error(f"خطأ في تحديد سلوك السوق: {str(e)}")
            return MarketBehavior.NEUTRAL

    def _determine_trading_intention(self, buying_pressure: float, selling_pressure: float, volume_intensity: float) -> TradingIntention:
        """تحديد نية التداول"""
        try:
            pressure_ratio = buying_pressure / (selling_pressure + 0.01)  # تجنب القسمة على صفر

            # تجميع قوي
            if pressure_ratio > 2.0 and volume_intensity > 0.7:
                return TradingIntention.STRONG_ACCUMULATION

            # تجميع عادي
            elif pressure_ratio > 1.5:
                return TradingIntention.ACCUMULATION

            # توزيع
            elif pressure_ratio < 0.5 and volume_intensity > 0.7:
                return TradingIntention.STRONG_DISTRIBUTION

            # توزيع عادي
            elif pressure_ratio < 0.7:
                return TradingIntention.DISTRIBUTION

            # تحضير للاختراق
            elif volume_intensity > 0.8 and abs(buying_pressure - selling_pressure) < 0.2:
                return TradingIntention.BREAKOUT_PREPARATION

            # توطيد
            else:
                return TradingIntention.CONSOLIDATION

        except Exception as e:
            logger.error(f"خطأ في تحديد نية التداول: {str(e)}")
            return TradingIntention.CONSOLIDATION

    def _determine_price_action(self, candles: List[Dict[str, Any]], aggression: float) -> PriceAction:
        """تحديد حركة السعر"""
        try:
            if len(candles) < 2:
                return PriceAction.TESTING

            current_candle = candles[-1]
            prev_candle = candles[-2]

            # حساب التغيير السعري
            price_change = current_candle['close'] - prev_candle['close']
            price_range = current_candle['high'] - current_candle['low']

            if price_range == 0:
                return PriceAction.TESTING

            change_ratio = abs(price_change) / price_range

            # رفض قوي
            if change_ratio > 0.7 and aggression > 0.8:
                return PriceAction.STRONG_REJECTION

            # رفض ضعيف
            elif change_ratio > 0.4 and aggression > 0.5:
                return PriceAction.WEAK_REJECTION

            # اختراق
            elif change_ratio > 0.6 and aggression > 0.6:
                return PriceAction.BREAKTHROUGH

            # قبول
            elif change_ratio < 0.3:
                return PriceAction.ACCEPTANCE

            # اختبار
            else:
                return PriceAction.TESTING

        except Exception as e:
            logger.error(f"خطأ في تحديد حركة السعر: {str(e)}")
            return PriceAction.TESTING

    def _calculate_behavior_confidence(self, aggression: float, volume_intensity: float, buying_pressure: float, selling_pressure: float) -> float:
        """حساب ثقة السلوك"""
        try:
            # النقاط الأساسية من وضوح السلوك
            pressure_clarity = abs(buying_pressure - selling_pressure) * 50

            # مكافأة للعدوانية الواضحة
            aggression_bonus = aggression * 30

            # مكافأة لكثافة الحجم
            volume_bonus = volume_intensity * 20

            total_confidence = pressure_clarity + aggression_bonus + volume_bonus

            return min(95, max(30, total_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة السلوك: {str(e)}")
            return 50

    async def _analyze_market_pressures(self, candles: List[Dict[str, Any]]) -> Dict[str, float]:
        """تحليل ضغوط السوق"""
        try:
            if len(candles) < 5:
                return {'buying': 0.5, 'selling': 0.5, 'net': 0}

            recent_candles = candles[-5:]

            total_buying = 0
            total_selling = 0

            for candle in recent_candles:
                buying = await self._calculate_buying_pressure([candle])
                selling = await self._calculate_selling_pressure([candle])

                total_buying += buying
                total_selling += selling

            avg_buying = total_buying / len(recent_candles)
            avg_selling = total_selling / len(recent_candles)
            net_pressure = avg_buying - avg_selling

            return {
                'buying': avg_buying,
                'selling': avg_selling,
                'net': net_pressure
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل ضغوط السوق: {str(e)}")
            return {'buying': 0.5, 'selling': 0.5, 'net': 0}

    async def _analyze_price_action(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل حركة السعر"""
        try:
            if len(candles) < 3:
                return {'trend': 'NEUTRAL', 'strength': 0.5, 'consistency': 0.5}

            recent_candles = candles[-3:]
            prices = [candle['close'] for candle in recent_candles]

            # تحديد الاتجاه
            if prices[-1] > prices[0]:
                trend = 'BULLISH'
                strength = (prices[-1] - prices[0]) / prices[0]
            elif prices[-1] < prices[0]:
                trend = 'BEARISH'
                strength = (prices[0] - prices[-1]) / prices[0]
            else:
                trend = 'NEUTRAL'
                strength = 0

            # حساب الاتساق
            changes = []
            for i in range(1, len(prices)):
                change = 1 if prices[i] > prices[i-1] else -1 if prices[i] < prices[i-1] else 0
                changes.append(change)

            if changes:
                consistency = abs(sum(changes)) / len(changes)
            else:
                consistency = 0

            return {
                'trend': trend,
                'strength': abs(strength) * 100,  # تحويل إلى نسبة مئوية
                'consistency': consistency
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل حركة السعر: {str(e)}")
            return {'trend': 'NEUTRAL', 'strength': 0.5, 'consistency': 0.5}

    def _evaluate_overall_behavior(self, behavior_signals: List[BehaviorSignal], market_pressures: Dict[str, float], price_action: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم السلوك الإجمالي"""
        try:
            if not behavior_signals:
                return {
                    'behavior': MarketBehavior.NEUTRAL,
                    'intention': TradingIntention.CONSOLIDATION,
                    'market_state': 'UNCERTAIN',
                    'aggression': 0.5,
                    'pressure_ratio': 1.0,
                    'sentiment': 0.0,
                    'is_actionable': False,
                    'action': 'WAIT',
                    'timing': 'POOR'
                }

            # حساب الأوزان المرجحة للسلوك
            behavior_weights = []
            total_confidence = 0

            for signal in behavior_signals:
                weight = self.behavior_weights.get(signal.behavior_type, 0.0)
                weighted_score = weight * signal.confidence
                behavior_weights.append(weighted_score)
                total_confidence += signal.confidence

            # تحديد السلوك الإجمالي
            avg_behavior_weight = statistics.mean(behavior_weights) if behavior_weights else 0

            if avg_behavior_weight > 0.5:
                overall_behavior = MarketBehavior.AGGRESSIVE_BUYING
            elif avg_behavior_weight > 0.2:
                overall_behavior = MarketBehavior.MODERATE_BUYING
            elif avg_behavior_weight > -0.2:
                overall_behavior = MarketBehavior.NEUTRAL
            elif avg_behavior_weight > -0.5:
                overall_behavior = MarketBehavior.MODERATE_SELLING
            else:
                overall_behavior = MarketBehavior.AGGRESSIVE_SELLING

            # تحديد النية السائدة
            intentions = [signal.intention for signal in behavior_signals]
            if intentions:
                # أخذ النية الأكثر تكراراً
                intention_counts = {}
                for intention in intentions:
                    intention_counts[intention] = intention_counts.get(intention, 0) + 1
                dominant_intention = max(intention_counts.keys(), key=lambda x: intention_counts[x])
            else:
                dominant_intention = TradingIntention.CONSOLIDATION

            # حساب العدوانية الإجمالية
            overall_aggression = statistics.mean([signal.aggression_level for signal in behavior_signals])

            # حساب نسبة الضغط
            buying_pressure = market_pressures.get('buying', 0.5)
            selling_pressure = market_pressures.get('selling', 0.5)
            pressure_ratio = buying_pressure / (selling_pressure + 0.01)

            # حساب المعنويات
            sentiment_score = market_pressures.get('net', 0.0)

            # تحديد حالة السوق
            if price_action['trend'] != 'NEUTRAL' and price_action['consistency'] > 0.7:
                market_state = 'TRENDING'
            elif overall_aggression > 0.7:
                market_state = 'VOLATILE'
            elif overall_aggression < 0.3:
                market_state = 'QUIET'
            else:
                market_state = 'NORMAL'

            # تحديد قابلية التنفيذ
            avg_confidence = total_confidence / len(behavior_signals)
            is_actionable = (
                avg_confidence >= self.min_confidence_threshold and
                overall_aggression >= 0.4 and
                abs(sentiment_score) >= 0.2 and
                overall_behavior != MarketBehavior.NEUTRAL
            )

            # تحديد الإجراء المطلوب
            if is_actionable:
                if overall_behavior in [MarketBehavior.AGGRESSIVE_BUYING, MarketBehavior.MODERATE_BUYING]:
                    action = 'BUY'
                elif overall_behavior in [MarketBehavior.AGGRESSIVE_SELLING, MarketBehavior.MODERATE_SELLING]:
                    action = 'SELL'
                else:
                    action = 'WAIT'
            else:
                action = 'WAIT'

            # تحديد جودة التوقيت
            if overall_aggression > 0.7 and abs(sentiment_score) > 0.4:
                timing = 'EXCELLENT'
            elif overall_aggression > 0.5 and abs(sentiment_score) > 0.2:
                timing = 'GOOD'
            elif overall_aggression > 0.3:
                timing = 'FAIR'
            else:
                timing = 'POOR'

            return {
                'behavior': overall_behavior,
                'intention': dominant_intention,
                'market_state': market_state,
                'aggression': overall_aggression,
                'pressure_ratio': pressure_ratio,
                'sentiment': sentiment_score,
                'is_actionable': is_actionable,
                'action': action,
                'timing': timing
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم السلوك الإجمالي: {str(e)}")
            return {
                'behavior': MarketBehavior.NEUTRAL,
                'intention': TradingIntention.CONSOLIDATION,
                'market_state': 'UNCERTAIN',
                'aggression': 0.5,
                'pressure_ratio': 1.0,
                'sentiment': 0.0,
                'is_actionable': False,
                'action': 'WAIT',
                'timing': 'POOR'
            }

    def _generate_behavior_recommendations(self, behavior_signals: List[BehaviorSignal], overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات السلوك"""
        try:
            recommendations = []

            if not behavior_signals:
                recommendations.append("لا توجد إشارات سلوك واضحة")
                return recommendations

            # توصيات بناء على السلوك
            behavior = overall_assessment['behavior']
            if behavior in [MarketBehavior.AGGRESSIVE_BUYING, MarketBehavior.AGGRESSIVE_SELLING]:
                recommendations.append("سلوك عدواني - فرصة قوية للدخول")
            elif behavior == MarketBehavior.NEUTRAL:
                recommendations.append("سلوك محايد - انتظار إشارة أوضح")

            # توصيات بناء على النية
            intention = overall_assessment['intention']
            if intention == TradingIntention.STRONG_ACCUMULATION:
                recommendations.append("تجميع قوي - اتجاه صاعد محتمل")
            elif intention == TradingIntention.STRONG_DISTRIBUTION:
                recommendations.append("توزيع قوي - اتجاه هابط محتمل")
            elif intention == TradingIntention.BREAKOUT_PREPARATION:
                recommendations.append("تحضير للاختراق - مراقبة لحظية مطلوبة")

            # توصيات بناء على جودة التوقيت
            timing = overall_assessment['timing']
            if timing == 'EXCELLENT':
                recommendations.append("توقيت ممتاز للدخول")
            elif timing == 'POOR':
                recommendations.append("توقيت ضعيف - تأجيل الدخول")

            # توصيات بناء على حالة السوق
            market_state = overall_assessment['market_state']
            if market_state == 'VOLATILE':
                recommendations.append("سوق متقلب - تقليل حجم الصفقة")
            elif market_state == 'QUIET':
                recommendations.append("سوق هادئ - قد لا يكون مناسب للسكالبينغ")

            return recommendations[:5]  # أقصى 5 توصيات

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات السلوك: {str(e)}")
            return []

    def get_behavior_summary(self, analysis: RealtimeBehaviorAnalysis) -> Dict[str, Any]:
        """الحصول على ملخص تحليل السلوك"""
        if not analysis:
            return {}

        return {
            'asset': analysis.asset,
            'overall_behavior': analysis.overall_behavior.value,
            'dominant_intention': analysis.dominant_intention.value,
            'market_state': analysis.market_state,
            'overall_aggression': analysis.overall_aggression,
            'market_pressure_ratio': analysis.market_pressure_ratio,
            'sentiment_score': analysis.sentiment_score,
            'is_actionable': analysis.is_actionable,
            'recommended_action': analysis.recommended_action,
            'timing_quality': analysis.timing_quality,
            'processing_time_ms': analysis.processing_time_ms,
            'signals_count': len(analysis.behavior_signals),
            'key_signals': [
                {
                    'timeframe': signal.timeframe_seconds,
                    'behavior': signal.behavior_type.value,
                    'intention': signal.intention.value,
                    'confidence': signal.confidence,
                    'aggression': signal.aggression_level,
                    'buying_pressure': signal.buying_pressure,
                    'selling_pressure': signal.selling_pressure
                }
                for signal in analysis.behavior_signals
            ],
            'top_recommendations': analysis.recommendations[:3]
        }

    def is_behavior_actionable(self, analysis: RealtimeBehaviorAnalysis, min_confidence: float = None) -> bool:
        """تحديد ما إذا كان السلوك قابل للتنفيذ"""
        if not analysis:
            return False

        min_conf = min_confidence or self.min_confidence_threshold

        avg_confidence = statistics.mean([signal.confidence for signal in analysis.behavior_signals]) if analysis.behavior_signals else 0

        return (
            analysis.is_actionable and
            avg_confidence >= min_conf and
            analysis.overall_aggression >= 0.4 and
            abs(analysis.sentiment_score) >= 0.2 and
            analysis.overall_behavior != MarketBehavior.NEUTRAL
        )

    def get_behavior_history(self, asset: str, limit: int = 10) -> List[RealtimeBehaviorAnalysis]:
        """الحصول على تاريخ السلوك لأصل معين"""
        if asset not in self.behavior_history:
            return []

        return self.behavior_history[asset][-limit:]

# إنشاء instance عام للاستخدام
realtime_behavior_analyzer = RealtimeBehaviorAnalyzer()
