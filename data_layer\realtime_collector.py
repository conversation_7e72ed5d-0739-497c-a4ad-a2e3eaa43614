"""
مجمع البيانات المباشرة لنظام السكالبينغ
"""

import asyncio
import time
import redis
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, AsyncGenerator
from BinaryOptionsToolsV2.pocketoption import PocketOption, PocketOptionAsync

from config.trading_config import TradingConfig, default_trading_config
from database.repository import historical_data_repo
from database.models import HistoricalData
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, ConnectionError, DataError
from utils.helpers import validate_candle_data, clean_candle_data, get_current_timestamp

logger = scalping_logger.get_logger("realtime_collector")

class RealTimeDataCollector:
    """مجمع البيانات المباشرة مع دعم WebSocket"""

    def __init__(self, config: TradingConfig = None):
        self.config = config or default_trading_config
        self.api = None
        self.async_api = None
        self.is_connected = False
        self.last_data_time = {}
        self.data_cache = {}
        self.connection_retry_count = 0
        self.max_retry_attempts = 5

        # إعدادات WebSocket
        self.active_streams = {}
        self.redis_client = None
        self.is_streaming = False
        self.stream_stats = {
            'active_subscriptions': 0,
            'failed_subscriptions': 0,
            'data_received': 0,
            'connection_established': False
        }
        
    @handle_errors(raise_on_error=True)
    def initialize_connection(self):
        """تهيئة الاتصال مع Pocket Option"""
        try:
            if not self.config.pocket_option_ssid:
                raise ConnectionError("SSID غير موجود في الإعدادات")
            
            # إنشاء الاتصال المتزامن
            self.api = PocketOption(self.config.pocket_option_ssid)
            time.sleep(5)  # انتظار تأسيس الاتصال
            
            # اختبار الاتصال
            balance = self.api.balance()
            if balance is None:
                raise ConnectionError("فشل في الحصول على الرصيد - الاتصال غير مستقر")
            
            self.is_connected = True
            self.connection_retry_count = 0
            
            logger.info(f"تم تأسيس الاتصال بنجاح - الرصيد: ${balance}")
            
        except Exception as e:
            self.is_connected = False
            self.connection_retry_count += 1
            logger.error(f"فشل في تهيئة الاتصال: {str(e)}")
            raise ConnectionError(f"فشل الاتصال: {str(e)}")
    
    @handle_async_errors(raise_on_error=True)
    async def initialize_async_connection(self):
        """تهيئة الاتصال غير المتزامن"""
        try:
            if not self.config.pocket_option_ssid:
                raise ConnectionError("SSID غير موجود في الإعدادات")
            
            # إنشاء الاتصال غير المتزامن
            self.async_api = PocketOptionAsync(self.config.pocket_option_ssid)
            await asyncio.sleep(5)  # انتظار تأسيس الاتصال
            
            # اختبار الاتصال
            balance = await self.async_api.balance()
            if balance is None:
                raise ConnectionError("فشل في الحصول على الرصيد - الاتصال غير مستقر")
            
            self.is_connected = True
            self.connection_retry_count = 0
            
            logger.info(f"تم تأسيس الاتصال غير المتزامن بنجاح - الرصيد: ${balance}")
            
        except Exception as e:
            self.is_connected = False
            self.connection_retry_count += 1
            logger.error(f"فشل في تهيئة الاتصال غير المتزامن: {str(e)}")
            raise ConnectionError(f"فشل الاتصال غير المتزامن: {str(e)}")
    
    @handle_errors(default_return=None, log_error=True)
    def get_current_price(self, asset: str) -> Optional[float]:
        """الحصول على السعر الحالي لأصل معين"""
        try:
            if not self.is_connected:
                self.initialize_connection()
            
            # جلب آخر شمعة
            candles = self.api.get_candles(asset, 60, 60)  # آخر دقيقة
            if candles and len(candles) > 0:
                latest_candle = candles[-1]
                return float(latest_candle['close'])
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في جلب السعر الحالي لـ {asset}: {str(e)}")
            return None
    
    @handle_errors(default_return=[], log_error=True)
    def get_latest_candles(self, asset: str, timeframe: int = 60, count: int = 30) -> List[Dict[str, Any]]:
        """الحصول على آخر شموع لأصل معين"""
        try:
            if not self.is_connected:
                self.initialize_connection()
            
            # حساب المدة المطلوبة بالثواني
            duration = timeframe * count
            
            # جلب البيانات
            candles = self.api.get_candles(asset, timeframe, duration)
            
            if not candles:
                logger.warning(f"لم يتم جلب أي بيانات لـ {asset}")
                return []
            
            # تنظيف البيانات
            cleaned_candles = clean_candle_data(candles)
            
            # إضافة timestamp إذا لم يكن موجود
            current_time = get_current_timestamp()
            for i, candle in enumerate(cleaned_candles):
                if 'timestamp' not in candle:
                    candle['timestamp'] = current_time - timedelta(seconds=timeframe * (len(cleaned_candles) - i - 1))
            
            # تحديث الكاش
            self.data_cache[f"{asset}_{timeframe}"] = {
                'data': cleaned_candles,
                'timestamp': current_time
            }
            
            logger.debug(f"تم جلب {len(cleaned_candles)} شمعة لـ {asset} ({timeframe}s)")
            return cleaned_candles
            
        except Exception as e:
            logger.error(f"خطأ في جلب الشموع لـ {asset}: {str(e)}")
            return []
    
    @handle_async_errors(default_return=[], log_error=True)
    async def get_latest_candles_async(self, asset: str, timeframe: int = 60, count: int = 30) -> List[Dict[str, Any]]:
        """الحصول على آخر شموع بشكل غير متزامن"""
        try:
            if not self.is_connected:
                await self.initialize_async_connection()
            
            # حساب المدة المطلوبة بالثواني
            duration = timeframe * count
            
            # جلب البيانات
            candles = await self.async_api.get_candles(asset, timeframe, duration)
            
            if not candles:
                logger.warning(f"لم يتم جلب أي بيانات لـ {asset}")
                return []
            
            # تنظيف البيانات
            cleaned_candles = clean_candle_data(candles)
            
            # إضافة timestamp إذا لم يكن موجود
            current_time = get_current_timestamp()
            for i, candle in enumerate(cleaned_candles):
                if 'timestamp' not in candle:
                    candle['timestamp'] = current_time - timedelta(seconds=timeframe * (len(cleaned_candles) - i - 1))
            
            # تحديث الكاش
            self.data_cache[f"{asset}_{timeframe}"] = {
                'data': cleaned_candles,
                'timestamp': current_time
            }
            
            logger.debug(f"تم جلب {len(cleaned_candles)} شمعة لـ {asset} ({timeframe}s) - غير متزامن")
            return cleaned_candles
            
        except Exception as e:
            logger.error(f"خطأ في جلب الشموع لـ {asset} (غير متزامن): {str(e)}")
            return []
    
    @handle_errors(default_return={}, log_error=True)
    def get_multiple_assets_data(self, assets: List[str], timeframe: int = 60, count: int = 30) -> Dict[str, List[Dict[str, Any]]]:
        """جلب بيانات عدة أصول"""
        results = {}
        
        for asset in assets:
            try:
                candles = self.get_latest_candles(asset, timeframe, count)
                results[asset] = candles
                
                # تأخير قصير لتجنب إرهاق الخادم
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"خطأ في جلب بيانات {asset}: {str(e)}")
                results[asset] = []
        
        return results
    
    @handle_async_errors(default_return={}, log_error=True)
    async def get_multiple_assets_data_async(self, assets: List[str], timeframe: int = 60, count: int = 30) -> Dict[str, List[Dict[str, Any]]]:
        """جلب بيانات عدة أصول بشكل غير متزامن"""
        tasks = []
        
        for asset in assets:
            task = self.get_latest_candles_async(asset, timeframe, count)
            tasks.append((asset, task))
        
        results = {}
        for asset, task in tasks:
            try:
                candles = await task
                results[asset] = candles
            except Exception as e:
                logger.error(f"خطأ في جلب بيانات {asset} (غير متزامن): {str(e)}")
                results[asset] = []
        
        return results
    
    @handle_errors(default_return=False, log_error=True)
    def store_candles_to_database(self, asset: str, candles: List[Dict[str, Any]], timeframe: int) -> bool:
        """حفظ الشموع في قاعدة البيانات"""
        try:
            if not candles:
                return False
            
            # تحويل البيانات لتنسيق قاعدة البيانات
            db_candles = []
            for candle in candles:
                if validate_candle_data(candle):
                    db_candle = HistoricalData.from_candle_dict(candle, asset, timeframe)
                    db_candles.append(db_candle.to_dict())
            
            if db_candles:
                success = historical_data_repo.bulk_insert_candles(db_candles)
                if success:
                    logger.debug(f"تم حفظ {len(db_candles)} شمعة لـ {asset} في قاعدة البيانات")
                return success
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الشموع لـ {asset}: {str(e)}")
            return False
    
    def get_cached_data(self, asset: str, timeframe: int, max_age_seconds: int = 60) -> Optional[List[Dict[str, Any]]]:
        """الحصول على البيانات من الكاش"""
        cache_key = f"{asset}_{timeframe}"
        
        if cache_key in self.data_cache:
            cached_data = self.data_cache[cache_key]
            age = (get_current_timestamp() - cached_data['timestamp']).total_seconds()
            
            if age <= max_age_seconds:
                logger.debug(f"استخدام البيانات المخزنة لـ {asset} (عمر: {age:.1f}s)")
                return cached_data['data']
        
        return None
    
    def clear_cache(self):
        """مسح الكاش"""
        self.data_cache.clear()
        logger.debug("تم مسح كاش البيانات")
    
    @handle_errors(default_return=False, log_error=True)
    def test_connection(self) -> bool:
        """اختبار الاتصال"""
        try:
            if not self.is_connected:
                self.initialize_connection()
            
            # اختبار بسيط
            balance = self.api.balance()
            return balance is not None
            
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {str(e)}")
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """الحصول على حالة الاتصال"""
        return {
            'is_connected': self.is_connected,
            'retry_count': self.connection_retry_count,
            'max_retries': self.max_retry_attempts,
            'cache_size': len(self.data_cache),
            'last_data_times': self.last_data_time.copy()
        }
    
    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_redis(self) -> bool:
        """تهيئة اتصال Redis للبيانات المباشرة"""
        try:
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                decode_responses=True
            )
            # اختبار الاتصال
            self.redis_client.ping()
            logger.info("تم تهيئة اتصال Redis بنجاح")
            return True
        except Exception as e:
            logger.error(f"فشل في تهيئة Redis: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def subscribe_to_asset(self, asset: str) -> bool:
        """الاشتراك في البث المباشر لأصل واحد"""
        try:
            if not self.async_api:
                await self.initialize_async_connection()

            logger.info(f"الاشتراك في البث المباشر لـ {asset}")

            # إنشاء البث المباشر من نفس الاتصال مع timeout
            try:
                stream = await asyncio.wait_for(
                    self.async_api.subscribe_symbol(asset),
                    timeout=30.0  # 30 ثانية timeout
                )
                self.active_streams[asset] = stream

                # بدء معالجة البيانات
                asyncio.create_task(self._process_stream_data(asset, stream))

                self.stream_stats['active_subscriptions'] += 1
                logger.info(f"✅ تم الاشتراك في {asset}")
                return True

            except asyncio.TimeoutError:
                logger.warning(f"⏰ انتهت مهلة الاشتراك في {asset} (30s)")
                self.stream_stats['failed_subscriptions'] += 1
                return False

        except Exception as e:
            logger.error(f"❌ فشل في الاشتراك في {asset}: {str(e)}")
            self.stream_stats['failed_subscriptions'] += 1
            return False

    @handle_async_errors(default_return={}, log_error=True)
    async def subscribe_to_multiple_assets(self, assets: List[str], batch_size: int = 25) -> Dict[str, bool]:
        """الاشتراك في عدة أصول من نفس الاتصال"""
        results = {}

        # التأكد من وجود الاتصال
        if not self.async_api:
            await self.initialize_async_connection()

        # تهيئة Redis
        redis_ready = await self.initialize_redis()
        if not redis_ready:
            logger.warning("Redis غير متاح - سيتم تجاهل التخزين")

        logger.info(f"بدء الاشتراك في {len(assets)} أصل من نفس الاتصال")

        # تقسيم الأصول إلى مجموعات لتجنب الحمل الزائد
        for i in range(0, len(assets), batch_size):
            batch = assets[i:i + batch_size]
            logger.info(f"معالجة المجموعة {i//batch_size + 1}: {len(batch)} أصول")

            # إنشاء مهام الاشتراك للمجموعة الحالية
            tasks = []
            for asset in batch:
                task = asyncio.create_task(self.subscribe_to_asset(asset))
                tasks.append((asset, task))

            # انتظار اكتمال المجموعة
            for asset, task in tasks:
                try:
                    success = await task
                    results[asset] = success
                except Exception as e:
                    logger.error(f"خطأ في الاشتراك في {asset}: {str(e)}")
                    results[asset] = False

            # تأخير قصير بين المجموعات لتجنب إرهاق الخادم
            if i + batch_size < len(assets):
                await asyncio.sleep(0.5)  # تقليل التأخير لزيادة السرعة

        return results

    async def _process_stream_data(self, asset: str, stream):
        """معالجة بيانات البث المباشر"""
        try:
            async for candle in stream:
                if not self.is_streaming:
                    break

                # تحديث الإحصائيات
                self.stream_stats['data_received'] += 1

                # تخزين في Redis
                if self.redis_client:
                    # التحقق من وجود البيانات المطلوبة
                    if candle and isinstance(candle, dict):
                        candle_data = {
                            'asset': asset,
                            'timestamp': datetime.now().isoformat(),
                            'open': float(candle.get('open', 0)),
                            'high': float(candle.get('high', 0)),
                            'low': float(candle.get('low', 0)),
                            'close': float(candle.get('close', 0)),
                            'volume': float(candle.get('volume', 0)),
                            'time': candle.get('time', int(datetime.now().timestamp()))
                        }

                        # تخزين في Redis مع انتهاء صلاحية
                        redis_key = f"live_candle:{asset}"
                        self.redis_client.setex(
                            redis_key,
                            300,  # 5 دقائق انتهاء صلاحية
                            json.dumps(candle_data)
                        )

                        # تخزين في قائمة التاريخ المباشر
                        history_key = f"live_history:{asset}"
                        self.redis_client.lpush(history_key, json.dumps(candle_data))
                        self.redis_client.ltrim(history_key, 0, 99)  # الاحتفاظ بآخر 100 شمعة
                        self.redis_client.expire(history_key, 3600)  # ساعة واحدة

                        # تحديث الكاش المحلي
                        self.last_data_time[asset] = datetime.now()

                        logger.debug(f"📊 {asset}: {candle.get('close', 'N/A')} - تم التخزين في Redis")
                    else:
                        logger.warning(f"بيانات غير صالحة لـ {asset}: {candle}")

        except Exception as e:
            logger.error(f"خطأ في معالجة بيانات {asset}: {str(e)}")

    def start_streaming(self, asset: str) -> bool:
        """بدء البث المباشر لأصل واحد (دالة متزامنة للتوافق)"""
        try:
            logger.debug(f"بدء البث المباشر لـ {asset}")
            # محاكاة بدء البث المباشر
            return True
        except Exception as e:
            logger.error(f"خطأ في بدء البث المباشر لـ {asset}: {str(e)}")
            return False

    @handle_async_errors(default_return={}, log_error=True)
    async def start_streaming_async(self, assets: List[str], duration_seconds: int = None) -> Dict[str, bool]:
        """بدء البث المباشر لجميع الأصول"""
        self.is_streaming = True

        # الاشتراك في جميع الأصول
        results = await self.subscribe_to_multiple_assets(assets)

        # إحصائيات الاشتراك
        successful = sum(1 for success in results.values() if success)
        logger.info(f"نجح الاشتراك في {successful}/{len(assets)} أصل")

        # عرض معلومات الاتصال
        connection_info = await self.get_connection_info()
        logger.info(f"معلومات الاتصال: {connection_info}")

        # إذا تم تحديد مدة، انتظر ثم أوقف
        if duration_seconds:
            logger.info(f"بدء البث المباشر لمدة {duration_seconds} ثانية...")
            await asyncio.sleep(duration_seconds)
            await self.stop_streaming()
        else:
            logger.info("بدء البث المباشر المستمر...")

        return results

    async def stop_streaming(self):
        """إيقاف البث المباشر"""
        self.is_streaming = False

        logger.info("إيقاف البث المباشر...")

        # مسح الاشتراكات
        self.active_streams.clear()
        self.stream_stats['active_subscriptions'] = 0

        logger.info("تم إيقاف البث المباشر")

    async def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال"""
        if not self.async_api:
            return {"connected": False}

        try:
            balance = await self.async_api.balance()
            payouts = await self.async_api.payout()

            return {
                "connected": True,
                "balance": balance,
                "available_assets": len(payouts) if payouts else 0,
                "active_subscriptions": len(self.active_streams),
                "redis_connected": self.redis_client is not None
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الاتصال: {str(e)}")
            return {"connected": False, "error": str(e)}

    def get_live_data_from_redis(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات المباشرة من Redis"""
        if not self.redis_client:
            return None

        try:
            redis_key = f"live_candle:{asset}"
            data = self.redis_client.get(redis_key)

            if data:
                return json.loads(data)
            return None

        except Exception as e:
            logger.error(f"خطأ في جلب البيانات المباشرة من Redis لـ {asset}: {str(e)}")
            return None

    def get_live_history_from_redis(self, asset: str, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على تاريخ البيانات المباشرة من Redis"""
        if not self.redis_client:
            return []

        try:
            history_key = f"live_history:{asset}"
            data_list = self.redis_client.lrange(history_key, 0, limit - 1)

            history = []
            for data in data_list:
                try:
                    candle_data = json.loads(data)
                    history.append(candle_data)
                except json.JSONDecodeError:
                    continue

            return history

        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ البيانات من Redis لـ {asset}: {str(e)}")
            return []

    @handle_async_errors(default_return=False, log_error=True)
    async def store_closed_candle_to_database(self, asset: str, candle_data: Dict[str, Any]) -> bool:
        """نقل الشمعة المغلقة من Redis إلى PostgreSQL"""
        try:
            # التحقق من صحة البيانات
            if not validate_candle_data(candle_data):
                logger.warning(f"بيانات شمعة غير صالحة لـ {asset}")
                return False

            # تحويل البيانات لتنسيق قاعدة البيانات
            db_candle = HistoricalData.from_candle_dict(candle_data, asset, 60)  # افتراض إطار زمني دقيقة

            # حفظ في قاعدة البيانات
            success = historical_data_repo.create_candle(db_candle.to_dict())

            if success:
                logger.debug(f"تم نقل شمعة مغلقة لـ {asset} إلى قاعدة البيانات")

                # حذف من Redis بعد النقل الناجح
                redis_key = f"live_candle:{asset}"
                self.redis_client.delete(redis_key)

            return success

        except Exception as e:
            logger.error(f"خطأ في نقل الشمعة المغلقة لـ {asset}: {str(e)}")
            return False

    def get_streaming_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات البث المباشر"""
        redis_stats = {}
        if self.redis_client:
            try:
                # إحصائيات Redis
                info = self.redis_client.info()
                redis_stats = {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                }
            except Exception as e:
                logger.error(f"خطأ في جلب إحصائيات Redis: {str(e)}")

        return {
            'connection_status': self.is_connected,
            'streaming_status': self.is_streaming,
            'active_subscriptions': self.stream_stats['active_subscriptions'],
            'failed_subscriptions': self.stream_stats['failed_subscriptions'],
            'data_received': self.stream_stats['data_received'],
            'active_streams': len(self.active_streams),
            'redis_stats': redis_stats,
            'last_data_times': {asset: time.isoformat() for asset, time in self.last_data_time.items()}
        }

    @handle_async_errors(default_return=False, log_error=True)
    async def update_payout_rates(self) -> bool:
        """تحديث نسب الأرباح في Redis"""
        try:
            if not self.async_api:
                await self.initialize_async_connection()

            # جلب نسب الأرباح الحالية
            payouts = await self.async_api.payout()

            if not payouts:
                logger.warning("لم يتم جلب نسب الأرباح")
                return False

            # تخزين في Redis
            if self.redis_client:
                payout_data = {
                    'timestamp': datetime.now().isoformat(),
                    'payouts': payouts,
                    'total_assets': len(payouts)
                }

                self.redis_client.setex(
                    'current_payouts',
                    3600,  # ساعة واحدة
                    json.dumps(payout_data)
                )

                # تخزين نسب الأرباح لكل أصل منفرد
                for asset, payout in payouts.items():
                    asset_key = f"payout:{asset}"
                    self.redis_client.setex(asset_key, 3600, str(payout))

                logger.info(f"تم تحديث نسب الأرباح لـ {len(payouts)} أصل في Redis")
                return True

            return False

        except Exception as e:
            logger.error(f"خطأ في تحديث نسب الأرباح: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def update_account_balance(self) -> bool:
        """تحديث رصيد الحساب في Redis"""
        try:
            if not self.async_api:
                await self.initialize_async_connection()

            # جلب الرصيد الحالي
            balance = await self.async_api.balance()

            if balance is None:
                logger.warning("لم يتم جلب الرصيد")
                return False

            # تخزين في Redis
            if self.redis_client:
                balance_data = {
                    'timestamp': datetime.now().isoformat(),
                    'balance': balance,
                    'currency': 'USD'  # افتراضي
                }

                self.redis_client.setex(
                    'current_balance',
                    300,  # 5 دقائق
                    json.dumps(balance_data)
                )

                logger.debug(f"تم تحديث الرصيد في Redis: ${balance}")
                return True

            return False

        except Exception as e:
            logger.error(f"خطأ في تحديث الرصيد: {str(e)}")
            return False

    def get_payout_from_redis(self, asset: str = None) -> Optional[Dict[str, Any]]:
        """الحصول على نسب الأرباح من Redis"""
        if not self.redis_client:
            return None

        try:
            if asset:
                # نسبة ربح أصل محدد
                asset_key = f"payout:{asset}"
                payout = self.redis_client.get(asset_key)
                if payout:
                    return {asset: float(payout)}
                return None
            else:
                # جميع نسب الأرباح
                data = self.redis_client.get('current_payouts')
                if data:
                    return json.loads(data)
                return None

        except Exception as e:
            logger.error(f"خطأ في جلب نسب الأرباح من Redis: {str(e)}")
            return None

    def get_balance_from_redis(self) -> Optional[Dict[str, Any]]:
        """الحصول على الرصيد من Redis"""
        if not self.redis_client:
            return None

        try:
            data = self.redis_client.get('current_balance')
            if data:
                return json.loads(data)
            return None

        except Exception as e:
            logger.error(f"خطأ في جلب الرصيد من Redis: {str(e)}")
            return None

    @handle_async_errors(default_return=False, log_error=True)
    async def cleanup_old_redis_data(self, max_age_hours: int = 24) -> bool:
        """تنظيف البيانات القديمة من Redis"""
        try:
            if not self.redis_client:
                return False

            # البحث عن المفاتيح القديمة
            pattern = "live_history:*"
            keys = self.redis_client.keys(pattern)

            cleaned_keys = 0
            for key in keys:
                try:
                    # فحص عمر المفتاح
                    ttl = self.redis_client.ttl(key)
                    if ttl == -1:  # لا يوجد انتهاء صلاحية
                        self.redis_client.expire(key, 3600)  # تعيين انتهاء صلاحية ساعة
                        cleaned_keys += 1
                except Exception:
                    continue

            if cleaned_keys > 0:
                logger.info(f"تم تنظيف {cleaned_keys} مفتاح في Redis")

            return True

        except Exception as e:
            logger.error(f"خطأ في تنظيف Redis: {str(e)}")
            return False

    def disconnect(self):
        """قطع الاتصال وتنظيف الموارد"""
        try:
            # إيقاف البث المباشر
            self.is_streaming = False

            # مسح الاشتراكات
            self.active_streams.clear()

            # إغلاق اتصال Redis
            if self.redis_client:
                self.redis_client.close()
                self.redis_client = None

            # تحديث الحالة
            self.is_connected = False

            logger.info("تم قطع الاتصال وتنظيف الموارد")

        except Exception as e:
            logger.error(f"خطأ في قطع الاتصال: {str(e)}")

# إنشاء مثيل عام للاستخدام
realtime_collector = RealTimeDataCollector()
