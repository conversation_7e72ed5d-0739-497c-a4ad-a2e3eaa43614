"""
نظام التحليل اللحظي للمؤشرات الفنية
Real-time Technical Indicators Analysis System
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import threading

from config.trading_config import default_trading_config
from config.currency_pairs import CURRENCY_PAIRS_70
from data_layer.realtime_collector import RealTimeDataCollector
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

# استيراد جميع المؤشرات الفنية الـ15
from indicators.base_indicator import BaseIndicator
from indicators.ema_indicator import EMAIndicator
from indicators.sma_indicator import SMAIndicator
from indicators.rsi_indicator import RSI5, RSI14
from indicators.macd_indicator import MACD
from indicators.momentum_indicator import Momentum
from indicators.bollinger_bands_indicator import BollingerBands
from indicators.atr_indicator import ATR5, ATR14
from indicators.heiken_ashi_indicator import Heike<PERSON><PERSON>hi
from indicators.zscore_indicator import ZScore
from indicators.stochastic_indicator import Stochastic
from indicators.williams_r_indicator import WilliamsR

logger = scalping_logger.get_logger("realtime_indicator_analyzer")

class RealTimeIndicatorAnalyzer:
    """محلل المؤشرات الفنية اللحظي"""
    
    def __init__(self, config=None):
        self.config = config or default_trading_config
        self.realtime_collector = RealTimeDataCollector(self.config)
        
        # إعداد المؤشرات الفنية الـ15
        self.indicators = self._initialize_indicators()
        
        # إعدادات النظام
        self.is_analyzing = False
        self.analysis_interval = 1.0  # ثانية واحدة
        self.min_candles_required = 26  # الحد الأدنى من الشموع للتحليل (أكبر period في المؤشرات)
        
        # إحصائيات التحليل
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'assets_analyzed': set(),
            'indicators_calculated': 0,
            'last_analysis_time': None,
            'analysis_duration_avg': 0.0
        }
        
        # تخزين النتائج
        self.latest_indicator_values = {}  # {asset: {indicator_name: values}}
        self.indicator_signals = {}  # {asset: {indicator_name: signal}}
        self.analysis_history = {}  # {asset: [analysis_results]}
        
        # إعدادات التشغيل المتوازي
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.analysis_lock = threading.Lock()
        
        logger.info(f"تم تهيئة محلل المؤشرات اللحظي مع {len(self.indicators)} مؤشر")
    
    def _initialize_indicators(self) -> Dict[str, BaseIndicator]:
        """تهيئة جميع المؤشرات الفنية الـ15"""
        indicators = {
            'EMA_5': EMAIndicator(5),
            'EMA_10': EMAIndicator(10),
            'EMA_21': EMAIndicator(21),
            'SMA_10': SMAIndicator(10),
            'RSI_5': RSI5(),
            'RSI_14': RSI14(),
            'MACD': MACD(),
            'Momentum': Momentum(),
            'BollingerBands': BollingerBands(),
            'ATR_5': ATR5(),
            'ATR_14': ATR14(),
            'HeikenAshi': HeikenAshi(),
            'ZScore': ZScore(),
            'Stochastic': Stochastic(),
            'WilliamsR': WilliamsR()
        }
        
        logger.info(f"تم تهيئة {len(indicators)} مؤشر فني:")
        for name, indicator in indicators.items():
            logger.info(f"  - {name}: {indicator.name} (period={indicator.period})")
        
        return indicators
    
    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_system(self) -> bool:
        """تهيئة النظام الكامل"""
        try:
            logger.info("🚀 تهيئة نظام التحليل اللحظي للمؤشرات...")
            
            # تهيئة جامع البيانات المباشرة
            await self.realtime_collector.initialize_async_connection()
            await self.realtime_collector.initialize_redis()
            
            if not self.realtime_collector.is_connected:
                logger.error("❌ فشل في تهيئة الاتصال مع المنصة")
                return False
            
            if not self.realtime_collector.redis_client:
                logger.error("❌ فشل في تهيئة اتصال Redis")
                return False
            
            logger.info("✅ تم تهيئة النظام بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة النظام: {str(e)}")
            return False
    
    @handle_async_errors(default_return=[], log_error=True)
    async def get_live_candles_for_analysis(self, asset: str, count: int = None) -> List[Dict[str, Any]]:
        """الحصول على الشموع المباشرة للتحليل"""
        try:
            # تحديد عدد الشموع المطلوبة
            required_count = count or max(indicator.period for indicator in self.indicators.values()) + 10
            
            # جلب البيانات المباشرة من Redis
            live_history = self.realtime_collector.get_live_history_from_redis(asset, required_count)
            
            # إذا لم تكن البيانات كافية، جلب من المنصة مباشرة
            if len(live_history) < self.min_candles_required:
                logger.debug(f"البيانات المباشرة غير كافية لـ {asset} ({len(live_history)}), جلب من المنصة...")

                # التأكد من وجود الاتصال
                if not self.realtime_collector.is_connected:
                    await self.realtime_collector.initialize_async_connection()

                historical_candles = await self.realtime_collector.get_latest_candles_async(asset, 60, required_count)
                
                # دمج البيانات
                if historical_candles:
                    # تحويل البيانات التاريخية لتنسيق موحد
                    formatted_candles = []
                    for candle in historical_candles:
                        formatted_candle = {
                            'asset': asset,
                            'timestamp': candle.get('timestamp', datetime.now().isoformat()),
                            'open': float(candle.get('open', 0)),
                            'high': float(candle.get('high', 0)),
                            'low': float(candle.get('low', 0)),
                            'close': float(candle.get('close', 0)),
                            'volume': float(candle.get('volume', 0)),
                            'time': candle.get('time', int(datetime.now().timestamp()))
                        }
                        formatted_candles.append(formatted_candle)
                    
                    # دمج مع البيانات المباشرة
                    all_candles = formatted_candles + live_history
                    
                    # إزالة التكرارات وترتيب حسب الوقت
                    unique_candles = {}
                    for candle in all_candles:
                        timestamp_key = candle.get('time', candle.get('timestamp'))
                        unique_candles[timestamp_key] = candle
                    
                    # ترتيب حسب الوقت
                    sorted_candles = sorted(unique_candles.values(), 
                                          key=lambda x: x.get('time', 0))
                    
                    return sorted_candles[-required_count:]  # آخر العدد المطلوب
            
            return live_history
            
        except Exception as e:
            logger.error(f"خطأ في جلب الشموع للتحليل لـ {asset}: {str(e)}")
            return []
    
    @handle_errors(default_return={}, log_error=True)
    def calculate_indicators_for_single_candle(self, asset: str, current_candle: Dict[str, Any], historical_candles: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """حساب جميع المؤشرات لشمعة واحدة مع الاستعانة بالبيانات التاريخية"""
        try:
            start_time = time.time()
            results = {}

            # إذا لم تكن هناك بيانات تاريخية، جلبها من قاعدة البيانات
            if not historical_candles:
                historical_candles = self._get_historical_candles_from_db(asset, 100)

            # دمج البيانات التاريخية مع الشمعة الحالية
            all_candles = historical_candles + [current_candle]

            # تحويل البيانات لتنسيق المؤشرات
            indicator_data = []
            for candle in all_candles:
                indicator_candle = {
                    'open': float(candle.get('open', 0)),
                    'high': float(candle.get('high', 0)),
                    'low': float(candle.get('low', 0)),
                    'close': float(candle.get('close', 0)),
                    'volume': float(candle.get('volume', 0))
                }
                indicator_data.append(indicator_candle)

            # حساب كل مؤشر للشمعة الحالية فقط
            for indicator_name, indicator in self.indicators.items():
                try:
                    # التحقق من كفاية البيانات
                    if len(indicator_data) >= indicator.period:
                        # حساب المؤشر لجميع البيانات
                        values = indicator.calculate(indicator_data)

                        if values:
                            # أخذ القيمة الأخيرة فقط (للشمعة الحالية)
                            current_value = values[-1] if values else None

                            # الحصول على الإشارة للشمعة الحالية
                            current_price = indicator_data[-1]['close']
                            signal = indicator.get_signal([current_value], current_price)

                            results[indicator_name] = {
                                'values': current_value,
                                'signal': signal,
                                'timestamp': datetime.now().isoformat(),
                                'period': indicator.period,
                                'asset': asset,
                                'calculation_time_ms': int((time.time() - start_time) * 1000)
                            }

                            self.analysis_stats['indicators_calculated'] += 1
                        else:
                            logger.debug(f"لم يتم حساب {indicator_name} لـ {asset}")
                    else:
                        logger.debug(f"بيانات غير كافية لـ {indicator_name} لـ {asset}: {len(indicator_data)}/{indicator.period}")

                except Exception as e:
                    logger.error(f"خطأ في حساب {indicator_name} لـ {asset}: {str(e)}")
                    continue

            calculation_time = time.time() - start_time

            # تحديث الإحصائيات
            self.analysis_stats['assets_analyzed'].add(asset)

            logger.debug(f"تم حساب {len(results)} مؤشر للشمعة الحالية لـ {asset} في {calculation_time:.3f}s")

            return results

        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات للشمعة الحالية لـ {asset}: {str(e)}")
            return {}

    def _get_historical_candles_from_db(self, asset: str, limit: int = 100) -> List[Dict[str, Any]]:
        """جلب البيانات التاريخية من قاعدة البيانات"""
        try:
            from database.repository import historical_data_repo

            # جلب آخر البيانات من قاعدة البيانات
            candles = historical_data_repo.get_latest_candles(asset, 60, limit)

            if candles:
                # تحويل لتنسيق موحد
                formatted_candles = []
                for candle in candles:
                    formatted_candle = {
                        'open': candle.get('open_price', candle.get('open', 0)),
                        'high': candle.get('high_price', candle.get('high', 0)),
                        'low': candle.get('low_price', candle.get('low', 0)),
                        'close': candle.get('close_price', candle.get('close', 0)),
                        'volume': candle.get('volume', 0),
                        'timestamp': candle.get('timestamp', datetime.now())
                    }
                    formatted_candles.append(formatted_candle)

                return formatted_candles

            return []

        except Exception as e:
            logger.error(f"خطأ في جلب البيانات التاريخية لـ {asset}: {str(e)}")
            return []

    @handle_async_errors(default_return=False, log_error=True)
    async def _store_candle_to_database(self, asset: str, candle: Dict[str, Any]) -> bool:
        """تخزين الشمعة الخام في قاعدة البيانات"""
        try:
            from database.repository import historical_data_repo

            # التحقق من وجود البيانات المطلوبة
            if not all(key in candle for key in ['open', 'high', 'low', 'close']):
                logger.debug(f"بيانات شمعة غير مكتملة لـ {asset}")
                return False

            # إعداد timestamp مع timezone awareness
            from datetime import timezone
            import pytz

            candle_timestamp = candle.get('timestamp', datetime.now())
            if isinstance(candle_timestamp, str):
                try:
                    candle_timestamp = datetime.fromisoformat(candle_timestamp.replace('Z', '+00:00'))
                except:
                    candle_timestamp = datetime.now(timezone.utc)
            elif isinstance(candle_timestamp, datetime):
                # إذا كان datetime بدون timezone، أضف UTC
                if candle_timestamp.tzinfo is None:
                    candle_timestamp = candle_timestamp.replace(tzinfo=timezone.utc)
                else:
                    # تحويل إلى UTC
                    candle_timestamp = candle_timestamp.astimezone(timezone.utc)

            # التحقق من عدم وجود الشمعة مسبقاً
            existing_candle = historical_data_repo.get_latest_timestamp(asset, 60)
            if existing_candle:
                # تأكد من أن التوقيت في قاعدة البيانات له timezone
                if isinstance(existing_candle, datetime) and existing_candle.tzinfo is None:
                    existing_candle = existing_candle.replace(tzinfo=timezone.utc)

                if existing_candle >= candle_timestamp:
                    logger.debug(f"الشمعة موجودة مسبقاً لـ {asset} في {candle_timestamp}")
                    return True

            # إعداد بيانات الشمعة
            candle_data = {
                'asset': asset,
                'timestamp': candle_timestamp,
                'timeframe': 60,
                'open_price': float(candle['open']),
                'high_price': float(candle['high']),
                'low_price': float(candle['low']),
                'close_price': float(candle['close']),
                'volume': float(candle.get('volume', 0))
            }

            # حفظ في قاعدة البيانات
            saved_candle = historical_data_repo.create(**candle_data)

            if saved_candle:
                logger.debug(f"✅ تم حفظ شمعة {asset} في {candle_timestamp}")
                return True
            else:
                logger.warning(f"⚠️ فشل في حفظ شمعة {asset}")
                return False

        except Exception as e:
            logger.error(f"خطأ في حفظ الشمعة لـ {asset}: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def _store_indicators_to_database(self, asset: str, candle: Dict[str, Any], indicators_results: Dict[str, Any]) -> bool:
        """تخزين المؤشرات المحسوبة في قاعدة البيانات"""
        try:
            from database.repository import technical_indicator_repo

            if not indicators_results:
                return False

            # إعداد بيانات المؤشرات للتخزين
            indicators_data = []
            candle_timestamp = candle.get('timestamp', datetime.now())

            for indicator_name, result in indicators_results.items():
                # تحويل numpy types إلى Python types
                indicator_value = result.get('values')
                if hasattr(indicator_value, 'item'):
                    indicator_value = float(indicator_value.item())
                elif hasattr(indicator_value, 'dtype'):
                    indicator_value = float(indicator_value)
                elif indicator_value is not None:
                    indicator_value = float(indicator_value)
                else:
                    indicator_value = 0.0

                indicator_data = {
                    'asset': asset,
                    'timestamp': candle_timestamp,
                    'timeframe': 60,  # إطار زمني دقيقة واحدة
                    'indicator_name': indicator_name,
                    'indicator_value': indicator_value,
                    'indicator_signal': result.get('signal', 'NEUTRAL'),
                    'parameters': {
                        'period': result.get('period'),
                        'calculation_time_ms': result.get('calculation_time_ms', 0)
                    },
                    'confidence_score': self._calculate_confidence_score(result),
                    'calculation_time_ms': result.get('calculation_time_ms', 0)
                }
                indicators_data.append(indicator_data)

            # تخزين في قاعدة البيانات
            success = technical_indicator_repo.store_indicators_batch(indicators_data)

            if success:
                logger.debug(f"تم تخزين {len(indicators_data)} مؤشر لـ {asset} في قاعدة البيانات")

            return success

        except Exception as e:
            logger.error(f"خطأ في تخزين المؤشرات لـ {asset}: {str(e)}")
            return False

    def _calculate_confidence_score(self, indicator_result: Dict[str, Any]) -> float:
        """حساب درجة الثقة للمؤشر"""
        try:
            # درجة ثقة أساسية حسب الإشارة
            signal = indicator_result.get('signal', 'NEUTRAL')

            if signal in ['BULLISH', 'BEARISH']:
                base_confidence = 0.8
            elif signal in ['STRONG_BULLISH', 'STRONG_BEARISH']:
                base_confidence = 0.9
            elif signal in ['WEAK_BULLISH', 'WEAK_BEARISH']:
                base_confidence = 0.6
            else:
                base_confidence = 0.5

            # تعديل حسب وقت الحساب (أسرع = أفضل)
            calc_time = indicator_result.get('calculation_time_ms', 100)
            if calc_time < 50:
                time_factor = 1.0
            elif calc_time < 100:
                time_factor = 0.95
            else:
                time_factor = 0.9

            return round(base_confidence * time_factor, 2)

        except Exception:
            return 0.5
    
    @handle_async_errors(default_return={}, log_error=True)
    async def analyze_asset_realtime(self, asset: str) -> Dict[str, Any]:
        """تحليل أصل واحد بشكل لحظي"""
        try:
            # جلب البيانات المباشرة
            candles = await self.get_live_candles_for_analysis(asset)
            
            if not candles:
                logger.warning(f"لا توجد بيانات لـ {asset}")
                return {}
            
            # حساب المؤشرات للشمعة الحالية
            if candles:
                current_candle = candles[-1]
                historical_candles = candles[:-1] if len(candles) > 1 else []
                indicators_results = self.calculate_indicators_for_single_candle(asset, current_candle, historical_candles)
            else:
                indicators_results = {}
            
            if not indicators_results:
                logger.warning(f"فشل في حساب المؤشرات لـ {asset}")
                return {}
            
            # تحديث النتائج المحفوظة
            with self.analysis_lock:
                self.latest_indicator_values[asset] = {}
                self.indicator_signals[asset] = {}
                
                for indicator_name, result in indicators_results.items():
                    self.latest_indicator_values[asset][indicator_name] = result['values']
                    self.indicator_signals[asset][indicator_name] = result['signal']
            
            # تخزين في Redis للوصول السريع
            if self.realtime_collector.redis_client:
                redis_key = f"indicators:{asset}"
                redis_data = {
                    'timestamp': datetime.now().isoformat(),
                    'indicators': indicators_results,
                    'asset': asset,
                    'candles_count': len(candles)
                }

                self.realtime_collector.redis_client.setex(
                    redis_key,
                    300,  # 5 دقائق
                    json.dumps(redis_data, default=str)
                )

            # تخزين الشمعة الخام في قاعدة البيانات أولاً
            await self._store_candle_to_database(asset, current_candle)

            # تخزين المؤشرات في قاعدة البيانات
            await self._store_indicators_to_database(asset, current_candle, indicators_results)
            
            # إنشاء تقرير التحليل
            analysis_result = {
                'asset': asset,
                'timestamp': datetime.now().isoformat(),
                'indicators_count': len(indicators_results),
                'candles_analyzed': len(candles),
                'indicators': indicators_results,
                'current_price': candles[-1]['close'] if candles else None,
                'analysis_quality': self._assess_analysis_quality(indicators_results)
            }
            
            # حفظ في التاريخ
            if asset not in self.analysis_history:
                self.analysis_history[asset] = []
            
            self.analysis_history[asset].append(analysis_result)
            
            # الاحتفاظ بآخر 100 تحليل فقط
            if len(self.analysis_history[asset]) > 100:
                self.analysis_history[asset] = self.analysis_history[asset][-100:]
            
            self.analysis_stats['successful_analyses'] += 1
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل {asset}: {str(e)}")
            self.analysis_stats['failed_analyses'] += 1
            return {}
    
    def _assess_analysis_quality(self, indicators_results: Dict[str, Any]) -> str:
        """تقييم جودة التحليل"""
        try:
            total_indicators = len(self.indicators)
            calculated_indicators = len(indicators_results)
            
            quality_ratio = calculated_indicators / total_indicators
            
            if quality_ratio >= 0.9:
                return "EXCELLENT"
            elif quality_ratio >= 0.7:
                return "GOOD"
            elif quality_ratio >= 0.5:
                return "FAIR"
            else:
                return "POOR"
                
        except Exception:
            return "UNKNOWN"
    
    @handle_async_errors(default_return={}, log_error=True)
    async def analyze_multiple_assets_realtime(self, assets: List[str]) -> Dict[str, Dict[str, Any]]:
        """تحليل عدة أصول بشكل متوازي"""
        try:
            start_time = time.time()
            
            # إنشاء مهام التحليل المتوازي
            tasks = []
            for asset in assets:
                task = self.analyze_asset_realtime(asset)
                tasks.append((asset, task))
            
            # تنفيذ المهام
            results = {}
            for asset, task in tasks:
                try:
                    result = await task
                    results[asset] = result
                except Exception as e:
                    logger.error(f"خطأ في تحليل {asset}: {str(e)}")
                    results[asset] = {}
            
            analysis_duration = time.time() - start_time
            
            # تحديث الإحصائيات
            self.analysis_stats['total_analyses'] += 1
            self.analysis_stats['last_analysis_time'] = datetime.now().isoformat()
            
            # تحديث متوسط مدة التحليل
            if self.analysis_stats['analysis_duration_avg'] == 0:
                self.analysis_stats['analysis_duration_avg'] = analysis_duration
            else:
                self.analysis_stats['analysis_duration_avg'] = (
                    self.analysis_stats['analysis_duration_avg'] + analysis_duration
                ) / 2
            
            successful_analyses = sum(1 for result in results.values() if result)
            
            logger.info(f"تم تحليل {successful_analyses}/{len(assets)} أصل في {analysis_duration:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتعدد: {str(e)}")
            return {}

    @handle_async_errors(default_return=False, log_error=True)
    async def start_continuous_analysis(self, assets: List[str] = None) -> bool:
        """بدء التحليل المستمر للمؤشرات"""
        try:
            if self.is_analyzing:
                logger.warning("التحليل المستمر يعمل بالفعل")
                return False

            # استخدام جميع الأزواج إذا لم يتم تحديد أصول
            target_assets = assets or CURRENCY_PAIRS_70

            logger.info(f"🚀 بدء التحليل المستمر للمؤشرات لـ {len(target_assets)} أصل")

            # بدء البث المباشر للبيانات
            streaming_results = await self.realtime_collector.start_streaming_async(target_assets)
            successful_streams = sum(1 for success in streaming_results.values() if success)

            logger.info(f"نجح البث المباشر لـ {successful_streams}/{len(target_assets)} أصل")

            if successful_streams == 0:
                logger.error("❌ فشل في بدء البث المباشر لأي أصل")
                return False

            self.is_analyzing = True

            # بدء حلقة التحليل المستمر
            asyncio.create_task(self._continuous_analysis_loop(target_assets))

            logger.info("✅ تم بدء التحليل المستمر للمؤشرات")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في بدء التحليل المستمر: {str(e)}")
            return False

    async def _continuous_analysis_loop(self, assets: List[str]):
        """حلقة التحليل المستمر مع تحديث المؤشرات اللحظي"""
        try:
            logger.info(f"بدء حلقة التحليل المستمر لـ {len(assets)} أصل")

            while self.is_analyzing:
                try:
                    # تحليل جميع الأصول مع التحديث اللحظي
                    analysis_results = await self.analyze_multiple_assets_realtime(assets)

                    # إحصائيات سريعة
                    successful_analyses = sum(1 for result in analysis_results.values() if result)

                    if successful_analyses > 0:
                        logger.debug(f"تم تحليل {successful_analyses}/{len(assets)} أصل بنجاح")

                        # تحديث المؤشرات في الذاكرة للوصول السريع
                        await self._update_realtime_indicators_cache(analysis_results)

                    # انتظار قبل التحليل التالي
                    await asyncio.sleep(self.analysis_interval)

                except Exception as e:
                    logger.error(f"خطأ في حلقة التحليل: {str(e)}")
                    await asyncio.sleep(5)  # انتظار أطول عند الخطأ

            logger.info("تم إيقاف حلقة التحليل المستمر")

        except Exception as e:
            logger.error(f"خطأ في حلقة التحليل المستمر: {str(e)}")

    @handle_async_errors(default_return=False, log_error=True)
    async def _update_realtime_indicators_cache(self, analysis_results: Dict[str, Dict[str, Any]]) -> bool:
        """تحديث كاش المؤشرات اللحظي في Redis"""
        try:
            if not self.realtime_collector.redis_client:
                return False

            updated_assets = 0

            for asset, result in analysis_results.items():
                if not result or 'indicators' not in result:
                    continue

                # إعداد بيانات الكاش
                cache_data = {
                    'asset': asset,
                    'timestamp': result.get('timestamp', datetime.now().isoformat()),
                    'current_price': result.get('current_price'),
                    'indicators_count': result.get('indicators_count', 0),
                    'analysis_quality': result.get('analysis_quality', 'UNKNOWN'),
                    'indicators': {}
                }

                # تنظيم بيانات المؤشرات
                for indicator_name, indicator_data in result.get('indicators', {}).items():
                    cache_data['indicators'][indicator_name] = {
                        'value': indicator_data.get('values'),
                        'signal': indicator_data.get('signal', 'NEUTRAL'),
                        'timestamp': indicator_data.get('timestamp'),
                        'period': indicator_data.get('period')
                    }

                # تخزين في Redis مع انتهاء صلاحية قصير للبيانات المباشرة
                cache_key = f"realtime_indicators:{asset}"
                self.realtime_collector.redis_client.setex(
                    cache_key,
                    60,  # دقيقة واحدة للبيانات المباشرة
                    json.dumps(cache_data, default=str)
                )

                # تحديث قائمة الأصول النشطة
                active_assets_key = "active_realtime_assets"
                self.realtime_collector.redis_client.sadd(active_assets_key, asset)
                self.realtime_collector.redis_client.expire(active_assets_key, 300)  # 5 دقائق

                updated_assets += 1

            logger.debug(f"تم تحديث كاش المؤشرات لـ {updated_assets} أصل")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث كاش المؤشرات: {str(e)}")
            return False

    async def stop_continuous_analysis(self):
        """إيقاف التحليل المستمر"""
        try:
            logger.info("إيقاف التحليل المستمر...")

            self.is_analyzing = False

            # إيقاف البث المباشر
            await self.realtime_collector.stop_streaming()

            logger.info("✅ تم إيقاف التحليل المستمر")

        except Exception as e:
            logger.error(f"خطأ في إيقاف التحليل: {str(e)}")

    @handle_errors(default_return={}, log_error=True)
    def get_latest_indicators(self, asset: str) -> Dict[str, Any]:
        """الحصول على آخر المؤشرات المحسوبة لأصل معين"""
        try:
            # جلب من الكاش أولاً
            if self.realtime_collector.redis_client:
                cache_key = f"realtime_indicators:{asset}"
                cached_data = self.realtime_collector.redis_client.get(cache_key)

                if cached_data:
                    return json.loads(cached_data)

            # جلب من الذاكرة المحلية
            if asset in self.latest_indicator_values:
                return {
                    'asset': asset,
                    'indicators': self.latest_indicator_values[asset],
                    'signals': self.indicator_signals.get(asset, {}),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'local_cache'
                }

            return {}

        except Exception as e:
            logger.error(f"خطأ في جلب المؤشرات لـ {asset}: {str(e)}")
            return {}

    @handle_errors(default_return={}, log_error=True)
    def get_all_active_indicators(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على جميع المؤشرات النشطة"""
        try:
            all_indicators = {}

            if self.realtime_collector.redis_client:
                # جلب قائمة الأصول النشطة
                active_assets = self.realtime_collector.redis_client.smembers("active_realtime_assets")

                for asset in active_assets:
                    if isinstance(asset, bytes):
                        asset = asset.decode('utf-8')

                    indicators_data = self.get_latest_indicators(asset)
                    if indicators_data:
                        all_indicators[asset] = indicators_data

            return all_indicators

        except Exception as e:
            logger.error(f"خطأ في جلب جميع المؤشرات النشطة: {str(e)}")
            return {}

    @handle_errors(default_return={}, log_error=True)
    def get_indicator_signals_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص إشارات المؤشرات"""
        try:
            all_indicators = self.get_all_active_indicators()

            signals_summary = {
                'total_assets': len(all_indicators),
                'bullish_signals': 0,
                'bearish_signals': 0,
                'neutral_signals': 0,
                'strong_signals': 0,
                'assets_by_signal': {
                    'BULLISH': [],
                    'BEARISH': [],
                    'NEUTRAL': [],
                    'STRONG_BULLISH': [],
                    'STRONG_BEARISH': []
                },
                'timestamp': datetime.now().isoformat()
            }

            for asset, data in all_indicators.items():
                indicators = data.get('indicators', {})

                # تحليل الإشارات
                asset_signals = []
                for indicator_name, indicator_data in indicators.items():
                    signal = indicator_data.get('signal', 'NEUTRAL')
                    asset_signals.append(signal)

                    # تصنيف الإشارات
                    if 'BULLISH' in signal:
                        if 'STRONG' in signal:
                            signals_summary['strong_signals'] += 1
                            signals_summary['assets_by_signal']['STRONG_BULLISH'].append(asset)
                        else:
                            signals_summary['bullish_signals'] += 1
                            signals_summary['assets_by_signal']['BULLISH'].append(asset)
                    elif 'BEARISH' in signal:
                        if 'STRONG' in signal:
                            signals_summary['strong_signals'] += 1
                            signals_summary['assets_by_signal']['STRONG_BEARISH'].append(asset)
                        else:
                            signals_summary['bearish_signals'] += 1
                            signals_summary['assets_by_signal']['BEARISH'].append(asset)
                    else:
                        signals_summary['neutral_signals'] += 1
                        signals_summary['assets_by_signal']['NEUTRAL'].append(asset)

            return signals_summary

        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص الإشارات: {str(e)}")
            return {}

    def get_analysis_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحليل"""
        try:
            current_time = datetime.now()

            # حساب معدل النجاح
            total_analyses = self.analysis_stats['total_analyses']
            success_rate = 0
            if total_analyses > 0:
                success_rate = (self.analysis_stats['successful_analyses'] / total_analyses) * 100

            # حساب متوسط المؤشرات المحسوبة لكل أصل
            avg_indicators_per_asset = 0
            if len(self.analysis_stats['assets_analyzed']) > 0:
                avg_indicators_per_asset = (
                    self.analysis_stats['indicators_calculated'] /
                    len(self.analysis_stats['assets_analyzed'])
                )

            return {
                'system_status': {
                    'is_analyzing': self.is_analyzing,
                    'analysis_interval': self.analysis_interval,
                    'min_candles_required': self.min_candles_required
                },
                'performance_stats': {
                    'total_analyses': total_analyses,
                    'successful_analyses': self.analysis_stats['successful_analyses'],
                    'failed_analyses': self.analysis_stats['failed_analyses'],
                    'success_rate_percent': round(success_rate, 2),
                    'analysis_duration_avg_seconds': round(self.analysis_stats['analysis_duration_avg'], 3)
                },
                'indicators_stats': {
                    'total_indicators_available': len(self.indicators),
                    'total_indicators_calculated': self.analysis_stats['indicators_calculated'],
                    'avg_indicators_per_asset': round(avg_indicators_per_asset, 1),
                    'assets_analyzed_count': len(self.analysis_stats['assets_analyzed'])
                },
                'timing_info': {
                    'last_analysis_time': self.analysis_stats['last_analysis_time'],
                    'current_time': current_time.isoformat(),
                    'system_uptime_minutes': self._calculate_uptime_minutes()
                }
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التحليل: {str(e)}")
            return {}

    def _calculate_uptime_minutes(self) -> float:
        """حساب وقت تشغيل النظام بالدقائق"""
        try:
            if hasattr(self, 'system_start_time'):
                uptime = datetime.now() - self.system_start_time
                return round(uptime.total_seconds() / 60, 2)
            return 0.0
        except Exception:
            return 0.0

    def get_system_health_report(self) -> Dict[str, Any]:
        """تقرير صحة النظام"""
        try:
            stats = self.get_analysis_statistics()
            signals_summary = self.get_indicator_signals_summary()

            # تقييم صحة النظام
            health_score = 100
            health_issues = []

            # فحص معدل النجاح
            success_rate = stats.get('performance_stats', {}).get('success_rate_percent', 0)
            if success_rate < 80:
                health_score -= 20
                health_issues.append(f"معدل نجاح منخفض: {success_rate}%")

            # فحص متوسط وقت التحليل
            avg_duration = stats.get('performance_stats', {}).get('analysis_duration_avg_seconds', 0)
            if avg_duration > 5:
                health_score -= 15
                health_issues.append(f"وقت تحليل طويل: {avg_duration}s")

            # فحص عدد الأصول النشطة
            active_assets = signals_summary.get('total_assets', 0)
            if active_assets < len(CURRENCY_PAIRS_70) * 0.8:  # أقل من 80% من الأصول
                health_score -= 25
                health_issues.append(f"أصول نشطة قليلة: {active_assets}/{len(CURRENCY_PAIRS_70)}")

            # تحديد حالة الصحة
            if health_score >= 90:
                health_status = "EXCELLENT"
            elif health_score >= 75:
                health_status = "GOOD"
            elif health_score >= 60:
                health_status = "FAIR"
            else:
                health_status = "POOR"

            return {
                'health_status': health_status,
                'health_score': max(0, health_score),
                'health_issues': health_issues,
                'system_stats': stats,
                'signals_summary': signals_summary,
                'recommendations': self._generate_health_recommendations(health_score, health_issues),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير صحة النظام: {str(e)}")
            return {'health_status': 'UNKNOWN', 'error': str(e)}

    def _generate_health_recommendations(self, health_score: int, health_issues: List[str]) -> List[str]:
        """إنشاء توصيات لتحسين صحة النظام"""
        recommendations = []

        if health_score < 90:
            if "معدل نجاح منخفض" in str(health_issues):
                recommendations.append("فحص اتصال المنصة وجودة البيانات")
                recommendations.append("زيادة فترة انتظار البيانات")

            if "وقت تحليل طويل" in str(health_issues):
                recommendations.append("تحسين خوارزميات المؤشرات")
                recommendations.append("زيادة موارد النظام")

            if "أصول نشطة قليلة" in str(health_issues):
                recommendations.append("فحص اتصالات WebSocket")
                recommendations.append("إعادة تشغيل البث المباشر")

        if not recommendations:
            recommendations.append("النظام يعمل بكفاءة عالية")

        return recommendations

# إنشاء مثيل عام للمحلل
realtime_indicator_analyzer = RealTimeIndicatorAnalyzer()

# دوال مساعدة للوصول السريع
async def start_realtime_indicators_analysis(assets: List[str] = None) -> bool:
    """بدء تحليل المؤشرات المباشر"""
    return await realtime_indicator_analyzer.start_continuous_analysis(assets)

async def stop_realtime_indicators_analysis():
    """إيقاف تحليل المؤشرات المباشر"""
    await realtime_indicator_analyzer.stop_continuous_analysis()

def get_asset_indicators(asset: str) -> Dict[str, Any]:
    """الحصول على مؤشرات أصل معين"""
    return realtime_indicator_analyzer.get_latest_indicators(asset)

def get_all_indicators() -> Dict[str, Dict[str, Any]]:
    """الحصول على جميع المؤشرات النشطة"""
    return realtime_indicator_analyzer.get_all_active_indicators()

def get_indicators_signals_summary() -> Dict[str, Any]:
    """الحصول على ملخص إشارات المؤشرات"""
    return realtime_indicator_analyzer.get_indicator_signals_summary()

def get_system_health() -> Dict[str, Any]:
    """الحصول على تقرير صحة النظام"""
    return realtime_indicator_analyzer.get_system_health_report()

def get_analysis_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات التحليل"""
    return realtime_indicator_analyzer.get_analysis_statistics()
