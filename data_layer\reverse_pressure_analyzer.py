"""
نظام تحليل الضغط العكسي - المرحلة الرابعة
Reverse Pressure Analysis System for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("reverse_pressure_analyzer")

class PressureType(Enum):
    """نوع الضغط"""
    STRONG_BULLISH_REJECTION = "STRONG_BULLISH_REJECTION"    # رفض صاعد قوي
    MODERATE_BULLISH_REJECTION = "MODERATE_BULLISH_REJECTION" # رفض صاعد معتدل
    WEAK_BULLISH_REJECTION = "WEAK_BULLISH_REJECTION"        # رفض صاعد ضعيف
    NEUTRAL = "NEUTRAL"                                      # محايد
    WEAK_BEARISH_REJECTION = "WEAK_BEARISH_REJECTION"        # رفض هابط ضعيف
    MODERATE_BEARISH_REJECTION = "MODERATE_BEARISH_REJECTION" # رفض هابط معتدل
    STRONG_BEARISH_REJECTION = "STRONG_BEARISH_REJECTION"    # رفض هابط قوي

class ShadowType(Enum):
    """نوع الظل"""
    LONG_UPPER_SHADOW = "LONG_UPPER_SHADOW"      # ظل علوي طويل
    LONG_LOWER_SHADOW = "LONG_LOWER_SHADOW"      # ظل سفلي طويل
    DOUBLE_SHADOWS = "DOUBLE_SHADOWS"            # ظلال مزدوجة
    NO_SIGNIFICANT_SHADOWS = "NO_SIGNIFICANT_SHADOWS" # لا توجد ظلال مهمة

class RejectionStrength(Enum):
    """قوة الرفض"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class PressureSignal:
    """إشارة الضغط العكسي"""
    pressure_type: PressureType
    shadow_type: ShadowType
    rejection_strength: RejectionStrength
    confidence: float  # 0-100
    
    # قياسات الضغط
    upper_shadow_ratio: float    # نسبة الظل العلوي
    lower_shadow_ratio: float    # نسبة الظل السفلي
    body_ratio: float           # نسبة الجسم
    rejection_level: float      # مستوى الرفض
    
    # معلومات الشمعة
    candle_index: int
    price_level: float
    volume_confirmation: bool
    
    # تحليل الضغط
    buying_rejection: float     # رفض الشراء
    selling_rejection: float    # رفض البيع
    pressure_intensity: float   # كثافة الضغط
    
    # وصف الإشارة
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class ReversePressureAnalysis:
    """تحليل الضغط العكسي"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # إشارات الضغط
    pressure_signals: List[PressureSignal]
    
    # التقييم الإجمالي
    overall_pressure: PressureType
    dominant_shadow_type: ShadowType
    overall_rejection_strength: RejectionStrength
    overall_confidence: float
    
    # إحصائيات
    total_rejections: int
    bullish_rejections: int
    bearish_rejections: int
    strong_rejections: int
    
    # توصيات
    is_actionable: bool
    recommended_action: str
    support_resistance_levels: List[float]
    recommendations: List[str]

class ReversePressureAnalyzer:
    """محلل الضغط العكسي"""
    
    def __init__(self):
        self.shadow_thresholds = {
            'long_shadow_min': 0.4,      # الحد الأدنى للظل الطويل
            'body_max_for_rejection': 0.3, # الحد الأقصى للجسم في الرفض
            'rejection_strength_min': 0.5   # الحد الأدنى لقوة الرفض
        }
        self.min_confidence_threshold = 65.0
        self.pressure_history = {}  # تاريخ الضغط لكل أصل
        
        logger.info("تم تهيئة محلل الضغط العكسي")

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_pressure_patterns(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[ReversePressureAnalysis]:
        """تحليل أنماط الضغط"""
        return await self.analyze_reverse_pressure(asset, candles_data)

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_reverse_pressure(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[ReversePressureAnalysis]:
        """تحليل الضغط العكسي"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 5:
                logger.warning(f"بيانات غير كافية لتحليل الضغط العكسي لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل الضغط العكسي لـ {asset} مع {len(candles_data)} شمعة")
            
            pressure_signals = []
            
            # تحليل كل شمعة للبحث عن إشارات الضغط العكسي
            for i in range(len(candles_data)):
                signal = await self._analyze_candle_pressure(candles_data, i)
                if signal:
                    pressure_signals.append(signal)
            
            # التقييم الإجمالي
            overall_assessment = self._evaluate_overall_pressure(pressure_signals)
            
            # تحديد مستويات الدعم والمقاومة
            support_resistance = self._identify_support_resistance_levels(pressure_signals, candles_data)
            
            # حساب الإحصائيات
            stats = self._calculate_pressure_statistics(pressure_signals)
            
            # إنتاج التوصيات
            recommendations = self._generate_pressure_recommendations(pressure_signals, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = ReversePressureAnalysis(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                pressure_signals=pressure_signals,
                overall_pressure=overall_assessment['pressure'],
                dominant_shadow_type=overall_assessment['shadow_type'],
                overall_rejection_strength=overall_assessment['strength'],
                overall_confidence=overall_assessment['confidence'],
                total_rejections=stats['total'],
                bullish_rejections=stats['bullish'],
                bearish_rejections=stats['bearish'],
                strong_rejections=stats['strong'],
                is_actionable=overall_assessment['is_actionable'],
                recommended_action=overall_assessment['action'],
                support_resistance_levels=support_resistance,
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.pressure_history:
                self.pressure_history[asset] = []
            
            self.pressure_history[asset].append(result)
            
            # الاحتفاظ بآخر 20 تحليل فقط
            if len(self.pressure_history[asset]) > 20:
                self.pressure_history[asset] = self.pressure_history[asset][-20:]
            
            logger.info(f"تم إكمال تحليل الضغط العكسي لـ {asset}: {len(pressure_signals)} إشارة، النوع: {overall_assessment['pressure'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الضغط العكسي لـ {asset}: {str(e)}")
            return None

    async def _analyze_candle_pressure(self, candles: List[Dict[str, Any]], index: int) -> Optional[PressureSignal]:
        """تحليل ضغط شمعة واحدة"""
        try:
            candle = candles[index]
            
            open_price = candle['open']
            high_price = candle['high']
            low_price = candle['low']
            close_price = candle['close']
            
            # حساب خصائص الشمعة
            total_range = high_price - low_price
            if total_range == 0:
                return None
            
            body_size = abs(close_price - open_price)
            upper_shadow = high_price - max(open_price, close_price)
            lower_shadow = min(open_price, close_price) - low_price
            
            upper_shadow_ratio = upper_shadow / total_range
            lower_shadow_ratio = lower_shadow / total_range
            body_ratio = body_size / total_range
            
            # تحديد نوع الظل
            shadow_type = self._determine_shadow_type(upper_shadow_ratio, lower_shadow_ratio)
            
            # تحديد نوع الضغط
            pressure_type = self._determine_pressure_type(upper_shadow_ratio, lower_shadow_ratio, body_ratio, open_price, close_price)
            
            # إذا لم يكن هناك ضغط عكسي واضح، تجاهل الشمعة
            if pressure_type == PressureType.NEUTRAL:
                return None
            
            # حساب قوة الرفض
            rejection_strength = self._calculate_rejection_strength(upper_shadow_ratio, lower_shadow_ratio, body_ratio)
            
            # حساب مستوى الرفض
            if pressure_type in [PressureType.STRONG_BULLISH_REJECTION, PressureType.MODERATE_BULLISH_REJECTION, PressureType.WEAK_BULLISH_REJECTION]:
                rejection_level = low_price  # مستوى الرفض السفلي
                buying_rejection = 0
                selling_rejection = lower_shadow_ratio
            else:
                rejection_level = high_price  # مستوى الرفض العلوي
                buying_rejection = upper_shadow_ratio
                selling_rejection = 0
            
            # حساب كثافة الضغط
            pressure_intensity = max(upper_shadow_ratio, lower_shadow_ratio) + (1 - body_ratio)
            
            # حساب الثقة
            confidence = self._calculate_pressure_confidence(upper_shadow_ratio, lower_shadow_ratio, body_ratio, rejection_strength)
            
            # فحص تأكيد الحجم
            volume_confirmation = self._check_volume_confirmation(candles, index)
            
            return PressureSignal(
                pressure_type=pressure_type,
                shadow_type=shadow_type,
                rejection_strength=rejection_strength,
                confidence=confidence,
                upper_shadow_ratio=upper_shadow_ratio,
                lower_shadow_ratio=lower_shadow_ratio,
                body_ratio=body_ratio,
                rejection_level=rejection_level,
                candle_index=index,
                price_level=close_price,
                volume_confirmation=volume_confirmation,
                buying_rejection=buying_rejection,
                selling_rejection=selling_rejection,
                pressure_intensity=pressure_intensity,
                description=f"ضغط عكسي: {pressure_type.value}",
                reasoning=f"ظل علوي {upper_shadow_ratio:.2f}, ظل سفلي {lower_shadow_ratio:.2f}, جسم {body_ratio:.2f}",
                supporting_data={
                    'candle_data': candle,
                    'total_range': total_range,
                    'body_size': body_size
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل ضغط الشمعة {index}: {str(e)}")
            return None

    def _determine_shadow_type(self, upper_shadow_ratio: float, lower_shadow_ratio: float) -> ShadowType:
        """تحديد نوع الظل"""
        try:
            long_threshold = self.shadow_thresholds['long_shadow_min']
            
            upper_is_long = upper_shadow_ratio >= long_threshold
            lower_is_long = lower_shadow_ratio >= long_threshold
            
            if upper_is_long and lower_is_long:
                return ShadowType.DOUBLE_SHADOWS
            elif upper_is_long:
                return ShadowType.LONG_UPPER_SHADOW
            elif lower_is_long:
                return ShadowType.LONG_LOWER_SHADOW
            else:
                return ShadowType.NO_SIGNIFICANT_SHADOWS
                
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع الظل: {str(e)}")
            return ShadowType.NO_SIGNIFICANT_SHADOWS

    def _determine_pressure_type(self, upper_shadow_ratio: float, lower_shadow_ratio: float, body_ratio: float, open_price: float, close_price: float) -> PressureType:
        """تحديد نوع الضغط العكسي"""
        try:
            # شروط الرفض الصاعد (ظل سفلي طويل)
            if lower_shadow_ratio >= 0.5 and body_ratio <= 0.3:
                if lower_shadow_ratio >= 0.7:
                    return PressureType.STRONG_BULLISH_REJECTION
                elif lower_shadow_ratio >= 0.6:
                    return PressureType.MODERATE_BULLISH_REJECTION
                else:
                    return PressureType.WEAK_BULLISH_REJECTION
            
            # شروط الرفض الهابط (ظل علوي طويل)
            elif upper_shadow_ratio >= 0.5 and body_ratio <= 0.3:
                if upper_shadow_ratio >= 0.7:
                    return PressureType.STRONG_BEARISH_REJECTION
                elif upper_shadow_ratio >= 0.6:
                    return PressureType.MODERATE_BEARISH_REJECTION
                else:
                    return PressureType.WEAK_BEARISH_REJECTION
            
            else:
                return PressureType.NEUTRAL
                
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع الضغط: {str(e)}")
            return PressureType.NEUTRAL

    def _calculate_rejection_strength(self, upper_shadow_ratio: float, lower_shadow_ratio: float, body_ratio: float) -> RejectionStrength:
        """حساب قوة الرفض"""
        try:
            # حساب النقاط المركبة
            shadow_score = max(upper_shadow_ratio, lower_shadow_ratio) * 5
            body_score = (1 - body_ratio) * 3  # كلما قل الجسم، زادت القوة
            
            total_score = shadow_score + body_score
            
            if total_score >= 7:
                return RejectionStrength.VERY_STRONG
            elif total_score >= 5.5:
                return RejectionStrength.STRONG
            elif total_score >= 4:
                return RejectionStrength.MODERATE
            elif total_score >= 2.5:
                return RejectionStrength.WEAK
            else:
                return RejectionStrength.VERY_WEAK
                
        except Exception as e:
            logger.error(f"خطأ في حساب قوة الرفض: {str(e)}")
            return RejectionStrength.WEAK

    def _calculate_pressure_confidence(self, upper_shadow_ratio: float, lower_shadow_ratio: float, body_ratio: float, rejection_strength: RejectionStrength) -> float:
        """حساب ثقة الضغط العكسي"""
        try:
            # النقاط الأساسية من طول الظل
            shadow_confidence = max(upper_shadow_ratio, lower_shadow_ratio) * 60
            
            # مكافأة للجسم الصغير
            body_bonus = (1 - body_ratio) * 25
            
            # مكافأة لقوة الرفض
            strength_bonus = rejection_strength.value * 5
            
            total_confidence = shadow_confidence + body_bonus + strength_bonus
            
            return min(95, max(30, total_confidence))
            
        except Exception as e:
            logger.error(f"خطأ في حساب ثقة الضغط: {str(e)}")
            return 50

    def _check_volume_confirmation(self, candles: List[Dict[str, Any]], index: int) -> bool:
        """فحص تأكيد الحجم للضغط العكسي"""
        try:
            if index < 2 or index >= len(candles):
                return False
            
            current_volume = candles[index].get('volume', 0)
            if current_volume == 0:
                return False
            
            # مقارنة مع متوسط الحجم للشموع السابقة
            prev_volumes = [candles[i].get('volume', 0) for i in range(max(0, index-3), index)]
            prev_volumes = [v for v in prev_volumes if v > 0]
            
            if not prev_volumes:
                return False
            
            avg_prev_volume = statistics.mean(prev_volumes)
            
            # تأكيد إذا كان الحجم أعلى من المتوسط بـ 15%
            return current_volume > avg_prev_volume * 1.15
            
        except Exception as e:
            logger.error(f"خطأ في فحص تأكيد الحجم: {str(e)}")
            return False

    def _evaluate_overall_pressure(self, pressure_signals: List[PressureSignal]) -> Dict[str, Any]:
        """تقييم الضغط العكسي الإجمالي"""
        try:
            if not pressure_signals:
                return {
                    'pressure': PressureType.NEUTRAL,
                    'shadow_type': ShadowType.NO_SIGNIFICANT_SHADOWS,
                    'strength': RejectionStrength.VERY_WEAK,
                    'confidence': 0,
                    'is_actionable': False,
                    'action': 'WAIT'
                }

            # تحديد النوع السائد
            pressure_counts = {}
            for signal in pressure_signals:
                pressure_type = signal.pressure_type
                pressure_counts[pressure_type] = pressure_counts.get(pressure_type, 0) + 1

            # النوع الأكثر تكراراً
            dominant_pressure = max(pressure_counts.keys(), key=lambda x: pressure_counts[x])

            # تحديد نوع الظل السائد
            shadow_counts = {}
            for signal in pressure_signals:
                shadow_type = signal.shadow_type
                shadow_counts[shadow_type] = shadow_counts.get(shadow_type, 0) + 1

            dominant_shadow = max(shadow_counts.keys(), key=lambda x: shadow_counts[x])

            # حساب القوة الإجمالية
            strength_values = [signal.rejection_strength.value for signal in pressure_signals]
            avg_strength = statistics.mean(strength_values)

            if avg_strength >= 4.5:
                overall_strength = RejectionStrength.VERY_STRONG
            elif avg_strength >= 3.5:
                overall_strength = RejectionStrength.STRONG
            elif avg_strength >= 2.5:
                overall_strength = RejectionStrength.MODERATE
            elif avg_strength >= 1.5:
                overall_strength = RejectionStrength.WEAK
            else:
                overall_strength = RejectionStrength.VERY_WEAK

            # حساب الثقة الإجمالية
            confidence_values = [signal.confidence for signal in pressure_signals]
            overall_confidence = statistics.mean(confidence_values)

            # تحديد قابلية التنفيذ
            strong_signals = [s for s in pressure_signals if s.rejection_strength.value >= 4]
            is_actionable = (
                overall_confidence >= self.min_confidence_threshold and
                len(strong_signals) >= 1 and
                dominant_pressure != PressureType.NEUTRAL
            )

            # تحديد الإجراء المطلوب
            if is_actionable:
                if dominant_pressure in [PressureType.STRONG_BULLISH_REJECTION, PressureType.MODERATE_BULLISH_REJECTION]:
                    action = 'BUY'
                elif dominant_pressure in [PressureType.STRONG_BEARISH_REJECTION, PressureType.MODERATE_BEARISH_REJECTION]:
                    action = 'SELL'
                else:
                    action = 'WAIT'
            else:
                action = 'WAIT'

            return {
                'pressure': dominant_pressure,
                'shadow_type': dominant_shadow,
                'strength': overall_strength,
                'confidence': round(overall_confidence, 2),
                'is_actionable': is_actionable,
                'action': action
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم الضغط الإجمالي: {str(e)}")
            return {
                'pressure': PressureType.NEUTRAL,
                'shadow_type': ShadowType.NO_SIGNIFICANT_SHADOWS,
                'strength': RejectionStrength.VERY_WEAK,
                'confidence': 0,
                'is_actionable': False,
                'action': 'WAIT'
            }

    def _identify_support_resistance_levels(self, pressure_signals: List[PressureSignal], candles: List[Dict[str, Any]]) -> List[float]:
        """تحديد مستويات الدعم والمقاومة من الضغط العكسي"""
        try:
            levels = []

            # جمع مستويات الرفض القوية
            for signal in pressure_signals:
                if signal.rejection_strength.value >= 4:  # رفض قوي أو أقوى
                    levels.append(signal.rejection_level)

            # إضافة مستويات من الشموع الحديثة
            if candles:
                recent_candles = candles[-10:]  # آخر 10 شموع
                for candle in recent_candles:
                    levels.append(candle['high'])  # مقاومة محتملة
                    levels.append(candle['low'])   # دعم محتمل

            # إزالة المستويات المتقاربة وترتيبها
            if levels:
                levels = sorted(set(levels))

                # دمج المستويات المتقاربة (ضمن 0.1% من بعضها)
                merged_levels = []
                for level in levels:
                    if not merged_levels:
                        merged_levels.append(level)
                    else:
                        last_level = merged_levels[-1]
                        if abs(level - last_level) / last_level > 0.001:  # 0.1%
                            merged_levels.append(level)

                return merged_levels[:10]  # أقصى 10 مستويات

            return []

        except Exception as e:
            logger.error(f"خطأ في تحديد مستويات الدعم والمقاومة: {str(e)}")
            return []

    def _calculate_pressure_statistics(self, pressure_signals: List[PressureSignal]) -> Dict[str, int]:
        """حساب إحصائيات الضغط العكسي"""
        try:
            total = len(pressure_signals)

            bullish = len([s for s in pressure_signals if s.pressure_type in [
                PressureType.STRONG_BULLISH_REJECTION,
                PressureType.MODERATE_BULLISH_REJECTION,
                PressureType.WEAK_BULLISH_REJECTION
            ]])

            bearish = len([s for s in pressure_signals if s.pressure_type in [
                PressureType.STRONG_BEARISH_REJECTION,
                PressureType.MODERATE_BEARISH_REJECTION,
                PressureType.WEAK_BEARISH_REJECTION
            ]])

            strong = len([s for s in pressure_signals if s.rejection_strength.value >= 4])

            return {
                'total': total,
                'bullish': bullish,
                'bearish': bearish,
                'strong': strong
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الضغط: {str(e)}")
            return {'total': 0, 'bullish': 0, 'bearish': 0, 'strong': 0}

    def _generate_pressure_recommendations(self, pressure_signals: List[PressureSignal], overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات الضغط العكسي"""
        try:
            recommendations = []

            if not pressure_signals:
                recommendations.append("لا توجد إشارات ضغط عكسي واضحة")
                return recommendations

            # توصيات بناء على نوع الضغط
            pressure_type = overall_assessment['pressure']
            if pressure_type in [PressureType.STRONG_BULLISH_REJECTION, PressureType.STRONG_BEARISH_REJECTION]:
                recommendations.append("رفض قوي - إشارة انعكاس موثوقة")
            elif pressure_type == PressureType.NEUTRAL:
                recommendations.append("ضغط محايد - انتظار إشارة أوضح")

            # توصيات بناء على نوع الظل
            shadow_type = overall_assessment['shadow_type']
            if shadow_type == ShadowType.DOUBLE_SHADOWS:
                recommendations.append("ظلال مزدوجة - تردد في السوق")
            elif shadow_type in [ShadowType.LONG_UPPER_SHADOW, ShadowType.LONG_LOWER_SHADOW]:
                recommendations.append("ظل طويل - رفض واضح للمستوى")

            # توصيات بناء على القوة
            strength = overall_assessment['strength']
            if strength.value >= 4:
                recommendations.append("قوة رفض عالية - إشارة قوية")
            elif strength.value <= 2:
                recommendations.append("قوة رفض ضعيفة - توخي الحذر")

            # توصيات بناء على تأكيد الحجم
            volume_confirmed = [s for s in pressure_signals if s.volume_confirmation]
            if len(volume_confirmed) >= 2:
                recommendations.append("تأكيد قوي من الحجم")
            elif not volume_confirmed:
                recommendations.append("نقص في تأكيد الحجم")

            return recommendations[:5]  # أقصى 5 توصيات

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات الضغط: {str(e)}")
            return []

    def get_pressure_summary(self, analysis: ReversePressureAnalysis) -> Dict[str, Any]:
        """الحصول على ملخص تحليل الضغط العكسي"""
        if not analysis:
            return {}

        return {
            'asset': analysis.asset,
            'overall_pressure': analysis.overall_pressure.value,
            'dominant_shadow_type': analysis.dominant_shadow_type.value,
            'overall_rejection_strength': analysis.overall_rejection_strength.value,
            'overall_confidence': analysis.overall_confidence,
            'is_actionable': analysis.is_actionable,
            'recommended_action': analysis.recommended_action,
            'processing_time_ms': analysis.processing_time_ms,
            'total_rejections': analysis.total_rejections,
            'bullish_rejections': analysis.bullish_rejections,
            'bearish_rejections': analysis.bearish_rejections,
            'strong_rejections': analysis.strong_rejections,
            'support_resistance_count': len(analysis.support_resistance_levels),
            'key_signals': [
                {
                    'pressure_type': signal.pressure_type.value,
                    'shadow_type': signal.shadow_type.value,
                    'strength': signal.rejection_strength.value,
                    'confidence': signal.confidence,
                    'rejection_level': signal.rejection_level,
                    'upper_shadow_ratio': signal.upper_shadow_ratio,
                    'lower_shadow_ratio': signal.lower_shadow_ratio
                }
                for signal in analysis.pressure_signals[:3]  # أهم 3 إشارات
            ],
            'top_recommendations': analysis.recommendations[:3]
        }

    def is_pressure_actionable(self, analysis: ReversePressureAnalysis, min_confidence: float = None) -> bool:
        """تحديد ما إذا كان الضغط العكسي قابل للتنفيذ"""
        if not analysis:
            return False

        min_conf = min_confidence or self.min_confidence_threshold

        return (
            analysis.is_actionable and
            analysis.overall_confidence >= min_conf and
            analysis.overall_rejection_strength.value >= 3 and
            analysis.strong_rejections >= 1 and
            analysis.overall_pressure != PressureType.NEUTRAL
        )

    def get_pressure_history(self, asset: str, limit: int = 10) -> List[ReversePressureAnalysis]:
        """الحصول على تاريخ الضغط العكسي لأصل معين"""
        if asset not in self.pressure_history:
            return []

        return self.pressure_history[asset][-limit:]

# إنشاء instance عام للاستخدام
reverse_pressure_analyzer = ReversePressureAnalyzer()
