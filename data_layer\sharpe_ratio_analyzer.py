"""
محلل نسبة شارب - المرحلة الرابعة
Sharpe Ratio Analyzer for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
import math
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("sharpe_ratio_analyzer")

class SharpeQuality(Enum):
    """جودة نسبة شارب"""
    EXCELLENT = "EXCELLENT"      # >2.0
    VERY_GOOD = "VERY_GOOD"      # 1.5-2.0
    GOOD = "GOOD"                # 1.0-1.5
    ACCEPTABLE = "ACCEPTABLE"    # 0.5-1.0
    POOR = "POOR"                # 0-0.5
    VERY_POOR = "VERY_POOR"      # <0

class RiskLevel(Enum):
    """مستوى المخاطر"""
    VERY_LOW = "VERY_LOW"
    LOW = "LOW"
    MODERATE = "MODERATE"
    HIGH = "HIGH"
    VERY_HIGH = "VERY_HIGH"

class PerformanceRating(Enum):
    """تقييم الأداء"""
    OUTSTANDING = "OUTSTANDING"
    EXCELLENT = "EXCELLENT"
    GOOD = "GOOD"
    AVERAGE = "AVERAGE"
    BELOW_AVERAGE = "BELOW_AVERAGE"
    POOR = "POOR"

@dataclass
class SharpeAnalysis:
    """تحليل نسبة شارب"""
    sharpe_ratio: float
    quality: SharpeQuality
    risk_level: RiskLevel
    performance_rating: PerformanceRating
    
    # مكونات الحساب
    average_return: float
    return_volatility: float
    risk_free_rate: float
    excess_return: float
    
    # إحصائيات الأداء
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # مقاييس المخاطر
    max_drawdown: float
    value_at_risk: float
    downside_deviation: float
    
    # فترة التحليل
    analysis_period_days: int
    data_quality_score: float
    
    # وصف التحليل
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class SharpeRatioResult:
    """نتيجة تحليل نسبة شارب"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # تحليلات نسبة شارب لفترات مختلفة
    sharpe_analyses: List[SharpeAnalysis]
    
    # التقييم الإجمالي
    overall_sharpe_ratio: float
    overall_quality: SharpeQuality
    overall_risk_level: RiskLevel
    overall_performance: PerformanceRating
    
    # مقارنات
    benchmark_comparison: float  # مقارنة مع المعيار
    peer_ranking: str           # ترتيب بين الأقران
    
    # توصيات
    is_investment_worthy: bool
    recommended_allocation: float
    risk_management_advice: str
    recommendations: List[str]

class SharpeRatioAnalyzer:
    """محلل نسبة شارب"""
    
    def __init__(self):
        self.analysis_periods = [7, 14, 30]  # فترات التحليل بالأيام
        self.risk_free_rate = 0.02  # معدل خالي من المخاطر (2% سنوياً)
        self.benchmark_sharpe = 1.0  # نسبة شارب المرجعية
        self.min_trades_threshold = 20  # الحد الأدنى للصفقات
        self.sharpe_history = {}  # تاريخ نسب شارب لكل أصل
        
        logger.info("تم تهيئة محلل نسبة شارب")

    @handle_async_errors(default_return=None, log_error=True)
    async def calculate_sharpe_ratio(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[SharpeAnalysis]:
        """حساب نسبة شارب"""
        return await self.analyze_sharpe_performance(asset, candles_data)

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_sharpe_performance(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[SharpeAnalysis]:
        """تحليل أداء شارب من بيانات الشموع"""
        try:
            if not candles_data or len(candles_data) < 30:
                logger.warning(f"بيانات غير كافية لتحليل شارب لـ {asset}")
                return None

            # تحويل بيانات الشموع إلى عوائد
            returns = []
            for i in range(1, len(candles_data)):
                prev_close = float(candles_data[i-1].get('close', 0))
                curr_close = float(candles_data[i].get('close', 0))

                if prev_close > 0:
                    return_rate = (curr_close - prev_close) / prev_close
                    returns.append(return_rate)

            if not returns:
                return None

            # حساب الإحصائيات الأساسية
            average_return = statistics.mean(returns)
            return_volatility = statistics.stdev(returns) if len(returns) > 1 else 0

            # معدل خالي المخاطر يومي
            daily_risk_free_rate = self.risk_free_rate / 365
            excess_return = average_return - daily_risk_free_rate

            # حساب نسبة شارب
            if return_volatility > 0:
                sharpe_ratio = excess_return / return_volatility
            else:
                sharpe_ratio = 0 if excess_return <= 0 else float('inf')

            # تحديد الجودة والمخاطر
            quality = self._determine_sharpe_quality(sharpe_ratio)
            risk_level = self._determine_risk_level(return_volatility, returns)
            performance_rating = self._determine_performance_rating(sharpe_ratio, average_return, 0.5)

            # حساب المقاييس الإضافية
            max_drawdown = self._calculate_max_drawdown(returns)
            value_at_risk = self._calculate_var(returns)
            downside_deviation = self._calculate_downside_deviation(returns, daily_risk_free_rate)

            return SharpeAnalysis(
                sharpe_ratio=sharpe_ratio,
                quality=quality,
                risk_level=risk_level,
                performance_rating=performance_rating,
                average_return=average_return,
                return_volatility=return_volatility,
                risk_free_rate=daily_risk_free_rate,
                excess_return=excess_return,
                total_trades=len(returns),
                winning_trades=len([r for r in returns if r > 0]),
                losing_trades=len([r for r in returns if r < 0]),
                win_rate=len([r for r in returns if r > 0]) / len(returns) if returns else 0,
                max_drawdown=max_drawdown,
                value_at_risk=value_at_risk,
                downside_deviation=downside_deviation,
                analysis_period_days=len(returns),
                data_quality_score=0.8,
                description=f"تحليل شارب لـ {asset}: {sharpe_ratio:.2f}",
                reasoning=f"عائد متوسط {average_return*100:.2f}% مع تقلبات {return_volatility*100:.2f}%",
                supporting_data={'candles_count': len(candles_data)}
            )

        except Exception as e:
            logger.error(f"خطأ في تحليل أداء شارب لـ {asset}: {str(e)}")
            return None

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_sharpe_ratio(self, asset: str, trading_results: List[Dict[str, Any]]) -> Optional[SharpeRatioResult]:
        """تحليل نسبة شارب"""
        start_time = time.time()
        
        try:
            if not trading_results or len(trading_results) < self.min_trades_threshold:
                logger.warning(f"بيانات غير كافية لتحليل نسبة شارب لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل نسبة شارب لـ {asset} مع {len(trading_results)} صفقة")
            
            # 1. تحليل نسبة شارب لفترات مختلفة
            sharpe_analyses = []
            for period in self.analysis_periods:
                analysis = await self._analyze_sharpe_period(trading_results, period)
                if analysis:
                    sharpe_analyses.append(analysis)
            
            if not sharpe_analyses:
                logger.warning(f"لم يتم إنتاج أي تحليل نسبة شارب لـ {asset}")
                return None
            
            # 2. التقييم الإجمالي
            overall_assessment = self._evaluate_overall_sharpe(sharpe_analyses)
            
            # 3. المقارنات
            benchmark_comparison = overall_assessment['sharpe_ratio'] / self.benchmark_sharpe
            peer_ranking = self._determine_peer_ranking(overall_assessment['sharpe_ratio'])
            
            # 4. إنتاج التوصيات
            recommendations = self._generate_sharpe_recommendations(sharpe_analyses, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = SharpeRatioResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                sharpe_analyses=sharpe_analyses,
                overall_sharpe_ratio=overall_assessment['sharpe_ratio'],
                overall_quality=overall_assessment['quality'],
                overall_risk_level=overall_assessment['risk_level'],
                overall_performance=overall_assessment['performance'],
                benchmark_comparison=benchmark_comparison,
                peer_ranking=peer_ranking,
                is_investment_worthy=overall_assessment['investment_worthy'],
                recommended_allocation=overall_assessment['allocation'],
                risk_management_advice=overall_assessment['risk_advice'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.sharpe_history:
                self.sharpe_history[asset] = []
            
            self.sharpe_history[asset].append(result)
            
            # الاحتفاظ بآخر 20 تحليل فقط
            if len(self.sharpe_history[asset]) > 20:
                self.sharpe_history[asset] = self.sharpe_history[asset][-20:]
            
            logger.info(f"تم إكمال تحليل نسبة شارب لـ {asset}: {overall_assessment['sharpe_ratio']:.2f} ({overall_assessment['quality'].value})")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نسبة شارب لـ {asset}: {str(e)}")
            return None

    async def _analyze_sharpe_period(self, trading_results: List[Dict[str, Any]], period_days: int) -> Optional[SharpeAnalysis]:
        """تحليل نسبة شارب لفترة محددة"""
        try:
            # تصفية النتائج للفترة المحددة
            cutoff_date = datetime.now() - timedelta(days=period_days)
            period_results = []
            
            for result in trading_results:
                trade_date = result.get('timestamp')
                if isinstance(trade_date, str):
                    trade_date = datetime.fromisoformat(trade_date)
                elif not isinstance(trade_date, datetime):
                    continue
                
                if trade_date >= cutoff_date:
                    period_results.append(result)
            
            if len(period_results) < 10:  # الحد الأدنى للفترة
                return None
            
            # حساب العوائد
            returns = []
            total_trades = len(period_results)
            winning_trades = 0
            losing_trades = 0
            
            for result in period_results:
                profit_loss = result.get('profit_loss', 0)
                initial_amount = result.get('trade_amount', 1.0)
                
                if initial_amount > 0:
                    return_rate = profit_loss / initial_amount
                    returns.append(return_rate)
                    
                    if profit_loss > 0:
                        winning_trades += 1
                    elif profit_loss < 0:
                        losing_trades += 1
            
            if not returns:
                return None
            
            # حساب الإحصائيات الأساسية
            average_return = statistics.mean(returns)
            return_volatility = statistics.stdev(returns) if len(returns) > 1 else 0
            
            # تحويل معدل خالي المخاطر للفترة
            daily_risk_free_rate = self.risk_free_rate / 365
            period_risk_free_rate = daily_risk_free_rate * period_days
            
            # حساب العائد الزائد
            excess_return = average_return - period_risk_free_rate
            
            # حساب نسبة شارب
            if return_volatility > 0:
                sharpe_ratio = excess_return / return_volatility
            else:
                sharpe_ratio = 0 if excess_return <= 0 else float('inf')
            
            # تحديد الجودة
            quality = self._determine_sharpe_quality(sharpe_ratio)
            
            # تحديد مستوى المخاطر
            risk_level = self._determine_risk_level(return_volatility, returns)
            
            # تحديد تقييم الأداء
            performance_rating = self._determine_performance_rating(sharpe_ratio, average_return, winning_trades / total_trades if total_trades > 0 else 0)
            
            # حساب معدل الفوز
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # حساب أقصى انخفاض
            max_drawdown = self._calculate_max_drawdown(returns)
            
            # حساب القيمة المعرضة للمخاطر (VaR)
            value_at_risk = self._calculate_var(returns)
            
            # حساب انحراف الجانب السفلي
            downside_deviation = self._calculate_downside_deviation(returns, period_risk_free_rate)
            
            # حساب جودة البيانات
            data_quality_score = self._calculate_data_quality(period_results, period_days)
            
            return SharpeAnalysis(
                sharpe_ratio=sharpe_ratio,
                quality=quality,
                risk_level=risk_level,
                performance_rating=performance_rating,
                average_return=average_return,
                return_volatility=return_volatility,
                risk_free_rate=period_risk_free_rate,
                excess_return=excess_return,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                max_drawdown=max_drawdown,
                value_at_risk=value_at_risk,
                downside_deviation=downside_deviation,
                analysis_period_days=period_days,
                data_quality_score=data_quality_score,
                description=f"نسبة شارب {period_days} يوم: {sharpe_ratio:.2f} ({quality.value})",
                reasoning=f"عائد متوسط {average_return*100:.2f}% مع تقلبات {return_volatility*100:.2f}%",
                supporting_data={
                    'period_days': period_days,
                    'returns_sample': returns[-10:],  # آخر 10 عوائد
                    'trade_frequency': total_trades / period_days
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نسبة شارب للفترة {period_days}: {str(e)}")
            return None

    def _determine_sharpe_quality(self, sharpe_ratio: float) -> SharpeQuality:
        """تحديد جودة نسبة شارب"""
        try:
            if sharpe_ratio >= 2.0:
                return SharpeQuality.EXCELLENT
            elif sharpe_ratio >= 1.5:
                return SharpeQuality.VERY_GOOD
            elif sharpe_ratio >= 1.0:
                return SharpeQuality.GOOD
            elif sharpe_ratio >= 0.5:
                return SharpeQuality.ACCEPTABLE
            elif sharpe_ratio >= 0:
                return SharpeQuality.POOR
            else:
                return SharpeQuality.VERY_POOR

        except Exception as e:
            logger.error(f"خطأ في تحديد جودة نسبة شارب: {str(e)}")
            return SharpeQuality.POOR

    def _determine_risk_level(self, volatility: float, returns: List[float]) -> RiskLevel:
        """تحديد مستوى المخاطر"""
        try:
            # تحليل التقلبات
            if volatility <= 0.02:  # 2%
                volatility_risk = "VERY_LOW"
            elif volatility <= 0.05:  # 5%
                volatility_risk = "LOW"
            elif volatility <= 0.10:  # 10%
                volatility_risk = "MODERATE"
            elif volatility <= 0.20:  # 20%
                volatility_risk = "HIGH"
            else:
                volatility_risk = "VERY_HIGH"

            # تحليل توزيع العوائد
            negative_returns = [r for r in returns if r < 0]
            negative_ratio = len(negative_returns) / len(returns) if returns else 0

            if negative_ratio <= 0.2:
                distribution_risk = "VERY_LOW"
            elif negative_ratio <= 0.3:
                distribution_risk = "LOW"
            elif negative_ratio <= 0.4:
                distribution_risk = "MODERATE"
            elif negative_ratio <= 0.5:
                distribution_risk = "HIGH"
            else:
                distribution_risk = "VERY_HIGH"

            # دمج المخاطر
            risk_levels = {"VERY_LOW": 1, "LOW": 2, "MODERATE": 3, "HIGH": 4, "VERY_HIGH": 5}
            avg_risk = (risk_levels[volatility_risk] + risk_levels[distribution_risk]) / 2

            if avg_risk <= 1.5:
                return RiskLevel.VERY_LOW
            elif avg_risk <= 2.5:
                return RiskLevel.LOW
            elif avg_risk <= 3.5:
                return RiskLevel.MODERATE
            elif avg_risk <= 4.5:
                return RiskLevel.HIGH
            else:
                return RiskLevel.VERY_HIGH

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى المخاطر: {str(e)}")
            return RiskLevel.MODERATE

    def _determine_performance_rating(self, sharpe_ratio: float, average_return: float, win_rate: float) -> PerformanceRating:
        """تحديد تقييم الأداء"""
        try:
            # نقاط الأداء
            performance_score = 0

            # نقاط نسبة شارب (40%)
            if sharpe_ratio >= 2.0:
                performance_score += 40
            elif sharpe_ratio >= 1.5:
                performance_score += 32
            elif sharpe_ratio >= 1.0:
                performance_score += 24
            elif sharpe_ratio >= 0.5:
                performance_score += 16
            elif sharpe_ratio >= 0:
                performance_score += 8

            # نقاط العائد المتوسط (35%)
            if average_return >= 0.05:  # 5%
                performance_score += 35
            elif average_return >= 0.03:  # 3%
                performance_score += 28
            elif average_return >= 0.01:  # 1%
                performance_score += 21
            elif average_return >= 0:
                performance_score += 14
            elif average_return >= -0.01:  # -1%
                performance_score += 7

            # نقاط معدل الفوز (25%)
            if win_rate >= 0.7:  # 70%
                performance_score += 25
            elif win_rate >= 0.6:  # 60%
                performance_score += 20
            elif win_rate >= 0.5:  # 50%
                performance_score += 15
            elif win_rate >= 0.4:  # 40%
                performance_score += 10
            elif win_rate >= 0.3:  # 30%
                performance_score += 5

            # تحديد التقييم
            if performance_score >= 90:
                return PerformanceRating.OUTSTANDING
            elif performance_score >= 75:
                return PerformanceRating.EXCELLENT
            elif performance_score >= 60:
                return PerformanceRating.GOOD
            elif performance_score >= 45:
                return PerformanceRating.AVERAGE
            elif performance_score >= 30:
                return PerformanceRating.BELOW_AVERAGE
            else:
                return PerformanceRating.POOR

        except Exception as e:
            logger.error(f"خطأ في تحديد تقييم الأداء: {str(e)}")
            return PerformanceRating.AVERAGE

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """حساب أقصى انخفاض"""
        try:
            if not returns:
                return 0.0

            # حساب القيم التراكمية
            cumulative_returns = []
            cumulative = 1.0

            for return_rate in returns:
                cumulative *= (1 + return_rate)
                cumulative_returns.append(cumulative)

            # حساب أقصى انخفاض
            max_drawdown = 0.0
            peak = cumulative_returns[0]

            for value in cumulative_returns:
                if value > peak:
                    peak = value

                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            return max_drawdown

        except Exception as e:
            logger.error(f"خطأ في حساب أقصى انخفاض: {str(e)}")
            return 0.0

    def _calculate_var(self, returns: List[float], confidence_level: float = 0.05) -> float:
        """حساب القيمة المعرضة للمخاطر (VaR)"""
        try:
            if not returns or len(returns) < 10:
                return 0.0

            sorted_returns = sorted(returns)
            var_index = int(len(sorted_returns) * confidence_level)

            return abs(sorted_returns[var_index]) if var_index < len(sorted_returns) else 0.0

        except Exception as e:
            logger.error(f"خطأ في حساب VaR: {str(e)}")
            return 0.0

    def _calculate_downside_deviation(self, returns: List[float], target_return: float) -> float:
        """حساب انحراف الجانب السفلي"""
        try:
            if not returns:
                return 0.0

            downside_returns = [r for r in returns if r < target_return]

            if not downside_returns:
                return 0.0

            downside_variance = statistics.variance(downside_returns)
            return math.sqrt(downside_variance)

        except Exception as e:
            logger.error(f"خطأ في حساب انحراف الجانب السفلي: {str(e)}")
            return 0.0

    def _calculate_data_quality(self, trading_results: List[Dict[str, Any]], period_days: int) -> float:
        """حساب جودة البيانات"""
        try:
            if not trading_results:
                return 0.0

            quality_factors = []

            # عامل اكتمال البيانات
            expected_trades = period_days * 2  # افتراض صفقتين يومياً
            actual_trades = len(trading_results)
            completeness = min(1.0, actual_trades / expected_trades)
            quality_factors.append(completeness)

            # عامل توزيع البيانات عبر الفترة
            timestamps = []
            for result in trading_results:
                timestamp = result.get('timestamp')
                if timestamp:
                    if isinstance(timestamp, str):
                        timestamp = datetime.fromisoformat(timestamp)
                    timestamps.append(timestamp)

            if timestamps:
                timestamps.sort()
                time_gaps = []
                for i in range(1, len(timestamps)):
                    gap = (timestamps[i] - timestamps[i-1]).total_seconds() / 3600  # ساعات
                    time_gaps.append(gap)

                if time_gaps:
                    avg_gap = statistics.mean(time_gaps)
                    expected_gap = 24 / 2  # 12 ساعة بين الصفقات
                    distribution_quality = max(0.1, min(1.0, expected_gap / avg_gap))
                    quality_factors.append(distribution_quality)

            # عامل اتساق البيانات
            valid_results = 0
            for result in trading_results:
                if (result.get('profit_loss') is not None and
                    result.get('trade_amount') is not None and
                    result.get('timestamp') is not None):
                    valid_results += 1

            consistency = valid_results / len(trading_results)
            quality_factors.append(consistency)

            return statistics.mean(quality_factors)

        except Exception as e:
            logger.error(f"خطأ في حساب جودة البيانات: {str(e)}")
            return 0.5

    def _evaluate_overall_sharpe(self, sharpe_analyses: List[SharpeAnalysis]) -> Dict[str, Any]:
        """تقييم نسبة شارب الإجمالية"""
        try:
            if not sharpe_analyses:
                return {
                    'sharpe_ratio': 0.0,
                    'quality': SharpeQuality.VERY_POOR,
                    'risk_level': RiskLevel.VERY_HIGH,
                    'performance': PerformanceRating.POOR,
                    'investment_worthy': False,
                    'allocation': 0.0,
                    'risk_advice': 'تجنب الاستثمار'
                }

            # حساب متوسط نسبة شارب مرجح بجودة البيانات
            weighted_sharpe = 0
            total_weight = 0

            for analysis in sharpe_analyses:
                weight = analysis.data_quality_score
                weighted_sharpe += analysis.sharpe_ratio * weight
                total_weight += weight

            if total_weight > 0:
                overall_sharpe = weighted_sharpe / total_weight
            else:
                overall_sharpe = 0

            # تحديد الجودة الإجمالية
            overall_quality = self._determine_sharpe_quality(overall_sharpe)

            # تحديد مستوى المخاطر الإجمالي
            risk_levels = [analysis.risk_level for analysis in sharpe_analyses]
            risk_values = {"VERY_LOW": 1, "LOW": 2, "MODERATE": 3, "HIGH": 4, "VERY_HIGH": 5}
            avg_risk_value = statistics.mean([risk_values[risk.value] for risk in risk_levels])

            if avg_risk_value <= 1.5:
                overall_risk = RiskLevel.VERY_LOW
            elif avg_risk_value <= 2.5:
                overall_risk = RiskLevel.LOW
            elif avg_risk_value <= 3.5:
                overall_risk = RiskLevel.MODERATE
            elif avg_risk_value <= 4.5:
                overall_risk = RiskLevel.HIGH
            else:
                overall_risk = RiskLevel.VERY_HIGH

            # تحديد الأداء الإجمالي
            performance_ratings = [analysis.performance_rating for analysis in sharpe_analyses]
            performance_values = {
                "OUTSTANDING": 6, "EXCELLENT": 5, "GOOD": 4,
                "AVERAGE": 3, "BELOW_AVERAGE": 2, "POOR": 1
            }
            avg_performance_value = statistics.mean([performance_values[perf.value] for perf in performance_ratings])

            if avg_performance_value >= 5.5:
                overall_performance = PerformanceRating.OUTSTANDING
            elif avg_performance_value >= 4.5:
                overall_performance = PerformanceRating.EXCELLENT
            elif avg_performance_value >= 3.5:
                overall_performance = PerformanceRating.GOOD
            elif avg_performance_value >= 2.5:
                overall_performance = PerformanceRating.AVERAGE
            elif avg_performance_value >= 1.5:
                overall_performance = PerformanceRating.BELOW_AVERAGE
            else:
                overall_performance = PerformanceRating.POOR

            # تحديد جدارة الاستثمار
            investment_worthy = (
                overall_sharpe >= 1.0 and
                overall_quality.value in ['GOOD', 'VERY_GOOD', 'EXCELLENT'] and
                overall_risk.value in ['VERY_LOW', 'LOW', 'MODERATE']
            )

            # تحديد التخصيص المقترح
            if overall_sharpe >= 2.0:
                allocation = 0.25  # 25%
            elif overall_sharpe >= 1.5:
                allocation = 0.20  # 20%
            elif overall_sharpe >= 1.0:
                allocation = 0.15  # 15%
            elif overall_sharpe >= 0.5:
                allocation = 0.10  # 10%
            else:
                allocation = 0.05  # 5%

            # تعديل التخصيص حسب المخاطر
            if overall_risk == RiskLevel.VERY_HIGH:
                allocation *= 0.5
            elif overall_risk == RiskLevel.HIGH:
                allocation *= 0.7
            elif overall_risk == RiskLevel.VERY_LOW:
                allocation *= 1.2

            allocation = min(0.30, allocation)  # حد أقصى 30%

            # نصائح إدارة المخاطر
            if overall_risk == RiskLevel.VERY_HIGH:
                risk_advice = "مخاطر عالية جداً - استخدم حجم صفقات صغير جداً"
            elif overall_risk == RiskLevel.HIGH:
                risk_advice = "مخاطر عالية - استخدم حجم صفقات صغير"
            elif overall_risk == RiskLevel.MODERATE:
                risk_advice = "مخاطر معتدلة - استخدم حجم صفقات متوسط"
            elif overall_risk == RiskLevel.LOW:
                risk_advice = "مخاطر منخفضة - يمكن زيادة حجم الصفقات"
            else:
                risk_advice = "مخاطر منخفضة جداً - استثمار آمن نسبياً"

            return {
                'sharpe_ratio': overall_sharpe,
                'quality': overall_quality,
                'risk_level': overall_risk,
                'performance': overall_performance,
                'investment_worthy': investment_worthy,
                'allocation': allocation,
                'risk_advice': risk_advice
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم نسبة شارب الإجمالية: {str(e)}")
            return {
                'sharpe_ratio': 0.0,
                'quality': SharpeQuality.VERY_POOR,
                'risk_level': RiskLevel.VERY_HIGH,
                'performance': PerformanceRating.POOR,
                'investment_worthy': False,
                'allocation': 0.0,
                'risk_advice': 'تجنب الاستثمار'
            }

    def _determine_peer_ranking(self, sharpe_ratio: float) -> str:
        """تحديد الترتيب بين الأقران"""
        try:
            if sharpe_ratio >= 2.0:
                return "أعلى 5% (ممتاز)"
            elif sharpe_ratio >= 1.5:
                return "أعلى 10% (جيد جداً)"
            elif sharpe_ratio >= 1.0:
                return "أعلى 25% (جيد)"
            elif sharpe_ratio >= 0.5:
                return "أعلى 50% (متوسط)"
            elif sharpe_ratio >= 0:
                return "أسفل 50% (ضعيف)"
            else:
                return "أسفل 10% (ضعيف جداً)"

        except Exception as e:
            logger.error(f"خطأ في تحديد الترتيب: {str(e)}")
            return "غير محدد"

    def _generate_sharpe_recommendations(self, sharpe_analyses: List[SharpeAnalysis], overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات نسبة شارب"""
        try:
            recommendations = []

            sharpe_ratio = overall_assessment['sharpe_ratio']
            risk_level = overall_assessment['risk_level']

            # توصيات حسب نسبة شارب
            if sharpe_ratio >= 2.0:
                recommendations.append("نسبة شارب ممتازة - استراتيجية عالية الجودة")
                recommendations.append("يُنصح بزيادة التخصيص لهذه الاستراتيجية")
            elif sharpe_ratio >= 1.5:
                recommendations.append("نسبة شارب جيدة جداً - أداء قوي")
                recommendations.append("استراتيجية موثوقة للاستثمار")
            elif sharpe_ratio >= 1.0:
                recommendations.append("نسبة شارب جيدة - أداء مقبول")
                recommendations.append("يمكن الاستثمار بحذر")
            elif sharpe_ratio >= 0.5:
                recommendations.append("نسبة شارب مقبولة - أداء متوسط")
                recommendations.append("تحتاج تحسينات في الاستراتيجية")
            else:
                recommendations.append("نسبة شارب ضعيفة - تجنب الاستثمار")
                recommendations.append("مراجعة شاملة للاستراتيجية مطلوبة")

            # توصيات حسب المخاطر
            if risk_level == RiskLevel.VERY_HIGH:
                recommendations.append("مخاطر عالية جداً - استخدم stop loss صارم")
                recommendations.append("لا تتجاوز 2% من رأس المال في الصفقة الواحدة")
            elif risk_level == RiskLevel.HIGH:
                recommendations.append("مخاطر عالية - إدارة مخاطر صارمة")
                recommendations.append("لا تتجاوز 3% من رأس المال في الصفقة الواحدة")
            elif risk_level == RiskLevel.MODERATE:
                recommendations.append("مخاطر معتدلة - إدارة مخاطر متوازنة")
                recommendations.append("يمكن استخدام 5% من رأس المال في الصفقة")
            else:
                recommendations.append("مخاطر منخفضة - استراتيجية آمنة نسبياً")
                recommendations.append("يمكن زيادة حجم الصفقات تدريجياً")

            # توصيات التخصيص
            allocation = overall_assessment['allocation']
            recommendations.append(f"التخصيص المقترح: {allocation*100:.1f}% من المحفظة")

            # توصيات التحسين
            worst_analysis = min(sharpe_analyses, key=lambda x: x.sharpe_ratio)
            if worst_analysis.sharpe_ratio < 0.5:
                recommendations.append(f"تحسين الأداء في فترة {worst_analysis.analysis_period_days} يوم")

            # توصيات المراقبة
            recommendations.append("مراقبة نسبة شارب شهرياً للتأكد من الاستمرارية")
            recommendations.append("إعادة تقييم الاستراتيجية عند انخفاض النسبة عن 0.8")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات نسبة شارب: {str(e)}")
            return ["خطأ في إنتاج التوصيات"]

    def get_sharpe_history(self, asset: str, limit: int = 10) -> List[SharpeRatioResult]:
        """الحصول على تاريخ نسبة شارب"""
        try:
            if asset not in self.sharpe_history:
                return []

            return self.sharpe_history[asset][-limit:]

        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ نسبة شارب لـ {asset}: {str(e)}")
            return []

# إنشاء instance عام للاستخدام
sharpe_ratio_analyzer = SharpeRatioAnalyzer()

if __name__ == "__main__":
    # اختبار سريع
    import asyncio

    async def test_analyzer():
        # بيانات اختبار
        test_results = []

        # إنشاء نتائج تداول وهمية
        for i in range(100):
            result = {
                'timestamp': datetime.now() - timedelta(days=i//3),
                'profit_loss': 0.5 if i % 3 != 0 else -0.3,  # 67% نجاح
                'trade_amount': 10.0,
                'signal_type': 'BUY' if i % 2 == 0 else 'SELL'
            }
            test_results.append(result)

        # تشغيل التحليل
        result = await sharpe_ratio_analyzer.analyze_sharpe_ratio("TEST_PAIR", test_results)

        if result:
            print(f"تحليل نسبة شارب:")
            print(f"نسبة شارب الإجمالية: {result.overall_sharpe_ratio:.2f}")
            print(f"الجودة: {result.overall_quality.value}")
            print(f"مستوى المخاطر: {result.overall_risk_level.value}")
            print(f"تقييم الأداء: {result.overall_performance.value}")
            print(f"جدير بالاستثمار: {result.is_investment_worthy}")
            print(f"التخصيص المقترح: {result.recommended_allocation*100:.1f}%")
            print(f"مقارنة مع المعيار: {result.benchmark_comparison:.2f}x")
            print(f"الترتيب: {result.peer_ranking}")

            print(f"\nتحليلات الفترات:")
            for analysis in result.sharpe_analyses:
                print(f"  {analysis.analysis_period_days} يوم: {analysis.sharpe_ratio:.2f} ({analysis.quality.value})")
        else:
            print("فشل في التحليل")

    # تشغيل الاختبار
    asyncio.run(test_analyzer())
