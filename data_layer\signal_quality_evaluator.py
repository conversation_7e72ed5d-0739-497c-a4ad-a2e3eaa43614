"""
نظام تقييم جودة الإشارات - المرحلة الرابعة
Signal Quality Evaluation System for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors
from data_layer.technical_signals_engine import TechnicalSignal, CombinedSignal, SignalDirection, SignalStrength

logger = scalping_logger.get_logger("signal_quality_evaluator")

class QualityGrade(Enum):
    """درجة جودة الإشارة"""
    EXCELLENT = "EXCELLENT"  # 90-100%
    VERY_GOOD = "VERY_GOOD"  # 80-89%
    GOOD = "GOOD"            # 70-79%
    FAIR = "FAIR"            # 60-69%
    POOR = "POOR"            # 50-59%
    VERY_POOR = "VERY_POOR"  # أقل من 50%

class QualityFactor(Enum):
    """عوامل تقييم الجودة"""
    SIGNAL_STRENGTH = "SIGNAL_STRENGTH"
    CONFIDENCE_LEVEL = "CONFIDENCE_LEVEL"
    INDICATOR_CONSENSUS = "INDICATOR_CONSENSUS"
    HISTORICAL_ACCURACY = "HISTORICAL_ACCURACY"
    MARKET_CONDITIONS = "MARKET_CONDITIONS"
    TIMING_QUALITY = "TIMING_QUALITY"
    VOLATILITY_ALIGNMENT = "VOLATILITY_ALIGNMENT"

@dataclass
class QualityMetrics:
    """مقاييس جودة الإشارة"""
    overall_score: float  # 0-100
    grade: QualityGrade
    factor_scores: Dict[QualityFactor, float]
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    risk_level: str  # LOW, MEDIUM, HIGH
    confidence_adjustment: float  # تعديل الثقة بناء على الجودة

@dataclass
class HistoricalPerformance:
    """الأداء التاريخي للإشارات"""
    total_signals: int
    successful_signals: int
    failed_signals: int
    success_rate: float
    avg_confidence: float
    avg_actual_accuracy: float
    last_updated: datetime

class SignalQualityEvaluator:
    """نظام تقييم جودة الإشارات المتقدم"""
    
    def __init__(self):
        self.quality_weights = self._initialize_quality_weights()
        self.historical_performance = {}  # تاريخ أداء كل أصل
        self.market_conditions_cache = {}
        self.evaluation_history = {}  # تاريخ التقييمات
        
        logger.info("تم تهيئة نظام تقييم جودة الإشارات")

    def _initialize_quality_weights(self) -> Dict[QualityFactor, float]:
        """تهيئة أوزان عوامل الجودة"""
        return {
            QualityFactor.SIGNAL_STRENGTH: 0.20,        # قوة الإشارة
            QualityFactor.CONFIDENCE_LEVEL: 0.18,       # مستوى الثقة
            QualityFactor.INDICATOR_CONSENSUS: 0.16,    # إجماع المؤشرات
            QualityFactor.HISTORICAL_ACCURACY: 0.15,    # الدقة التاريخية
            QualityFactor.MARKET_CONDITIONS: 0.12,      # ظروف السوق
            QualityFactor.TIMING_QUALITY: 0.10,         # جودة التوقيت
            QualityFactor.VOLATILITY_ALIGNMENT: 0.09    # توافق التقلبات
        }

    @handle_async_errors(default_return=None, log_error=True)
    async def evaluate_signal_quality(self, signal: CombinedSignal, market_data: Dict[str, Any] = None) -> Optional[QualityMetrics]:
        """تقييم جودة الإشارة المدمجة"""
        try:
            if not signal:
                return None

            # حساب نقاط كل عامل جودة
            factor_scores = {}
            
            # 1. تقييم قوة الإشارة
            factor_scores[QualityFactor.SIGNAL_STRENGTH] = self._evaluate_signal_strength(signal)
            
            # 2. تقييم مستوى الثقة
            factor_scores[QualityFactor.CONFIDENCE_LEVEL] = self._evaluate_confidence_level(signal)
            
            # 3. تقييم إجماع المؤشرات
            factor_scores[QualityFactor.INDICATOR_CONSENSUS] = self._evaluate_indicator_consensus(signal)
            
            # 4. تقييم الدقة التاريخية
            factor_scores[QualityFactor.HISTORICAL_ACCURACY] = self._evaluate_historical_accuracy(signal.asset)
            
            # 5. تقييم ظروف السوق
            factor_scores[QualityFactor.MARKET_CONDITIONS] = await self._evaluate_market_conditions(signal, market_data)
            
            # 6. تقييم جودة التوقيت
            factor_scores[QualityFactor.TIMING_QUALITY] = self._evaluate_timing_quality(signal)
            
            # 7. تقييم توافق التقلبات
            factor_scores[QualityFactor.VOLATILITY_ALIGNMENT] = self._evaluate_volatility_alignment(signal)

            # حساب النقاط الإجمالية
            overall_score = sum(
                score * self.quality_weights[factor] 
                for factor, score in factor_scores.items()
            )

            # تحديد الدرجة
            grade = self._determine_quality_grade(overall_score)
            
            # تحليل نقاط القوة والضعف
            strengths, weaknesses = self._analyze_strengths_weaknesses(factor_scores)
            
            # إنتاج التوصيات
            recommendations = self._generate_recommendations(factor_scores, signal)
            
            # تحديد مستوى المخاطر
            risk_level = self._determine_risk_level(overall_score, signal)
            
            # حساب تعديل الثقة
            confidence_adjustment = self._calculate_confidence_adjustment(overall_score)

            quality_metrics = QualityMetrics(
                overall_score=round(overall_score, 2),
                grade=grade,
                factor_scores=factor_scores,
                strengths=strengths,
                weaknesses=weaknesses,
                recommendations=recommendations,
                risk_level=risk_level,
                confidence_adjustment=confidence_adjustment
            )

            # حفظ في تاريخ التقييمات
            if signal.asset not in self.evaluation_history:
                self.evaluation_history[signal.asset] = []
            
            self.evaluation_history[signal.asset].append({
                'timestamp': datetime.now(),
                'signal': signal,
                'quality': quality_metrics
            })

            # الاحتفاظ بآخر 50 تقييم فقط
            if len(self.evaluation_history[signal.asset]) > 50:
                self.evaluation_history[signal.asset] = self.evaluation_history[signal.asset][-50:]

            logger.info(f"تم تقييم جودة الإشارة لـ {signal.asset}: {grade.value} ({overall_score:.1f}%)")
            
            return quality_metrics

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة الإشارة: {str(e)}")
            return None

    def _evaluate_signal_strength(self, signal: CombinedSignal) -> float:
        """تقييم قوة الإشارة"""
        strength_mapping = {
            SignalStrength.VERY_STRONG: 95,
            SignalStrength.STRONG: 80,
            SignalStrength.MODERATE: 65,
            SignalStrength.WEAK: 45,
            SignalStrength.VERY_WEAK: 25
        }
        
        base_score = strength_mapping.get(signal.overall_strength, 50)
        
        # تعديل بناء على عدد الإشارات الداعمة
        supporting_bonus = min(20, len(signal.supporting_signals) * 3)
        
        # خصم بناء على الإشارات المتضاربة
        conflicting_penalty = min(15, len(signal.conflicting_signals) * 2)
        
        final_score = base_score + supporting_bonus - conflicting_penalty
        return max(0, min(100, final_score))

    def _evaluate_confidence_level(self, signal: CombinedSignal) -> float:
        """تقييم مستوى الثقة"""
        # النقاط الأساسية من مستوى الثقة
        base_score = signal.confidence_score
        
        # تعديل بناء على استقرار الثقة
        if hasattr(signal, 'confidence_stability'):
            stability_bonus = signal.confidence_stability * 10
            base_score += stability_bonus
        
        return max(0, min(100, base_score))

    def _evaluate_indicator_consensus(self, signal: CombinedSignal) -> float:
        """تقييم إجماع المؤشرات"""
        total_indicators = len(signal.supporting_signals) + len(signal.conflicting_signals)
        
        if total_indicators == 0:
            return 0
        
        # نسبة الإجماع
        consensus_ratio = len(signal.supporting_signals) / total_indicators
        
        # نقاط الإجماع
        consensus_score = consensus_ratio * 100
        
        # مكافأة للإجماع العالي
        if consensus_ratio >= 0.8:
            consensus_score += 10
        elif consensus_ratio >= 0.7:
            consensus_score += 5
        
        # خصم للتضارب العالي
        if consensus_ratio < 0.5:
            consensus_score -= 15
        
        return max(0, min(100, consensus_score))

    def _evaluate_historical_accuracy(self, asset: str) -> float:
        """تقييم الدقة التاريخية"""
        if asset not in self.historical_performance:
            return 60  # نقاط افتراضية للأصول الجديدة
        
        performance = self.historical_performance[asset]
        
        # النقاط الأساسية من معدل النجاح
        base_score = performance.success_rate * 100
        
        # مكافأة للعينة الكبيرة
        if performance.total_signals >= 50:
            base_score += 10
        elif performance.total_signals >= 20:
            base_score += 5
        
        # تعديل بناء على الفرق بين الثقة المتوقعة والدقة الفعلية
        confidence_accuracy_diff = abs(performance.avg_confidence - performance.avg_actual_accuracy)
        if confidence_accuracy_diff < 10:
            base_score += 5  # دقة جيدة في التوقع
        elif confidence_accuracy_diff > 20:
            base_score -= 10  # دقة ضعيفة في التوقع
        
        return max(0, min(100, base_score))

    async def _evaluate_market_conditions(self, signal: CombinedSignal, market_data: Dict[str, Any] = None) -> float:
        """تقييم ظروف السوق"""
        try:
            # إذا لم تتوفر بيانات السوق، استخدم تقييم افتراضي
            if not market_data:
                return 70
            
            score = 70  # نقاط أساسية
            
            # تقييم التقلبات
            volatility = market_data.get('volatility', 0)
            if 0.5 <= volatility <= 2.0:  # تقلبات مثالية للسكالبينغ
                score += 15
            elif volatility > 3.0:  # تقلبات عالية جداً
                score -= 20
            elif volatility < 0.2:  # تقلبات منخفضة جداً
                score -= 10
            
            # تقييم الاتجاه
            trend_strength = market_data.get('trend_strength', 0)
            if trend_strength > 0.7:  # اتجاه قوي
                score += 10
            elif trend_strength < 0.3:  # اتجاه ضعيف
                score -= 5
            
            # تقييم الحجم
            volume_ratio = market_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:  # حجم عالي
                score += 5
            elif volume_ratio < 0.5:  # حجم منخفض
                score -= 10
            
            return max(0, min(100, score))
            
        except Exception as e:
            logger.error(f"خطأ في تقييم ظروف السوق: {str(e)}")
            return 70

    def _evaluate_timing_quality(self, signal: CombinedSignal) -> float:
        """تقييم جودة التوقيت"""
        current_time = datetime.now()
        signal_age = (current_time - signal.timestamp).total_seconds()
        
        # الإشارات الحديثة أفضل
        if signal_age < 30:  # أقل من 30 ثانية
            timing_score = 95
        elif signal_age < 60:  # أقل من دقيقة
            timing_score = 85
        elif signal_age < 120:  # أقل من دقيقتين
            timing_score = 70
        elif signal_age < 300:  # أقل من 5 دقائق
            timing_score = 50
        else:  # أكثر من 5 دقائق
            timing_score = 20
        
        # تقييم توقيت السوق (تجنب بداية ونهاية الساعة)
        minute = current_time.minute
        if 5 <= minute <= 55:  # توقيت جيد
            timing_score += 5
        else:  # بداية أو نهاية الساعة
            timing_score -= 10
        
        return max(0, min(100, timing_score))

    def _evaluate_volatility_alignment(self, signal: CombinedSignal) -> float:
        """تقييم توافق التقلبات"""
        # البحث عن مؤشرات ATR في الإشارات الداعمة
        atr_signals = [s for s in signal.supporting_signals if 'ATR' in s.indicator_name]
        
        if not atr_signals:
            return 60  # نقاط افتراضية
        
        # تحليل إشارات ATR
        atr_values = [s.value for s in atr_signals]
        avg_atr = statistics.mean(atr_values) if atr_values else 0
        
        # تقييم مستوى التقلبات
        if 0.0005 <= avg_atr <= 0.002:  # تقلبات مثالية للسكالبينغ
            return 90
        elif 0.0002 <= avg_atr <= 0.003:  # تقلبات جيدة
            return 75
        elif avg_atr > 0.005:  # تقلبات عالية جداً
            return 30
        else:  # تقلبات منخفضة جداً
            return 40

    def _determine_quality_grade(self, score: float) -> QualityGrade:
        """تحديد درجة الجودة"""
        if score >= 90:
            return QualityGrade.EXCELLENT
        elif score >= 80:
            return QualityGrade.VERY_GOOD
        elif score >= 70:
            return QualityGrade.GOOD
        elif score >= 60:
            return QualityGrade.FAIR
        elif score >= 50:
            return QualityGrade.POOR
        else:
            return QualityGrade.VERY_POOR

    def _analyze_strengths_weaknesses(self, factor_scores: Dict[QualityFactor, float]) -> Tuple[List[str], List[str]]:
        """تحليل نقاط القوة والضعف"""
        strengths = []
        weaknesses = []
        
        for factor, score in factor_scores.items():
            if score >= 80:
                strengths.append(f"{factor.value}: {score:.1f}%")
            elif score < 50:
                weaknesses.append(f"{factor.value}: {score:.1f}%")
        
        return strengths, weaknesses

    def _generate_recommendations(self, factor_scores: Dict[QualityFactor, float], signal: CombinedSignal) -> List[str]:
        """إنتاج التوصيات"""
        recommendations = []
        
        # توصيات بناء على نقاط الضعف
        if factor_scores[QualityFactor.INDICATOR_CONSENSUS] < 60:
            recommendations.append("انتظار المزيد من الإجماع بين المؤشرات")
        
        if factor_scores[QualityFactor.HISTORICAL_ACCURACY] < 50:
            recommendations.append("توخي الحذر - الأداء التاريخي ضعيف")
        
        if factor_scores[QualityFactor.MARKET_CONDITIONS] < 60:
            recommendations.append("ظروف السوق غير مثالية - تقليل حجم الصفقة")
        
        if factor_scores[QualityFactor.TIMING_QUALITY] < 70:
            recommendations.append("تحسين توقيت الدخول")
        
        # توصيات إيجابية
        if signal.confidence_score >= 85:
            recommendations.append("إشارة قوية - يمكن زيادة حجم الصفقة")
        
        if len(signal.supporting_signals) >= 8:
            recommendations.append("إجماع قوي بين المؤشرات")
        
        return recommendations

    def _determine_risk_level(self, score: float, signal: CombinedSignal) -> str:
        """تحديد مستوى المخاطر"""
        if score >= 80 and signal.confidence_score >= 80:
            return "LOW"
        elif score >= 60 and signal.confidence_score >= 60:
            return "MEDIUM"
        else:
            return "HIGH"

    def _calculate_confidence_adjustment(self, score: float) -> float:
        """حساب تعديل الثقة بناء على الجودة"""
        if score >= 90:
            return 1.1  # زيادة الثقة بـ 10%
        elif score >= 80:
            return 1.05  # زيادة الثقة بـ 5%
        elif score >= 70:
            return 1.0  # بدون تعديل
        elif score >= 60:
            return 0.95  # تقليل الثقة بـ 5%
        elif score >= 50:
            return 0.9  # تقليل الثقة بـ 10%
        else:
            return 0.8  # تقليل الثقة بـ 20%

    def update_historical_performance(self, asset: str, signal_success: bool, predicted_confidence: float, actual_accuracy: float):
        """تحديث الأداء التاريخي للأصل"""
        try:
            if asset not in self.historical_performance:
                self.historical_performance[asset] = HistoricalPerformance(
                    total_signals=0,
                    successful_signals=0,
                    failed_signals=0,
                    success_rate=0.0,
                    avg_confidence=0.0,
                    avg_actual_accuracy=0.0,
                    last_updated=datetime.now()
                )

            performance = self.historical_performance[asset]

            # تحديث العدادات
            performance.total_signals += 1
            if signal_success:
                performance.successful_signals += 1
            else:
                performance.failed_signals += 1

            # إعادة حساب المعدلات
            performance.success_rate = performance.successful_signals / performance.total_signals

            # تحديث متوسط الثقة والدقة (متوسط متحرك)
            alpha = 0.1  # معامل التنعيم
            performance.avg_confidence = (1 - alpha) * performance.avg_confidence + alpha * predicted_confidence
            performance.avg_actual_accuracy = (1 - alpha) * performance.avg_actual_accuracy + alpha * actual_accuracy

            performance.last_updated = datetime.now()

            logger.info(f"تم تحديث الأداء التاريخي لـ {asset}: معدل النجاح {performance.success_rate:.2%}")

        except Exception as e:
            logger.error(f"خطأ في تحديث الأداء التاريخي لـ {asset}: {str(e)}")

    def get_quality_summary(self, quality_metrics: QualityMetrics) -> Dict[str, Any]:
        """الحصول على ملخص تقييم الجودة"""
        if not quality_metrics:
            return {}

        return {
            'overall_score': quality_metrics.overall_score,
            'grade': quality_metrics.grade.value,
            'risk_level': quality_metrics.risk_level,
            'confidence_adjustment': quality_metrics.confidence_adjustment,
            'top_strengths': quality_metrics.strengths[:3],
            'main_weaknesses': quality_metrics.weaknesses[:3],
            'key_recommendations': quality_metrics.recommendations[:3],
            'factor_breakdown': {
                factor.value: round(score, 1)
                for factor, score in quality_metrics.factor_scores.items()
            }
        }

    def is_signal_tradeable(self, quality_metrics: QualityMetrics, min_score: float = 60.0) -> bool:
        """تحديد ما إذا كانت الإشارة قابلة للتداول"""
        if not quality_metrics:
            return False

        # التحقق من النقاط الإجمالية
        if quality_metrics.overall_score < min_score:
            return False

        # التحقق من مستوى المخاطر
        if quality_metrics.risk_level == "HIGH":
            return False

        # التحقق من العوامل الحرجة
        critical_factors = [
            QualityFactor.SIGNAL_STRENGTH,
            QualityFactor.CONFIDENCE_LEVEL,
            QualityFactor.INDICATOR_CONSENSUS
        ]

        for factor in critical_factors:
            if quality_metrics.factor_scores.get(factor, 0) < 50:
                return False

        return True

    def get_asset_performance_summary(self, asset: str) -> Dict[str, Any]:
        """الحصول على ملخص أداء الأصل"""
        if asset not in self.historical_performance:
            return {
                'asset': asset,
                'status': 'NEW_ASSET',
                'message': 'لا توجد بيانات تاريخية'
            }

        performance = self.historical_performance[asset]

        return {
            'asset': asset,
            'total_signals': performance.total_signals,
            'success_rate': round(performance.success_rate * 100, 2),
            'avg_confidence': round(performance.avg_confidence, 2),
            'avg_accuracy': round(performance.avg_actual_accuracy, 2),
            'confidence_accuracy_gap': round(abs(performance.avg_confidence - performance.avg_actual_accuracy), 2),
            'last_updated': performance.last_updated.isoformat(),
            'reliability': self._assess_reliability(performance)
        }

    def _assess_reliability(self, performance: HistoricalPerformance) -> str:
        """تقييم موثوقية الأصل"""
        if performance.total_signals < 10:
            return "INSUFFICIENT_DATA"
        elif performance.success_rate >= 0.75:
            return "HIGHLY_RELIABLE"
        elif performance.success_rate >= 0.65:
            return "RELIABLE"
        elif performance.success_rate >= 0.55:
            return "MODERATELY_RELIABLE"
        else:
            return "UNRELIABLE"

    async def batch_evaluate_signals(self, signals: List[CombinedSignal], market_data: Dict[str, Any] = None) -> Dict[str, QualityMetrics]:
        """تقييم مجموعة من الإشارات دفعة واحدة"""
        results = {}

        for signal in signals:
            try:
                quality = await self.evaluate_signal_quality(signal, market_data)
                if quality:
                    results[signal.asset] = quality
            except Exception as e:
                logger.error(f"خطأ في تقييم إشارة {signal.asset}: {str(e)}")

        logger.info(f"تم تقييم {len(results)} إشارة من أصل {len(signals)}")
        return results

    def get_evaluation_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التقييم"""
        total_evaluations = sum(len(evaluations) for evaluations in self.evaluation_history.values())

        if total_evaluations == 0:
            return {'message': 'لا توجد تقييمات'}

        # جمع جميع النقاط
        all_scores = []
        grade_counts = {}

        for asset_evaluations in self.evaluation_history.values():
            for evaluation in asset_evaluations:
                quality = evaluation['quality']
                all_scores.append(quality.overall_score)

                grade = quality.grade.value
                grade_counts[grade] = grade_counts.get(grade, 0) + 1

        return {
            'total_evaluations': total_evaluations,
            'avg_score': round(statistics.mean(all_scores), 2),
            'median_score': round(statistics.median(all_scores), 2),
            'min_score': round(min(all_scores), 2),
            'max_score': round(max(all_scores), 2),
            'grade_distribution': grade_counts,
            'assets_evaluated': len(self.evaluation_history)
        }

# إنشاء instance عام للاستخدام
signal_quality_evaluator = SignalQualityEvaluator()
