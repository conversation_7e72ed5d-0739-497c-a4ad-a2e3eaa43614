"""
نظام تقاطع المؤشرات الذكي - المرحلة الرابعة
Smart Crossover Analysis System for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors
from data_layer.technical_signals_engine import TechnicalSignal, CombinedSignal, SignalDirection, SignalStrength
from config.currency_pairs import CURRENCY_PAIRS_70

logger = scalping_logger.get_logger("smart_crossover_analyzer")

class CrossoverType(Enum):
    """نوع التقاطع"""
    GOLDEN_CROSS = "GOLDEN_CROSS"           # تقاطع ذهبي (متوسط قصير يعبر طويل صعوداً)
    DEATH_CROSS = "DEATH_CROSS"             # تقاطع الموت (متوسط قصير يعبر طويل هبوطاً)
    BULLISH_DIVERGENCE = "BULLISH_DIVERGENCE"  # تباعد صاعد
    BEARISH_DIVERGENCE = "BEARISH_DIVERGENCE"  # تباعد هابط
    MOMENTUM_CROSS = "MOMENTUM_CROSS"       # تقاطع الزخم
    OSCILLATOR_CROSS = "OSCILLATOR_CROSS"   # تقاطع المذبذبات
    PRICE_MA_CROSS = "PRICE_MA_CROSS"       # تقاطع السعر مع المتوسط
    SIGNAL_LINE_CROSS = "SIGNAL_LINE_CROSS" # تقاطع خط الإشارة

class CrossoverStrength(Enum):
    """قوة التقاطع"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5

@dataclass
class CrossoverEvent:
    """حدث تقاطع"""
    crossover_type: CrossoverType
    strength: CrossoverStrength
    confidence: float  # 0-100
    direction: SignalDirection
    timestamp: datetime
    
    # تفاصيل التقاطع
    indicator1_name: str
    indicator2_name: str
    indicator1_value: float
    indicator2_value: float
    
    # معلومات إضافية
    angle_of_cross: float  # زاوية التقاطع
    volume_confirmation: bool
    price_confirmation: bool
    reasoning: str
    supporting_data: Dict[str, Any]

@dataclass
class CrossoverAnalysis:
    """تحليل التقاطعات الشامل"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # أحداث التقاطع
    crossover_events: List[CrossoverEvent]
    
    # التقييم الإجمالي
    overall_direction: SignalDirection
    overall_strength: CrossoverStrength
    overall_confidence: float
    
    # إحصائيات
    total_crossovers: int
    bullish_crossovers: int
    bearish_crossovers: int
    strong_crossovers: int
    
    # توصيات
    is_tradeable: bool
    recommended_action: str
    risk_level: str
    recommendations: List[str]

class SmartCrossoverAnalyzer:
    """نظام تقاطع المؤشرات الذكي"""
    
    def __init__(self):
        self.crossover_patterns = self._initialize_crossover_patterns()
        self.strength_weights = self._initialize_strength_weights()
        self.min_confidence_threshold = 65.0
        self.crossover_history = {}  # تاريخ التقاطعات لكل أصل
        
        logger.info("تم تهيئة نظام تقاطع المؤشرات الذكي")

    def _initialize_crossover_patterns(self) -> Dict[str, Dict[str, Any]]:
        """تهيئة أنماط التقاطع المدعومة"""
        return {
            # تقاطعات المتوسطات المتحركة
            'EMA_5_EMA_10': {
                'type': CrossoverType.GOLDEN_CROSS,
                'weight': 0.20,
                'min_angle': 15,  # الحد الأدنى لزاوية التقاطع
                'lookback_periods': 5
            },
            'EMA_5_EMA_21': {
                'type': CrossoverType.GOLDEN_CROSS,
                'weight': 0.25,
                'min_angle': 10,
                'lookback_periods': 8
            },
            'EMA_10_EMA_21': {
                'type': CrossoverType.GOLDEN_CROSS,
                'weight': 0.18,
                'min_angle': 8,
                'lookback_periods': 10
            },
            
            # تقاطعات السعر مع المتوسطات
            'PRICE_EMA_5': {
                'type': CrossoverType.PRICE_MA_CROSS,
                'weight': 0.15,
                'min_angle': 20,
                'lookback_periods': 3
            },
            'PRICE_EMA_10': {
                'type': CrossoverType.PRICE_MA_CROSS,
                'weight': 0.12,
                'min_angle': 15,
                'lookback_periods': 5
            },
            
            # تقاطعات MACD
            'MACD_SIGNAL': {
                'type': CrossoverType.SIGNAL_LINE_CROSS,
                'weight': 0.22,
                'min_angle': 25,
                'lookback_periods': 3
            },
            'MACD_ZERO': {
                'type': CrossoverType.MOMENTUM_CROSS,
                'weight': 0.20,
                'min_angle': 30,
                'lookback_periods': 2
            },
            
            # تقاطعات المذبذبات
            'RSI_LEVELS': {
                'type': CrossoverType.OSCILLATOR_CROSS,
                'weight': 0.10,
                'min_angle': 35,
                'lookback_periods': 2
            },
            'STOCHASTIC_LEVELS': {
                'type': CrossoverType.OSCILLATOR_CROSS,
                'weight': 0.08,
                'min_angle': 40,
                'lookback_periods': 2
            }
        }

    def _initialize_strength_weights(self) -> Dict[CrossoverType, float]:
        """تهيئة أوزان قوة التقاطع"""
        return {
            CrossoverType.GOLDEN_CROSS: 1.0,
            CrossoverType.DEATH_CROSS: 1.0,
            CrossoverType.SIGNAL_LINE_CROSS: 0.8,
            CrossoverType.MOMENTUM_CROSS: 0.9,
            CrossoverType.PRICE_MA_CROSS: 0.7,
            CrossoverType.OSCILLATOR_CROSS: 0.6,
            CrossoverType.BULLISH_DIVERGENCE: 0.85,
            CrossoverType.BEARISH_DIVERGENCE: 0.85
        }

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_crossovers(self, asset: str, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> Optional[CrossoverAnalysis]:
        """تحليل تقاطعات المؤشرات"""
        start_time = time.time()
        
        try:
            if not signals or len(signals) < 3:
                logger.warning(f"إشارات غير كافية لتحليل التقاطعات لـ {asset}")
                return None
            
            if not candles_data or len(candles_data) < 20:
                logger.warning(f"بيانات شموع غير كافية لتحليل التقاطعات لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل التقاطعات لـ {asset} مع {len(signals)} إشارة")
            
            # تحليل أنواع التقاطعات المختلفة
            crossover_events = []
            
            # 1. تحليل تقاطعات المتوسطات المتحركة
            ma_crossovers = await self._analyze_moving_average_crossovers(signals, candles_data)
            crossover_events.extend(ma_crossovers)
            
            # 2. تحليل تقاطعات MACD
            macd_crossovers = await self._analyze_macd_crossovers(signals, candles_data)
            crossover_events.extend(macd_crossovers)
            
            # 3. تحليل تقاطعات المذبذبات
            oscillator_crossovers = await self._analyze_oscillator_crossovers(signals, candles_data)
            crossover_events.extend(oscillator_crossovers)
            
            # 4. تحليل تقاطعات السعر مع المؤشرات
            price_crossovers = await self._analyze_price_crossovers(signals, candles_data)
            crossover_events.extend(price_crossovers)
            
            # 5. تحليل التباعدات
            divergence_events = await self._analyze_divergences(signals, candles_data)
            crossover_events.extend(divergence_events)
            
            # تقييم التقاطعات الإجمالي
            overall_analysis = self._evaluate_overall_crossovers(crossover_events)
            
            # حساب الإحصائيات
            stats = self._calculate_crossover_statistics(crossover_events)
            
            # إنتاج التوصيات
            recommendations = self._generate_crossover_recommendations(crossover_events, overall_analysis)
            
            processing_time = (time.time() - start_time) * 1000
            
            analysis = CrossoverAnalysis(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                crossover_events=crossover_events,
                overall_direction=overall_analysis['direction'],
                overall_strength=overall_analysis['strength'],
                overall_confidence=overall_analysis['confidence'],
                total_crossovers=stats['total'],
                bullish_crossovers=stats['bullish'],
                bearish_crossovers=stats['bearish'],
                strong_crossovers=stats['strong'],
                is_tradeable=overall_analysis['is_tradeable'],
                recommended_action=overall_analysis['action'],
                risk_level=overall_analysis['risk_level'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.crossover_history:
                self.crossover_history[asset] = []
            
            self.crossover_history[asset].append(analysis)
            
            # الاحتفاظ بآخر 50 تحليل فقط
            if len(self.crossover_history[asset]) > 50:
                self.crossover_history[asset] = self.crossover_history[asset][-50:]
            
            logger.info(f"تم إكمال تحليل التقاطعات لـ {asset}: {len(crossover_events)} تقاطع، الاتجاه: {overall_analysis['direction'].value}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التقاطعات لـ {asset}: {str(e)}")
            return None

    async def _analyze_moving_average_crossovers(self, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> List[CrossoverEvent]:
        """تحليل تقاطعات المتوسطات المتحركة"""
        try:
            crossovers = []

            # البحث عن إشارات المتوسطات المتحركة
            ma_signals = {s.indicator_name: s for s in signals if 'EMA' in s.indicator_name or 'SMA' in s.indicator_name}

            if len(ma_signals) < 2:
                return crossovers

            # تحليل تقاطعات EMA
            ema_pairs = [
                ('EMA_5', 'EMA_10'),
                ('EMA_5', 'EMA_21'),
                ('EMA_10', 'EMA_21')
            ]

            for fast_ma, slow_ma in ema_pairs:
                if fast_ma in ma_signals and slow_ma in ma_signals:
                    crossover = await self._detect_ma_crossover(
                        ma_signals[fast_ma], ma_signals[slow_ma], candles_data
                    )
                    if crossover:
                        crossovers.append(crossover)

            return crossovers

        except Exception as e:
            logger.error(f"خطأ في تحليل تقاطعات المتوسطات: {str(e)}")
            return []

    async def _detect_ma_crossover(self, fast_signal: TechnicalSignal, slow_signal: TechnicalSignal, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع بين متوسطين متحركين"""
        try:
            fast_value = fast_signal.value
            slow_value = slow_signal.value

            # الحصول على القيم السابقة من supporting_data
            fast_prev = fast_signal.supporting_data.get('previous_value', fast_value)
            slow_prev = slow_signal.supporting_data.get('previous_value', slow_value)

            # التحقق من حدوث تقاطع
            crossover_occurred = False
            direction = SignalDirection.NEUTRAL

            # تقاطع صاعد (Golden Cross)
            if fast_prev <= slow_prev and fast_value > slow_value:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                crossover_type = CrossoverType.GOLDEN_CROSS
                reasoning = f"تقاطع ذهبي: {fast_signal.indicator_name} عبر {slow_signal.indicator_name} صعوداً"

            # تقاطع هابط (Death Cross)
            elif fast_prev >= slow_prev and fast_value < slow_value:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                crossover_type = CrossoverType.DEATH_CROSS
                reasoning = f"تقاطع الموت: {fast_signal.indicator_name} عبر {slow_signal.indicator_name} هبوطاً"

            if not crossover_occurred:
                return None

            # حساب زاوية التقاطع
            angle = self._calculate_crossover_angle(fast_value, fast_prev, slow_value, slow_prev)

            # تحديد قوة التقاطع
            strength = self._determine_crossover_strength(angle, fast_signal.confidence, slow_signal.confidence)

            # حساب الثقة
            confidence = self._calculate_crossover_confidence(fast_signal, slow_signal, angle, candles_data)

            # التحقق من تأكيد السعر والحجم
            price_confirmation = self._check_price_confirmation(direction, candles_data)
            volume_confirmation = self._check_volume_confirmation(candles_data)

            return CrossoverEvent(
                crossover_type=crossover_type,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name=fast_signal.indicator_name,
                indicator2_name=slow_signal.indicator_name,
                indicator1_value=fast_value,
                indicator2_value=slow_value,
                angle_of_cross=angle,
                volume_confirmation=volume_confirmation,
                price_confirmation=price_confirmation,
                reasoning=reasoning,
                supporting_data={
                    'fast_prev': fast_prev,
                    'slow_prev': slow_prev,
                    'fast_confidence': fast_signal.confidence,
                    'slow_confidence': slow_signal.confidence
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع المتوسطات: {str(e)}")
            return None

    async def _analyze_macd_crossovers(self, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> List[CrossoverEvent]:
        """تحليل تقاطعات MACD"""
        try:
            crossovers = []

            # البحث عن إشارة MACD
            macd_signal = next((s for s in signals if s.indicator_name == 'MACD'), None)
            if not macd_signal:
                return crossovers

            # تحليل تقاطع خط الصفر
            zero_line_crossover = await self._detect_macd_zero_crossover(macd_signal, candles_data)
            if zero_line_crossover:
                crossovers.append(zero_line_crossover)

            # تحليل تقاطع خط الإشارة (إذا توفرت البيانات)
            signal_line_crossover = await self._detect_macd_signal_crossover(macd_signal, candles_data)
            if signal_line_crossover:
                crossovers.append(signal_line_crossover)

            return crossovers

        except Exception as e:
            logger.error(f"خطأ في تحليل تقاطعات MACD: {str(e)}")
            return []

    async def _detect_macd_zero_crossover(self, macd_signal: TechnicalSignal, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع MACD مع خط الصفر"""
        try:
            current_macd = macd_signal.value
            previous_macd = macd_signal.supporting_data.get('previous_value', current_macd)

            # التحقق من تقاطع خط الصفر
            crossover_occurred = False
            direction = SignalDirection.NEUTRAL

            if previous_macd <= 0 and current_macd > 0:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                reasoning = "MACD عبر خط الصفر صعوداً"
            elif previous_macd >= 0 and current_macd < 0:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                reasoning = "MACD عبر خط الصفر هبوطاً"

            if not crossover_occurred:
                return None

            # حساب زاوية التقاطع
            angle = abs(current_macd - previous_macd) * 1000  # تكبير للحصول على زاوية مناسبة

            # تحديد القوة
            if angle > 50:
                strength = CrossoverStrength.VERY_STRONG
            elif angle > 30:
                strength = CrossoverStrength.STRONG
            elif angle > 15:
                strength = CrossoverStrength.MODERATE
            else:
                strength = CrossoverStrength.WEAK

            # حساب الثقة
            confidence = min(95, 70 + angle)

            return CrossoverEvent(
                crossover_type=CrossoverType.MOMENTUM_CROSS,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name='MACD',
                indicator2_name='ZERO_LINE',
                indicator1_value=current_macd,
                indicator2_value=0.0,
                angle_of_cross=angle,
                volume_confirmation=self._check_volume_confirmation(candles_data),
                price_confirmation=self._check_price_confirmation(direction, candles_data),
                reasoning=reasoning,
                supporting_data={
                    'previous_macd': previous_macd,
                    'macd_confidence': macd_signal.confidence
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع MACD مع الصفر: {str(e)}")
            return None

    async def _detect_macd_signal_crossover(self, macd_signal: TechnicalSignal, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع MACD مع خط الإشارة"""
        try:
            # هذه الطريقة تحتاج لبيانات إضافية من مؤشر MACD
            # سنتركها للتطوير المستقبلي عندما تتوفر بيانات خط الإشارة
            return None

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع MACD مع خط الإشارة: {str(e)}")
            return None

    async def _analyze_oscillator_crossovers(self, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> List[CrossoverEvent]:
        """تحليل تقاطعات المذبذبات"""
        try:
            crossovers = []

            # تحليل RSI
            rsi_signals = [s for s in signals if 'RSI' in s.indicator_name]
            for rsi_signal in rsi_signals:
                rsi_crossover = await self._detect_rsi_level_crossover(rsi_signal, candles_data)
                if rsi_crossover:
                    crossovers.append(rsi_crossover)

            # تحليل Stochastic
            stoch_signal = next((s for s in signals if s.indicator_name == 'STOCHASTIC'), None)
            if stoch_signal:
                stoch_crossover = await self._detect_stochastic_crossover(stoch_signal, candles_data)
                if stoch_crossover:
                    crossovers.append(stoch_crossover)

            return crossovers

        except Exception as e:
            logger.error(f"خطأ في تحليل تقاطعات المذبذبات: {str(e)}")
            return []

    async def _detect_rsi_level_crossover(self, rsi_signal: TechnicalSignal, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع RSI مع المستويات الحرجة"""
        try:
            current_rsi = rsi_signal.value
            previous_rsi = rsi_signal.supporting_data.get('previous_value', current_rsi)

            crossover_occurred = False
            direction = SignalDirection.NEUTRAL
            level_crossed = 0

            # تقاطع مستوى 30 (خروج من تشبع بيعي)
            if previous_rsi <= 30 and current_rsi > 30:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                level_crossed = 30
                reasoning = f"{rsi_signal.indicator_name} خرج من منطقة التشبع البيعي"

            # تقاطع مستوى 70 (دخول في تشبع شرائي)
            elif previous_rsi < 70 and current_rsi >= 70:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                level_crossed = 70
                reasoning = f"{rsi_signal.indicator_name} دخل منطقة التشبع الشرائي"

            # تقاطع مستوى 50 (خط الوسط)
            elif previous_rsi <= 50 and current_rsi > 50:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                level_crossed = 50
                reasoning = f"{rsi_signal.indicator_name} عبر خط الوسط صعوداً"

            elif previous_rsi >= 50 and current_rsi < 50:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                level_crossed = 50
                reasoning = f"{rsi_signal.indicator_name} عبر خط الوسط هبوطاً"

            if not crossover_occurred:
                return None

            # تحديد القوة بناء على المستوى المعبور
            if level_crossed in [30, 70]:
                strength = CrossoverStrength.STRONG
                confidence = 80
            else:  # مستوى 50
                strength = CrossoverStrength.MODERATE
                confidence = 60

            # حساب زاوية التقاطع
            angle = abs(current_rsi - previous_rsi) * 2

            return CrossoverEvent(
                crossover_type=CrossoverType.OSCILLATOR_CROSS,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name=rsi_signal.indicator_name,
                indicator2_name=f'LEVEL_{level_crossed}',
                indicator1_value=current_rsi,
                indicator2_value=level_crossed,
                angle_of_cross=angle,
                volume_confirmation=self._check_volume_confirmation(candles_data),
                price_confirmation=self._check_price_confirmation(direction, candles_data),
                reasoning=reasoning,
                supporting_data={
                    'previous_rsi': previous_rsi,
                    'level_crossed': level_crossed,
                    'rsi_confidence': rsi_signal.confidence
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع RSI: {str(e)}")
            return None

    async def _detect_stochastic_crossover(self, stoch_signal: TechnicalSignal, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع Stochastic مع المستويات الحرجة"""
        try:
            current_stoch = stoch_signal.value
            previous_stoch = stoch_signal.supporting_data.get('previous_value', current_stoch)

            crossover_occurred = False
            direction = SignalDirection.NEUTRAL
            level_crossed = 0

            # تقاطع مستوى 20 (خروج من تشبع بيعي)
            if previous_stoch <= 20 and current_stoch > 20:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                level_crossed = 20
                reasoning = "Stochastic خرج من منطقة التشبع البيعي"

            # تقاطع مستوى 80 (دخول في تشبع شرائي)
            elif previous_stoch < 80 and current_stoch >= 80:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                level_crossed = 80
                reasoning = "Stochastic دخل منطقة التشبع الشرائي"

            if not crossover_occurred:
                return None

            strength = CrossoverStrength.MODERATE
            confidence = 70
            angle = abs(current_stoch - previous_stoch) * 1.5

            return CrossoverEvent(
                crossover_type=CrossoverType.OSCILLATOR_CROSS,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name='STOCHASTIC',
                indicator2_name=f'LEVEL_{level_crossed}',
                indicator1_value=current_stoch,
                indicator2_value=level_crossed,
                angle_of_cross=angle,
                volume_confirmation=self._check_volume_confirmation(candles_data),
                price_confirmation=self._check_price_confirmation(direction, candles_data),
                reasoning=reasoning,
                supporting_data={
                    'previous_stoch': previous_stoch,
                    'level_crossed': level_crossed,
                    'stoch_confidence': stoch_signal.confidence
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع Stochastic: {str(e)}")
            return None

    async def _analyze_price_crossovers(self, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> List[CrossoverEvent]:
        """تحليل تقاطعات السعر مع المؤشرات"""
        try:
            crossovers = []

            if not candles_data:
                return crossovers

            current_price = candles_data[-1]['close']
            previous_price = candles_data[-2]['close'] if len(candles_data) > 1 else current_price

            # تحليل تقاطع السعر مع المتوسطات المتحركة
            ma_signals = [s for s in signals if 'EMA' in s.indicator_name or 'SMA' in s.indicator_name]

            for ma_signal in ma_signals:
                crossover = await self._detect_price_ma_crossover(ma_signal, current_price, previous_price, candles_data)
                if crossover:
                    crossovers.append(crossover)

            return crossovers

        except Exception as e:
            logger.error(f"خطأ في تحليل تقاطعات السعر: {str(e)}")
            return []

    async def _detect_price_ma_crossover(self, ma_signal: TechnicalSignal, current_price: float, previous_price: float, candles_data: List[Dict[str, Any]]) -> Optional[CrossoverEvent]:
        """كشف تقاطع السعر مع المتوسط المتحرك"""
        try:
            current_ma = ma_signal.value
            previous_ma = ma_signal.supporting_data.get('previous_value', current_ma)

            crossover_occurred = False
            direction = SignalDirection.NEUTRAL

            # تقاطع السعر مع المتوسط صعوداً
            if previous_price <= previous_ma and current_price > current_ma:
                crossover_occurred = True
                direction = SignalDirection.BULLISH
                reasoning = f"السعر عبر {ma_signal.indicator_name} صعوداً"

            # تقاطع السعر مع المتوسط هبوطاً
            elif previous_price >= previous_ma and current_price < current_ma:
                crossover_occurred = True
                direction = SignalDirection.BEARISH
                reasoning = f"السعر عبر {ma_signal.indicator_name} هبوطاً"

            if not crossover_occurred:
                return None

            # حساب زاوية التقاطع
            price_angle = abs(current_price - previous_price) / previous_price * 10000
            ma_angle = abs(current_ma - previous_ma) / previous_ma * 10000
            angle = (price_angle + ma_angle) / 2

            # تحديد القوة
            strength = self._determine_crossover_strength(angle, ma_signal.confidence, 80)

            # حساب الثقة
            confidence = min(90, 60 + angle/2)

            return CrossoverEvent(
                crossover_type=CrossoverType.PRICE_MA_CROSS,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name='PRICE',
                indicator2_name=ma_signal.indicator_name,
                indicator1_value=current_price,
                indicator2_value=current_ma,
                angle_of_cross=angle,
                volume_confirmation=self._check_volume_confirmation(candles_data),
                price_confirmation=True,  # السعر هو المؤكد بحد ذاته
                reasoning=reasoning,
                supporting_data={
                    'previous_price': previous_price,
                    'previous_ma': previous_ma,
                    'ma_confidence': ma_signal.confidence
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف تقاطع السعر مع المتوسط: {str(e)}")
            return None

    async def _analyze_divergences(self, signals: List[TechnicalSignal], candles_data: List[Dict[str, Any]]) -> List[CrossoverEvent]:
        """تحليل التباعدات بين السعر والمؤشرات"""
        try:
            divergences = []

            if len(candles_data) < 10:
                return divergences

            # تحليل التباعد مع RSI
            rsi_signals = [s for s in signals if 'RSI' in s.indicator_name]
            for rsi_signal in rsi_signals:
                divergence = await self._detect_price_indicator_divergence(rsi_signal, candles_data, 'RSI')
                if divergence:
                    divergences.append(divergence)

            # تحليل التباعد مع MACD
            macd_signal = next((s for s in signals if s.indicator_name == 'MACD'), None)
            if macd_signal:
                divergence = await self._detect_price_indicator_divergence(macd_signal, candles_data, 'MACD')
                if divergence:
                    divergences.append(divergence)

            return divergences

        except Exception as e:
            logger.error(f"خطأ في تحليل التباعدات: {str(e)}")
            return []

    async def _detect_price_indicator_divergence(self, indicator_signal: TechnicalSignal, candles_data: List[Dict[str, Any]], indicator_type: str) -> Optional[CrossoverEvent]:
        """كشف التباعد بين السعر والمؤشر"""
        try:
            # هذا تحليل مبسط للتباعد
            # يحتاج لتطوير أكثر تعقيداً في المستقبل

            if len(candles_data) < 5:
                return None

            # مقارنة اتجاه السعر مع اتجاه المؤشر
            recent_prices = [c['close'] for c in candles_data[-5:]]
            price_trend = recent_prices[-1] - recent_prices[0]

            current_indicator = indicator_signal.value
            previous_indicator = indicator_signal.supporting_data.get('previous_value', current_indicator)
            indicator_trend = current_indicator - previous_indicator

            # كشف التباعد
            divergence_detected = False
            direction = SignalDirection.NEUTRAL

            # تباعد صاعد: السعر ينخفض والمؤشر يرتفع
            if price_trend < 0 and indicator_trend > 0:
                divergence_detected = True
                direction = SignalDirection.BULLISH
                crossover_type = CrossoverType.BULLISH_DIVERGENCE
                reasoning = f"تباعد صاعد بين السعر و{indicator_type}"

            # تباعد هابط: السعر يرتفع والمؤشر ينخفض
            elif price_trend > 0 and indicator_trend < 0:
                divergence_detected = True
                direction = SignalDirection.BEARISH
                crossover_type = CrossoverType.BEARISH_DIVERGENCE
                reasoning = f"تباعد هابط بين السعر و{indicator_type}"

            if not divergence_detected:
                return None

            # تحديد القوة والثقة
            strength = CrossoverStrength.MODERATE
            confidence = 65

            return CrossoverEvent(
                crossover_type=crossover_type,
                strength=strength,
                confidence=confidence,
                direction=direction,
                timestamp=datetime.now(),
                indicator1_name='PRICE',
                indicator2_name=indicator_signal.indicator_name,
                indicator1_value=recent_prices[-1],
                indicator2_value=current_indicator,
                angle_of_cross=abs(price_trend) + abs(indicator_trend),
                volume_confirmation=self._check_volume_confirmation(candles_data),
                price_confirmation=True,
                reasoning=reasoning,
                supporting_data={
                    'price_trend': price_trend,
                    'indicator_trend': indicator_trend,
                    'lookback_periods': 5
                }
            )

        except Exception as e:
            logger.error(f"خطأ في كشف التباعد: {str(e)}")
            return None

    def _calculate_crossover_angle(self, value1_current: float, value1_prev: float, value2_current: float, value2_prev: float) -> float:
        """حساب زاوية التقاطع"""
        try:
            # حساب الميل لكل خط
            slope1 = value1_current - value1_prev
            slope2 = value2_current - value2_prev

            # حساب الفرق في الميل (زاوية التقاطع)
            angle = abs(slope1 - slope2) * 1000  # تكبير للحصول على قيمة مناسبة

            return min(90, angle)  # الحد الأقصى 90 درجة

        except Exception as e:
            logger.error(f"خطأ في حساب زاوية التقاطع: {str(e)}")
            return 0

    def _determine_crossover_strength(self, angle: float, confidence1: float, confidence2: float) -> CrossoverStrength:
        """تحديد قوة التقاطع"""
        try:
            # حساب النقاط المركبة
            avg_confidence = (confidence1 + confidence2) / 2
            composite_score = (angle * 0.6) + (avg_confidence * 0.4)

            if composite_score >= 80:
                return CrossoverStrength.VERY_STRONG
            elif composite_score >= 65:
                return CrossoverStrength.STRONG
            elif composite_score >= 50:
                return CrossoverStrength.MODERATE
            elif composite_score >= 35:
                return CrossoverStrength.WEAK
            else:
                return CrossoverStrength.VERY_WEAK

        except Exception as e:
            logger.error(f"خطأ في تحديد قوة التقاطع: {str(e)}")
            return CrossoverStrength.WEAK

    def _calculate_crossover_confidence(self, signal1: TechnicalSignal, signal2: TechnicalSignal, angle: float, candles_data: List[Dict[str, Any]]) -> float:
        """حساب ثقة التقاطع"""
        try:
            # النقاط الأساسية من متوسط ثقة الإشارات
            base_confidence = (signal1.confidence + signal2.confidence) / 2

            # مكافأة للزاوية الحادة
            angle_bonus = min(20, angle / 2)

            # مكافأة لتأكيد الحجم
            volume_bonus = 5 if self._check_volume_confirmation(candles_data) else 0

            # مكافأة لقوة الإشارات
            strength_bonus = 0
            if signal1.strength.value >= 4:  # STRONG أو VERY_STRONG
                strength_bonus += 5
            if signal2.strength.value >= 4:
                strength_bonus += 5

            total_confidence = base_confidence + angle_bonus + volume_bonus + strength_bonus

            return min(95, max(30, total_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة التقاطع: {str(e)}")
            return 50

    def _check_price_confirmation(self, direction: SignalDirection, candles_data: List[Dict[str, Any]]) -> bool:
        """التحقق من تأكيد السعر للتقاطع"""
        try:
            if len(candles_data) < 3:
                return False

            recent_candles = candles_data[-3:]
            price_trend = recent_candles[-1]['close'] - recent_candles[0]['close']

            if direction == SignalDirection.BULLISH and price_trend > 0:
                return True
            elif direction == SignalDirection.BEARISH and price_trend < 0:
                return True

            return False

        except Exception as e:
            logger.error(f"خطأ في فحص تأكيد السعر: {str(e)}")
            return False

    def _check_volume_confirmation(self, candles_data: List[Dict[str, Any]]) -> bool:
        """التحقق من تأكيد الحجم للتقاطع"""
        try:
            if len(candles_data) < 5:
                return False

            # مقارنة الحجم الحالي مع المتوسط
            recent_volumes = [c.get('volume', 0) for c in candles_data[-5:]]
            if not any(recent_volumes):  # لا توجد بيانات حجم
                return False

            current_volume = recent_volumes[-1]
            avg_volume = statistics.mean(recent_volumes[:-1])

            # تأكيد إذا كان الحجم أعلى من المتوسط بـ 20%
            return current_volume > avg_volume * 1.2

        except Exception as e:
            logger.error(f"خطأ في فحص تأكيد الحجم: {str(e)}")
            return False

    def _evaluate_overall_crossovers(self, crossover_events: List[CrossoverEvent]) -> Dict[str, Any]:
        """تقييم التقاطعات الإجمالي"""
        try:
            if not crossover_events:
                return {
                    'direction': SignalDirection.NEUTRAL,
                    'strength': CrossoverStrength.VERY_WEAK,
                    'confidence': 0,
                    'is_tradeable': False,
                    'action': 'WAIT',
                    'risk_level': 'HIGH'
                }

            # حساب الأوزان المرجحة للاتجاهات
            bullish_weight = 0
            bearish_weight = 0

            for event in crossover_events:
                weight = self.strength_weights.get(event.crossover_type, 0.5)
                weighted_score = weight * event.confidence * event.strength.value

                if event.direction == SignalDirection.BULLISH:
                    bullish_weight += weighted_score
                elif event.direction == SignalDirection.BEARISH:
                    bearish_weight += weighted_score

            # تحديد الاتجاه الإجمالي
            if bullish_weight > bearish_weight * 1.2:  # تحيز للصعود
                overall_direction = SignalDirection.BULLISH
                confidence_ratio = bullish_weight / (bullish_weight + bearish_weight)
            elif bearish_weight > bullish_weight * 1.2:  # تحيز للهبوط
                overall_direction = SignalDirection.BEARISH
                confidence_ratio = bearish_weight / (bullish_weight + bearish_weight)
            else:
                overall_direction = SignalDirection.NEUTRAL
                confidence_ratio = 0.5

            # حساب الثقة الإجمالية
            overall_confidence = confidence_ratio * 100

            # تحديد القوة الإجمالية
            strong_events = [e for e in crossover_events if e.strength.value >= 4]
            if len(strong_events) >= 3:
                overall_strength = CrossoverStrength.VERY_STRONG
            elif len(strong_events) >= 2:
                overall_strength = CrossoverStrength.STRONG
            elif len(strong_events) >= 1:
                overall_strength = CrossoverStrength.MODERATE
            else:
                overall_strength = CrossoverStrength.WEAK

            # تحديد قابلية التداول
            is_tradeable = (
                overall_confidence >= self.min_confidence_threshold and
                overall_strength.value >= 3 and
                len(crossover_events) >= 2 and
                overall_direction != SignalDirection.NEUTRAL
            )

            # تحديد الإجراء المطلوب
            if is_tradeable:
                if overall_direction == SignalDirection.BULLISH:
                    action = 'BUY'
                else:
                    action = 'SELL'
            else:
                action = 'WAIT'

            # تحديد مستوى المخاطر
            if overall_confidence >= 80 and overall_strength.value >= 4:
                risk_level = 'LOW'
            elif overall_confidence >= 65 and overall_strength.value >= 3:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'HIGH'

            return {
                'direction': overall_direction,
                'strength': overall_strength,
                'confidence': round(overall_confidence, 2),
                'is_tradeable': is_tradeable,
                'action': action,
                'risk_level': risk_level
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم التقاطعات الإجمالي: {str(e)}")
            return {
                'direction': SignalDirection.NEUTRAL,
                'strength': CrossoverStrength.VERY_WEAK,
                'confidence': 0,
                'is_tradeable': False,
                'action': 'WAIT',
                'risk_level': 'HIGH'
            }

    def _calculate_crossover_statistics(self, crossover_events: List[CrossoverEvent]) -> Dict[str, int]:
        """حساب إحصائيات التقاطعات"""
        try:
            total = len(crossover_events)
            bullish = len([e for e in crossover_events if e.direction == SignalDirection.BULLISH])
            bearish = len([e for e in crossover_events if e.direction == SignalDirection.BEARISH])
            strong = len([e for e in crossover_events if e.strength.value >= 4])

            return {
                'total': total,
                'bullish': bullish,
                'bearish': bearish,
                'strong': strong
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات التقاطعات: {str(e)}")
            return {'total': 0, 'bullish': 0, 'bearish': 0, 'strong': 0}

    def _generate_crossover_recommendations(self, crossover_events: List[CrossoverEvent], overall_analysis: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات التقاطعات"""
        try:
            recommendations = []

            if not crossover_events:
                recommendations.append("لا توجد تقاطعات مؤشرات واضحة")
                return recommendations

            # توصيات بناء على عدد التقاطعات
            if len(crossover_events) >= 4:
                recommendations.append("تقاطعات متعددة تؤكد الإشارة")
            elif len(crossover_events) == 1:
                recommendations.append("تقاطع واحد فقط - انتظار المزيد من التأكيد")

            # توصيات بناء على القوة
            strong_crossovers = [e for e in crossover_events if e.strength.value >= 4]
            if len(strong_crossovers) >= 2:
                recommendations.append("تقاطعات قوية متعددة - إشارة موثوقة")
            elif not strong_crossovers:
                recommendations.append("تقاطعات ضعيفة - توخي الحذر")

            # توصيات بناء على التأكيد
            confirmed_crossovers = [e for e in crossover_events if e.price_confirmation and e.volume_confirmation]
            if len(confirmed_crossovers) >= 2:
                recommendations.append("تأكيد قوي من السعر والحجم")
            elif not confirmed_crossovers:
                recommendations.append("نقص في تأكيد السعر أو الحجم")

            # توصيات بناء على نوع التقاطع
            golden_crosses = [e for e in crossover_events if e.crossover_type == CrossoverType.GOLDEN_CROSS]
            death_crosses = [e for e in crossover_events if e.crossover_type == CrossoverType.DEATH_CROSS]

            if len(golden_crosses) >= 2:
                recommendations.append("تقاطعات ذهبية متعددة - اتجاه صاعد قوي")
            elif len(death_crosses) >= 2:
                recommendations.append("تقاطعات موت متعددة - اتجاه هابط قوي")

            # توصيات بناء على المخاطر
            if overall_analysis['risk_level'] == 'HIGH':
                recommendations.append("مستوى مخاطر عالي - تقليل حجم الصفقة")
            elif overall_analysis['risk_level'] == 'LOW':
                recommendations.append("مستوى مخاطر منخفض - يمكن زيادة حجم الصفقة")

            return recommendations[:5]  # أقصى 5 توصيات

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات التقاطعات: {str(e)}")
            return []

    def get_crossover_summary(self, analysis: CrossoverAnalysis) -> Dict[str, Any]:
        """الحصول على ملخص تحليل التقاطعات"""
        if not analysis:
            return {}

        return {
            'asset': analysis.asset,
            'total_crossovers': analysis.total_crossovers,
            'overall_direction': analysis.overall_direction.value,
            'overall_strength': analysis.overall_strength.value,
            'overall_confidence': analysis.overall_confidence,
            'is_tradeable': analysis.is_tradeable,
            'recommended_action': analysis.recommended_action,
            'risk_level': analysis.risk_level,
            'processing_time_ms': analysis.processing_time_ms,
            'key_crossovers': [
                {
                    'type': event.crossover_type.value,
                    'direction': event.direction.value,
                    'strength': event.strength.value,
                    'confidence': event.confidence,
                    'indicators': f"{event.indicator1_name} x {event.indicator2_name}"
                }
                for event in analysis.crossover_events[:3]  # أهم 3 تقاطعات
            ],
            'top_recommendations': analysis.recommendations[:3]
        }

    def is_crossover_tradeable(self, analysis: CrossoverAnalysis, min_confidence: float = None) -> bool:
        """تحديد ما إذا كانت التقاطعات قابلة للتداول"""
        if not analysis:
            return False

        min_conf = min_confidence or self.min_confidence_threshold

        return (
            analysis.is_tradeable and
            analysis.overall_confidence >= min_conf and
            analysis.overall_strength.value >= 3 and
            analysis.total_crossovers >= 2 and
            analysis.overall_direction != SignalDirection.NEUTRAL
        )

    def get_crossover_history(self, asset: str, limit: int = 10) -> List[CrossoverAnalysis]:
        """الحصول على تاريخ التقاطعات لأصل معين"""
        if asset not in self.crossover_history:
            return []

        return self.crossover_history[asset][-limit:]

    def get_crossover_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات عامة للتقاطعات"""
        try:
            total_analyses = sum(len(history) for history in self.crossover_history.values())
            total_assets = len(self.crossover_history)

            if total_analyses == 0:
                return {
                    'total_analyses': 0,
                    'total_assets': 0,
                    'avg_crossovers_per_analysis': 0,
                    'most_active_asset': None
                }

            # حساب متوسط التقاطعات لكل تحليل
            total_crossovers = sum(
                analysis.total_crossovers
                for history in self.crossover_history.values()
                for analysis in history
            )
            avg_crossovers = total_crossovers / total_analyses

            # العثور على الأصل الأكثر نشاطاً
            most_active_asset = max(
                self.crossover_history.keys(),
                key=lambda asset: len(self.crossover_history[asset])
            ) if self.crossover_history else None

            return {
                'total_analyses': total_analyses,
                'total_assets': total_assets,
                'avg_crossovers_per_analysis': round(avg_crossovers, 2),
                'most_active_asset': most_active_asset,
                'supported_crossover_types': len(self.crossover_patterns),
                'min_confidence_threshold': self.min_confidence_threshold
            }

        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات التقاطعات: {str(e)}")
            return {}

# إنشاء instance عام للاستخدام
smart_crossover_analyzer = SmartCrossoverAnalyzer()
