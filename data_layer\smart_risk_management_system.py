#!/usr/bin/env python3
"""
نظام إدارة المخاطر الذكي - Smart Risk Management System
يدير المخاطر ويحدد أحجام الصفقات بناءً على تحليل شامل للمخاطر
"""

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from config.trading_config import TradingConfig
from data_layer.final_signal_evaluation_engine import SignalEvaluation, SignalQuality, MarketCondition

logger = scalping_logger.get_logger("smart_risk_management")

class RiskLevel(Enum):
    """مستوى المخاطر"""
    VERY_LOW = "منخفض جداً"
    LOW = "منخفض"
    MODERATE = "متوسط"
    HIGH = "عالي"
    VERY_HIGH = "عالي جداً"
    EXTREME = "خطر شديد"

class PositionSizing(Enum):
    """حجم المركز"""
    MICRO = "صغير جداً"
    SMALL = "صغير"
    NORMAL = "عادي"
    LARGE = "كبير"
    MAXIMUM = "أقصى"

@dataclass
class RiskAssessment:
    """تقييم المخاطر"""
    currency_pair: str
    overall_risk_score: float  # 0.0 - 1.0
    risk_level: RiskLevel
    risk_factors: List[str]
    risk_breakdown: Dict[str, float]
    volatility_risk: float
    market_risk: float
    signal_risk: float
    portfolio_risk: float
    recommended_position_size: float  # نسبة من الرصيد
    max_position_size: float
    stop_loss_level: Optional[float]
    take_profit_level: Optional[float]
    risk_reward_ratio: float
    confidence_adjustment: float
    timestamp: datetime

@dataclass
class PortfolioRisk:
    """مخاطر المحفظة"""
    total_exposure: float  # إجمالي التعرض
    open_positions: int
    correlation_risk: float
    concentration_risk: float
    drawdown_risk: float
    available_capital: float
    risk_budget_used: float  # نسبة ميزانية المخاطر المستخدمة

@dataclass
class RiskLimits:
    """حدود المخاطر"""
    max_single_trade_risk: float = 0.02  # 2% من الرصيد
    max_daily_risk: float = 0.10  # 10% من الرصيد يومياً
    max_portfolio_risk: float = 0.20  # 20% من الرصيد إجمالي
    max_correlation_exposure: float = 0.15  # 15% للأزواج المترابطة
    max_drawdown_threshold: float = 0.15  # 15% حد أقصى للخسائر
    min_risk_reward_ratio: float = 1.5  # نسبة مخاطر/عائد دنيا

class SmartRiskManagementSystem:
    """نظام إدارة المخاطر الذكي"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logger
        self.risk_limits = RiskLimits()
        
        # تتبع المحفظة
        self.portfolio_state = {
            'current_balance': 0.0,  # سيتم تحديثه من المنصة
            'open_positions': [],
            'daily_pnl': 0.0,
            'total_risk_exposure': 0.0,
            'last_update': datetime.now()
        }

        # تهيئة مدير الأرباح والرصيد
        self._initialize_balance_manager()
        
        # إحصائيات المخاطر
        self.risk_stats = {
            'total_assessments': 0,
            'high_risk_rejections': 0,
            'position_size_adjustments': 0,
            'risk_level_distribution': {},
            'average_risk_score': 0.0,
            'successful_trades': 0,
            'failed_trades': 0
        }
        
        # تاريخ التقلبات
        self.volatility_history = {}
        
        self.logger.info("تم تهيئة نظام إدارة المخاطر الذكي")

    def _initialize_balance_manager(self):
        """تهيئة مدير الأرباح والرصيد"""
        try:
            from data_layer.profit_balance_manager import profit_balance_manager
            self.balance_manager = profit_balance_manager

            # جلب الرصيد الحالي
            account_balance = self.balance_manager.get_account_balance()
            if account_balance and account_balance.get('current_balance', 0) > 0:
                self.portfolio_state['current_balance'] = account_balance['current_balance']
                self.logger.info(f"تم تحديث الرصيد من المنصة: ${self.portfolio_state['current_balance']:.2f}")
            else:
                self.logger.warning("لم يتم العثور على رصيد حقيقي، سيتم استخدام رصيد افتراضي")
                self.portfolio_state['current_balance'] = 100.0  # رصيد افتراضي منخفض

        except Exception as e:
            self.logger.error(f"خطأ في تهيئة مدير الأرباح والرصيد: {str(e)}")
            self.portfolio_state['current_balance'] = 100.0  # رصيد افتراضي منخفض

    async def assess_risk(self, currency_pair: str, signal_evaluation: SignalEvaluation, 
                         current_balance: float = None) -> RiskAssessment:
        """تقييم المخاطر الشامل"""
        try:
            self.risk_stats['total_assessments'] += 1
            assessment_start = datetime.now()
            
            # تحديث الرصيد من المنصة أو استخدام المُمرر
            await self._update_current_balance(current_balance)

            self.logger.info(f"بدء تقييم المخاطر لـ {currency_pair} - الرصيد الحالي: ${self.portfolio_state['current_balance']:.2f}")

            # تحديث حالة المحفظة
            await self._update_portfolio_state()
            
            # تقييم مخاطر التقلبات
            volatility_risk = await self._assess_volatility_risk(currency_pair)
            
            # تقييم مخاطر السوق
            market_risk = await self._assess_market_risk(currency_pair, signal_evaluation.market_condition)
            
            # تقييم مخاطر الإشارة
            signal_risk = self._assess_signal_risk(signal_evaluation)
            
            # تقييم مخاطر المحفظة
            portfolio_risk = await self._assess_portfolio_risk(currency_pair)
            
            # حساب النقاط الإجمالية للمخاطر
            overall_risk_score = self._calculate_overall_risk_score(
                volatility_risk, market_risk, signal_risk, portfolio_risk
            )
            
            # تحديد مستوى المخاطر
            risk_level = self._determine_risk_level(overall_risk_score)
            
            # حساب حجم المركز المقترح
            recommended_position_size = self._calculate_position_size(
                overall_risk_score, signal_evaluation, portfolio_risk
            )
            
            # حساب مستويات الإيقاف والهدف
            stop_loss, take_profit, risk_reward_ratio = await self._calculate_risk_reward_levels(
                currency_pair, signal_evaluation, recommended_position_size
            )
            
            # تجميع عوامل المخاطر
            risk_factors = self._identify_risk_factors(
                volatility_risk, market_risk, signal_risk, portfolio_risk, signal_evaluation
            )
            
            # تفصيل المخاطر
            risk_breakdown = {
                'volatility_risk': volatility_risk,
                'market_risk': market_risk,
                'signal_risk': signal_risk,
                'portfolio_risk': portfolio_risk
            }
            
            # تعديل الثقة بناءً على المخاطر
            confidence_adjustment = self._calculate_confidence_adjustment(overall_risk_score, risk_level)
            
            risk_assessment = RiskAssessment(
                currency_pair=currency_pair,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                risk_factors=risk_factors,
                risk_breakdown=risk_breakdown,
                volatility_risk=volatility_risk,
                market_risk=market_risk,
                signal_risk=signal_risk,
                portfolio_risk=portfolio_risk,
                recommended_position_size=recommended_position_size,
                max_position_size=self.risk_limits.max_single_trade_risk,
                stop_loss_level=stop_loss,
                take_profit_level=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                confidence_adjustment=confidence_adjustment,
                timestamp=datetime.now()
            )
            
            # تحديث الإحصائيات
            self._update_risk_statistics(risk_assessment)
            
            assessment_duration = (datetime.now() - assessment_start).total_seconds()
            self.logger.info(f"اكتمل تقييم المخاطر لـ {currency_pair} في {assessment_duration:.2f} ثانية")
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم المخاطر لـ {currency_pair}: {str(e)}")
            raise

    async def _update_current_balance(self, provided_balance: float = None):
        """تحديث الرصيد الحالي من المنصة أو استخدام المُمرر"""
        try:
            if provided_balance is not None:
                # استخدام الرصيد المُمرر
                self.portfolio_state['current_balance'] = provided_balance
                self.logger.debug(f"تم استخدام الرصيد المُمرر: ${provided_balance:.2f}")
            else:
                # محاولة جلب الرصيد من مدير الأرباح والرصيد
                if hasattr(self, 'balance_manager'):
                    account_balance = self.balance_manager.get_account_balance()
                    if account_balance and account_balance.get('current_balance', 0) > 0:
                        old_balance = self.portfolio_state['current_balance']
                        new_balance = account_balance['current_balance']
                        self.portfolio_state['current_balance'] = new_balance

                        if abs(new_balance - old_balance) > 0.01:  # تغيير أكثر من سنت واحد
                            self.logger.info(f"تم تحديث الرصيد من المنصة: ${old_balance:.2f} → ${new_balance:.2f}")
                    else:
                        self.logger.warning("لم يتم العثور على رصيد محدث من المنصة")
                else:
                    self.logger.warning("مدير الأرباح والرصيد غير متاح")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الرصيد الحالي: {str(e)}")

    async def _assess_volatility_risk(self, currency_pair: str) -> float:
        """تقييم مخاطر التقلبات"""
        try:
            # جلب البيانات التاريخية
            from database.repository import historical_data_repo
            recent_candles = historical_data_repo.get_latest_candles(currency_pair, 100)

            if not recent_candles or len(recent_candles) < 20:
                return 0.5  # مخاطر متوسطة عند عدم توفر البيانات

            # حساب التقلبات - التعامل مع النوعين المختلفين من البيانات
            prices = []
            for candle in recent_candles:
                if isinstance(candle, dict):
                    prices.append(float(candle['close']))
                else:
                    prices.append(float(candle.close))
            returns = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
            
            # التقلبات الحديثة (آخر 20 شمعة)
            recent_volatility = np.std(returns[-20:]) * 100
            
            # التقلبات التاريخية (كامل البيانات)
            historical_volatility = np.std(returns) * 100
            
            # حفظ في التاريخ
            self.volatility_history[currency_pair] = {
                'recent': recent_volatility,
                'historical': historical_volatility,
                'timestamp': datetime.now()
            }
            
            # تحديد مستوى المخاطر بناءً على التقلبات
            if recent_volatility > historical_volatility * 1.5:
                volatility_risk = 0.8  # تقلبات عالية
            elif recent_volatility > historical_volatility * 1.2:
                volatility_risk = 0.6  # تقلبات متوسطة عالية
            elif recent_volatility < historical_volatility * 0.5:
                volatility_risk = 0.2  # تقلبات منخفضة
            else:
                volatility_risk = 0.4  # تقلبات عادية
            
            return volatility_risk
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم مخاطر التقلبات: {str(e)}")
            return 0.5

    async def _assess_market_risk(self, currency_pair: str, market_condition: MarketCondition) -> float:
        """تقييم مخاطر السوق"""
        try:
            # مخاطر بناءً على حالة السوق
            market_risk_map = {
                MarketCondition.CALM: 0.2,
                MarketCondition.TRENDING: 0.3,
                MarketCondition.RANGING: 0.4,
                MarketCondition.VOLATILE: 0.7,
                MarketCondition.UNCERTAIN: 0.8
            }
            
            base_market_risk = market_risk_map.get(market_condition, 0.5)
            
            # تعديل بناءً على الوقت (ساعات التداول النشطة)
            current_hour = datetime.now().hour
            
            # ساعات التداول عالية المخاطر (تداخل الجلسات)
            if current_hour in [8, 9, 13, 14, 15, 21, 22]:  # تداخل الجلسات
                time_risk_multiplier = 1.2
            elif current_hour in [0, 1, 2, 3, 4, 5, 6]:  # ساعات هادئة
                time_risk_multiplier = 0.8
            else:
                time_risk_multiplier = 1.0
            
            # تعديل بناءً على يوم الأسبوع
            weekday = datetime.now().weekday()
            if weekday == 0:  # الاثنين - بداية الأسبوع
                day_risk_multiplier = 1.1
            elif weekday == 4:  # الجمعة - نهاية الأسبوع
                day_risk_multiplier = 1.2
            else:
                day_risk_multiplier = 1.0
            
            market_risk = base_market_risk * time_risk_multiplier * day_risk_multiplier
            return min(1.0, market_risk)
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم مخاطر السوق: {str(e)}")
            return 0.5

    def _assess_signal_risk(self, signal_evaluation: SignalEvaluation) -> float:
        """تقييم مخاطر الإشارة"""
        try:
            # مخاطر بناءً على جودة الإشارة
            quality_risk_map = {
                SignalQuality.EXCELLENT: 0.1,
                SignalQuality.GOOD: 0.2,
                SignalQuality.AVERAGE: 0.4,
                SignalQuality.POOR: 0.7,
                SignalQuality.REJECTED: 0.9
            }
            
            quality_risk = quality_risk_map.get(signal_evaluation.quality_grade, 0.5)
            
            # تعديل بناءً على مستوى الثقة
            confidence_risk = 1.0 - signal_evaluation.confidence_score
            
            # تعديل بناءً على مخاطر الإشارة الأصلية
            original_risk = signal_evaluation.risk_score
            
            # حساب المخاطر المجمعة
            signal_risk = (quality_risk * 0.4) + (confidence_risk * 0.3) + (original_risk * 0.3)
            
            return min(1.0, signal_risk)
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم مخاطر الإشارة: {str(e)}")
            return 0.5

    async def _assess_portfolio_risk(self, currency_pair: str) -> float:
        """تقييم مخاطر المحفظة"""
        try:
            portfolio_risk = 0.0
            
            # مخاطر التعرض الإجمالي
            total_exposure = self.portfolio_state['total_risk_exposure']
            if total_exposure > self.risk_limits.max_portfolio_risk:
                portfolio_risk += 0.3
            elif total_exposure > self.risk_limits.max_portfolio_risk * 0.8:
                portfolio_risk += 0.2
            
            # مخاطر عدد المراكز المفتوحة
            open_positions = len(self.portfolio_state['open_positions'])
            if open_positions > 5:
                portfolio_risk += 0.2
            elif open_positions > 3:
                portfolio_risk += 0.1
            
            # مخاطر الخسائر اليومية
            daily_pnl = self.portfolio_state['daily_pnl']
            current_balance = self.portfolio_state['current_balance']
            
            if daily_pnl < 0:
                daily_loss_ratio = abs(daily_pnl) / current_balance
                if daily_loss_ratio > self.risk_limits.max_daily_risk:
                    portfolio_risk += 0.4
                elif daily_loss_ratio > self.risk_limits.max_daily_risk * 0.7:
                    portfolio_risk += 0.2
            
            # مخاطر الترابط (إذا كان هناك مراكز مفتوحة في أزواج مترابطة)
            correlation_risk = await self._calculate_correlation_risk(currency_pair)
            portfolio_risk += correlation_risk * 0.1
            
            return min(1.0, portfolio_risk)
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم مخاطر المحفظة: {str(e)}")
            return 0.3

    async def _calculate_correlation_risk(self, currency_pair: str) -> float:
        """حساب مخاطر الترابط"""
        try:
            # أزواج العملات المترابطة
            correlation_groups = {
                'EUR': ['EURUSD', 'EURJPY', 'EURGBP', 'EURAUD', 'EURCHF'],
                'USD': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD'],
                'GBP': ['GBPUSD', 'GBPJPY', 'EURGBP', 'GBPAUD', 'GBPCHF'],
                'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'AUDJPY', 'CHFJPY']
            }
            
            correlation_risk = 0.0
            
            # البحث عن المراكز المفتوحة في أزواج مترابطة
            for position in self.portfolio_state['open_positions']:
                position_pair = position.get('currency_pair', '')
                
                # فحص الترابط
                for currency, related_pairs in correlation_groups.items():
                    if currency_pair in related_pairs and position_pair in related_pairs:
                        if currency_pair != position_pair:
                            correlation_risk += 0.2
            
            return min(1.0, correlation_risk)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب مخاطر الترابط: {str(e)}")
            return 0.0

    def _calculate_overall_risk_score(self, volatility_risk: float, market_risk: float, 
                                    signal_risk: float, portfolio_risk: float) -> float:
        """حساب النقاط الإجمالية للمخاطر"""
        try:
            # أوزان المخاطر
            weights = {
                'volatility': 0.25,
                'market': 0.20,
                'signal': 0.35,
                'portfolio': 0.20
            }
            
            overall_risk = (
                volatility_risk * weights['volatility'] +
                market_risk * weights['market'] +
                signal_risk * weights['signal'] +
                portfolio_risk * weights['portfolio']
            )
            
            return min(1.0, overall_risk)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب النقاط الإجمالية للمخاطر: {str(e)}")
            return 0.5

    def _determine_risk_level(self, overall_risk_score: float) -> RiskLevel:
        """تحديد مستوى المخاطر"""
        if overall_risk_score >= 0.8:
            return RiskLevel.EXTREME
        elif overall_risk_score >= 0.7:
            return RiskLevel.VERY_HIGH
        elif overall_risk_score >= 0.5:
            return RiskLevel.HIGH
        elif overall_risk_score >= 0.3:
            return RiskLevel.MODERATE
        elif overall_risk_score >= 0.15:
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW

    def _calculate_position_size(self, overall_risk_score: float, signal_evaluation: SignalEvaluation,
                               portfolio_risk: float) -> float:
        """حساب حجم المركز المقترح"""
        try:
            # حجم المركز الأساسي
            base_position_size = self.risk_limits.max_single_trade_risk

            # تعديل بناءً على مستوى المخاطر
            risk_multiplier = 1.0 - overall_risk_score

            # تعديل بناءً على جودة الإشارة
            quality_multiplier = signal_evaluation.confidence_score

            # تعديل بناءً على مخاطر المحفظة
            portfolio_multiplier = 1.0 - portfolio_risk

            # تعديل بناءً على أولوية التنفيذ
            priority_multiplier = (6 - signal_evaluation.execution_priority) / 5

            # حساب الحجم المقترح
            recommended_size = (base_position_size *
                             risk_multiplier *
                             quality_multiplier *
                             portfolio_multiplier *
                             priority_multiplier)

            # تطبيق الحدود
            min_size = 0.005  # 0.5% حد أدنى
            max_size = self.risk_limits.max_single_trade_risk

            recommended_size = max(min_size, min(max_size, recommended_size))

            # تحقق من الرصيد المتاح
            available_capital = self.portfolio_state['current_balance'] * (1 - self.portfolio_state['total_risk_exposure'])
            max_affordable = available_capital * 0.1  # 10% من الرصيد المتاح

            if recommended_size * self.portfolio_state['current_balance'] > max_affordable:
                recommended_size = max_affordable / self.portfolio_state['current_balance']

            return recommended_size

        except Exception as e:
            self.logger.error(f"خطأ في حساب حجم المركز: {str(e)}")
            return 0.01

    async def _calculate_risk_reward_levels(self, currency_pair: str, signal_evaluation: SignalEvaluation,
                                          position_size: float) -> Tuple[Optional[float], Optional[float], float]:
        """حساب مستويات الإيقاف والهدف"""
        try:
            # جلب السعر الحالي
            from database.repository import historical_data_repo
            latest_candle = historical_data_repo.get_latest_candles(currency_pair, 1)

            if not latest_candle:
                return None, None, 0.0

            # التعامل مع النوعين المختلفين من البيانات
            if isinstance(latest_candle[0], dict):
                current_price = float(latest_candle[0]['close'])
            else:
                current_price = float(latest_candle[0].close)

            # حساب التقلبات للتحديد الديناميكي للمستويات
            recent_candles = historical_data_repo.get_latest_candles(currency_pair, 20)
            if len(recent_candles) >= 20:
                highs = []
                lows = []
                for c in recent_candles:
                    if isinstance(c, dict):
                        highs.append(float(c['high']))
                        lows.append(float(c['low']))
                    else:
                        highs.append(float(c.high))
                        lows.append(float(c.low))
                atr = np.mean([highs[i] - lows[i] for i in range(len(highs))])
            else:
                atr = current_price * 0.001  # 0.1% افتراضي

            # تحديد المستويات بناءً على اتجاه الإشارة
            stop_loss = None
            take_profit = None
            risk_reward_ratio = 0.0

            if signal_evaluation.final_signal == 'CALL':
                # إشارة شراء
                stop_loss = current_price - (atr * 2)  # إيقاف خسارة تحت السعر
                take_profit = current_price + (atr * 3)  # هدف ربح فوق السعر

                risk_amount = current_price - stop_loss
                reward_amount = take_profit - current_price

                if risk_amount > 0:
                    risk_reward_ratio = reward_amount / risk_amount

            elif signal_evaluation.final_signal == 'PUT':
                # إشارة بيع
                stop_loss = current_price + (atr * 2)  # إيقاف خسارة فوق السعر
                take_profit = current_price - (atr * 3)  # هدف ربح تحت السعر

                risk_amount = stop_loss - current_price
                reward_amount = current_price - take_profit

                if risk_amount > 0:
                    risk_reward_ratio = reward_amount / risk_amount

            # تعديل المستويات بناءً على جودة الإشارة
            if signal_evaluation.quality_grade in [SignalQuality.EXCELLENT, SignalQuality.GOOD]:
                # إشارات عالية الجودة - مستويات أوسع
                if signal_evaluation.final_signal == 'CALL':
                    take_profit = current_price + (atr * 4)
                elif signal_evaluation.final_signal == 'PUT':
                    take_profit = current_price - (atr * 4)

            # التأكد من نسبة مخاطر/عائد مقبولة
            if risk_reward_ratio < self.risk_limits.min_risk_reward_ratio:
                # تعديل الهدف لتحسين النسبة
                if signal_evaluation.final_signal == 'CALL':
                    take_profit = current_price + (atr * 3.5)
                elif signal_evaluation.final_signal == 'PUT':
                    take_profit = current_price - (atr * 3.5)

                # إعادة حساب النسبة
                if signal_evaluation.final_signal == 'CALL':
                    risk_amount = current_price - stop_loss
                    reward_amount = take_profit - current_price
                elif signal_evaluation.final_signal == 'PUT':
                    risk_amount = stop_loss - current_price
                    reward_amount = current_price - take_profit

                if risk_amount > 0:
                    risk_reward_ratio = reward_amount / risk_amount

            return stop_loss, take_profit, risk_reward_ratio

        except Exception as e:
            self.logger.error(f"خطأ في حساب مستويات المخاطر والعائد: {str(e)}")
            return None, None, 0.0

    def _identify_risk_factors(self, volatility_risk: float, market_risk: float,
                             signal_risk: float, portfolio_risk: float,
                             signal_evaluation: SignalEvaluation) -> List[str]:
        """تحديد عوامل المخاطر"""
        try:
            risk_factors = []

            # عوامل مخاطر التقلبات
            if volatility_risk > 0.7:
                risk_factors.append("تقلبات عالية في السوق")
            elif volatility_risk > 0.5:
                risk_factors.append("تقلبات متوسطة إلى عالية")

            # عوامل مخاطر السوق
            if market_risk > 0.6:
                risk_factors.append("ظروف سوق غير مستقرة")

            if signal_evaluation.market_condition == MarketCondition.VOLATILE:
                risk_factors.append("حالة سوق متقلبة")
            elif signal_evaluation.market_condition == MarketCondition.UNCERTAIN:
                risk_factors.append("حالة سوق غير مؤكدة")

            # عوامل مخاطر الإشارة
            if signal_risk > 0.6:
                risk_factors.append("جودة إشارة منخفضة")

            if signal_evaluation.confidence_score < 0.7:
                risk_factors.append("مستوى ثقة منخفض")

            if signal_evaluation.execution_priority > 3:
                risk_factors.append("أولوية تنفيذ منخفضة")

            # عوامل مخاطر المحفظة
            if portfolio_risk > 0.4:
                risk_factors.append("تعرض عالي للمحفظة")

            if len(self.portfolio_state['open_positions']) > 3:
                risk_factors.append("عدد كبير من المراكز المفتوحة")

            if self.portfolio_state['daily_pnl'] < 0:
                daily_loss_ratio = abs(self.portfolio_state['daily_pnl']) / self.portfolio_state['current_balance']
                if daily_loss_ratio > 0.05:
                    risk_factors.append("خسائر يومية مرتفعة")

            # عوامل زمنية
            current_hour = datetime.now().hour
            if current_hour in [21, 22, 23, 0, 1]:
                risk_factors.append("ساعات تداول عالية المخاطر")

            weekday = datetime.now().weekday()
            if weekday == 4:  # الجمعة
                risk_factors.append("نهاية الأسبوع - مخاطر إضافية")

            return risk_factors

        except Exception as e:
            self.logger.error(f"خطأ في تحديد عوامل المخاطر: {str(e)}")
            return ["خطأ في تحليل المخاطر"]

    def _calculate_confidence_adjustment(self, overall_risk_score: float, risk_level: RiskLevel) -> float:
        """حساب تعديل الثقة بناءً على المخاطر"""
        try:
            # تقليل الثقة بناءً على مستوى المخاطر
            risk_adjustment_map = {
                RiskLevel.VERY_LOW: 1.0,
                RiskLevel.LOW: 0.95,
                RiskLevel.MODERATE: 0.85,
                RiskLevel.HIGH: 0.70,
                RiskLevel.VERY_HIGH: 0.50,
                RiskLevel.EXTREME: 0.30
            }

            base_adjustment = risk_adjustment_map.get(risk_level, 0.8)

            # تعديل إضافي بناءً على النقاط الدقيقة
            fine_adjustment = 1.0 - (overall_risk_score * 0.3)

            return base_adjustment * fine_adjustment

        except Exception as e:
            self.logger.error(f"خطأ في حساب تعديل الثقة: {str(e)}")
            return 0.8

    async def _update_portfolio_state(self):
        """تحديث حالة المحفظة"""
        try:
            # هنا يمكن تحديث حالة المحفظة من قاعدة البيانات أو API
            # حالياً نستخدم بيانات افتراضية

            self.portfolio_state['last_update'] = datetime.now()

            # حساب إجمالي التعرض
            total_exposure = 0.0
            for position in self.portfolio_state['open_positions']:
                total_exposure += position.get('risk_amount', 0.0)

            self.portfolio_state['total_risk_exposure'] = total_exposure / self.portfolio_state['current_balance']

        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة المحفظة: {str(e)}")

    def _update_risk_statistics(self, risk_assessment: RiskAssessment):
        """تحديث إحصائيات المخاطر"""
        try:
            # تحديث توزيع مستويات المخاطر
            risk_level_name = risk_assessment.risk_level.value
            if risk_level_name not in self.risk_stats['risk_level_distribution']:
                self.risk_stats['risk_level_distribution'][risk_level_name] = 0
            self.risk_stats['risk_level_distribution'][risk_level_name] += 1

            # تحديث متوسط نقاط المخاطر
            total_assessments = self.risk_stats['total_assessments']
            current_avg = self.risk_stats['average_risk_score']
            self.risk_stats['average_risk_score'] = ((current_avg * (total_assessments - 1)) +
                                                   risk_assessment.overall_risk_score) / total_assessments

            # تحديث عدادات الرفض
            if risk_assessment.risk_level in [RiskLevel.VERY_HIGH, RiskLevel.EXTREME]:
                self.risk_stats['high_risk_rejections'] += 1

            # تحديث تعديلات حجم المركز
            if risk_assessment.recommended_position_size < self.risk_limits.max_single_trade_risk:
                self.risk_stats['position_size_adjustments'] += 1

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات المخاطر: {str(e)}")

    def should_reject_trade(self, risk_assessment: RiskAssessment) -> Tuple[bool, List[str]]:
        """تحديد ما إذا كان يجب رفض الصفقة"""
        try:
            rejection_reasons = []

            # رفض بناءً على مستوى المخاطر
            if risk_assessment.risk_level == RiskLevel.EXTREME:
                rejection_reasons.append("مستوى مخاطر شديد")

            # رفض بناءً على نسبة المخاطر/العائد
            if risk_assessment.risk_reward_ratio < self.risk_limits.min_risk_reward_ratio:
                rejection_reasons.append(f"نسبة مخاطر/عائد منخفضة ({risk_assessment.risk_reward_ratio:.2f})")

            # رفض بناءً على تعرض المحفظة
            if self.portfolio_state['total_risk_exposure'] > self.risk_limits.max_portfolio_risk:
                rejection_reasons.append("تجاوز حد تعرض المحفظة")

            # رفض بناءً على الخسائر اليومية
            daily_loss_ratio = abs(self.portfolio_state['daily_pnl']) / self.portfolio_state['current_balance']
            if self.portfolio_state['daily_pnl'] < 0 and daily_loss_ratio > self.risk_limits.max_daily_risk:
                rejection_reasons.append("تجاوز حد الخسائر اليومية")

            # رفض بناءً على حجم المركز
            if risk_assessment.recommended_position_size < 0.005:  # أقل من 0.5%
                rejection_reasons.append("حجم مركز صغير جداً")

            should_reject = len(rejection_reasons) > 0
            return should_reject, rejection_reasons

        except Exception as e:
            self.logger.error(f"خطأ في تحديد رفض الصفقة: {str(e)}")
            return True, ["خطأ في تقييم المخاطر"]

    def get_risk_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المخاطر"""
        try:
            total_assessments = self.risk_stats['total_assessments']
            if total_assessments == 0:
                return self.risk_stats

            # حساب النسب المئوية
            risk_level_percentages = {}
            for level, count in self.risk_stats['risk_level_distribution'].items():
                risk_level_percentages[level] = (count / total_assessments) * 100

            # حساب معدلات الأداء
            rejection_rate = (self.risk_stats['high_risk_rejections'] / total_assessments) * 100
            adjustment_rate = (self.risk_stats['position_size_adjustments'] / total_assessments) * 100

            # حساب معدل النجاح
            total_trades = self.risk_stats['successful_trades'] + self.risk_stats['failed_trades']
            success_rate = (self.risk_stats['successful_trades'] / total_trades * 100) if total_trades > 0 else 0

            return {
                'total_assessments': total_assessments,
                'average_risk_score': self.risk_stats['average_risk_score'],
                'risk_level_distribution': self.risk_stats['risk_level_distribution'],
                'risk_level_percentages': risk_level_percentages,
                'high_risk_rejection_rate': rejection_rate,
                'position_size_adjustment_rate': adjustment_rate,
                'trade_success_rate': success_rate,
                'portfolio_state': self.portfolio_state,
                'risk_limits': {
                    'max_single_trade_risk': self.risk_limits.max_single_trade_risk,
                    'max_daily_risk': self.risk_limits.max_daily_risk,
                    'max_portfolio_risk': self.risk_limits.max_portfolio_risk,
                    'min_risk_reward_ratio': self.risk_limits.min_risk_reward_ratio
                }
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات المخاطر: {str(e)}")
            return self.risk_stats

    async def update_trade_result(self, currency_pair: str, was_successful: bool,
                                pnl: float, trade_amount: float):
        """تحديث نتيجة الصفقة"""
        try:
            # تحديث إحصائيات النجاح
            if was_successful:
                self.risk_stats['successful_trades'] += 1
            else:
                self.risk_stats['failed_trades'] += 1

            # تحديث الربح والخسارة اليومية
            self.portfolio_state['daily_pnl'] += pnl

            # تحديث الرصيد
            self.portfolio_state['current_balance'] += pnl

            # إزالة المركز من المراكز المفتوحة
            self.portfolio_state['open_positions'] = [
                pos for pos in self.portfolio_state['open_positions']
                if pos.get('currency_pair') != currency_pair
            ]

            # إعادة حساب التعرض
            await self._update_portfolio_state()

        except Exception as e:
            self.logger.error(f"خطأ في تحديث نتيجة الصفقة: {str(e)}")

# إنشاء مثيل عام للنظام
smart_risk_management_system = SmartRiskManagementSystem()
