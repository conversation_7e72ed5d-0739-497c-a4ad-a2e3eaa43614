"""
محرك الإشارات الفنية المتقدم - المرحلة الرابعة
Technical Signals Engine for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70

# استيراد جميع المؤشرات الفنية
from indicators.ema_indicator import EMAIndicator
from indicators.sma_indicator import SMAIndicator
from indicators.rsi_indicator import RSIIndicator
from indicators.macd_indicator import MACDIndicator
from indicators.bollinger_bands_indicator import BollingerBandsIndicator
from indicators.atr_indicator import ATRIndicator
from indicators.momentum_indicator import MomentumIndicator
from indicators.heiken_ashi_indicator import HeikenAshiIndicator
from indicators.zscore_indicator import ZScoreIndicator
from indicators.stochastic_indicator import StochasticIndicator
from indicators.williams_r_indicator import WilliamsRIndicator

logger = scalping_logger.get_logger("technical_signals_engine")

class SignalStrength(Enum):
    """قوة الإشارة"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5

class SignalDirection(Enum):
    """اتجاه الإشارة"""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    NEUTRAL = "NEUTRAL"

@dataclass
class TechnicalSignal:
    """إشارة فنية"""
    indicator_name: str
    direction: SignalDirection
    strength: SignalStrength
    confidence: float  # 0-100
    value: float
    timestamp: datetime
    reasoning: str
    supporting_data: Dict[str, Any]

@dataclass
class CombinedSignal:
    """إشارة مدمجة من عدة مؤشرات"""
    direction: SignalDirection
    overall_strength: SignalStrength
    confidence_score: float  # 0-100
    supporting_signals: List[TechnicalSignal]
    conflicting_signals: List[TechnicalSignal]
    timestamp: datetime
    asset: str
    timeframe: int

class TechnicalSignalsEngine:
    """محرك الإشارات الفنية المتقدم"""
    
    def __init__(self):
        self.indicators = self._initialize_indicators()
        self.signal_weights = self._initialize_signal_weights()
        self.min_confidence_threshold = 70.0  # الحد الأدنى للثقة
        self.signal_history = {}  # تاريخ الإشارات لكل أصل
        
        logger.info("تم تهيئة محرك الإشارات الفنية المتقدم")
        logger.info(f"عدد المؤشرات المحملة: {len(self.indicators)}")

    def _initialize_indicators(self) -> Dict[str, Any]:
        """تهيئة جميع المؤشرات الفنية"""
        return {
            # المتوسطات المتحركة
            'EMA_5': EMAIndicator(period=5),
            'EMA_10': EMAIndicator(period=10),
            'EMA_21': EMAIndicator(period=21),
            'SMA_10': SMAIndicator(period=10),
            
            # مؤشرات الزخم
            'RSI_5': RSIIndicator(period=5),
            'RSI_14': RSIIndicator(period=14),
            'MOMENTUM_10': MomentumIndicator(period=10),
            'STOCHASTIC': StochasticIndicator(),
            'WILLIAMS_R': WilliamsRIndicator(),
            
            # مؤشرات التقلبات والاتجاه
            'MACD': MACDIndicator(),
            'BOLLINGER_BANDS': BollingerBandsIndicator(period=20, std_dev=2),
            'ATR_5': ATRIndicator(period=5),
            'ATR_14': ATRIndicator(period=14),
            
            # مؤشرات متقدمة
            'HEIKEN_ASHI': HeikenAshiIndicator(),
            'ZSCORE': ZScoreIndicator(period=20)
        }

    def _initialize_signal_weights(self) -> Dict[str, float]:
        """تهيئة أوزان المؤشرات حسب الأهمية"""
        return {
            # أوزان عالية للمؤشرات الأساسية
            'EMA_5': 0.15,
            'EMA_10': 0.15,
            'EMA_21': 0.12,
            'RSI_14': 0.12,
            'MACD': 0.10,
            'BOLLINGER_BANDS': 0.08,
            
            # أوزان متوسطة
            'RSI_5': 0.06,
            'MOMENTUM_10': 0.05,
            'ATR_14': 0.05,
            'HEIKEN_ASHI': 0.04,
            
            # أوزان منخفضة للمؤشرات المساعدة
            'SMA_10': 0.03,
            'STOCHASTIC': 0.02,
            'WILLIAMS_R': 0.02,
            'ATR_5': 0.01,
            'ZSCORE': 0.01
        }

    @handle_errors(default_return=None, log_error=True)
    def analyze_single_indicator(self, indicator_name: str, candles_data: List[Dict[str, Any]]) -> Optional[TechnicalSignal]:
        """تحليل مؤشر واحد وإنتاج إشارة"""
        try:
            if indicator_name not in self.indicators:
                logger.error(f"مؤشر غير موجود: {indicator_name}")
                return None

            indicator = self.indicators[indicator_name]
            
            # حساب المؤشر
            result = indicator.calculate_with_validation(candles_data)
            
            if 'error' in result:
                logger.warning(f"خطأ في حساب {indicator_name}: {result['error']}")
                return None

            values = result.get('values', [])
            if not values or len(values) < 2:
                return None

            # تحليل الإشارة حسب نوع المؤشر
            signal = self._analyze_indicator_signal(indicator_name, values, candles_data)
            
            if signal:
                signal.timestamp = datetime.now()
                signal.supporting_data = {
                    'calculation_time_ms': result.get('calculation_time_ms', 0),
                    'data_points': result.get('data_points', 0),
                    'latest_value': values[-1] if values else None,
                    'previous_value': values[-2] if len(values) > 1 else None
                }

            return signal

        except Exception as e:
            logger.error(f"خطأ في تحليل المؤشر {indicator_name}: {str(e)}")
            return None

    def _analyze_indicator_signal(self, indicator_name: str, values: List[float], candles_data: List[Dict[str, Any]]) -> Optional[TechnicalSignal]:
        """تحليل إشارة المؤشر حسب نوعه"""
        try:
            current_value = values[-1]
            previous_value = values[-2] if len(values) > 1 else current_value
            current_price = candles_data[-1]['close']
            
            # تحليل المتوسطات المتحركة
            if indicator_name.startswith('EMA') or indicator_name.startswith('SMA'):
                return self._analyze_moving_average_signal(indicator_name, current_value, previous_value, current_price)
            
            # تحليل RSI
            elif indicator_name.startswith('RSI'):
                return self._analyze_rsi_signal(indicator_name, current_value, previous_value)
            
            # تحليل MACD
            elif indicator_name == 'MACD':
                return self._analyze_macd_signal(values, candles_data)
            
            # تحليل Bollinger Bands
            elif indicator_name == 'BOLLINGER_BANDS':
                return self._analyze_bollinger_signal(values, current_price)
            
            # تحليل ATR
            elif indicator_name.startswith('ATR'):
                return self._analyze_atr_signal(indicator_name, current_value, previous_value)
            
            # تحليل Momentum
            elif indicator_name.startswith('MOMENTUM'):
                return self._analyze_momentum_signal(current_value, previous_value)
            
            # تحليل Stochastic
            elif indicator_name == 'STOCHASTIC':
                return self._analyze_stochastic_signal(current_value, previous_value)
            
            # تحليل Williams %R
            elif indicator_name == 'WILLIAMS_R':
                return self._analyze_williams_signal(current_value, previous_value)
            
            # تحليل Heiken Ashi
            elif indicator_name == 'HEIKEN_ASHI':
                return self._analyze_heiken_ashi_signal(values, candles_data)
            
            # تحليل Z-Score
            elif indicator_name == 'ZSCORE':
                return self._analyze_zscore_signal(current_value, previous_value)
            
            else:
                logger.warning(f"نوع مؤشر غير مدعوم: {indicator_name}")
                return None

        except Exception as e:
            logger.error(f"خطأ في تحليل إشارة {indicator_name}: {str(e)}")
            return None

    def _analyze_moving_average_signal(self, indicator_name: str, current_ma: float, previous_ma: float, current_price: float) -> TechnicalSignal:
        """تحليل إشارة المتوسط المتحرك"""
        # تحديد الاتجاه
        if current_price > current_ma and current_ma > previous_ma:
            direction = SignalDirection.BULLISH
            reasoning = f"السعر أعلى من {indicator_name} والمتوسط في اتجاه صاعد"
            confidence = min(95, 60 + abs((current_price - current_ma) / current_ma) * 1000)
        elif current_price < current_ma and current_ma < previous_ma:
            direction = SignalDirection.BEARISH
            reasoning = f"السعر أقل من {indicator_name} والمتوسط في اتجاه هابط"
            confidence = min(95, 60 + abs((current_price - current_ma) / current_ma) * 1000)
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = f"إشارة غير واضحة من {indicator_name}"
            confidence = 30

        # تحديد قوة الإشارة
        price_distance = abs(current_price - current_ma) / current_ma * 100
        if price_distance > 0.5:
            strength = SignalStrength.VERY_STRONG
        elif price_distance > 0.3:
            strength = SignalStrength.STRONG
        elif price_distance > 0.1:
            strength = SignalStrength.MODERATE
        else:
            strength = SignalStrength.WEAK

        return TechnicalSignal(
            indicator_name=indicator_name,
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_ma,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_rsi_signal(self, indicator_name: str, current_rsi: float, previous_rsi: float) -> TechnicalSignal:
        """تحليل إشارة RSI"""
        # تحديد الاتجاه والقوة
        if current_rsi < 30:
            direction = SignalDirection.BULLISH
            reasoning = f"{indicator_name} في منطقة تشبع بيعي ({current_rsi:.1f})"
            strength = SignalStrength.STRONG if current_rsi < 20 else SignalStrength.MODERATE
            confidence = min(90, 70 + (30 - current_rsi))
        elif current_rsi > 70:
            direction = SignalDirection.BEARISH
            reasoning = f"{indicator_name} في منطقة تشبع شرائي ({current_rsi:.1f})"
            strength = SignalStrength.STRONG if current_rsi > 80 else SignalStrength.MODERATE
            confidence = min(90, 70 + (current_rsi - 70))
        elif current_rsi > 50 and current_rsi > previous_rsi:
            direction = SignalDirection.BULLISH
            reasoning = f"{indicator_name} أعلى من 50 وفي اتجاه صاعد"
            strength = SignalStrength.WEAK
            confidence = 55
        elif current_rsi < 50 and current_rsi < previous_rsi:
            direction = SignalDirection.BEARISH
            reasoning = f"{indicator_name} أقل من 50 وفي اتجاه هابط"
            strength = SignalStrength.WEAK
            confidence = 55
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = f"{indicator_name} في منطقة محايدة"
            strength = SignalStrength.VERY_WEAK
            confidence = 30

        return TechnicalSignal(
            indicator_name=indicator_name,
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_rsi,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_macd_signal(self, values: List[float], candles_data: List[Dict[str, Any]]) -> TechnicalSignal:
        """تحليل إشارة MACD"""
        if len(values) < 3:
            return TechnicalSignal("MACD", SignalDirection.NEUTRAL, SignalStrength.VERY_WEAK, 30, 0, datetime.now(), "بيانات غير كافية", {})

        current_macd = values[-1]
        previous_macd = values[-2]

        # تحليل تقاطع خط الصفر
        if current_macd > 0 and previous_macd <= 0:
            direction = SignalDirection.BULLISH
            reasoning = "MACD عبر خط الصفر صعوداً"
            strength = SignalStrength.STRONG
            confidence = 85
        elif current_macd < 0 and previous_macd >= 0:
            direction = SignalDirection.BEARISH
            reasoning = "MACD عبر خط الصفر هبوطاً"
            strength = SignalStrength.STRONG
            confidence = 85
        elif current_macd > previous_macd and current_macd > 0:
            direction = SignalDirection.BULLISH
            reasoning = "MACD في اتجاه صاعد فوق الصفر"
            strength = SignalStrength.MODERATE
            confidence = 65
        elif current_macd < previous_macd and current_macd < 0:
            direction = SignalDirection.BEARISH
            reasoning = "MACD في اتجاه هابط تحت الصفر"
            strength = SignalStrength.MODERATE
            confidence = 65
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "MACD في حالة محايدة"
            strength = SignalStrength.WEAK
            confidence = 40

        return TechnicalSignal(
            indicator_name="MACD",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_macd,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_bollinger_signal(self, values: List[float], current_price: float) -> TechnicalSignal:
        """تحليل إشارة Bollinger Bands"""
        if len(values) < 3:
            return TechnicalSignal("BOLLINGER_BANDS", SignalDirection.NEUTRAL, SignalStrength.VERY_WEAK, 30, 0, datetime.now(), "بيانات غير كافية", {})

        # افتراض أن القيم هي [lower_band, middle_band, upper_band]
        lower_band = values[0]
        middle_band = values[1]
        upper_band = values[2]

        # تحليل موقع السعر
        if current_price <= lower_band:
            direction = SignalDirection.BULLISH
            reasoning = f"السعر لمس النطاق السفلي ({lower_band:.5f})"
            strength = SignalStrength.STRONG
            confidence = 80
        elif current_price >= upper_band:
            direction = SignalDirection.BEARISH
            reasoning = f"السعر لمس النطاق العلوي ({upper_band:.5f})"
            strength = SignalStrength.STRONG
            confidence = 80
        elif current_price > middle_band:
            direction = SignalDirection.BULLISH
            reasoning = "السعر أعلى من الخط الأوسط"
            strength = SignalStrength.WEAK
            confidence = 55
        elif current_price < middle_band:
            direction = SignalDirection.BEARISH
            reasoning = "السعر أقل من الخط الأوسط"
            strength = SignalStrength.WEAK
            confidence = 55
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "السعر عند الخط الأوسط"
            strength = SignalStrength.VERY_WEAK
            confidence = 30

        return TechnicalSignal(
            indicator_name="BOLLINGER_BANDS",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_price,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={"lower_band": lower_band, "middle_band": middle_band, "upper_band": upper_band}
        )

    def _analyze_atr_signal(self, indicator_name: str, current_atr: float, previous_atr: float) -> TechnicalSignal:
        """تحليل إشارة ATR (مؤشر التقلبات)"""
        # ATR لا يعطي إشارة اتجاه، بل يقيس التقلبات
        atr_change = (current_atr - previous_atr) / previous_atr * 100 if previous_atr > 0 else 0

        if atr_change > 10:
            direction = SignalDirection.NEUTRAL  # ATR لا يحدد الاتجاه
            reasoning = f"زيادة كبيرة في التقلبات ({atr_change:.1f}%)"
            strength = SignalStrength.STRONG
            confidence = 70
        elif atr_change < -10:
            direction = SignalDirection.NEUTRAL
            reasoning = f"انخفاض كبير في التقلبات ({atr_change:.1f}%)"
            strength = SignalStrength.MODERATE
            confidence = 60
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "تقلبات مستقرة"
            strength = SignalStrength.WEAK
            confidence = 40

        return TechnicalSignal(
            indicator_name=indicator_name,
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_atr,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={"atr_change_percent": atr_change}
        )

    def _analyze_momentum_signal(self, current_momentum: float, previous_momentum: float) -> TechnicalSignal:
        """تحليل إشارة Momentum"""
        if current_momentum > 0 and previous_momentum <= 0:
            direction = SignalDirection.BULLISH
            reasoning = "Momentum عبر خط الصفر صعوداً"
            strength = SignalStrength.STRONG
            confidence = 75
        elif current_momentum < 0 and previous_momentum >= 0:
            direction = SignalDirection.BEARISH
            reasoning = "Momentum عبر خط الصفر هبوطاً"
            strength = SignalStrength.STRONG
            confidence = 75
        elif current_momentum > previous_momentum and current_momentum > 0:
            direction = SignalDirection.BULLISH
            reasoning = "Momentum في تسارع صاعد"
            strength = SignalStrength.MODERATE
            confidence = 60
        elif current_momentum < previous_momentum and current_momentum < 0:
            direction = SignalDirection.BEARISH
            reasoning = "Momentum في تسارع هابط"
            strength = SignalStrength.MODERATE
            confidence = 60
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "Momentum محايد"
            strength = SignalStrength.WEAK
            confidence = 35

        return TechnicalSignal(
            indicator_name="MOMENTUM_10",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_momentum,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_stochastic_signal(self, current_stoch: float, previous_stoch: float) -> TechnicalSignal:
        """تحليل إشارة Stochastic"""
        if current_stoch < 20:
            direction = SignalDirection.BULLISH
            reasoning = f"Stochastic في منطقة تشبع بيعي ({current_stoch:.1f})"
            strength = SignalStrength.MODERATE
            confidence = 70
        elif current_stoch > 80:
            direction = SignalDirection.BEARISH
            reasoning = f"Stochastic في منطقة تشبع شرائي ({current_stoch:.1f})"
            strength = SignalStrength.MODERATE
            confidence = 70
        elif current_stoch > previous_stoch and current_stoch > 50:
            direction = SignalDirection.BULLISH
            reasoning = "Stochastic في اتجاه صاعد"
            strength = SignalStrength.WEAK
            confidence = 50
        elif current_stoch < previous_stoch and current_stoch < 50:
            direction = SignalDirection.BEARISH
            reasoning = "Stochastic في اتجاه هابط"
            strength = SignalStrength.WEAK
            confidence = 50
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "Stochastic محايد"
            strength = SignalStrength.VERY_WEAK
            confidence = 30

        return TechnicalSignal(
            indicator_name="STOCHASTIC",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_stoch,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_williams_signal(self, current_williams: float, previous_williams: float) -> TechnicalSignal:
        """تحليل إشارة Williams %R"""
        if current_williams < -80:
            direction = SignalDirection.BULLISH
            reasoning = f"Williams %R في منطقة تشبع بيعي ({current_williams:.1f})"
            strength = SignalStrength.MODERATE
            confidence = 65
        elif current_williams > -20:
            direction = SignalDirection.BEARISH
            reasoning = f"Williams %R في منطقة تشبع شرائي ({current_williams:.1f})"
            strength = SignalStrength.MODERATE
            confidence = 65
        elif current_williams > previous_williams:
            direction = SignalDirection.BULLISH
            reasoning = "Williams %R في تحسن"
            strength = SignalStrength.WEAK
            confidence = 45
        elif current_williams < previous_williams:
            direction = SignalDirection.BEARISH
            reasoning = "Williams %R في تراجع"
            strength = SignalStrength.WEAK
            confidence = 45
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "Williams %R محايد"
            strength = SignalStrength.VERY_WEAK
            confidence = 30

        return TechnicalSignal(
            indicator_name="WILLIAMS_R",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_williams,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_heiken_ashi_signal(self, values: List[float], candles_data: List[Dict[str, Any]]) -> TechnicalSignal:
        """تحليل إشارة Heiken Ashi"""
        if len(values) < 2 or len(candles_data) < 2:
            return TechnicalSignal("HEIKEN_ASHI", SignalDirection.NEUTRAL, SignalStrength.VERY_WEAK, 30, 0, datetime.now(), "بيانات غير كافية", {})

        # تحليل لون الشموع (افتراض أن القيم تمثل الاتجاه)
        current_ha = values[-1]
        previous_ha = values[-2]

        if current_ha > previous_ha:
            direction = SignalDirection.BULLISH
            reasoning = "شموع Heiken Ashi صاعدة"
            strength = SignalStrength.MODERATE
            confidence = 60
        elif current_ha < previous_ha:
            direction = SignalDirection.BEARISH
            reasoning = "شموع Heiken Ashi هابطة"
            strength = SignalStrength.MODERATE
            confidence = 60
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "شموع Heiken Ashi محايدة"
            strength = SignalStrength.WEAK
            confidence = 35

        return TechnicalSignal(
            indicator_name="HEIKEN_ASHI",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_ha,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    def _analyze_zscore_signal(self, current_zscore: float, previous_zscore: float) -> TechnicalSignal:
        """تحليل إشارة Z-Score"""
        if current_zscore > 2:
            direction = SignalDirection.BEARISH
            reasoning = f"Z-Score مرتفع جداً ({current_zscore:.2f}) - انحراف سعري كبير للأعلى"
            strength = SignalStrength.STRONG
            confidence = 80
        elif current_zscore < -2:
            direction = SignalDirection.BULLISH
            reasoning = f"Z-Score منخفض جداً ({current_zscore:.2f}) - انحراف سعري كبير للأسفل"
            strength = SignalStrength.STRONG
            confidence = 80
        elif current_zscore > 1:
            direction = SignalDirection.BEARISH
            reasoning = f"Z-Score مرتفع ({current_zscore:.2f})"
            strength = SignalStrength.MODERATE
            confidence = 60
        elif current_zscore < -1:
            direction = SignalDirection.BULLISH
            reasoning = f"Z-Score منخفض ({current_zscore:.2f})"
            strength = SignalStrength.MODERATE
            confidence = 60
        else:
            direction = SignalDirection.NEUTRAL
            reasoning = "Z-Score في النطاق الطبيعي"
            strength = SignalStrength.WEAK
            confidence = 35

        return TechnicalSignal(
            indicator_name="ZSCORE",
            direction=direction,
            strength=strength,
            confidence=confidence,
            value=current_zscore,
            timestamp=datetime.now(),
            reasoning=reasoning,
            supporting_data={}
        )

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_all_indicators(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[CombinedSignal]:
        """تحليل جميع المؤشرات وإنتاج إشارة مدمجة"""
        try:
            if not candles_data or len(candles_data) < 30:
                logger.warning(f"بيانات غير كافية لـ {asset}: {len(candles_data) if candles_data else 0}")
                return None

            # تحليل جميع المؤشرات
            individual_signals = []

            for indicator_name in self.indicators.keys():
                signal = self.analyze_single_indicator(indicator_name, candles_data)
                if signal and signal.confidence >= 30:  # تجاهل الإشارات الضعيفة جداً
                    individual_signals.append(signal)

            if not individual_signals:
                logger.warning(f"لم يتم إنتاج أي إشارات صالحة لـ {asset}")
                return None

            # دمج الإشارات
            combined_signal = self._combine_signals(asset, individual_signals, 60)  # إطار زمني دقيقة واحدة

            # حفظ في التاريخ
            if asset not in self.signal_history:
                self.signal_history[asset] = []

            self.signal_history[asset].append(combined_signal)

            # الاحتفاظ بآخر 100 إشارة فقط
            if len(self.signal_history[asset]) > 100:
                self.signal_history[asset] = self.signal_history[asset][-100:]

            logger.info(f"تم إنتاج إشارة مدمجة لـ {asset}: {combined_signal.direction.value} (ثقة: {combined_signal.confidence_score:.1f}%)")

            return combined_signal

        except Exception as e:
            logger.error(f"خطأ في تحليل المؤشرات لـ {asset}: {str(e)}")
            return None

    def _combine_signals(self, asset: str, signals: List[TechnicalSignal], timeframe: int) -> CombinedSignal:
        """دمج الإشارات الفردية في إشارة واحدة"""
        try:
            # تصنيف الإشارات حسب الاتجاه
            bullish_signals = [s for s in signals if s.direction == SignalDirection.BULLISH]
            bearish_signals = [s for s in signals if s.direction == SignalDirection.BEARISH]
            neutral_signals = [s for s in signals if s.direction == SignalDirection.NEUTRAL]

            # حساب الأوزان المرجحة
            bullish_weight = sum(self.signal_weights.get(s.indicator_name, 0.01) * s.confidence * s.strength.value for s in bullish_signals)
            bearish_weight = sum(self.signal_weights.get(s.indicator_name, 0.01) * s.confidence * s.strength.value for s in bearish_signals)

            # تحديد الاتجاه النهائي
            if bullish_weight > bearish_weight * 1.2:  # تحيز طفيف للصعود
                final_direction = SignalDirection.BULLISH
                supporting_signals = bullish_signals
                conflicting_signals = bearish_signals
                confidence_score = min(95, (bullish_weight / (bullish_weight + bearish_weight)) * 100) if (bullish_weight + bearish_weight) > 0 else 50
            elif bearish_weight > bullish_weight * 1.2:  # تحيز طفيف للهبوط
                final_direction = SignalDirection.BEARISH
                supporting_signals = bearish_signals
                conflicting_signals = bullish_signals
                confidence_score = min(95, (bearish_weight / (bullish_weight + bearish_weight)) * 100) if (bullish_weight + bearish_weight) > 0 else 50
            else:
                final_direction = SignalDirection.NEUTRAL
                supporting_signals = neutral_signals
                conflicting_signals = bullish_signals + bearish_signals
                confidence_score = 30

            # تحديد القوة الإجمالية
            if confidence_score >= 85:
                overall_strength = SignalStrength.VERY_STRONG
            elif confidence_score >= 70:
                overall_strength = SignalStrength.STRONG
            elif confidence_score >= 55:
                overall_strength = SignalStrength.MODERATE
            elif confidence_score >= 40:
                overall_strength = SignalStrength.WEAK
            else:
                overall_strength = SignalStrength.VERY_WEAK

            return CombinedSignal(
                direction=final_direction,
                overall_strength=overall_strength,
                confidence_score=confidence_score,
                supporting_signals=supporting_signals,
                conflicting_signals=conflicting_signals,
                timestamp=datetime.now(),
                asset=asset,
                timeframe=timeframe
            )

        except Exception as e:
            logger.error(f"خطأ في دمج الإشارات لـ {asset}: {str(e)}")
            return CombinedSignal(
                direction=SignalDirection.NEUTRAL,
                overall_strength=SignalStrength.VERY_WEAK,
                confidence_score=0,
                supporting_signals=[],
                conflicting_signals=signals,
                timestamp=datetime.now(),
                asset=asset,
                timeframe=timeframe
            )

    def is_signal_valid(self, signal: CombinedSignal) -> bool:
        """التحقق من صحة الإشارة"""
        if not signal:
            return False

        # التحقق من الحد الأدنى للثقة
        if signal.confidence_score < self.min_confidence_threshold:
            return False

        # التحقق من وجود إشارات داعمة كافية
        if len(signal.supporting_signals) < 2:
            return False

        # التحقق من عدم وجود تضارب كبير
        if len(signal.conflicting_signals) > len(signal.supporting_signals):
            return False

        return True

    def get_signal_summary(self, signal: CombinedSignal) -> Dict[str, Any]:
        """الحصول على ملخص الإشارة"""
        if not signal:
            return {}

        return {
            'asset': signal.asset,
            'direction': signal.direction.value,
            'strength': signal.overall_strength.name,
            'confidence': round(signal.confidence_score, 2),
            'timestamp': signal.timestamp.isoformat(),
            'supporting_indicators': len(signal.supporting_signals),
            'conflicting_indicators': len(signal.conflicting_signals),
            'is_valid': self.is_signal_valid(signal),
            'supporting_details': [
                {
                    'indicator': s.indicator_name,
                    'confidence': s.confidence,
                    'reasoning': s.reasoning
                } for s in signal.supporting_signals[:5]  # أفضل 5 إشارات داعمة
            ]
        }

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_with_crossovers(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """تحليل شامل مع تقاطعات المؤشرات"""
        try:
            if not candles_data or len(candles_data) < 30:
                logger.warning(f"بيانات غير كافية لتحليل التقاطعات لـ {asset}")
                return None

            # 1. التحليل الفني العادي
            combined_signal = await self.analyze_all_indicators(asset, candles_data)
            if not combined_signal:
                logger.warning(f"فشل في إنتاج الإشارة المدمجة لـ {asset}")
                return None

            # 2. تحليل التقاطعات (إذا كان متاحاً)
            crossover_analysis = None
            if smart_crossover_analyzer:
                try:
                    crossover_analysis = await smart_crossover_analyzer.analyze_crossovers(
                        asset, combined_signal.supporting_signals + combined_signal.conflicting_signals, candles_data
                    )
                except Exception as e:
                    logger.error(f"خطأ في تحليل التقاطعات لـ {asset}: {str(e)}")

            # 3. دمج النتائج
            enhanced_analysis = {
                'asset': asset,
                'timestamp': datetime.now().isoformat(),

                # التحليل الفني الأساسي
                'technical_signal': {
                    'direction': combined_signal.direction.value,
                    'strength': combined_signal.overall_strength.value,
                    'confidence': combined_signal.confidence_score,
                    'supporting_signals': len(combined_signal.supporting_signals),
                    'conflicting_signals': len(combined_signal.conflicting_signals),
                    'is_valid': self.is_signal_valid(combined_signal)
                },

                # تحليل التقاطعات
                'crossover_analysis': None,

                # التقييم المدمج
                'combined_assessment': {
                    'overall_direction': combined_signal.direction.value,
                    'overall_confidence': combined_signal.confidence_score,
                    'is_tradeable': self.is_signal_valid(combined_signal),
                    'recommended_action': 'WAIT',
                    'risk_level': 'MEDIUM'
                }
            }

            # إضافة تحليل التقاطعات إذا توفر
            if crossover_analysis:
                enhanced_analysis['crossover_analysis'] = {
                    'total_crossovers': crossover_analysis.total_crossovers,
                    'direction': crossover_analysis.overall_direction.value,
                    'strength': crossover_analysis.overall_strength.value,
                    'confidence': crossover_analysis.overall_confidence,
                    'is_tradeable': crossover_analysis.is_tradeable,
                    'recommended_action': crossover_analysis.recommended_action,
                    'risk_level': crossover_analysis.risk_level,
                    'key_crossovers': [
                        {
                            'type': event.crossover_type.value,
                            'direction': event.direction.value,
                            'confidence': event.confidence,
                            'indicators': f"{event.indicator1_name} x {event.indicator2_name}"
                        }
                        for event in crossover_analysis.crossover_events[:3]
                    ]
                }

                # تحديث التقييم المدمج بناء على التقاطعات
                enhanced_analysis['combined_assessment'] = self._combine_technical_and_crossover_analysis(
                    combined_signal, crossover_analysis
                )

            logger.info(f"تم إكمال التحليل الشامل لـ {asset}")
            return enhanced_analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل لـ {asset}: {str(e)}")
            return None

    def _combine_technical_and_crossover_analysis(self, technical_signal: CombinedSignal, crossover_analysis) -> Dict[str, Any]:
        """دمج التحليل الفني مع تحليل التقاطعات"""
        try:
            # حساب الثقة المدمجة (70% فني + 30% تقاطعات)
            combined_confidence = (technical_signal.confidence_score * 0.7) + (crossover_analysis.overall_confidence * 0.3)

            # تحديد الاتجاه المدمج
            if (technical_signal.direction == crossover_analysis.overall_direction and
                technical_signal.direction != SignalDirection.NEUTRAL):
                # اتفاق في الاتجاه
                overall_direction = technical_signal.direction.value
                confidence_boost = 10  # مكافأة للاتفاق
            elif technical_signal.direction == SignalDirection.NEUTRAL:
                # الاعتماد على التقاطعات
                overall_direction = crossover_analysis.overall_direction.value
                confidence_boost = 0
            elif crossover_analysis.overall_direction == SignalDirection.NEUTRAL:
                # الاعتماد على التحليل الفني
                overall_direction = technical_signal.direction.value
                confidence_boost = 0
            else:
                # تضارب في الاتجاه
                overall_direction = 'NEUTRAL'
                confidence_boost = -15  # خصم للتضارب

            final_confidence = min(95, max(30, combined_confidence + confidence_boost))

            # تحديد قابلية التداول
            is_tradeable = (
                final_confidence >= 65 and
                overall_direction != 'NEUTRAL' and
                (self.is_signal_valid(technical_signal) or crossover_analysis.is_tradeable)
            )

            # تحديد الإجراء المطلوب
            if is_tradeable:
                if overall_direction == 'BULLISH':
                    recommended_action = 'BUY'
                elif overall_direction == 'BEARISH':
                    recommended_action = 'SELL'
                else:
                    recommended_action = 'WAIT'
            else:
                recommended_action = 'WAIT'

            # تحديد مستوى المخاطر
            if final_confidence >= 80:
                risk_level = 'LOW'
            elif final_confidence >= 65:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'HIGH'

            return {
                'overall_direction': overall_direction,
                'overall_confidence': round(final_confidence, 2),
                'is_tradeable': is_tradeable,
                'recommended_action': recommended_action,
                'risk_level': risk_level,
                'analysis_agreement': technical_signal.direction == crossover_analysis.overall_direction,
                'confidence_sources': {
                    'technical_weight': 70,
                    'crossover_weight': 30,
                    'agreement_boost': confidence_boost
                }
            }

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {str(e)}")
            return {
                'overall_direction': 'NEUTRAL',
                'overall_confidence': 50,
                'is_tradeable': False,
                'recommended_action': 'WAIT',
                'risk_level': 'HIGH'
            }

# إنشاء instance عام للاستخدام
technical_signals_engine = TechnicalSignalsEngine()

# استيراد نظام تقاطع المؤشرات الذكي
try:
    from data_layer.smart_crossover_analyzer import smart_crossover_analyzer
    logger.info("تم تحميل نظام تقاطع المؤشرات الذكي بنجاح")
except ImportError as e:
    logger.warning(f"لم يتم تحميل نظام تقاطع المؤشرات: {str(e)}")
    smart_crossover_analyzer = None
