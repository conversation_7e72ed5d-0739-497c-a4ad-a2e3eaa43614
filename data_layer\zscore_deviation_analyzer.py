"""
نظام Z-Score والانحراف السعري - المرحلة الرابعة
Z-Score and Price Deviation Analysis System for Advanced Trading Strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import statistics
import math
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors

logger = scalping_logger.get_logger("zscore_deviation_analyzer")

class DeviationLevel(Enum):
    """مستوى الانحراف"""
    EXTREME_HIGH = "EXTREME_HIGH"        # انحراف عالي جداً (+3σ)
    HIGH = "HIGH"                        # انحراف عالي (+2σ)
    MODERATE_HIGH = "MODERATE_HIGH"      # انحراف معتدل عالي (+1σ)
    NORMAL = "NORMAL"                    # طبيعي (±1σ)
    MODERATE_LOW = "MODERATE_LOW"        # انحراف معتدل منخفض (-1σ)
    LOW = "LOW"                          # انحراف منخفض (-2σ)
    EXTREME_LOW = "EXTREME_LOW"          # انحراف منخفض جداً (-3σ)

class DeviationSignal(Enum):
    """إشارة الانحراف"""
    STRONG_REVERSION_BUY = "STRONG_REVERSION_BUY"    # عودة قوية للشراء
    REVERSION_BUY = "REVERSION_BUY"                  # عودة للشراء
    NEUTRAL = "NEUTRAL"                              # محايد
    REVERSION_SELL = "REVERSION_SELL"                # عودة للبيع
    STRONG_REVERSION_SELL = "STRONG_REVERSION_SELL"  # عودة قوية للبيع

class TrendState(Enum):
    """حالة الاتجاه"""
    STRONG_UPTREND = "STRONG_UPTREND"
    UPTREND = "UPTREND"
    SIDEWAYS = "SIDEWAYS"
    DOWNTREND = "DOWNTREND"
    STRONG_DOWNTREND = "STRONG_DOWNTREND"

@dataclass
class ZScoreAnalysis:
    """تحليل Z-Score"""
    zscore_value: float
    deviation_level: DeviationLevel
    signal: DeviationSignal
    confidence: float  # 0-100
    
    # إحصائيات الحساب
    current_price: float
    mean_price: float
    std_deviation: float
    
    # معلومات الفترة
    period_analyzed: int
    data_quality: float
    
    # تحليل الاتجاه
    trend_state: TrendState
    trend_strength: float
    
    # وصف التحليل
    description: str
    reasoning: str
    
    # بيانات إضافية
    supporting_data: Dict[str, Any]

@dataclass
class DeviationAnalysisResult:
    """نتيجة تحليل الانحراف السعري"""
    asset: str
    analysis_timestamp: datetime
    processing_time_ms: float
    
    # تحليلات Z-Score لفترات مختلفة
    zscore_analyses: List[ZScoreAnalysis]
    
    # التقييم الإجمالي
    overall_deviation: DeviationLevel
    overall_signal: DeviationSignal
    overall_confidence: float
    
    # إحصائيات متقدمة
    price_percentile: float      # المئوية السعرية
    volatility_zscore: float     # Z-Score للتقلبات
    volume_zscore: float         # Z-Score للحجم
    
    # توصيات
    is_actionable: bool
    recommended_action: str
    entry_probability: float
    risk_assessment: str
    recommendations: List[str]

class ZScoreDeviationAnalyzer:
    """محلل Z-Score والانحراف السعري"""
    
    def __init__(self):
        self.analysis_periods = [20, 50, 100]  # فترات التحليل
        self.zscore_thresholds = {
            'extreme': 3.0,      # انحراف شديد
            'high': 2.0,         # انحراف عالي
            'moderate': 1.0,     # انحراف معتدل
            'normal': 0.5        # طبيعي
        }
        self.min_confidence_threshold = 70.0
        self.deviation_history = {}  # تاريخ الانحراف لكل أصل
        
        logger.info("تم تهيئة محلل Z-Score والانحراف السعري")

    @handle_async_errors(default_return=None, log_error=True)
    async def analyze_zscore_deviation(self, asset: str, candles_data: List[Dict[str, Any]]) -> Optional[DeviationAnalysisResult]:
        """تحليل Z-Score والانحراف السعري"""
        start_time = time.time()
        
        try:
            if not candles_data or len(candles_data) < 20:
                logger.warning(f"بيانات غير كافية لتحليل Z-Score لـ {asset}")
                return None
            
            logger.info(f"بدء تحليل Z-Score والانحراف السعري لـ {asset} مع {len(candles_data)} شمعة")
            
            # 1. تحليل Z-Score لفترات مختلفة
            zscore_analyses = []
            for period in self.analysis_periods:
                if len(candles_data) >= period:
                    analysis = await self._analyze_zscore_period(candles_data, period)
                    if analysis:
                        zscore_analyses.append(analysis)
            
            if not zscore_analyses:
                logger.warning(f"لم يتم إنتاج أي تحليل Z-Score لـ {asset}")
                return None
            
            # 2. حساب الإحصائيات المتقدمة
            price_percentile = await self._calculate_price_percentile(candles_data)
            volatility_zscore = await self._calculate_volatility_zscore(candles_data)
            volume_zscore = await self._calculate_volume_zscore(candles_data)
            
            # 3. التقييم الإجمالي
            overall_assessment = self._evaluate_overall_deviation(zscore_analyses)
            
            # 4. إنتاج التوصيات
            recommendations = self._generate_deviation_recommendations(zscore_analyses, overall_assessment)
            
            processing_time = (time.time() - start_time) * 1000
            
            result = DeviationAnalysisResult(
                asset=asset,
                analysis_timestamp=datetime.now(),
                processing_time_ms=processing_time,
                zscore_analyses=zscore_analyses,
                overall_deviation=overall_assessment['deviation'],
                overall_signal=overall_assessment['signal'],
                overall_confidence=overall_assessment['confidence'],
                price_percentile=price_percentile,
                volatility_zscore=volatility_zscore,
                volume_zscore=volume_zscore,
                is_actionable=overall_assessment['is_actionable'],
                recommended_action=overall_assessment['action'],
                entry_probability=overall_assessment['entry_probability'],
                risk_assessment=overall_assessment['risk'],
                recommendations=recommendations
            )
            
            # حفظ في التاريخ
            if asset not in self.deviation_history:
                self.deviation_history[asset] = []
            
            self.deviation_history[asset].append(result)
            
            # الاحتفاظ بآخر 30 تحليل فقط
            if len(self.deviation_history[asset]) > 30:
                self.deviation_history[asset] = self.deviation_history[asset][-30:]
            
            logger.info(f"تم إكمال تحليل Z-Score لـ {asset}: {overall_assessment['deviation'].value}, الإشارة: {overall_assessment['signal'].value}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل Z-Score والانحراف السعري لـ {asset}: {str(e)}")
            return None

    async def _analyze_zscore_period(self, candles: List[Dict[str, Any]], period: int) -> Optional[ZScoreAnalysis]:
        """تحليل Z-Score لفترة محددة"""
        try:
            if len(candles) < period:
                return None
            
            # استخراج الأسعار للفترة المحددة
            recent_candles = candles[-period:]
            prices = [candle['close'] for candle in recent_candles]
            
            # حساب الإحصائيات الأساسية
            current_price = prices[-1]
            mean_price = statistics.mean(prices)
            std_deviation = statistics.stdev(prices) if len(prices) > 1 else 0
            
            if std_deviation == 0:
                logger.warning(f"انحراف معياري صفر للفترة {period}")
                return None
            
            # حساب Z-Score
            zscore_value = (current_price - mean_price) / std_deviation
            
            # تحديد مستوى الانحراف
            deviation_level = self._determine_deviation_level(zscore_value)
            
            # تحديد الإشارة
            signal = self._determine_deviation_signal(zscore_value, deviation_level)
            
            # تحليل الاتجاه
            trend_state, trend_strength = await self._analyze_trend_state(recent_candles)
            
            # حساب جودة البيانات
            data_quality = self._calculate_data_quality(prices)
            
            # حساب الثقة
            confidence = self._calculate_zscore_confidence(zscore_value, deviation_level, data_quality, trend_strength)
            
            return ZScoreAnalysis(
                zscore_value=zscore_value,
                deviation_level=deviation_level,
                signal=signal,
                confidence=confidence,
                current_price=current_price,
                mean_price=mean_price,
                std_deviation=std_deviation,
                period_analyzed=period,
                data_quality=data_quality,
                trend_state=trend_state,
                trend_strength=trend_strength,
                description=f"Z-Score {period} شموع: {zscore_value:.2f} ({deviation_level.value})",
                reasoning=f"السعر الحالي {current_price:.5f} ينحرف بـ {zscore_value:.2f} انحراف معياري عن المتوسط {mean_price:.5f}",
                supporting_data={
                    'period': period,
                    'prices_sample': prices[-5:],  # آخر 5 أسعار
                    'price_range': max(prices) - min(prices)
                }
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل Z-Score للفترة {period}: {str(e)}")
            return None

    def _determine_deviation_level(self, zscore: float) -> DeviationLevel:
        """تحديد مستوى الانحراف"""
        try:
            abs_zscore = abs(zscore)

            if abs_zscore >= self.zscore_thresholds['extreme']:
                return DeviationLevel.EXTREME_HIGH if zscore > 0 else DeviationLevel.EXTREME_LOW
            elif abs_zscore >= self.zscore_thresholds['high']:
                return DeviationLevel.HIGH if zscore > 0 else DeviationLevel.LOW
            elif abs_zscore >= self.zscore_thresholds['moderate']:
                return DeviationLevel.MODERATE_HIGH if zscore > 0 else DeviationLevel.MODERATE_LOW
            else:
                return DeviationLevel.NORMAL

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى الانحراف: {str(e)}")
            return DeviationLevel.NORMAL

    def _determine_deviation_signal(self, zscore: float, deviation_level: DeviationLevel) -> DeviationSignal:
        """تحديد إشارة الانحراف"""
        try:
            # إشارات العودة للمتوسط (Mean Reversion)
            if deviation_level == DeviationLevel.EXTREME_HIGH:
                return DeviationSignal.STRONG_REVERSION_SELL
            elif deviation_level == DeviationLevel.HIGH:
                return DeviationSignal.REVERSION_SELL
            elif deviation_level == DeviationLevel.EXTREME_LOW:
                return DeviationSignal.STRONG_REVERSION_BUY
            elif deviation_level == DeviationLevel.LOW:
                return DeviationSignal.REVERSION_BUY
            else:
                return DeviationSignal.NEUTRAL

        except Exception as e:
            logger.error(f"خطأ في تحديد إشارة الانحراف: {str(e)}")
            return DeviationSignal.NEUTRAL

    async def _analyze_trend_state(self, candles: List[Dict[str, Any]]) -> tuple[TrendState, float]:
        """تحليل حالة الاتجاه"""
        try:
            if len(candles) < 10:
                return TrendState.SIDEWAYS, 0.5

            prices = [candle['close'] for candle in candles]

            # حساب الاتجاه باستخدام الانحدار الخطي البسيط
            n = len(prices)
            x_values = list(range(n))

            # حساب معامل الارتباط
            x_mean = statistics.mean(x_values)
            y_mean = statistics.mean(prices)

            numerator = sum((x_values[i] - x_mean) * (prices[i] - y_mean) for i in range(n))
            denominator_x = sum((x_values[i] - x_mean) ** 2 for i in range(n))
            denominator_y = sum((prices[i] - y_mean) ** 2 for i in range(n))

            if denominator_x == 0 or denominator_y == 0:
                return TrendState.SIDEWAYS, 0.5

            correlation = numerator / math.sqrt(denominator_x * denominator_y)

            # تحديد قوة الاتجاه
            trend_strength = abs(correlation)

            # تحديد حالة الاتجاه
            if correlation > 0.7:
                trend_state = TrendState.STRONG_UPTREND
            elif correlation > 0.3:
                trend_state = TrendState.UPTREND
            elif correlation < -0.7:
                trend_state = TrendState.STRONG_DOWNTREND
            elif correlation < -0.3:
                trend_state = TrendState.DOWNTREND
            else:
                trend_state = TrendState.SIDEWAYS

            return trend_state, trend_strength

        except Exception as e:
            logger.error(f"خطأ في تحليل حالة الاتجاه: {str(e)}")
            return TrendState.SIDEWAYS, 0.5

    def _calculate_data_quality(self, prices: List[float]) -> float:
        """حساب جودة البيانات"""
        try:
            if len(prices) < 5:
                return 0.5

            # فحص الاستمرارية (عدم وجود فجوات كبيرة)
            price_changes = []
            for i in range(1, len(prices)):
                change = abs(prices[i] - prices[i-1]) / prices[i-1]
                price_changes.append(change)

            if not price_changes:
                return 0.5

            # حساب متوسط التغيير
            avg_change = statistics.mean(price_changes)

            # فحص وجود قيم شاذة
            outliers = [change for change in price_changes if change > avg_change * 3]
            outlier_ratio = len(outliers) / len(price_changes)

            # حساب جودة البيانات (كلما قل عدد القيم الشاذة، زادت الجودة)
            quality = max(0.1, 1.0 - outlier_ratio)

            return quality

        except Exception as e:
            logger.error(f"خطأ في حساب جودة البيانات: {str(e)}")
            return 0.5

    def _calculate_zscore_confidence(self, zscore: float, deviation_level: DeviationLevel, data_quality: float, trend_strength: float) -> float:
        """حساب ثقة Z-Score"""
        try:
            # النقاط الأساسية من قوة الانحراف
            abs_zscore = abs(zscore)
            base_confidence = min(60, abs_zscore * 20)

            # مكافأة لمستوى الانحراف
            if deviation_level in [DeviationLevel.EXTREME_HIGH, DeviationLevel.EXTREME_LOW]:
                level_bonus = 25
            elif deviation_level in [DeviationLevel.HIGH, DeviationLevel.LOW]:
                level_bonus = 15
            elif deviation_level in [DeviationLevel.MODERATE_HIGH, DeviationLevel.MODERATE_LOW]:
                level_bonus = 10
            else:
                level_bonus = 0

            # مكافأة لجودة البيانات
            quality_bonus = data_quality * 15

            # تعديل حسب قوة الاتجاه (في الأسواق الجانبية، العودة للمتوسط أكثر موثوقية)
            trend_adjustment = (1 - trend_strength) * 10

            total_confidence = base_confidence + level_bonus + quality_bonus + trend_adjustment

            return min(95, max(20, total_confidence))

        except Exception as e:
            logger.error(f"خطأ في حساب ثقة Z-Score: {str(e)}")
            return 50

    async def _calculate_price_percentile(self, candles: List[Dict[str, Any]]) -> float:
        """حساب المئوية السعرية"""
        try:
            if len(candles) < 20:
                return 50.0

            prices = [candle['close'] for candle in candles]
            current_price = prices[-1]

            # حساب المئوية (كم بالمئة من الأسعار أقل من السعر الحالي)
            lower_prices = [p for p in prices if p < current_price]
            percentile = (len(lower_prices) / len(prices)) * 100

            return percentile

        except Exception as e:
            logger.error(f"خطأ في حساب المئوية السعرية: {str(e)}")
            return 50.0

    async def _calculate_volatility_zscore(self, candles: List[Dict[str, Any]]) -> float:
        """حساب Z-Score للتقلبات"""
        try:
            if len(candles) < 20:
                return 0.0

            # حساب التقلبات لكل شمعة (High - Low)
            volatilities = []
            for candle in candles:
                volatility = candle['high'] - candle['low']
                volatilities.append(volatility)

            if len(volatilities) < 2:
                return 0.0

            current_volatility = volatilities[-1]
            mean_volatility = statistics.mean(volatilities)
            std_volatility = statistics.stdev(volatilities)

            if std_volatility == 0:
                return 0.0

            volatility_zscore = (current_volatility - mean_volatility) / std_volatility
            return volatility_zscore

        except Exception as e:
            logger.error(f"خطأ في حساب Z-Score للتقلبات: {str(e)}")
            return 0.0

    async def _calculate_volume_zscore(self, candles: List[Dict[str, Any]]) -> float:
        """حساب Z-Score للحجم"""
        try:
            volumes = [candle.get('volume', 0) for candle in candles]
            volumes = [v for v in volumes if v > 0]  # إزالة الأحجام الصفرية

            if len(volumes) < 10:
                return 0.0

            current_volume = volumes[-1]
            mean_volume = statistics.mean(volumes)
            std_volume = statistics.stdev(volumes)

            if std_volume == 0:
                return 0.0

            volume_zscore = (current_volume - mean_volume) / std_volume
            return volume_zscore

        except Exception as e:
            logger.error(f"خطأ في حساب Z-Score للحجم: {str(e)}")
            return 0.0

    def _evaluate_overall_deviation(self, zscore_analyses: List[ZScoreAnalysis]) -> Dict[str, Any]:
        """تقييم الانحراف الإجمالي"""
        try:
            if not zscore_analyses:
                return {
                    'deviation': DeviationLevel.NORMAL,
                    'signal': DeviationSignal.NEUTRAL,
                    'confidence': 0,
                    'is_actionable': False,
                    'action': 'WAIT',
                    'entry_probability': 0,
                    'risk': 'HIGH'
                }

            # حساب متوسط Z-Score مرجح بالثقة
            weighted_zscore = 0
            total_weight = 0

            for analysis in zscore_analyses:
                weight = analysis.confidence / 100
                weighted_zscore += analysis.zscore_value * weight
                total_weight += weight

            if total_weight > 0:
                avg_zscore = weighted_zscore / total_weight
            else:
                avg_zscore = 0

            # تحديد المستوى الإجمالي
            overall_deviation = self._determine_deviation_level(avg_zscore)

            # تحديد الإشارة الإجمالية
            overall_signal = self._determine_deviation_signal(avg_zscore, overall_deviation)

            # حساب الثقة الإجمالية
            confidence_values = [analysis.confidence for analysis in zscore_analyses]
            overall_confidence = statistics.mean(confidence_values)

            # تحديد قابلية التنفيذ
            strong_signals = [a for a in zscore_analyses if abs(a.zscore_value) >= 2.0]
            is_actionable = (
                overall_confidence >= self.min_confidence_threshold and
                len(strong_signals) >= 1 and
                overall_signal != DeviationSignal.NEUTRAL
            )

            # تحديد الإجراء
            if is_actionable:
                if overall_signal in [DeviationSignal.STRONG_REVERSION_BUY, DeviationSignal.REVERSION_BUY]:
                    action = 'BUY'
                elif overall_signal in [DeviationSignal.STRONG_REVERSION_SELL, DeviationSignal.REVERSION_SELL]:
                    action = 'SELL'
                else:
                    action = 'WAIT'
            else:
                action = 'WAIT'

            # حساب احتمالية الدخول
            entry_probability = min(90, abs(avg_zscore) * 30 + overall_confidence * 0.3)

            # تقييم المخاطر
            if abs(avg_zscore) >= 3.0:
                risk = 'LOW'  # انحراف شديد = مخاطر منخفضة للعودة
            elif abs(avg_zscore) >= 2.0:
                risk = 'MEDIUM'
            else:
                risk = 'HIGH'

            return {
                'deviation': overall_deviation,
                'signal': overall_signal,
                'confidence': round(overall_confidence, 2),
                'is_actionable': is_actionable,
                'action': action,
                'entry_probability': round(entry_probability, 2),
                'risk': risk
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم الانحراف الإجمالي: {str(e)}")
            return {
                'deviation': DeviationLevel.NORMAL,
                'signal': DeviationSignal.NEUTRAL,
                'confidence': 0,
                'is_actionable': False,
                'action': 'WAIT',
                'entry_probability': 0,
                'risk': 'HIGH'
            }

    def _generate_deviation_recommendations(self, zscore_analyses: List[ZScoreAnalysis], overall_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج توصيات الانحراف"""
        try:
            recommendations = []

            signal = overall_assessment['signal']
            confidence = overall_assessment['confidence']

            # توصيات عامة
            if signal == DeviationSignal.STRONG_REVERSION_BUY:
                recommendations.append("إشارة شراء قوية - انحراف شديد للأسفل")
                recommendations.append("احتمالية عودة السعر للمتوسط عالية جداً")
                recommendations.append("مناسب للدخول بحجم صفقة أكبر")

            elif signal == DeviationSignal.REVERSION_BUY:
                recommendations.append("إشارة شراء معتدلة - انحراف للأسفل")
                recommendations.append("احتمالية عودة السعر للمتوسط جيدة")
                recommendations.append("مناسب للدخول بحجم صفقة عادي")

            elif signal == DeviationSignal.STRONG_REVERSION_SELL:
                recommendations.append("إشارة بيع قوية - انحراف شديد للأعلى")
                recommendations.append("احتمالية هبوط السعر للمتوسط عالية جداً")
                recommendations.append("مناسب للدخول بحجم صفقة أكبر")

            elif signal == DeviationSignal.REVERSION_SELL:
                recommendations.append("إشارة بيع معتدلة - انحراف للأعلى")
                recommendations.append("احتمالية هبوط السعر للمتوسط جيدة")
                recommendations.append("مناسب للدخول بحجم صفقة عادي")

            else:
                recommendations.append("لا توجد إشارة واضحة - السعر قريب من المتوسط")
                recommendations.append("انتظار انحراف أكبر للدخول")

            # توصيات حسب الثقة
            if confidence >= 85:
                recommendations.append("ثقة عالية جداً في الإشارة")
            elif confidence >= 75:
                recommendations.append("ثقة عالية في الإشارة")
            elif confidence >= 65:
                recommendations.append("ثقة معتدلة في الإشارة")
            else:
                recommendations.append("ثقة منخفضة - تحتاج تأكيد إضافي")

            # توصيات حسب التحليلات المتعددة
            extreme_analyses = [a for a in zscore_analyses if abs(a.zscore_value) >= 3.0]
            if extreme_analyses:
                recommendations.append(f"انحراف شديد في {len(extreme_analyses)} فترة زمنية")
                recommendations.append("فرصة ممتازة للعودة للمتوسط")

            # توصيات الاتجاه
            trend_states = [a.trend_state for a in zscore_analyses]
            sideways_count = trend_states.count(TrendState.SIDEWAYS)
            if sideways_count >= len(trend_states) // 2:
                recommendations.append("السوق في حالة جانبية - مثالي لاستراتيجية العودة للمتوسط")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات الانحراف: {str(e)}")
            return ["خطأ في إنتاج التوصيات"]

    def get_deviation_history(self, asset: str, limit: int = 10) -> List[DeviationAnalysisResult]:
        """الحصول على تاريخ تحليل الانحراف"""
        try:
            if asset not in self.deviation_history:
                return []

            return self.deviation_history[asset][-limit:]

        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ الانحراف لـ {asset}: {str(e)}")
            return []

    def get_current_deviation_summary(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملخص الانحراف الحالي"""
        try:
            if asset not in self.deviation_history or not self.deviation_history[asset]:
                return None

            latest_analysis = self.deviation_history[asset][-1]

            return {
                'asset': asset,
                'timestamp': latest_analysis.analysis_timestamp,
                'overall_deviation': latest_analysis.overall_deviation.value,
                'overall_signal': latest_analysis.overall_signal.value,
                'confidence': latest_analysis.overall_confidence,
                'is_actionable': latest_analysis.is_actionable,
                'recommended_action': latest_analysis.recommended_action,
                'entry_probability': latest_analysis.entry_probability,
                'risk_assessment': latest_analysis.risk_assessment,
                'price_percentile': latest_analysis.price_percentile,
                'zscore_analyses_count': len(latest_analysis.zscore_analyses)
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص الانحراف لـ {asset}: {str(e)}")
            return None

# إنشاء instance عام للاستخدام
zscore_deviation_analyzer = ZScoreDeviationAnalyzer()

if __name__ == "__main__":
    # اختبار سريع
    import asyncio

    async def test_analyzer():
        # بيانات اختبار
        test_candles = []
        base_price = 1.1000

        # إنشاء بيانات اختبار مع انحراف
        for i in range(50):
            if i < 40:
                # أسعار عادية
                price = base_price + (i * 0.0001)
            else:
                # انحراف شديد
                price = base_price + 0.01  # انحراف كبير

            candle = {
                'open': price,
                'high': price + 0.0002,
                'low': price - 0.0002,
                'close': price,
                'volume': 1000 + i * 10,
                'timestamp': i
            }
            test_candles.append(candle)

        # تشغيل التحليل
        result = await zscore_deviation_analyzer.analyze_zscore_deviation("TEST_PAIR", test_candles)

        if result:
            print(f"تحليل Z-Score:")
            print(f"الانحراف الإجمالي: {result.overall_deviation.value}")
            print(f"الإشارة: {result.overall_signal.value}")
            print(f"الثقة: {result.overall_confidence}%")
            print(f"قابل للتنفيذ: {result.is_actionable}")
            print(f"الإجراء المقترح: {result.recommended_action}")
            print(f"احتمالية الدخول: {result.entry_probability}%")
            print(f"تقييم المخاطر: {result.risk_assessment}")

            print(f"\nتحليلات Z-Score:")
            for analysis in result.zscore_analyses:
                print(f"  فترة {analysis.period_analyzed}: Z-Score = {analysis.zscore_value:.2f}, الثقة = {analysis.confidence:.1f}%")
        else:
            print("فشل في التحليل")

    # تشغيل الاختبار
    asyncio.run(test_analyzer())
