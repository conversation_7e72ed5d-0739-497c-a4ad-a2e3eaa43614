"""
مدير اتصالات قاعدة البيانات لنظام السكالبينغ
"""

import asyncio
import time
from contextlib import contextmanager, asynccontextmanager
from typing import Generator, AsyncGenerator, Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import QueuePool, NullPool
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError, OperationalError
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from config.database_config import DatabaseConfig, default_db_config
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, DatabaseError, ConnectionError

logger = scalping_logger.get_logger("database_manager")

@dataclass
class ConnectionStats:
    """إحصائيات الاتصال بقاعدة البيانات"""
    total_connections: int = 0
    total_disconnections: int = 0
    total_reconnections: int = 0
    total_failures: int = 0
    connection_errors: int = 0
    last_connection_time: Optional[datetime] = None
    last_disconnection_time: Optional[datetime] = None
    last_error_time: Optional[datetime] = None
    uptime_seconds: float = 0.0
    longest_uptime_seconds: float = 0.0

@dataclass
class ConnectionEvent:
    """حدث اتصال"""
    timestamp: datetime
    event_type: str  # CONNECT, DISCONNECT, RECONNECT, ERROR, RECOVERY
    details: str
    duration_seconds: float = 0.0

class DatabaseManager:
    """مدير قاعدة البيانات المتزامن وغير المتزامن"""
    
    def __init__(self, config: DatabaseConfig = None):
        self.config = config or default_db_config
        self.engine = None
        self.async_engine = None
        self.SessionLocal = None
        self.AsyncSessionLocal = None
        self.metadata = MetaData()
        self._is_initialized = False
        self._connection_pool_info = {}

        # إحصائيات ومراقبة الاتصال
        self.stats = ConnectionStats()
        self.events: List[ConnectionEvent] = []
        self.max_events = 1000
        self._connection_start_time: Optional[datetime] = None

        # إعدادات إعادة الاتصال
        self.auto_reconnect = True
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 2  # ثوانٍ
        self.reconnect_backoff = 1.5
        self.max_reconnect_delay = 60  # دقيقة واحدة

        # callbacks للأحداث
        self.on_connect_callbacks: List[Callable] = []
        self.on_disconnect_callbacks: List[Callable] = []
        self.on_error_callbacks: List[Callable] = []

    def _add_event(self, event_type: str, details: str, duration: float = 0.0):
        """إضافة حدث جديد"""
        try:
            event = ConnectionEvent(
                timestamp=datetime.now(),
                event_type=event_type,
                details=details,
                duration_seconds=duration
            )

            self.events.append(event)

            # الحفاظ على الحد الأقصى للأحداث
            if len(self.events) > self.max_events:
                self.events = self.events[-self.max_events:]

            logger.debug(f"حدث قاعدة البيانات: {event_type} - {details}")

        except Exception as e:
            logger.error(f"خطأ في إضافة حدث قاعدة البيانات: {str(e)}")

    def _execute_callbacks(self, callbacks: List[Callable]):
        """تنفيذ callbacks"""
        for callback in callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"خطأ في تنفيذ callback: {str(e)}")

    def _handle_connection_success(self):
        """معالجة نجاح الاتصال"""
        self._connection_start_time = datetime.now()
        self.stats.total_connections += 1
        self.stats.last_connection_time = datetime.now()
        self._add_event("CONNECT", "تم الاتصال بقاعدة البيانات بنجاح")
        self._execute_callbacks(self.on_connect_callbacks)

    def _handle_connection_failure(self, error: str):
        """معالجة فشل الاتصال"""
        self.stats.total_failures += 1
        self.stats.connection_errors += 1
        self.stats.last_error_time = datetime.now()
        self._add_event("ERROR", f"فشل الاتصال: {error}")
        self._execute_callbacks(self.on_error_callbacks)

    def _handle_disconnection(self, reason: str = "انقطاع غير متوقع"):
        """معالجة انقطاع الاتصال"""
        uptime = 0.0
        if self._connection_start_time:
            uptime = (datetime.now() - self._connection_start_time).total_seconds()
            self.stats.uptime_seconds = uptime

            if uptime > self.stats.longest_uptime_seconds:
                self.stats.longest_uptime_seconds = uptime

        self.stats.total_disconnections += 1
        self.stats.last_disconnection_time = datetime.now()
        self._add_event("DISCONNECT", reason, uptime)
        self._execute_callbacks(self.on_disconnect_callbacks)
    
    @handle_errors(raise_on_error=True)
    def initialize(self):
        """تهيئة اتصالات قاعدة البيانات"""
        if self._is_initialized:
            logger.info("مدير قاعدة البيانات مُهيأ مسبقاً")
            return
        
        try:
            # التحقق من صحة الإعدادات
            if not self.config.validate():
                raise DatabaseError("إعدادات قاعدة البيانات غير صالحة")
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            self._create_database_if_not_exists()
            
            # تهيئة المحرك المتزامن
            self._initialize_sync_engine()
            
            # تهيئة المحرك غير المتزامن
            # self._initialize_async_engine()

            # اختبار الاتصالات
            self._test_sync_connection()
            
            self._is_initialized = True
            self._handle_connection_success()
            logger.info("تم تهيئة مدير قاعدة البيانات بنجاح")

        except Exception as e:
            self._handle_connection_failure(str(e))
            logger.error(f"فشل في تهيئة قاعدة البيانات: {str(e)}")
            raise DatabaseError(f"فشل تهيئة قاعدة البيانات: {str(e)}")
    
    def _create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # حفظ اسم قاعدة البيانات الأصلي
            original_database = self.config.database

            # الاتصال بقاعدة postgres الافتراضية لإنشاء قاعدة البيانات
            conn = psycopg2.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database="postgres"
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            cursor = conn.cursor()
            
            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (original_database,)
            )

            if not cursor.fetchone():
                # إنشاء قاعدة البيانات
                cursor.execute(f'CREATE DATABASE "{original_database}"')
                logger.info(f"تم إنشاء قاعدة البيانات: {original_database}")

            cursor.close()
            conn.close()

            # التأكد من أن إعدادات قاعدة البيانات لم تتغير
            self.config.database = original_database
            
        except Exception as e:
            logger.warning(f"تعذر إنشاء قاعدة البيانات: {str(e)}")
    
    def _initialize_sync_engine(self):
        """تهيئة المحرك المتزامن"""
        try:
            self.engine = create_engine(
                self.config.connection_string,
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                pool_pre_ping=True,
                echo=self.config.echo,
                echo_pool=self.config.echo_pool,
                connect_args={
                    "connect_timeout": 10,
                    "application_name": "scalping_system"
                }
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine,
                expire_on_commit=False
            )
            
            logger.debug("تم تهيئة المحرك المتزامن")
            
        except Exception as e:
            raise DatabaseError(f"فشل تهيئة المحرك المتزامن: {str(e)}")
    
    def _initialize_async_engine(self):
        """تهيئة المحرك غير المتزامن"""
        try:
            self.async_engine = create_async_engine(
                self.config.async_connection_string,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                pool_pre_ping=True,
                echo=self.config.echo,
                connect_args={
                    "server_settings": {
                        "application_name": "scalping_system_async"
                    }
                }
            )
            
            self.AsyncSessionLocal = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
            
            logger.debug("تم تهيئة المحرك غير المتزامن")
            
        except Exception as e:
            raise DatabaseError(f"فشل تهيئة المحرك غير المتزامن: {str(e)}")
    
    @handle_errors(raise_on_error=True)
    def _test_sync_connection(self):
        """اختبار الاتصال المتزامن"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                assert result.fetchone()[0] == 1
            logger.debug("اختبار الاتصال المتزامن نجح")
        except Exception as e:
            raise ConnectionError(f"فشل اختبار الاتصال المتزامن: {str(e)}")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """الحصول على جلسة قاعدة البيانات المتزامنة مع معالجة انقطاع الاتصال"""
        if not self._is_initialized:
            self.initialize()

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except (DisconnectionError, OperationalError) as e:
            session.rollback()
            self._handle_disconnection(f"انقطاع الاتصال: {str(e)}")

            # محاولة إعادة الاتصال
            if self.auto_reconnect:
                if self._attempt_reconnect():
                    # إعادة المحاولة مع جلسة جديدة
                    session.close()
                    session = self.SessionLocal()
                    yield session
                    session.commit()
                else:
                    raise DatabaseError(f"فشل في إعادة الاتصال: {str(e)}")
            else:
                raise DatabaseError(f"انقطاع الاتصال: {str(e)}")
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات: {str(e)}")
            raise DatabaseError(f"خطأ في جلسة قاعدة البيانات: {str(e)}")
        finally:
            session.close()

    @handle_errors(default_return=False, log_error=True)
    def _attempt_reconnect(self) -> bool:
        """محاولة إعادة الاتصال"""
        try:
            logger.info("🔄 محاولة إعادة الاتصال بقاعدة البيانات...")

            attempt = 0
            current_delay = self.reconnect_delay

            while attempt < self.max_reconnect_attempts:
                attempt += 1

                try:
                    # إغلاق الاتصالات الحالية
                    if self.engine:
                        self.engine.dispose()

                    # إعادة تهيئة المحرك
                    self._initialize_sync_engine()

                    # اختبار الاتصال
                    self._test_sync_connection()

                    # نجحت إعادة الاتصال
                    self.stats.total_reconnections += 1
                    self._add_event("RECONNECT", f"نجحت إعادة الاتصال في المحاولة {attempt}")
                    logger.info(f"✅ نجحت إعادة الاتصال في المحاولة {attempt}")

                    return True

                except Exception as e:
                    logger.warning(f"❌ فشلت محاولة إعادة الاتصال {attempt}: {str(e)}")

                    if attempt < self.max_reconnect_attempts:
                        logger.info(f"⏳ انتظار {current_delay} ثانية قبل المحاولة التالية")
                        time.sleep(current_delay)
                        current_delay = min(current_delay * self.reconnect_backoff, self.max_reconnect_delay)

            # فشل في جميع المحاولات
            self._add_event("ERROR", f"فشل في إعادة الاتصال بعد {self.max_reconnect_attempts} محاولات")
            logger.error(f"❌ فشل في إعادة الاتصال بعد {self.max_reconnect_attempts} محاولات")
            return False

        except Exception as e:
            logger.error(f"خطأ في محاولة إعادة الاتصال: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def test_connection_with_recovery(self) -> bool:
        """اختبار الاتصال مع إعادة الاتصال التلقائي"""
        try:
            # محاولة الاختبار العادي أولاً
            if self.test_connection():
                return True

            # إذا فشل، محاولة إعادة الاتصال
            logger.warning("فشل اختبار الاتصال، محاولة إعادة الاتصال...")

            if self.auto_reconnect and self._attempt_reconnect():
                return self.test_connection()

            return False

        except Exception as e:
            logger.error(f"خطأ في اختبار الاتصال مع الاستعادة: {str(e)}")
            return False

    def get_connection_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاتصال المفصلة"""
        try:
            current_uptime = 0.0
            if self._connection_start_time:
                current_uptime = (datetime.now() - self._connection_start_time).total_seconds()

            base_info = self.get_connection_info()

            stats_info = {
                "connection_statistics": {
                    "total_connections": self.stats.total_connections,
                    "total_disconnections": self.stats.total_disconnections,
                    "total_reconnections": self.stats.total_reconnections,
                    "total_failures": self.stats.total_failures,
                    "connection_errors": self.stats.connection_errors,
                    "current_uptime_hours": round(current_uptime / 3600, 2),
                    "longest_uptime_hours": round(self.stats.longest_uptime_seconds / 3600, 2),
                    "last_connection": self.stats.last_connection_time.isoformat() if self.stats.last_connection_time else None,
                    "last_disconnection": self.stats.last_disconnection_time.isoformat() if self.stats.last_disconnection_time else None,
                    "last_error": self.stats.last_error_time.isoformat() if self.stats.last_error_time else None
                },
                "reconnection_settings": {
                    "auto_reconnect": self.auto_reconnect,
                    "max_attempts": self.max_reconnect_attempts,
                    "base_delay": self.reconnect_delay,
                    "max_delay": self.max_reconnect_delay
                },
                "recent_events": [
                    {
                        "timestamp": event.timestamp.isoformat(),
                        "type": event.event_type,
                        "details": event.details,
                        "duration": event.duration_seconds
                    }
                    for event in self.events[-10:]  # آخر 10 أحداث
                ]
            }

            # دمج المعلومات
            base_info.update(stats_info)
            return base_info

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الاتصال: {str(e)}")
            return self.get_connection_info()

    # إضافة callbacks
    def on_connect(self, callback: Callable):
        """إضافة callback للاتصال"""
        self.on_connect_callbacks.append(callback)

    def on_disconnect(self, callback: Callable):
        """إضافة callback لقطع الاتصال"""
        self.on_disconnect_callbacks.append(callback)

    def on_error(self, callback: Callable):
        """إضافة callback للأخطاء"""
        self.on_error_callbacks.append(callback)
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """الحصول على جلسة قاعدة البيانات غير المتزامنة"""
        if not self._is_initialized:
            self.initialize()
        
        session = self.AsyncSessionLocal()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات غير المتزامنة: {str(e)}")
            raise DatabaseError(f"خطأ في جلسة قاعدة البيانات غير المتزامنة: {str(e)}")
        finally:
            await session.close()
    
    @handle_errors(default_return=False, log_error=True)
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {str(e)}")
            return False
    
    @handle_async_errors(default_return=False, log_error=True)
    async def test_async_connection(self) -> bool:
        """اختبار الاتصال غير المتزامن"""
        try:
            async with self.get_async_session() as session:
                await session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال غير المتزامن: {str(e)}")
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال"""
        info = {
            "is_initialized": self._is_initialized,
            "database": self.config.database,
            "host": self.config.host,
            "port": self.config.port,
            "pool_size": self.config.pool_size,
            "max_overflow": self.config.max_overflow
        }
        
        if self.engine:
            pool = self.engine.pool
            info.update({
                "pool_checked_in": pool.checkedin(),
                "pool_checked_out": pool.checkedout(),
                "pool_overflow": pool.overflow(),
                "pool_size": pool.size()
            })
        
        return info
    
    @handle_errors(log_error=True)
    def execute_raw_sql(self, sql: str, params: Dict = None) -> Any:
        """تنفيذ SQL خام"""
        try:
            with self.get_session() as session:
                result = session.execute(text(sql), params or {})
                return result.fetchall()
        except Exception as e:
            logger.error(f"خطأ في تنفيذ SQL: {sql} - {str(e)}")
            raise DatabaseError(f"خطأ في تنفيذ SQL: {str(e)}")
    
    @handle_async_errors(log_error=True)
    async def execute_async_raw_sql(self, sql: str, params: Dict = None) -> Any:
        """تنفيذ SQL خام غير متزامن"""
        try:
            async with self.get_async_session() as session:
                result = await session.execute(text(sql), params or {})
                return await result.fetchall()
        except Exception as e:
            logger.error(f"خطأ في تنفيذ SQL غير متزامن: {sql} - {str(e)}")
            raise DatabaseError(f"خطأ في تنفيذ SQL غير متزامن: {str(e)}")
    
    def cleanup_connections(self):
        """تنظيف الاتصالات المعلقة"""
        try:
            if self.engine and hasattr(self.engine, 'pool'):
                # تنظيف pool الاتصالات
                if hasattr(self.engine.pool, 'invalidate'):
                    self.engine.pool.invalidate()
                    logger.info("تم تنظيف pool الاتصالات المتزامنة")
                else:
                    # استخدام dispose كبديل
                    self.engine.dispose()
                    logger.info("تم تنظيف الاتصالات المتزامنة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الاتصالات: {str(e)}")

    def reset_connection_pool(self):
        """إعادة تهيئة pool الاتصالات"""
        try:
            if self.engine:
                # إعادة إنشاء pool الاتصالات
                self.engine.dispose()
                self.engine = create_engine(
                    self.config.connection_string,
                    pool_size=10,
                    max_overflow=20,
                    pool_timeout=30,
                    pool_recycle=3600,
                    echo=False
                )
                logger.info("تم إعادة تهيئة pool الاتصالات المتزامنة")

            if self.async_engine:
                # إعادة إنشاء async pool
                asyncio.run(self.async_engine.dispose())
                self.async_engine = create_async_engine(
                    self.config.async_connection_string,
                    pool_size=10,
                    max_overflow=20,
                    pool_timeout=30,
                    pool_recycle=3600,
                    echo=False
                )
                logger.info("تم إعادة تهيئة pool الاتصالات غير المتزامنة")

        except Exception as e:
            logger.error(f"خطأ في إعادة تهيئة pool الاتصالات: {str(e)}")

    def close_connections(self):
        """إغلاق جميع الاتصالات"""
        try:
            if self.engine:
                self.engine.dispose()
                logger.info("تم إغلاق المحرك المتزامن")

            if self.async_engine:
                asyncio.run(self.async_engine.dispose())
                logger.info("تم إغلاق المحرك غير المتزامن")

            self._is_initialized = False

        except Exception as e:
            logger.error(f"خطأ في إغلاق الاتصالات: {str(e)}")
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        try:
            self.close_connections()
        except:
            pass

# إنشاء instance عام لمدير قاعدة البيانات
db_manager = DatabaseManager()
