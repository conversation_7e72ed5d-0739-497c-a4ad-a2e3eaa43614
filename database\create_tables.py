"""
إنشاء جداول قاعدة البيانات لنظام السكالبينغ
"""

from sqlalchemy import text
from database.models import Base
from database.connection_manager import db_manager
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, DatabaseError

logger = scalping_logger.get_logger("create_tables")

@handle_errors(raise_on_error=True)
def create_all_tables():
    """إنشاء جميع الجداول"""
    try:
        # تهيئة مدير قاعدة البيانات
        db_manager.initialize()
        
        # إنشاء الجداول
        Base.metadata.create_all(bind=db_manager.engine)
        
        logger.info("تم إنشاء جميع الجداول بنجاح")
        
        # إنشاء الفهارس الإضافية
        create_additional_indexes()
        
        # إنشاء الـ Views المفيدة
        create_useful_views()
        
        logger.info("تم إعداد قاعدة البيانات بالكامل")
        
    except Exception as e:
        logger.error(f"فشل في إنشاء الجداول: {str(e)}")
        raise DatabaseError(f"فشل إنشاء الجداول: {str(e)}")

@handle_errors(log_error=True)
def create_additional_indexes():
    """إنشاء فهارس إضافية لتحسين الأداء"""
    additional_indexes = [
        # فهارس للبيانات التاريخية
        "CREATE INDEX IF NOT EXISTS idx_historical_data_close_price ON historical_data(close_price);",
        "CREATE INDEX IF NOT EXISTS idx_historical_data_volume ON historical_data(volume) WHERE volume > 0;",
        
        # فهارس للمؤشرات الفنية
        "CREATE INDEX IF NOT EXISTS idx_technical_indicators_signal ON technical_indicators(indicator_signal);",
        "CREATE INDEX IF NOT EXISTS idx_technical_indicators_confidence ON technical_indicators(confidence_score);",
        
        # فهارس للصفقات
        "CREATE INDEX IF NOT EXISTS idx_trades_profit ON trades(profit);",
        "CREATE INDEX IF NOT EXISTS idx_trades_confidence ON trades(confidence_score);",
        "CREATE INDEX IF NOT EXISTS idx_trades_duration ON trades(duration);",
        
        # فهارس للأداء
        "CREATE INDEX IF NOT EXISTS idx_system_performance_session ON system_performance(session_id, measurement_time);",
        "CREATE INDEX IF NOT EXISTS idx_ai_model_performance_accuracy ON ai_model_performance(accuracy);",
        
        # فهارس لحالة السوق
        "CREATE INDEX IF NOT EXISTS idx_market_conditions_state ON market_conditions(market_state);",
        "CREATE INDEX IF NOT EXISTS idx_market_conditions_volatility ON market_conditions(volatility_level);"
    ]
    
    try:
        with db_manager.get_session() as session:
            for index_sql in additional_indexes:
                session.execute(text(index_sql))
        
        logger.info("تم إنشاء الفهارس الإضافية بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفهارس الإضافية: {str(e)}")

@handle_errors(log_error=True)
def create_useful_views():
    """إنشاء Views مفيدة للاستعلامات المعقدة"""
    views = [
        # View للإحصائيات اليومية
        """
        CREATE OR REPLACE VIEW daily_trading_stats AS
        SELECT 
            DATE(entry_time) as trading_date,
            asset,
            COUNT(*) as total_trades,
            COUNT(CASE WHEN result = 'WIN' THEN 1 END) as winning_trades,
            COUNT(CASE WHEN result = 'LOSS' THEN 1 END) as losing_trades,
            COUNT(CASE WHEN result = 'DRAW' THEN 1 END) as draw_trades,
            ROUND(
                (COUNT(CASE WHEN result = 'WIN' THEN 1 END) * 100.0 / COUNT(*)), 2
            ) as win_rate,
            ROUND(SUM(COALESCE(profit, 0)), 2) as total_profit,
            ROUND(AVG(COALESCE(confidence_score, 0)), 2) as avg_confidence
        FROM trades 
        WHERE entry_time >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(entry_time), asset
        ORDER BY trading_date DESC, asset;
        """,
        
        # View لأداء المؤشرات
        """
        CREATE OR REPLACE VIEW indicator_performance AS
        SELECT 
            indicator_name,
            asset,
            COUNT(*) as signal_count,
            COUNT(CASE WHEN indicator_signal = 'BULLISH' THEN 1 END) as bullish_signals,
            COUNT(CASE WHEN indicator_signal = 'BEARISH' THEN 1 END) as bearish_signals,
            COUNT(CASE WHEN indicator_signal = 'NEUTRAL' THEN 1 END) as neutral_signals,
            ROUND(AVG(COALESCE(confidence_score, 0)), 2) as avg_confidence,
            ROUND(AVG(COALESCE(calculation_time_ms, 0)), 2) as avg_calc_time_ms
        FROM technical_indicators 
        WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        GROUP BY indicator_name, asset
        ORDER BY indicator_name, asset;
        """,
        
        # View لحالة السوق الحالية
        """
        CREATE OR REPLACE VIEW current_market_state AS
        SELECT DISTINCT ON (asset)
            asset,
            market_state,
            trend_direction,
            trend_strength,
            volatility_level,
            volatility_value,
            current_price,
            is_trading_recommended,
            analysis_confidence,
            timestamp
        FROM market_conditions 
        ORDER BY asset, timestamp DESC;
        """,
        
        # View لأداء النماذج الحديث
        """
        CREATE OR REPLACE VIEW recent_model_performance AS
        SELECT DISTINCT ON (model_name, asset)
            model_name,
            asset,
            accuracy,
            precision,
            recall,
            f1_score,
            training_samples,
            evaluation_date
        FROM ai_model_performance 
        ORDER BY model_name, asset, evaluation_date DESC;
        """
    ]
    
    try:
        with db_manager.get_session() as session:
            for view_sql in views:
                session.execute(text(view_sql))
        
        logger.info("تم إنشاء الـ Views بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الـ Views: {str(e)}")

@handle_errors(log_error=True)
def create_stored_procedures():
    """إنشاء Stored Procedures مفيدة"""
    procedures = [
        # إجراء لحساب إحصائيات التداول
        """
        CREATE OR REPLACE FUNCTION calculate_trading_stats(
            p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
            p_end_date DATE DEFAULT CURRENT_DATE,
            p_asset VARCHAR DEFAULT NULL
        )
        RETURNS TABLE (
            total_trades INTEGER,
            winning_trades INTEGER,
            losing_trades INTEGER,
            win_rate DECIMAL(5,2),
            total_profit DECIMAL(12,2),
            avg_profit_per_trade DECIMAL(10,2),
            max_consecutive_losses INTEGER,
            profit_factor DECIMAL(8,4)
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                COUNT(*)::INTEGER as total_trades,
                COUNT(CASE WHEN result = 'WIN' THEN 1 END)::INTEGER as winning_trades,
                COUNT(CASE WHEN result = 'LOSS' THEN 1 END)::INTEGER as losing_trades,
                ROUND(
                    (COUNT(CASE WHEN result = 'WIN' THEN 1 END) * 100.0 / COUNT(*)), 2
                )::DECIMAL(5,2) as win_rate,
                ROUND(SUM(COALESCE(profit, 0)), 2)::DECIMAL(12,2) as total_profit,
                ROUND(
                    SUM(COALESCE(profit, 0)) / NULLIF(COUNT(*), 0), 2
                )::DECIMAL(10,2) as avg_profit_per_trade,
                0::INTEGER as max_consecutive_losses, -- يحتاج تطبيق معقد
                ROUND(
                    SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END) / 
                    NULLIF(ABS(SUM(CASE WHEN profit < 0 THEN profit ELSE 0 END)), 0), 4
                )::DECIMAL(8,4) as profit_factor
            FROM trades 
            WHERE entry_time >= p_start_date 
                AND entry_time <= p_end_date + INTERVAL '1 day'
                AND (p_asset IS NULL OR asset = p_asset);
        END;
        $$ LANGUAGE plpgsql;
        """,
        
        # إجراء لتنظيف البيانات القديمة
        """
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_days_to_keep INTEGER DEFAULT 90
        )
        RETURNS INTEGER AS $$
        DECLARE
            deleted_count INTEGER := 0;
            cutoff_date TIMESTAMP;
        BEGIN
            cutoff_date := CURRENT_TIMESTAMP - (p_days_to_keep || ' days')::INTERVAL;
            
            -- حذف البيانات التاريخية القديمة (الاحتفاظ بعينة)
            DELETE FROM historical_data 
            WHERE created_at < cutoff_date 
                AND id % 10 != 0; -- الاحتفاظ بكل 10th سجل
            
            GET DIAGNOSTICS deleted_count = ROW_COUNT;
            
            -- حذف المؤشرات القديمة
            DELETE FROM technical_indicators 
            WHERE created_at < cutoff_date;
            
            -- حذف بيانات الأداء القديمة
            DELETE FROM system_performance 
            WHERE measurement_time < cutoff_date;
            
            RETURN deleted_count;
        END;
        $$ LANGUAGE plpgsql;
        """
    ]
    
    try:
        with db_manager.get_session() as session:
            for procedure_sql in procedures:
                session.execute(text(procedure_sql))
        
        logger.info("تم إنشاء الـ Stored Procedures بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الـ Stored Procedures: {str(e)}")

@handle_errors(default_return=False, log_error=True)
def verify_database_setup() -> bool:
    """التحقق من إعداد قاعدة البيانات"""
    try:
        with db_manager.get_session() as session:
            # التحقق من وجود الجداول
            tables_check = session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE'
                ORDER BY table_name;
            """)).fetchall()
            
            expected_tables = [
                'historical_data', 'technical_indicators', 'trades',
                'ai_model_performance', 'system_performance', 'market_conditions'
            ]
            
            existing_tables = [row[0] for row in tables_check]
            missing_tables = [table for table in expected_tables if table not in existing_tables]
            
            if missing_tables:
                logger.error(f"جداول مفقودة: {missing_tables}")
                return False
            
            # التحقق من الفهارس
            indexes_check = session.execute(text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY indexname;
            """)).fetchall()
            
            logger.info(f"الجداول الموجودة: {existing_tables}")
            logger.info(f"عدد الفهارس: {len(indexes_check)}")
            
            return True
            
    except Exception as e:
        logger.error(f"خطأ في التحقق من قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    """تشغيل إعداد قاعدة البيانات"""
    try:
        logger.info("بدء إعداد قاعدة البيانات...")
        
        # إنشاء الجداول
        create_all_tables()
        
        # إنشاء الـ Stored Procedures
        create_stored_procedures()
        
        # التحقق من الإعداد
        if verify_database_setup():
            logger.info("✅ تم إعداد قاعدة البيانات بنجاح")
        else:
            logger.error("❌ فشل في إعداد قاعدة البيانات")
            
    except Exception as e:
        logger.error(f"خطأ في إعداد قاعدة البيانات: {str(e)}")
        raise
