"""
نماذج قاعدة البيانات لنظام السكالبينغ
"""

from sqlalchemy import Column, Integer, String, DECIMAL, TIMESTAMP, BigInteger, JSON, Boolean, Text, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any, List
import json

Base = declarative_base()

class HistoricalData(Base):
    """نموذج البيانات التاريخية للشموع"""
    __tablename__ = "historical_data"
    
    id = Column(Integer, primary_key=True, index=True)
    asset = Column(String(50), nullable=False, index=True)
    timestamp = Column(TIMESTAMP, nullable=False, index=True)
    open_price = Column(DECIMAL(15, 8), nullable=False)
    high_price = Column(DECIMAL(15, 8), nullable=False)
    low_price = Column(DECIMAL(15, 8), nullable=False)
    close_price = Column(DECIMAL(15, 8), nullable=False)
    volume = Column(BigInteger, default=0)
    timeframe = Column(Integer, nullable=False)  # بالثواني
    source = Column(String(50), default="pocket_option")
    data_quality_score = Column(DECIMAL(5, 2), default=100.0)
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    
    # فهارس مركبة لتحسين الأداء
    __table_args__ = (
        Index('idx_asset_timeframe_timestamp', 'asset', 'timeframe', 'timestamp'),
        Index('idx_timestamp_asset', 'timestamp', 'asset'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'asset': self.asset,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'open': float(self.open_price),
            'high': float(self.high_price),
            'low': float(self.low_price),
            'close': float(self.close_price),
            'volume': self.volume,
            'timeframe': self.timeframe,
            'source': self.source,
            'data_quality_score': float(self.data_quality_score) if self.data_quality_score else None
        }
    
    def to_candle_dict(self) -> Dict[str, Any]:
        """تحويل إلى تنسيق الشمعة المعياري"""
        return {
            'timestamp': self.timestamp,
            'open': float(self.open_price),
            'high': float(self.high_price),
            'low': float(self.low_price),
            'close': float(self.close_price),
            'volume': self.volume
        }
    
    @classmethod
    def from_candle_dict(cls, candle_data: Dict[str, Any], asset: str, timeframe: int):
        """إنشاء من بيانات الشمعة"""
        return cls(
            asset=asset,
            timestamp=candle_data['timestamp'],
            open_price=candle_data['open'],
            high_price=candle_data['high'],
            low_price=candle_data['low'],
            close_price=candle_data['close'],
            volume=candle_data.get('volume', 0),
            timeframe=timeframe
        )

class TechnicalIndicator(Base):
    """نموذج المؤشرات الفنية"""
    __tablename__ = "technical_indicators"
    
    id = Column(Integer, primary_key=True, index=True)
    asset = Column(String(50), nullable=False, index=True)
    timestamp = Column(TIMESTAMP, nullable=False, index=True)
    timeframe = Column(Integer, nullable=False)
    indicator_name = Column(String(50), nullable=False, index=True)
    indicator_value = Column(DECIMAL(20, 8))
    indicator_signal = Column(String(50))  # BULLISH, BEARISH, NEUTRAL, DECREASING_VOLATILITY, etc.
    parameters = Column(JSON)
    confidence_score = Column(DECIMAL(5, 2))
    calculation_time_ms = Column(Integer)
    created_at = Column(TIMESTAMP, default=func.now())
    
    # فهارس مركبة
    __table_args__ = (
        Index('idx_asset_indicator_timestamp', 'asset', 'indicator_name', 'timestamp'),
        Index('idx_timestamp_indicator', 'timestamp', 'indicator_name'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'asset': self.asset,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'timeframe': self.timeframe,
            'indicator_name': self.indicator_name,
            'indicator_value': float(self.indicator_value) if self.indicator_value else None,
            'indicator_signal': self.indicator_signal,
            'parameters': self.parameters,
            'confidence_score': float(self.confidence_score) if self.confidence_score else None,
            'calculation_time_ms': self.calculation_time_ms
        }

class Trade(Base):
    """نموذج الصفقات"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    trade_id = Column(String(100), unique=True, nullable=False, index=True)
    asset = Column(String(50), nullable=False, index=True)
    direction = Column(String(10), nullable=False)  # 'CALL' or 'PUT'
    amount = Column(DECIMAL(10, 2), nullable=False)
    duration = Column(Integer, nullable=False)  # بالثواني
    
    # أسعار الدخول والخروج
    entry_price = Column(DECIMAL(15, 8), nullable=False)
    exit_price = Column(DECIMAL(15, 8))
    
    # نتائج الصفقة
    result = Column(String(20))  # 'WIN', 'LOSS', 'DRAW', 'PENDING'
    profit = Column(DECIMAL(10, 2))
    payout_percentage = Column(DECIMAL(5, 2))
    
    # معلومات القرار
    confidence_score = Column(DECIMAL(5, 2))
    technical_signals = Column(JSON)
    behavioral_signals = Column(JSON)
    ai_predictions = Column(JSON)
    decision_reasoning = Column(Text)
    
    # أوقات مهمة
    entry_time = Column(TIMESTAMP, nullable=False, index=True)
    exit_time = Column(TIMESTAMP)
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    
    # معلومات إضافية
    session_id = Column(String(100))
    strategy_version = Column(String(20))
    market_conditions = Column(JSON)
    
    # فهارس مركبة
    __table_args__ = (
        Index('idx_asset_entry_time', 'asset', 'entry_time'),
        Index('idx_result_entry_time', 'result', 'entry_time'),
        Index('idx_session_entry_time', 'session_id', 'entry_time'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'trade_id': self.trade_id,
            'asset': self.asset,
            'direction': self.direction,
            'amount': float(self.amount),
            'duration': self.duration,
            'entry_price': float(self.entry_price),
            'exit_price': float(self.exit_price) if self.exit_price else None,
            'result': self.result,
            'profit': float(self.profit) if self.profit else None,
            'payout_percentage': float(self.payout_percentage) if self.payout_percentage else None,
            'confidence_score': float(self.confidence_score) if self.confidence_score else None,
            'technical_signals': self.technical_signals,
            'behavioral_signals': self.behavioral_signals,
            'ai_predictions': self.ai_predictions,
            'decision_reasoning': self.decision_reasoning,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'session_id': self.session_id,
            'strategy_version': self.strategy_version,
            'market_conditions': self.market_conditions
        }
    
    def calculate_profit_loss(self) -> Optional[float]:
        """حساب الربح/الخسارة"""
        if self.result == 'WIN' and self.payout_percentage:
            return float(self.amount) * (float(self.payout_percentage) / 100)
        elif self.result == 'LOSS':
            return -float(self.amount)
        elif self.result == 'DRAW':
            return 0.0
        return None
    
    def get_trade_duration_actual(self) -> Optional[int]:
        """حساب المدة الفعلية للصفقة بالثواني"""
        if self.entry_time and self.exit_time:
            return int((self.exit_time - self.entry_time).total_seconds())
        return None

class AIModelPerformance(Base):
    """نموذج أداء نماذج الذكاء الاصطناعي"""
    __tablename__ = "ai_model_performance"

    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String(50), nullable=False, index=True)
    model_version = Column(String(20), nullable=False)
    asset = Column(String(50), nullable=False, index=True)
    timeframe = Column(Integer, nullable=False)

    # معايير الأداء
    accuracy = Column(DECIMAL(5, 4))
    precision = Column(DECIMAL(5, 4))
    recall = Column(DECIMAL(5, 4))
    f1_score = Column(DECIMAL(5, 4))
    roc_auc = Column(DECIMAL(5, 4))

    # بيانات التدريب
    training_samples = Column(Integer)
    validation_samples = Column(Integer)
    test_samples = Column(Integer)
    training_time_seconds = Column(Integer)

    # معلومات النموذج
    model_parameters = Column(JSON)
    feature_importance = Column(JSON)
    confusion_matrix = Column(JSON)

    # أوقات مهمة
    training_date = Column(TIMESTAMP, nullable=False)
    evaluation_date = Column(TIMESTAMP, nullable=False)
    created_at = Column(TIMESTAMP, default=func.now())

    # فهارس
    __table_args__ = (
        Index('idx_model_asset_date', 'model_name', 'asset', 'evaluation_date'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'model_name': self.model_name,
            'model_version': self.model_version,
            'asset': self.asset,
            'timeframe': self.timeframe,
            'accuracy': float(self.accuracy) if self.accuracy else None,
            'precision': float(self.precision) if self.precision else None,
            'recall': float(self.recall) if self.recall else None,
            'f1_score': float(self.f1_score) if self.f1_score else None,
            'roc_auc': float(self.roc_auc) if self.roc_auc else None,
            'training_samples': self.training_samples,
            'validation_samples': self.validation_samples,
            'test_samples': self.test_samples,
            'training_time_seconds': self.training_time_seconds,
            'model_parameters': self.model_parameters,
            'feature_importance': self.feature_importance,
            'confusion_matrix': self.confusion_matrix,
            'training_date': self.training_date.isoformat() if self.training_date else None,
            'evaluation_date': self.evaluation_date.isoformat() if self.evaluation_date else None
        }

class SystemPerformance(Base):
    """نموذج أداء النظام العام"""
    __tablename__ = "system_performance"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(100), nullable=False, index=True)
    measurement_time = Column(TIMESTAMP, nullable=False, index=True)

    # إحصائيات التداول
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    draw_trades = Column(Integer, default=0)
    win_rate = Column(DECIMAL(5, 2))

    # الأرباح والخسائر
    total_profit = Column(DECIMAL(12, 2), default=0)
    gross_profit = Column(DECIMAL(12, 2), default=0)
    gross_loss = Column(DECIMAL(12, 2), default=0)
    profit_factor = Column(DECIMAL(8, 4))

    # إحصائيات المخاطر
    max_drawdown = Column(DECIMAL(12, 2))
    max_drawdown_percent = Column(DECIMAL(5, 2))
    consecutive_wins = Column(Integer, default=0)
    consecutive_losses = Column(Integer, default=0)
    max_consecutive_losses = Column(Integer, default=0)

    # أداء النماذج
    avg_confidence_score = Column(DECIMAL(5, 2))
    technical_signal_accuracy = Column(DECIMAL(5, 2))
    behavioral_signal_accuracy = Column(DECIMAL(5, 2))
    ai_prediction_accuracy = Column(DECIMAL(5, 2))

    # أداء النظام التقني
    avg_decision_time_ms = Column(Integer)
    avg_execution_time_ms = Column(Integer)
    system_uptime_percent = Column(DECIMAL(5, 2))
    error_count = Column(Integer, default=0)

    # معلومات إضافية
    active_assets = Column(JSON)
    market_conditions = Column(JSON)
    system_version = Column(String(20))

    created_at = Column(TIMESTAMP, default=func.now())

    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'measurement_time': self.measurement_time.isoformat() if self.measurement_time else None,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'draw_trades': self.draw_trades,
            'win_rate': float(self.win_rate) if self.win_rate else None,
            'total_profit': float(self.total_profit) if self.total_profit else None,
            'gross_profit': float(self.gross_profit) if self.gross_profit else None,
            'gross_loss': float(self.gross_loss) if self.gross_loss else None,
            'profit_factor': float(self.profit_factor) if self.profit_factor else None,
            'max_drawdown': float(self.max_drawdown) if self.max_drawdown else None,
            'max_drawdown_percent': float(self.max_drawdown_percent) if self.max_drawdown_percent else None,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses,
            'max_consecutive_losses': self.max_consecutive_losses,
            'avg_confidence_score': float(self.avg_confidence_score) if self.avg_confidence_score else None,
            'technical_signal_accuracy': float(self.technical_signal_accuracy) if self.technical_signal_accuracy else None,
            'behavioral_signal_accuracy': float(self.behavioral_signal_accuracy) if self.behavioral_signal_accuracy else None,
            'ai_prediction_accuracy': float(self.ai_prediction_accuracy) if self.ai_prediction_accuracy else None,
            'avg_decision_time_ms': self.avg_decision_time_ms,
            'avg_execution_time_ms': self.avg_execution_time_ms,
            'system_uptime_percent': float(self.system_uptime_percent) if self.system_uptime_percent else None,
            'error_count': self.error_count,
            'active_assets': self.active_assets,
            'market_conditions': self.market_conditions,
            'system_version': self.system_version
        }

class MarketCondition(Base):
    """نموذج حالة السوق"""
    __tablename__ = "market_conditions"

    id = Column(Integer, primary_key=True, index=True)
    asset = Column(String(50), nullable=False, index=True)
    timestamp = Column(TIMESTAMP, nullable=False, index=True)
    timeframe = Column(Integer, nullable=False)

    # حالة السوق
    market_state = Column(String(20))  # TRENDING, RANGING, VOLATILE, CALM
    trend_direction = Column(String(10))  # UP, DOWN, SIDEWAYS
    trend_strength = Column(DECIMAL(5, 2))  # 0-100
    volatility_level = Column(String(20))  # LOW, MEDIUM, HIGH, EXTREME
    volatility_value = Column(DECIMAL(8, 6))

    # مؤشرات السوق
    volume_profile = Column(String(20))  # LOW, NORMAL, HIGH
    support_level = Column(DECIMAL(15, 8))
    resistance_level = Column(DECIMAL(15, 8))
    current_price = Column(DECIMAL(15, 8))

    # معلومات إضافية
    news_impact = Column(String(20))  # NONE, LOW, MEDIUM, HIGH
    session_type = Column(String(20))  # ASIAN, EUROPEAN, AMERICAN, OVERLAP
    is_trading_recommended = Column(Boolean, default=True)

    # تحليل تلقائي
    analysis_confidence = Column(DECIMAL(5, 2))
    analysis_details = Column(JSON)

    created_at = Column(TIMESTAMP, default=func.now())

    # فهارس
    __table_args__ = (
        Index('idx_asset_timestamp_market', 'asset', 'timestamp', 'market_state'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'asset': self.asset,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'timeframe': self.timeframe,
            'market_state': self.market_state,
            'trend_direction': self.trend_direction,
            'trend_strength': float(self.trend_strength) if self.trend_strength else None,
            'volatility_level': self.volatility_level,
            'volatility_value': float(self.volatility_value) if self.volatility_value else None,
            'volume_profile': self.volume_profile,
            'support_level': float(self.support_level) if self.support_level else None,
            'resistance_level': float(self.resistance_level) if self.resistance_level else None,
            'current_price': float(self.current_price) if self.current_price else None,
            'news_impact': self.news_impact,
            'session_type': self.session_type,
            'is_trading_recommended': self.is_trading_recommended,
            'analysis_confidence': float(self.analysis_confidence) if self.analysis_confidence else None,
            'analysis_details': self.analysis_details
        }
