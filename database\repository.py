"""
نظام Repository للوصول لبيانات قاعدة البيانات
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func, text, insert
from sqlalchemy.exc import SQLAlchemyError

from database.models import (
    HistoricalData, TechnicalIndicator, Trade, 
    AIModelPerformance, SystemPerformance, MarketCondition
)
from database.connection_manager import db_manager
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, DatabaseError

logger = scalping_logger.get_logger("repository")

class BaseRepository:
    """Repository أساسي للعمليات المشتركة"""
    
    def __init__(self, model_class):
        self.model_class = model_class
        self.db_manager = db_manager
    
    @handle_errors(default_return=None, log_error=True)
    def create(self, **kwargs) -> Optional[Any]:
        """إنشاء سجل جديد"""
        try:
            with self.db_manager.get_session() as session:
                instance = self.model_class(**kwargs)
                session.add(instance)
                session.flush()
                session.refresh(instance)
                return instance
        except Exception as e:
            logger.error(f"خطأ في إنشاء سجل {self.model_class.__name__}: {str(e)}")
            raise DatabaseError(f"فشل إنشاء السجل: {str(e)}")
    
    @handle_errors(default_return=None, log_error=True)
    def get_by_id(self, record_id: int) -> Optional[Any]:
        """الحصول على سجل بالمعرف"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(self.model_class).filter(
                    self.model_class.id == record_id
                ).first()
        except Exception as e:
            logger.error(f"خطأ في جلب السجل {record_id}: {str(e)}")
            return None
    
    @handle_errors(default_return=[], log_error=True)
    def get_all(self, limit: int = 1000, offset: int = 0) -> List[Any]:
        """الحصول على جميع السجلات"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(self.model_class).offset(offset).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب السجلات: {str(e)}")
            return []
    
    @handle_errors(default_return=False, log_error=True)
    def update(self, record_id: int, **kwargs) -> bool:
        """تحديث سجل"""
        try:
            with self.db_manager.get_session() as session:
                updated_rows = session.query(self.model_class).filter(
                    self.model_class.id == record_id
                ).update(kwargs)
                return updated_rows > 0
        except Exception as e:
            logger.error(f"خطأ في تحديث السجل {record_id}: {str(e)}")
            return False
    
    @handle_errors(default_return=False, log_error=True)
    def delete(self, record_id: int) -> bool:
        """حذف سجل"""
        try:
            with self.db_manager.get_session() as session:
                deleted_rows = session.query(self.model_class).filter(
                    self.model_class.id == record_id
                ).delete()
                return deleted_rows > 0
        except Exception as e:
            logger.error(f"خطأ في حذف السجل {record_id}: {str(e)}")
            return False
    
    @handle_errors(default_return=0, log_error=True)
    def count(self, **filters) -> int:
        """عد السجلات"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(self.model_class)
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)
                return query.count()
        except Exception as e:
            logger.error(f"خطأ في عد السجلات: {str(e)}")
            return 0

class HistoricalDataRepository(BaseRepository):
    """Repository للبيانات التاريخية مع تحسينات للأداء والفهرسة"""

    def __init__(self):
        super().__init__(HistoricalData)
        self.batch_size = 1000  # حجم الدفعة للإدراج المجمع
    
    @handle_errors(default_return=[], log_error=True)
    def get_candles(self, asset: str, timeframe: int, 
                   start_time: datetime = None, end_time: datetime = None,
                   limit: int = 1000) -> List[HistoricalData]:
        """الحصول على الشموع لأصل معين"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                )
                
                if start_time:
                    query = query.filter(HistoricalData.timestamp >= start_time)
                if end_time:
                    query = query.filter(HistoricalData.timestamp <= end_time)
                
                return query.order_by(HistoricalData.timestamp.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب الشموع لـ {asset}: {str(e)}")
            return []
    
    @handle_errors(default_return=[], log_error=True)
    def get_latest_candles(self, asset: str, timeframe: int, count: int = 30) -> List[HistoricalData]:
        """الحصول على آخر شموع"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).order_by(HistoricalData.timestamp.desc()).limit(count).all()
        except Exception as e:
            logger.error(f"خطأ في جلب آخر الشموع لـ {asset}: {str(e)}")
            return []
    
    @handle_errors(default_return=False, log_error=True)
    def bulk_insert_candles(self, candles_data: List[Dict[str, Any]]) -> bool:
        """إدراج مجموعة من الشموع"""
        try:
            with self.db_manager.get_session() as session:
                candles = [HistoricalData(**candle_data) for candle_data in candles_data]
                session.bulk_save_objects(candles)
                return True
        except Exception as e:
            logger.error(f"خطأ في إدراج الشموع المجمعة: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def create_candle(self, candle_data: Dict[str, Any]) -> bool:
        """إنشاء شمعة واحدة"""
        try:
            with self.db_manager.get_session() as session:
                candle = HistoricalData(**candle_data)
                session.add(candle)
                session.commit()
                return True
        except Exception as e:
            logger.error(f"خطأ في إنشاء الشمعة: {str(e)}")
            return False
    
    @handle_errors(default_return=None, log_error=True)
    def get_latest_timestamp(self, asset: str, timeframe: int) -> Optional[datetime]:
        """الحصول على آخر timestamp للأصل"""
        try:
            with self.db_manager.get_session() as session:
                result = session.query(func.max(HistoricalData.timestamp)).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).scalar()
                return result
        except Exception as e:
            logger.error(f"خطأ في جلب آخر timestamp لـ {asset}: {str(e)}")
            return None

    @handle_errors(default_return=None, log_error=True)
    def get_oldest_timestamp(self, asset: str, timeframe: int) -> Optional[datetime]:
        """الحصول على أقدم timestamp للأصل"""
        try:
            with self.db_manager.get_session() as session:
                result = session.query(func.min(HistoricalData.timestamp)).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).scalar()
                return result
        except Exception as e:
            logger.error(f"خطأ في جلب أقدم timestamp لـ {asset}: {str(e)}")
            return None

    @handle_errors(default_return=[], log_error=True)
    def get_candles_range(self, asset: str, timeframe: int, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """الحصول على مجموعة من الشموع مع ترتيب زمني"""
        try:
            with self.db_manager.get_session() as session:
                candles = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).order_by(HistoricalData.timestamp.asc()).offset(offset).limit(limit).all()

                return [candle.to_dict() for candle in candles]
        except Exception as e:
            logger.error(f"خطأ في جلب مجموعة الشموع لـ {asset}: {str(e)}")
            return []

    @handle_errors(default_return=[], log_error=True)
    def get_candles_in_range(self, asset: str, timeframe: int, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """الحصول على الشموع في فترة زمنية محددة"""
        try:
            with self.db_manager.get_session() as session:
                candles = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe,
                        HistoricalData.timestamp >= start_time,
                        HistoricalData.timestamp <= end_time
                    )
                ).order_by(HistoricalData.timestamp.asc()).all()

                return [candle.to_dict() for candle in candles]
        except Exception as e:
            logger.error(f"خطأ في جلب الشموع في الفترة لـ {asset}: {str(e)}")
            return []

    @handle_errors(default_return=0, log_error=True)
    def count_candles_in_range(self, asset: str, timeframe: int, start_time: datetime, end_time: datetime) -> int:
        """عد الشموع في فترة زمنية محددة"""
        try:
            with self.db_manager.get_session() as session:
                count = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe,
                        HistoricalData.timestamp >= start_time,
                        HistoricalData.timestamp <= end_time
                    )
                ).count()

                return count
        except Exception as e:
            logger.error(f"خطأ في عد الشموع في الفترة لـ {asset}: {str(e)}")
            return 0

    @handle_errors(default_return=False, log_error=True)
    def store_candles_optimized(self, asset: str, candles: List[Dict], timeframe: int = 60) -> bool:
        """تخزين الشموع بطريقة محسنة مع تجنب التكرار"""
        try:
            if not candles:
                return True

            with self.db_manager.get_session() as session:
                # الحصول على آخر timestamp مخزن لتجنب التكرار
                last_timestamp = self.get_latest_timestamp(asset, timeframe)

                # تحضير البيانات للإدراج
                new_candles = []
                for candle_data in candles:
                    try:
                        # الحصول على timestamp من البيانات
                        time_value = candle_data.get('time', candle_data.get('timestamp'))

                        # تحويل timestamp إلى datetime
                        if isinstance(time_value, str):
                            try:
                                time_value = float(time_value)
                            except ValueError:
                                time_value = datetime.fromisoformat(time_value.replace('Z', '+00:00'))

                        if isinstance(time_value, (int, float)):
                            candle_timestamp = datetime.fromtimestamp(time_value)
                        else:
                            candle_timestamp = time_value

                        # تخطي الشموع المخزنة مسبقاً
                        if last_timestamp and candle_timestamp <= last_timestamp:
                            continue

                        candle_dict = {
                            'asset': asset,
                            'timestamp': candle_timestamp,
                            'timeframe': timeframe,
                            'open_price': float(candle_data['open']),
                            'high_price': float(candle_data['high']),
                            'low_price': float(candle_data['low']),
                            'close_price': float(candle_data['close']),
                            'volume': float(candle_data.get('volume', 0)),
                            'created_at': func.now()
                        }
                        new_candles.append(candle_dict)
                    except Exception as e:
                        logger.debug(f"تجاهل شمعة غير صالحة: {str(e)}")
                        continue

                if new_candles:
                    # إدراج مجمع للأداء الأمثل
                    session.execute(insert(HistoricalData).values(new_candles))
                    session.commit()
                    logger.info(f"تم تخزين {len(new_candles)} شمعة جديدة لـ {asset}")
                else:
                    logger.debug(f"لا توجد شموع جديدة لتخزينها لـ {asset}")

                return True

        except Exception as e:
            logger.error(f"خطأ في تخزين الشموع لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=0, log_error=True)
    def get_candles_count(self, asset: str, timeframe: int = 60) -> int:
        """الحصول على عدد الشموع المخزنة للأصل"""
        try:
            with self.db_manager.get_session() as session:
                count = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).count()

                return count
        except Exception as e:
            logger.error(f"خطأ في عد الشموع لـ {asset}: {str(e)}")
            return 0

    @handle_errors(default_return=False, log_error=True)
    def maintain_fifo_limit(self, asset: str, max_candles: int = 10080, timeframe: int = 60) -> bool:
        """الحفاظ على حد FIFO للشموع (حذف الأقدم عند تجاوز الحد)"""
        try:
            with self.db_manager.get_session() as session:
                # عد الشموع الحالية
                current_count = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).count()

                if current_count > max_candles:
                    # حذف الشموع الزائدة (الأقدم)
                    excess_count = current_count - max_candles

                    # الحصول على IDs الشموع الأقدم
                    old_candles = session.query(HistoricalData.id).filter(
                        and_(
                            HistoricalData.asset == asset,
                            HistoricalData.timeframe == timeframe
                        )
                    ).order_by(HistoricalData.timestamp.asc()).limit(excess_count).all()

                    if old_candles:
                        old_ids = [candle.id for candle in old_candles]

                        # حذف الشموع القديمة
                        session.query(HistoricalData).filter(
                            HistoricalData.id.in_(old_ids)
                        ).delete(synchronize_session=False)

                        session.commit()
                        logger.info(f"تم حذف {len(old_ids)} شمعة قديمة لـ {asset} للحفاظ على حد {max_candles}")

                return True

        except Exception as e:
            logger.error(f"خطأ في صيانة حد FIFO لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def store_candles_with_indicators(self, asset: str, candles: List[Dict],
                                    indicators_data: List[Dict], timeframe: int = 60) -> bool:
        """تخزين الشموع مع المؤشرات الفنية في معاملة واحدة"""
        try:
            with self.db_manager.get_session() as session:
                # تخزين الشموع أولاً
                success = self.store_candles_optimized(asset, candles, timeframe)
                if not success:
                    return False

                # تخزين المؤشرات
                if indicators_data:
                    session.execute(insert(TechnicalIndicator).values(indicators_data))
                    session.commit()
                    logger.info(f"تم تخزين {len(indicators_data)} مؤشر لـ {asset}")

                return True

        except Exception as e:
            logger.error(f"خطأ في تخزين الشموع والمؤشرات لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=[], log_error=True)
    def get_candles_for_indicators(self, asset: str, timeframe: int = 60,
                                 count: int = 100) -> List[Dict]:
        """جلب الشموع بتنسيق مناسب لحساب المؤشرات"""
        try:
            with self.db_manager.get_session() as session:
                candles = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).order_by(HistoricalData.timestamp.asc()).limit(count).all()

                # تحويل إلى تنسيق مناسب للمؤشرات
                result = []
                for candle in candles:
                    result.append({
                        'time': int(candle.timestamp.timestamp()),
                        'open': float(candle.open_price),
                        'high': float(candle.high_price),
                        'low': float(candle.low_price),
                        'close': float(candle.close_price),
                        'volume': float(candle.volume)
                    })

                return result

        except Exception as e:
            logger.error(f"خطأ في جلب الشموع للمؤشرات لـ {asset}: {str(e)}")
            return []

    @handle_errors(default_return={}, log_error=True)
    def get_storage_statistics(self, asset: str = None) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين"""
        try:
            with self.db_manager.get_session() as session:
                if asset:
                    # إحصائيات أصل واحد
                    count = session.query(HistoricalData).filter(
                        HistoricalData.asset == asset
                    ).count()

                    latest = session.query(func.max(HistoricalData.timestamp)).filter(
                        HistoricalData.asset == asset
                    ).scalar()

                    oldest = session.query(func.min(HistoricalData.timestamp)).filter(
                        HistoricalData.asset == asset
                    ).scalar()

                    return {
                        'asset': asset,
                        'total_candles': count,
                        'latest_timestamp': latest.isoformat() if latest else None,
                        'oldest_timestamp': oldest.isoformat() if oldest else None
                    }
                else:
                    # إحصائيات عامة
                    total_count = session.query(HistoricalData).count()
                    assets_count = session.query(HistoricalData.asset).distinct().count()

                    return {
                        'total_candles': total_count,
                        'total_assets': assets_count
                    }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التخزين: {str(e)}")
            return {}

    @handle_errors(default_return=False, log_error=True)
    def store_indicators_batch(self, indicators_data: List[Dict], asset: str) -> bool:
        """تخزين مجموعة من المؤشرات بطريقة محسنة"""
        try:
            if not indicators_data:
                return True

            with self.db_manager.get_session() as session:
                # إدراج مجمع للمؤشرات
                session.execute(insert(TechnicalIndicator).values(indicators_data))
                session.commit()
                logger.debug(f"تم تخزين {len(indicators_data)} مؤشر لـ {asset}")

                return True

        except Exception as e:
            logger.error(f"خطأ في تخزين المؤشرات لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=[], log_error=True)
    def get_all_assets_with_data(self) -> List[str]:
        """الحصول على قائمة جميع الأصول التي تحتوي على بيانات"""
        try:
            with self.db_manager.get_session() as session:
                assets = session.query(HistoricalData.asset).distinct().all()
                return [asset[0] for asset in assets]

        except Exception as e:
            logger.error(f"خطأ في جلب قائمة الأصول: {str(e)}")
            return []

    @handle_errors(default_return=False, log_error=True)
    def clear_asset_data(self, asset: str) -> bool:
        """حذف جميع البيانات لأصل معين (للاختبار فقط)"""
        try:
            with self.db_manager.get_session() as session:
                # حذف المؤشرات أولاً
                session.query(TechnicalIndicator).filter(
                    TechnicalIndicator.asset == asset
                ).delete()

                # حذف البيانات التاريخية
                session.query(HistoricalData).filter(
                    HistoricalData.asset == asset
                ).delete()

                session.commit()
                logger.info(f"تم حذف جميع البيانات لـ {asset}")

                return True

        except Exception as e:
            logger.error(f"خطأ في حذف البيانات لـ {asset}: {str(e)}")
            return False

    @handle_errors(default_return=[], log_error=True)
    def get_all_assets_with_data(self) -> List[str]:
        """الحصول على قائمة جميع الأصول التي تحتوي على بيانات"""
        try:
            with self.db_manager.get_session() as session:
                assets = session.query(HistoricalData.asset).distinct().all()
                return [asset[0] for asset in assets]

        except Exception as e:
            logger.error(f"خطأ في جلب قائمة الأصول: {str(e)}")
            return []

    @handle_errors(default_return=[], log_error=True)
    def get_latest_candles(self, asset: str, count: int = 100, timeframe: int = 60) -> List[Dict]:
        """جلب أحدث الشموع لأصل معين"""
        try:
            with self.db_manager.get_session() as session:
                candles = session.query(HistoricalData).filter(
                    and_(
                        HistoricalData.asset == asset,
                        HistoricalData.timeframe == timeframe
                    )
                ).order_by(HistoricalData.timestamp.desc()).limit(count).all()

                # تحويل إلى قاموس
                result = []
                for candle in reversed(candles):  # ترتيب تصاعدي حسب الوقت
                    result.append({
                        'timestamp': candle.timestamp,
                        'open': float(candle.open_price),
                        'high': float(candle.high_price),
                        'low': float(candle.low_price),
                        'close': float(candle.close_price),
                        'volume': float(candle.volume)
                    })

                return result

        except Exception as e:
            logger.error(f"خطأ في جلب أحدث الشموع لـ {asset}: {str(e)}")
            return []

    @handle_errors(default_return=False, log_error=True)
    def store_candle_with_indicators(self, candle_data: Dict[str, Any]) -> bool:
        """تخزين شمعة واحدة مع المؤشرات في معاملة واحدة"""
        try:
            with self.db_manager.get_session() as session:
                # إعداد بيانات الشمعة
                candle_dict = {
                    'asset': candle_data['asset'],
                    'timestamp': candle_data['timestamp'],
                    'timeframe': candle_data['timeframe'],
                    'open_price': candle_data['open_price'],
                    'high_price': candle_data['high_price'],
                    'low_price': candle_data['low_price'],
                    'close_price': candle_data['close_price'],
                    'volume': candle_data['volume'],
                    'created_at': func.now()
                }

                # إدراج الشمعة
                result = session.execute(insert(HistoricalData).values([candle_dict]))
                candle_id = result.inserted_primary_key[0] if result.inserted_primary_key else None

                # إعداد بيانات المؤشرات
                indicators = candle_data.get('indicators', {})
                if indicators and candle_id:
                    indicators_list = []
                    for indicator_name, indicator_data in indicators.items():
                        indicator_dict = {
                            'asset': candle_data['asset'],
                            'timeframe': candle_data['timeframe'],
                            'timestamp': candle_data['timestamp'],
                            'indicator_name': indicator_name,
                            'indicator_value': indicator_data.get('current_value'),
                            'signal': indicator_data.get('signal', 'NEUTRAL'),
                            'period': indicator_data.get('period', 0),
                            'historical_data_id': candle_id,
                            'created_at': func.now()
                        }
                        indicators_list.append(indicator_dict)

                    # إدراج المؤشرات
                    if indicators_list:
                        session.execute(insert(TechnicalIndicator).values(indicators_list))

                session.commit()
                logger.debug(f"تم تخزين شمعة مع {len(indicators)} مؤشر لـ {candle_data['asset']}")

                return True

        except Exception as e:
            logger.error(f"خطأ في تخزين الشمعة مع المؤشرات: {str(e)}")
            return False

class TechnicalIndicatorRepository(BaseRepository):
    """Repository للمؤشرات الفنية"""
    
    def __init__(self):
        super().__init__(TechnicalIndicator)
    
    @handle_errors(default_return=[], log_error=True)
    def get_by_asset(self, asset: str, timeframe: int = None, limit: int = 1000) -> List[TechnicalIndicator]:
        """الحصول على جميع المؤشرات لأصل معين"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(TechnicalIndicator).filter(
                    TechnicalIndicator.asset == asset
                )

                if timeframe:
                    query = query.filter(TechnicalIndicator.timeframe == timeframe)

                return query.order_by(TechnicalIndicator.timestamp.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب المؤشرات لـ {asset}: {str(e)}")
            return []

    @handle_errors(default_return=[], log_error=True)
    def get_indicator_values(self, asset: str, indicator_name: str,
                           start_time: datetime = None, end_time: datetime = None,
                           limit: int = 100) -> List[TechnicalIndicator]:
        """الحصول على قيم مؤشر معين"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(TechnicalIndicator).filter(
                    and_(
                        TechnicalIndicator.asset == asset,
                        TechnicalIndicator.indicator_name == indicator_name
                    )
                )

                if start_time:
                    query = query.filter(TechnicalIndicator.timestamp >= start_time)
                if end_time:
                    query = query.filter(TechnicalIndicator.timestamp <= end_time)

                return query.order_by(TechnicalIndicator.timestamp.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب المؤشر {indicator_name} لـ {asset}: {str(e)}")
            return []
    
    @handle_errors(default_return=False, log_error=True)
    def bulk_insert_indicators(self, indicators_data: List[Dict[str, Any]]) -> bool:
        """إدراج مجموعة من المؤشرات"""
        try:
            with self.db_manager.get_session() as session:
                indicators = [TechnicalIndicator(**indicator_data) for indicator_data in indicators_data]
                session.bulk_save_objects(indicators)
                return True
        except Exception as e:
            logger.error(f"خطأ في إدراج المؤشرات المجمعة: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def store_indicators_batch(self, indicators_data: List[Dict[str, Any]]) -> bool:
        """تخزين مجموعة من المؤشرات (alias لـ bulk_insert_indicators)"""
        return self.bulk_insert_indicators(indicators_data)

class TradeRepository(BaseRepository):
    """Repository للصفقات"""
    
    def __init__(self):
        super().__init__(Trade)
    
    @handle_errors(default_return=[], log_error=True)
    def get_trades_by_asset(self, asset: str, limit: int = 100) -> List[Trade]:
        """الحصول على صفقات أصل معين"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Trade).filter(
                    Trade.asset == asset
                ).order_by(Trade.entry_time.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب صفقات {asset}: {str(e)}")
            return []
    
    @handle_errors(default_return=[], log_error=True)
    def get_trades_by_result(self, result: str, limit: int = 100) -> List[Trade]:
        """الحصول على صفقات بنتيجة معينة"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Trade).filter(
                    Trade.result == result
                ).order_by(Trade.entry_time.desc()).limit(limit).all()
        except Exception as e:
            logger.error(f"خطأ في جلب صفقات بنتيجة {result}: {str(e)}")
            return []
    
    @handle_errors(default_return=[], log_error=True)
    def get_trades_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Trade]:
        """الحصول على صفقات في فترة زمنية"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Trade).filter(
                    and_(
                        Trade.entry_time >= start_date,
                        Trade.entry_time <= end_date
                    )
                ).order_by(Trade.entry_time.desc()).all()
        except Exception as e:
            logger.error(f"خطأ في جلب صفقات الفترة الزمنية: {str(e)}")
            return []
    
    @handle_errors(default_return={}, log_error=True)
    def get_trading_statistics(self, start_date: datetime = None, end_date: datetime = None) -> Dict[str, Any]:
        """الحصول على إحصائيات التداول"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(Trade)
                
                if start_date:
                    query = query.filter(Trade.entry_time >= start_date)
                if end_date:
                    query = query.filter(Trade.entry_time <= end_date)
                
                trades = query.all()
                
                if not trades:
                    return {}
                
                total_trades = len(trades)
                winning_trades = len([t for t in trades if t.result == 'WIN'])
                losing_trades = len([t for t in trades if t.result == 'LOSS'])
                draw_trades = len([t for t in trades if t.result == 'DRAW'])
                
                total_profit = sum([float(t.profit) for t in trades if t.profit])
                win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
                
                return {
                    'total_trades': total_trades,
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'draw_trades': draw_trades,
                    'win_rate': round(win_rate, 2),
                    'total_profit': round(total_profit, 2),
                    'avg_profit_per_trade': round(total_profit / total_trades, 2) if total_trades > 0 else 0
                }
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات التداول: {str(e)}")
            return {}

# إنشاء instances للـ repositories
historical_data_repo = HistoricalDataRepository()
technical_indicator_repo = TechnicalIndicatorRepository()
trade_repo = TradeRepository()
ai_performance_repo = BaseRepository(AIModelPerformance)
system_performance_repo = BaseRepository(SystemPerformance)
market_condition_repo = BaseRepository(MarketCondition)
