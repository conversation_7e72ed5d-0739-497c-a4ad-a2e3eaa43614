# التوثيق التفصيلي لنظام تداول السكالبينغ الاحترافي

## 📚 جدول المحتويات
1. [نظرة عامة على النظام](#نظرة-عامة-على-النظام)
2. [معمارية النظام](#معمارية-النظام)
3. [واجهات برمجة التطبيقات](#واجهات-برمجة-التطبيقات)
4. [دليل المطور](#دليل-المطور)
5. [دليل المستخدم](#دليل-المستخدم)
6. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 نظرة عامة على النظام

### الهدف الأساسي
نظام تداول سكالبينغ احترافي للخيارات الثنائية يستخدم تقنيات متقدمة في:
- **التحليل الفني**: مؤشرات متنوعة ودقيقة
- **التحليل الكمي**: نماذج رياضية وإحصائية
- **التحليل السلوكي**: فهم أنماط السوق
- **الذكاء الاصطناعي**: توقعات ذكية ودقيقة

### المميزات الرئيسية
- ✅ **اتصال مستقر** مع منصة Pocket Option
- ✅ **تحليل متعدد الطبقات** للقرارات الذكية
- ✅ **إدارة مخاطر متقدمة** لحماية رأس المال
- ✅ **مراقبة مستمرة** للأداء والنتائج
- ✅ **تسجيل شامل** لجميع العمليات
- ✅ **قابلية التوسع** والصيانة

### التقنيات المستخدمة
- **Python 3.9+**: لغة البرمجة الأساسية
- **PostgreSQL**: قاعدة بيانات متقدمة
- **BinaryOptionsTools-v2**: مكتبة الاتصال
- **Pandas/NumPy**: معالجة البيانات
- **Scikit-learn/XGBoost**: التعلم الآلي
- **TensorFlow**: الشبكات العصبية

---

## 🏗️ معمارية النظام

### الطبقات الخمس الأساسية

#### 1. طبقة جمع البيانات (Data Collection Layer)
**الغرض**: جمع ومعالجة البيانات من مصادر متعددة

**المكونات**:
- `RealTimeCollector`: جمع البيانات المباشرة
- `HistoricalCollector`: جمع البيانات التاريخية
- `MarketEvaluator`: تقييم حالة السوق
- `DataProcessor`: معالجة وتنظيف البيانات

**المدخلات**:
- بيانات الأسعار المباشرة من Pocket Option
- البيانات التاريخية للأصول المختلفة
- معلومات حالة السوق

**المخرجات**:
- بيانات منظفة ومعالجة
- آخر 30 شمعة لكل أصل
- تقييم حالة السوق الحالية

#### 2. طبقة المؤشرات الفنية (Technical Indicators Layer)
**الغرض**: حساب المؤشرات الفنية المختلفة

**المؤشرات المدعومة**:
```python
# المتوسطات المتحركة
- EMA(5, 10, 21)    # المتوسط المتحرك الأسي
- SMA(10)           # المتوسط المتحرك البسيط

# مؤشرات الزخم
- RSI(5, 14)        # مؤشر القوة النسبية
- MACD(12, 26, 9)   # تقارب وتباعد المتوسطات
- Momentum(10)      # مؤشر الزخم

# مؤشرات التقلب
- Bollinger Bands(20, 2)  # نطاقات بولينجر
- ATR(5, 14)             # متوسط المدى الحقيقي

# مؤشرات متقدمة
- Heiken Ashi       # شموع هايكن آشي
- Z-Score           # النتيجة المعيارية
```

#### 3. طبقة التحليل السلوكي (Behavioral Analysis Layer)
**الغرض**: تحليل أنماط السلوك السعري

**أنماط الشموع المدعومة**:
- **Doji**: شمعة التردد (جسم صغير)
- **Engulfing**: شمعة الابتلاع (صاعدة/هابطة)
- **Pin Bar**: شمعة الدبوس (ظل طويل)
- **Marubozu**: شمعة الجسم الكامل (بدون ظلال)

**معايير التحليل**:
- نسبة الجسم إلى الظلال
- موقع الشمعة في الاتجاه
- قوة الإغلاق والافتتاح
- تأكيد من الشموع التالية

#### 4. طبقة الذكاء الاصطناعي (AI/ML Layer)
**الغرض**: توقعات ذكية باستخدام التعلم الآلي

**النماذج المستخدمة**:

##### XGBoost Model
```python
# الغرض: تصنيف اتجاه السعر
# المدخلات: جميع المؤشرات الفنية + أنماط الشموع
# المخرجات: احتمالية الصعود/الهبوط (0-100%)
# نسبة الثقة المطلوبة: ≥ 80%

class XGBoostPredictor:
    def __init__(self):
        self.model = XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
    
    def predict_direction(self, features):
        """توقع اتجاه السعر"""
        probability = self.model.predict_proba(features)
        confidence = max(probability[0]) * 100
        direction = 'CALL' if probability[0][1] > 0.5 else 'PUT'
        return direction, confidence
```

##### LSTM Neural Network
```python
# الغرض: التنبؤ بالسعر المستقبلي
# المدخلات: تسلسل زمني من الأسعار (آخر 100 شمعة)
# المخرجات: السعر المتوقع للشمعة التالية

class LSTMPredictor:
    def __init__(self, sequence_length=100):
        self.sequence_length = sequence_length
        self.model = self._build_model()
    
    def _build_model(self):
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(self.sequence_length, 4)),
            LSTM(50, return_sequences=False),
            Dense(25),
            Dense(1)
        ])
        model.compile(optimizer='adam', loss='mse')
        return model
```

##### Random Forest
```python
# الغرض: تحليل أهمية المؤشرات
# المدخلات: جميع المؤشرات والأنماط
# المخرجات: ترتيب أهمية المؤشرات

class RandomForestAnalyzer:
    def __init__(self):
        self.model = RandomForestClassifier(
            n_estimators=100,
            random_state=42
        )
    
    def analyze_feature_importance(self, X, y):
        """تحليل أهمية المؤشرات"""
        self.model.fit(X, y)
        importance = self.model.feature_importances_
        return sorted(zip(X.columns, importance), key=lambda x: x[1], reverse=True)
```

#### 5. طبقة التنفيذ (Execution Layer)
**الغرض**: اتخاذ القرارات وتنفيذ الصفقات

**مكونات الطبقة**:
- `DecisionEngine`: محرك اتخاذ القرار
- `RiskManager`: إدارة المخاطر
- `TradeExecutor`: تنفيذ الصفقات
- `PerformanceMonitor`: مراقبة الأداء

---

## 🔧 واجهات برمجة التطبيقات

### واجهة طبقة البيانات

#### RealTimeCollector
```python
class RealTimeCollector:
    """مجمع البيانات المباشرة"""
    
    def __init__(self, api_client, db_manager):
        self.api = api_client
        self.db = db_manager
    
    async def collect_live_data(self, asset: str) -> Dict:
        """جمع البيانات المباشرة لأصل معين"""
        pass
    
    def get_last_candles(self, asset: str, count: int = 30) -> List[Dict]:
        """الحصول على آخر شموع"""
        pass
    
    def evaluate_market_state(self, asset: str) -> str:
        """تقييم حالة السوق (trending/ranging/volatile)"""
        pass
```

#### HistoricalCollector
```python
class HistoricalCollector:
    """مجمع البيانات التاريخية"""
    
    def collect_historical_data(self, asset: str, timeframe: int, duration: int) -> List[Dict]:
        """جمع البيانات التاريخية"""
        pass
    
    def store_data(self, data: List[Dict]) -> bool:
        """حفظ البيانات في قاعدة البيانات"""
        pass
    
    def get_stored_data(self, asset: str, start_time: datetime, end_time: datetime) -> List[Dict]:
        """استرجاع البيانات المحفوظة"""
        pass
```

### واجهة طبقة المؤشرات

#### BaseIndicator
```python
from abc import ABC, abstractmethod

class BaseIndicator(ABC):
    """الكلاس الأساسي لجميع المؤشرات"""
    
    def __init__(self, period: int):
        self.period = period
        self.name = self.__class__.__name__
    
    @abstractmethod
    def calculate(self, data: List[Dict]) -> List[float]:
        """حساب المؤشر"""
        pass
    
    def validate_data(self, data: List[Dict]) -> bool:
        """التحقق من صحة البيانات"""
        return len(data) >= self.period
    
    def get_signal(self, values: List[float]) -> str:
        """الحصول على إشارة التداول"""
        pass
```

#### MovingAverageIndicators
```python
class EMA(BaseIndicator):
    """المتوسط المتحرك الأسي"""
    
    def calculate(self, data: List[Dict]) -> List[float]:
        """حساب EMA"""
        prices = [candle['close'] for candle in data]
        return self._calculate_ema(prices, self.period)
    
    def _calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """حساب EMA الفعلي"""
        multiplier = 2 / (period + 1)
        ema_values = [prices[0]]  # البداية بأول سعر
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values

class SMA(BaseIndicator):
    """المتوسط المتحرك البسيط"""
    
    def calculate(self, data: List[Dict]) -> List[float]:
        """حساب SMA"""
        prices = [candle['close'] for candle in data]
        sma_values = []
        
        for i in range(len(prices)):
            if i < self.period - 1:
                sma_values.append(None)
            else:
                sma = sum(prices[i-self.period+1:i+1]) / self.period
                sma_values.append(sma)
        
        return sma_values
```

### واجهة طبقة التنفيذ

#### DecisionEngine
```python
class DecisionEngine:
    """محرك اتخاذ القرار"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.technical_weight = 0.4
        self.behavioral_weight = 0.3
        self.ai_weight = 0.3
    
    def make_decision(self, 
                     technical_signals: Dict,
                     behavioral_signals: Dict,
                     ai_predictions: Dict) -> Dict:
        """اتخاذ قرار التداول"""
        
        # تقييم الإشارات الفنية
        technical_score = self._evaluate_technical_signals(technical_signals)
        
        # تقييم الإشارات السلوكية
        behavioral_score = self._evaluate_behavioral_signals(behavioral_signals)
        
        # تقييم توقعات الذكاء الاصطناعي
        ai_score = self._evaluate_ai_predictions(ai_predictions)
        
        # حساب النتيجة الإجمالية
        total_score = (
            technical_score * self.technical_weight +
            behavioral_score * self.behavioral_weight +
            ai_score * self.ai_weight
        )
        
        # اتخاذ القرار
        if total_score >= self.config.min_confidence_score:
            direction = 'CALL' if total_score > 50 else 'PUT'
            return {
                'action': 'TRADE',
                'direction': direction,
                'confidence': total_score,
                'reasoning': self._generate_reasoning(technical_signals, behavioral_signals, ai_predictions)
            }
        else:
            return {
                'action': 'WAIT',
                'confidence': total_score,
                'reasoning': 'الثقة غير كافية للتداول'
            }
```

---

## 📖 دليل المطور

### إعداد بيئة التطوير

#### 1. متطلبات النظام
```bash
# Python 3.9 أو أحدث
python --version

# PostgreSQL 13 أو أحدث
psql --version

# Git للتحكم في الإصدارات
git --version
```

#### 2. تثبيت المشروع
```bash
# استنساخ المشروع
git clone <repository_url>
cd scalping_system

# إنشاء البيئة الافتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt
```

#### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb scalping_trading

# تشغيل الهجرات
python -m alembic upgrade head
```

#### 4. إعداد متغيرات البيئة
```bash
# إنشاء ملف .env
cat > .env << EOF
DATABASE_URL=postgresql://user:password@localhost/scalping_trading
POCKET_OPTION_SSID=your_ssid_here
LOG_LEVEL=INFO
EOF
```

### إضافة مؤشر فني جديد

#### 1. إنشاء كلاس المؤشر
```python
# في ملف indicators/custom_indicator.py
from indicators.base_indicator import BaseIndicator

class CustomIndicator(BaseIndicator):
    """مؤشر مخصص جديد"""
    
    def __init__(self, period: int, custom_param: float = 1.0):
        super().__init__(period)
        self.custom_param = custom_param
    
    def calculate(self, data: List[Dict]) -> List[float]:
        """حساب المؤشر المخصص"""
        # تطبيق منطق الحساب هنا
        pass
    
    def get_signal(self, values: List[float]) -> str:
        """الحصول على إشارة التداول"""
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        if current > previous * 1.01:  # زيادة 1%
            return 'BULLISH'
        elif current < previous * 0.99:  # انخفاض 1%
            return 'BEARISH'
        else:
            return 'NEUTRAL'
```

#### 2. تسجيل المؤشر
```python
# في ملف indicators/__init__.py
from .custom_indicator import CustomIndicator

# إضافة إلى قائمة المؤشرات المتاحة
AVAILABLE_INDICATORS = {
    'custom': CustomIndicator,
    # ... مؤشرات أخرى
}
```

#### 3. اختبار المؤشر
```python
# في ملف tests/test_custom_indicator.py
import unittest
from indicators.custom_indicator import CustomIndicator

class TestCustomIndicator(unittest.TestCase):
    
    def setUp(self):
        self.indicator = CustomIndicator(period=14)
        self.sample_data = [
            {'close': 1.1000}, {'close': 1.1010},
            {'close': 1.1020}, {'close': 1.1015},
            # ... المزيد من البيانات
        ]
    
    def test_calculation(self):
        """اختبار حساب المؤشر"""
        result = self.indicator.calculate(self.sample_data)
        self.assertIsNotNone(result)
        self.assertGreater(len(result), 0)
    
    def test_signal_generation(self):
        """اختبار توليد الإشارات"""
        values = [1.0, 1.1, 1.2, 1.15]
        signal = self.indicator.get_signal(values)
        self.assertIn(signal, ['BULLISH', 'BEARISH', 'NEUTRAL'])
```

---

## 👤 دليل المستخدم

### تشغيل النظام

#### 1. التشغيل الأساسي
```python
# في ملف main_execution.py
from scalping_system import ScalpingSystem
from config.trading_config import TradingConfig

# إعداد التكوين
config = TradingConfig()
config.target_assets = ['EURUSD_otc', 'GBPJPY_otc']
config.min_confidence_score = 85.0

# إنشاء النظام
system = ScalpingSystem(config)

# بدء التشغيل
system.start()
```

#### 2. مراقبة الأداء
```python
# الحصول على إحصائيات الأداء
stats = system.get_performance_stats()
print(f"عدد الصفقات: {stats['total_trades']}")
print(f"نسبة النجاح: {stats['win_rate']:.2f}%")
print(f"الربح الإجمالي: ${stats['total_profit']:.2f}")
```

#### 3. إيقاف النظام
```python
# إيقاف آمن للنظام
system.stop()
```

### إعدادات التخصيص

#### 1. تخصيص المؤشرات
```python
# تخصيص معاملات المؤشرات
config.indicators = {
    'ema_fast': {'period': 5},
    'ema_slow': {'period': 21},
    'rsi': {'period': 14, 'overbought': 70, 'oversold': 30},
    'bollinger': {'period': 20, 'std_dev': 2}
}
```

#### 2. تخصيص إدارة المخاطر
```python
# إعدادات إدارة المخاطر
config.risk_management = {
    'max_trade_amount': 50.0,
    'max_daily_loss': 100.0,
    'max_consecutive_losses': 5,
    'position_sizing': 'fixed'  # أو 'percentage'
}
```

#### 3. تخصيص الذكاء الاصطناعي
```python
# إعدادات نماذج الذكاء الاصطناعي
config.ai_models = {
    'xgboost': {
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1
    },
    'lstm': {
        'sequence_length': 100,
        'hidden_units': 50
    }
}
```

---

## 🔍 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها

#### 1. خطأ الاتصال بقاعدة البيانات
```
خطأ: psycopg2.OperationalError: could not connect to server
```

**الحل**:
```bash
# التحقق من تشغيل PostgreSQL
sudo systemctl status postgresql

# إعادة تشغيل الخدمة
sudo systemctl restart postgresql

# التحقق من إعدادات الاتصال
psql -h localhost -U scalping_user -d scalping_trading
```

#### 2. خطأ في SSID
```
خطأ: PocketOptionError, Failed to parse SSID
```

**الحل**:
```python
# التأكد من تنسيق SSID الصحيح
ssid = '42["auth",{"session":"your_session","isDemo":1,"uid":12345,"platform":2,"isFastHistory":false}]'

# اختبار SSID
from BinaryOptionsToolsV2.pocketoption import PocketOption
api = PocketOption(ssid)
```

#### 3. خطأ في البيانات
```
خطأ: DataError: insufficient data for indicator calculation
```

**الحل**:
```python
# التحقق من توفر البيانات الكافية
def validate_data_sufficiency(data, required_periods):
    if len(data) < required_periods:
        raise DataError(f"البيانات غير كافية: متوفر {len(data)}, مطلوب {required_periods}")
    return True
```

### نصائح التحسين

#### 1. تحسين الأداء
```python
# استخدام التخزين المؤقت للمؤشرات
from functools import lru_cache

@lru_cache(maxsize=1000)
def calculate_indicator_cached(data_hash, indicator_name, params):
    # حساب المؤشر مع التخزين المؤقت
    pass
```

#### 2. تحسين استهلاك الذاكرة
```python
# تنظيف البيانات القديمة
def cleanup_old_data(max_age_hours=24):
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    # حذف البيانات الأقدم من الحد المسموح
    pass
```

#### 3. مراقبة الأداء
```python
# إضافة مراقبة الأداء
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        logger.info(f"{func.__name__} استغرق {execution_time:.2f} ثانية")
        return result
    return wrapper
```

---

**تاريخ الإنشاء**: 2025-06-18  
**الإصدار**: 1.0  
**المطور**: BAX (Business Analyst Xpert)  
**الحالة**: قيد التطوير
