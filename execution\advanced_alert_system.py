"""
نظام إنذار الأخطاء المتقدم
Advanced Alert System for Errors and Issues
"""

import asyncio
import time
import threading
import smtplib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("advanced_alert_system")

class AlertSeverity(Enum):
    """مستويات خطورة التنبيهات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertType(Enum):
    """أنواع التنبيهات"""
    SYSTEM_ERROR = "system_error"
    PERFORMANCE_ISSUE = "performance_issue"
    DATA_COLLECTION_FAILURE = "data_collection_failure"
    DATABASE_CONNECTION_LOST = "database_connection_lost"
    WEBSOCKET_DISCONNECTION = "websocket_disconnection"
    HIGH_RESOURCE_USAGE = "high_resource_usage"
    INDICATOR_CALCULATION_ERROR = "indicator_calculation_error"
    ASSET_MONITORING_STOPPED = "asset_monitoring_stopped"
    BACKUP_FAILURE = "backup_failure"
    SECURITY_BREACH = "security_breach"

class AlertChannel(Enum):
    """قنوات الإنذار"""
    EMAIL = "email"
    WEBHOOK = "webhook"
    LOG_FILE = "log_file"
    CONSOLE = "console"
    TELEGRAM = "telegram"
    SLACK = "slack"

@dataclass
class AlertRule:
    """قاعدة تنبيه"""
    rule_id: str
    alert_type: AlertType
    severity: AlertSeverity
    condition: str  # وصف الشرط
    threshold_value: Optional[float] = None
    time_window_minutes: int = 5
    max_alerts_per_hour: int = 10
    enabled: bool = True
    channels: List[AlertChannel] = field(default_factory=list)
    custom_message: Optional[str] = None

@dataclass
class Alert:
    """تنبيه"""
    alert_id: str
    rule_id: str
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    asset: Optional[str] = None
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    acknowledgment_time: Optional[datetime] = None
    escalation_level: int = 0

@dataclass
class AlertStatistics:
    """إحصائيات التنبيهات"""
    total_alerts: int = 0
    alerts_by_severity: Dict[AlertSeverity, int] = field(default_factory=dict)
    alerts_by_type: Dict[AlertType, int] = field(default_factory=dict)
    alerts_by_asset: Dict[str, int] = field(default_factory=dict)
    resolved_alerts: int = 0
    pending_alerts: int = 0
    escalated_alerts: int = 0
    last_alert_time: Optional[datetime] = None

class AdvancedAlertSystem:
    """نظام إنذار الأخطاء المتقدم"""
    
    def __init__(self):
        self.config = TradingConfig()
        
        # حالة النظام
        self.alert_system_active = False
        self.monitoring_interval = 30  # ثانية
        
        # قواعد التنبيهات
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # التنبيهات النشطة
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        
        # إحصائيات
        self.statistics = AlertStatistics()
        
        # عدادات التنبيهات لكل قاعدة
        self.alert_counters: Dict[str, Dict[str, int]] = {}
        
        # callbacks للتنبيهات
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # إعدادات الإشعارات
        self.notification_settings = {
            'email': {
                'enabled': False,
                'smtp_server': '',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'recipients': []
            },
            'webhook': {
                'enabled': False,
                'url': '',
                'headers': {}
            },
            'telegram': {
                'enabled': False,
                'bot_token': '',
                'chat_id': ''
            }
        }
        
        # خيط المراقبة
        self.monitoring_thread = None
        
        # تهيئة قواعد التنبيهات الافتراضية
        self._initialize_default_alert_rules()
        
        # تهيئة الإحصائيات
        self._initialize_statistics()
        
        logger.info("تم تهيئة نظام إنذار الأخطاء المتقدم")

    def _initialize_default_alert_rules(self):
        """تهيئة قواعد التنبيهات الافتراضية"""
        try:
            # قاعدة أخطاء النظام الحرجة
            self.add_alert_rule(AlertRule(
                rule_id="critical_system_error",
                alert_type=AlertType.SYSTEM_ERROR,
                severity=AlertSeverity.CRITICAL,
                condition="خطأ حرج في النظام",
                max_alerts_per_hour=5,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG_FILE, AlertChannel.CONSOLE]
            ))
            
            # قاعدة انقطاع اتصال قاعدة البيانات
            self.add_alert_rule(AlertRule(
                rule_id="database_connection_lost",
                alert_type=AlertType.DATABASE_CONNECTION_LOST,
                severity=AlertSeverity.HIGH,
                condition="فقدان اتصال قاعدة البيانات",
                time_window_minutes=2,
                max_alerts_per_hour=3,
                channels=[AlertChannel.EMAIL, AlertChannel.WEBHOOK, AlertChannel.LOG_FILE]
            ))
            
            # قاعدة استخدام الموارد المرتفع
            self.add_alert_rule(AlertRule(
                rule_id="high_resource_usage",
                alert_type=AlertType.HIGH_RESOURCE_USAGE,
                severity=AlertSeverity.MEDIUM,
                condition="استخدام موارد مرتفع",
                threshold_value=85.0,  # 85%
                time_window_minutes=10,
                max_alerts_per_hour=6,
                channels=[AlertChannel.LOG_FILE, AlertChannel.CONSOLE]
            ))
            
            # قاعدة فشل جمع البيانات
            self.add_alert_rule(AlertRule(
                rule_id="data_collection_failure",
                alert_type=AlertType.DATA_COLLECTION_FAILURE,
                severity=AlertSeverity.HIGH,
                condition="فشل في جمع البيانات لأكثر من 5 أصول",
                threshold_value=5.0,
                time_window_minutes=5,
                max_alerts_per_hour=4,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG_FILE]
            ))
            
            # قاعدة انقطاع WebSocket
            self.add_alert_rule(AlertRule(
                rule_id="websocket_disconnection",
                alert_type=AlertType.WEBSOCKET_DISCONNECTION,
                severity=AlertSeverity.MEDIUM,
                condition="انقطاع اتصال WebSocket",
                time_window_minutes=3,
                max_alerts_per_hour=8,
                channels=[AlertChannel.LOG_FILE, AlertChannel.CONSOLE]
            ))
            
            # قاعدة توقف مراقبة الأصول
            self.add_alert_rule(AlertRule(
                rule_id="asset_monitoring_stopped",
                alert_type=AlertType.ASSET_MONITORING_STOPPED,
                severity=AlertSeverity.HIGH,
                condition="توقف مراقبة أصل لأكثر من 10 دقائق",
                time_window_minutes=10,
                max_alerts_per_hour=3,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG_FILE]
            ))
            
            logger.debug(f"تم تهيئة {len(self.alert_rules)} قاعدة تنبيه افتراضية")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة قواعد التنبيهات الافتراضية: {str(e)}")

    def _initialize_statistics(self):
        """تهيئة الإحصائيات"""
        try:
            for severity in AlertSeverity:
                self.statistics.alerts_by_severity[severity] = 0
            
            for alert_type in AlertType:
                self.statistics.alerts_by_type[alert_type] = 0
            
            for asset in CURRENCY_PAIRS_70:
                self.statistics.alerts_by_asset[asset] = 0
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة الإحصائيات: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_alert_system(self) -> bool:
        """بدء نظام التنبيهات"""
        try:
            if self.alert_system_active:
                logger.warning("نظام التنبيهات نشط بالفعل")
                return True
            
            logger.info("🔄 بدء نظام إنذار الأخطاء المتقدم")
            
            self.alert_system_active = True
            
            # بدء خيط المراقبة
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="AlertSystemMonitor"
            )
            self.monitoring_thread.start()
            
            logger.info("✅ تم بدء نظام إنذار الأخطاء المتقدم")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام التنبيهات: {str(e)}")
            return False

    def stop_alert_system(self):
        """إيقاف نظام التنبيهات"""
        try:
            logger.info("إيقاف نظام إنذار الأخطاء...")
            
            self.alert_system_active = False
            
            # انتظار انتهاء خيط المراقبة
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            logger.info("✅ تم إيقاف نظام إنذار الأخطاء")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام التنبيهات: {str(e)}")

    def add_alert_rule(self, rule: AlertRule):
        """إضافة قاعدة تنبيه"""
        try:
            self.alert_rules[rule.rule_id] = rule
            self.alert_counters[rule.rule_id] = {'count': 0, 'last_reset': datetime.now()}
            
            logger.debug(f"تم إضافة قاعدة تنبيه: {rule.rule_id}")
            
        except Exception as e:
            logger.error(f"خطأ في إضافة قاعدة التنبيه: {str(e)}")

    def remove_alert_rule(self, rule_id: str):
        """حذف قاعدة تنبيه"""
        try:
            if rule_id in self.alert_rules:
                del self.alert_rules[rule_id]
                if rule_id in self.alert_counters:
                    del self.alert_counters[rule_id]
                
                logger.debug(f"تم حذف قاعدة التنبيه: {rule_id}")
            
        except Exception as e:
            logger.error(f"خطأ في حذف قاعدة التنبيه: {str(e)}")

    def trigger_alert(self, 
                     alert_type: AlertType,
                     severity: AlertSeverity,
                     title: str,
                     message: str,
                     details: Dict[str, Any] = None,
                     asset: str = None,
                     rule_id: str = None) -> Optional[str]:
        """إطلاق تنبيه"""
        try:
            # البحث عن قاعدة مناسبة إذا لم تحدد
            if not rule_id:
                rule_id = self._find_matching_rule(alert_type, severity)
            
            if not rule_id or rule_id not in self.alert_rules:
                logger.warning(f"لم يتم العثور على قاعدة مناسبة للتنبيه: {alert_type.value}")
                return None
            
            rule = self.alert_rules[rule_id]
            
            # فحص إذا كانت القاعدة مفعلة
            if not rule.enabled:
                return None
            
            # فحص حد التنبيهات في الساعة
            if not self._check_alert_rate_limit(rule_id):
                logger.debug(f"تم تجاوز حد التنبيهات للقاعدة: {rule_id}")
                return None
            
            # إنشاء التنبيه
            alert_id = f"alert_{int(time.time() * 1000)}"
            alert = Alert(
                alert_id=alert_id,
                rule_id=rule_id,
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                details=details or {},
                timestamp=datetime.now(),
                asset=asset
            )
            
            # إضافة للتنبيهات النشطة
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # تحديث الإحصائيات
            self._update_alert_statistics(alert)
            
            # إرسال التنبيه عبر القنوات المحددة
            self._send_alert_notifications(alert, rule)
            
            # تشغيل callbacks
            self._trigger_alert_callbacks(alert)
            
            # تحديث عداد القاعدة
            self.alert_counters[rule_id]['count'] += 1
            
            logger.info(f"🚨 تم إطلاق تنبيه {severity.value}: {title}")
            
            return alert_id
            
        except Exception as e:
            logger.error(f"خطأ في إطلاق التنبيه: {str(e)}")
            return None

    def _find_matching_rule(self, alert_type: AlertType, severity: AlertSeverity) -> Optional[str]:
        """البحث عن قاعدة مناسبة للتنبيه"""
        try:
            for rule_id, rule in self.alert_rules.items():
                if rule.alert_type == alert_type and rule.enabled:
                    return rule_id
            return None
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن قاعدة مناسبة: {str(e)}")
            return None

    def _check_alert_rate_limit(self, rule_id: str) -> bool:
        """فحص حد معدل التنبيهات"""
        try:
            if rule_id not in self.alert_counters:
                return True
            
            rule = self.alert_rules[rule_id]
            counter_info = self.alert_counters[rule_id]
            
            # إعادة تعيين العداد كل ساعة
            current_time = datetime.now()
            if (current_time - counter_info['last_reset']).total_seconds() >= 3600:
                counter_info['count'] = 0
                counter_info['last_reset'] = current_time
            
            return counter_info['count'] < rule.max_alerts_per_hour
            
        except Exception as e:
            logger.error(f"خطأ في فحص حد معدل التنبيهات: {str(e)}")
            return True

    def _update_alert_statistics(self, alert: Alert):
        """تحديث إحصائيات التنبيهات"""
        try:
            self.statistics.total_alerts += 1
            self.statistics.alerts_by_severity[alert.severity] += 1
            self.statistics.alerts_by_type[alert.alert_type] += 1
            
            if alert.asset and alert.asset in self.statistics.alerts_by_asset:
                self.statistics.alerts_by_asset[alert.asset] += 1
            
            self.statistics.pending_alerts += 1
            self.statistics.last_alert_time = alert.timestamp
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات التنبيهات: {str(e)}")

    def _send_alert_notifications(self, alert: Alert, rule: AlertRule):
        """إرسال إشعارات التنبيه"""
        try:
            for channel in rule.channels:
                if channel == AlertChannel.CONSOLE:
                    self._send_console_notification(alert)
                elif channel == AlertChannel.LOG_FILE:
                    self._send_log_file_notification(alert)
                elif channel == AlertChannel.EMAIL:
                    self._send_email_notification(alert)
                elif channel == AlertChannel.WEBHOOK:
                    self._send_webhook_notification(alert)
                elif channel == AlertChannel.TELEGRAM:
                    self._send_telegram_notification(alert)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات التنبيه: {str(e)}")

    def _send_console_notification(self, alert: Alert):
        """إرسال إشعار للوحة التحكم"""
        try:
            severity_emoji = {
                AlertSeverity.LOW: "ℹ️",
                AlertSeverity.MEDIUM: "⚠️",
                AlertSeverity.HIGH: "🔥",
                AlertSeverity.CRITICAL: "🚨",
                AlertSeverity.EMERGENCY: "🆘"
            }
            
            emoji = severity_emoji.get(alert.severity, "⚠️")
            console_message = f"{emoji} [{alert.severity.value.upper()}] {alert.title}"
            
            if alert.asset:
                console_message += f" | الأصل: {alert.asset}"
            
            console_message += f" | {alert.message}"
            
            print(console_message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار الكونسول: {str(e)}")

    def _send_log_file_notification(self, alert: Alert):
        """إرسال إشعار لملف السجل"""
        try:
            log_message = f"ALERT [{alert.severity.value}] {alert.title}: {alert.message}"
            
            if alert.asset:
                log_message += f" | Asset: {alert.asset}"
            
            if alert.details:
                log_message += f" | Details: {json.dumps(alert.details)}"
            
            # استخدام مستوى السجل المناسب
            if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]:
                logger.critical(log_message)
            elif alert.severity == AlertSeverity.HIGH:
                logger.error(log_message)
            elif alert.severity == AlertSeverity.MEDIUM:
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار ملف السجل: {str(e)}")

    def _send_email_notification(self, alert: Alert):
        """إرسال إشعار بالبريد الإلكتروني"""
        try:
            if not self.notification_settings['email']['enabled']:
                return
            
            # إنشاء رسالة البريد الإلكتروني
            msg = MIMEMultipart()
            msg['From'] = self.notification_settings['email']['username']
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"
            
            # محتوى الرسالة
            body = f"""
تنبيه من نظام التداول السكالبينج

النوع: {alert.alert_type.value}
الخطورة: {alert.severity.value}
العنوان: {alert.title}
الرسالة: {alert.message}
الوقت: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            if alert.asset:
                body += f"الأصل: {alert.asset}\n"
            
            if alert.details:
                body += f"\nتفاصيل إضافية:\n{json.dumps(alert.details, indent=2, ensure_ascii=False)}"
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # إرسال البريد الإلكتروني
            server = smtplib.SMTP(
                self.notification_settings['email']['smtp_server'],
                self.notification_settings['email']['smtp_port']
            )
            server.starttls()
            server.login(
                self.notification_settings['email']['username'],
                self.notification_settings['email']['password']
            )
            
            for recipient in self.notification_settings['email']['recipients']:
                msg['To'] = recipient
                server.send_message(msg)
                del msg['To']
            
            server.quit()
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار البريد الإلكتروني: {str(e)}")

    def _send_webhook_notification(self, alert: Alert):
        """إرسال إشعار عبر webhook"""
        try:
            if not self.notification_settings['webhook']['enabled']:
                return
            
            payload = {
                'alert_id': alert.alert_id,
                'alert_type': alert.alert_type.value,
                'severity': alert.severity.value,
                'title': alert.title,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'asset': alert.asset,
                'details': alert.details
            }
            
            response = requests.post(
                self.notification_settings['webhook']['url'],
                json=payload,
                headers=self.notification_settings['webhook']['headers'],
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug(f"تم إرسال webhook للتنبيه {alert.alert_id}")
            else:
                logger.warning(f"فشل في إرسال webhook: {response.status_code}")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال webhook: {str(e)}")

    def _send_telegram_notification(self, alert: Alert):
        """إرسال إشعار عبر Telegram"""
        try:
            if not self.notification_settings['telegram']['enabled']:
                return
            
            bot_token = self.notification_settings['telegram']['bot_token']
            chat_id = self.notification_settings['telegram']['chat_id']
            
            message = f"🚨 *{alert.title}*\n\n"
            message += f"النوع: {alert.alert_type.value}\n"
            message += f"الخطورة: {alert.severity.value}\n"
            message += f"الرسالة: {alert.message}\n"
            message += f"الوقت: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            if alert.asset:
                message += f"الأصل: {alert.asset}\n"
            
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.debug(f"تم إرسال رسالة Telegram للتنبيه {alert.alert_id}")
            else:
                logger.warning(f"فشل في إرسال رسالة Telegram: {response.status_code}")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة Telegram: {str(e)}")

    def _trigger_alert_callbacks(self, alert: Alert):
        """تشغيل callbacks للتنبيهات"""
        try:
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"خطأ في تشغيل callback للتنبيه: {str(e)}")
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل callbacks: {str(e)}")

    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """إضافة callback للتنبيهات"""
        try:
            self.alert_callbacks.append(callback)
            logger.debug("تم إضافة callback للتنبيهات")
            
        except Exception as e:
            logger.error(f"خطأ في إضافة callback: {str(e)}")

    def _monitoring_loop(self):
        """حلقة مراقبة النظام للتنبيهات التلقائية"""
        try:
            while self.alert_system_active:
                try:
                    # فحص حالة النظام وإطلاق التنبيهات التلقائية
                    self._check_system_health()
                    
                    # تنظيف التنبيهات القديمة
                    self._cleanup_old_alerts()
                    
                    # انتظار حتى الفحص التالي
                    time.sleep(self.monitoring_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة مراقبة التنبيهات: {str(e)}")
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة التنبيهات: {str(e)}")

    def _check_system_health(self):
        """فحص صحة النظام وإطلاق التنبيهات التلقائية"""
        try:
            # هذه الدالة ستتكامل مع أنظمة المراقبة الأخرى
            # لفحص حالة النظام وإطلاق التنبيهات عند الحاجة
            pass
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة النظام: {str(e)}")

    def _cleanup_old_alerts(self):
        """تنظيف التنبيهات القديمة"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=24)
            
            # تنظيف التاريخ
            self.alert_history = [
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ]
            
            # تنظيف التنبيهات النشطة المحلولة
            resolved_alerts = [
                alert_id for alert_id, alert in self.active_alerts.items()
                if alert.resolved and alert.resolution_time and 
                (current_time - alert.resolution_time).total_seconds() > 3600  # ساعة واحدة
            ]
            
            for alert_id in resolved_alerts:
                del self.active_alerts[alert_id]
                self.statistics.pending_alerts -= 1
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التنبيهات القديمة: {str(e)}")

    def resolve_alert(self, alert_id: str, resolution_message: str = "") -> bool:
        """حل تنبيه"""
        try:
            if alert_id not in self.active_alerts:
                return False

            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolution_time = datetime.now()

            if resolution_message:
                alert.details['resolution_message'] = resolution_message

            self.statistics.resolved_alerts += 1
            self.statistics.pending_alerts -= 1

            logger.info(f"تم حل التنبيه: {alert_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حل التنبيه {alert_id}: {str(e)}")
            return False

    def acknowledge_alert(self, alert_id: str) -> bool:
        """الإقرار بالتنبيه"""
        try:
            if alert_id not in self.active_alerts:
                return False

            alert = self.active_alerts[alert_id]
            alert.acknowledgment_time = datetime.now()

            logger.info(f"تم الإقرار بالتنبيه: {alert_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في الإقرار بالتنبيه {alert_id}: {str(e)}")
            return False

    def escalate_alert(self, alert_id: str) -> bool:
        """تصعيد التنبيه"""
        try:
            if alert_id not in self.active_alerts:
                return False

            alert = self.active_alerts[alert_id]
            alert.escalation_level += 1

            # تصعيد الخطورة إذا لزم الأمر
            if alert.escalation_level >= 2 and alert.severity != AlertSeverity.EMERGENCY:
                if alert.severity == AlertSeverity.CRITICAL:
                    alert.severity = AlertSeverity.EMERGENCY
                elif alert.severity == AlertSeverity.HIGH:
                    alert.severity = AlertSeverity.CRITICAL
                elif alert.severity == AlertSeverity.MEDIUM:
                    alert.severity = AlertSeverity.HIGH

            self.statistics.escalated_alerts += 1

            logger.warning(f"تم تصعيد التنبيه: {alert_id} إلى المستوى {alert.escalation_level}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تصعيد التنبيه {alert_id}: {str(e)}")
            return False

    @handle_errors(default_return={}, log_error=True)
    def get_alert_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التنبيهات"""
        try:
            return {
                'system_status': {
                    'alert_system_active': self.alert_system_active,
                    'total_rules': len(self.alert_rules),
                    'enabled_rules': len([r for r in self.alert_rules.values() if r.enabled])
                },
                'alert_counts': {
                    'total_alerts': self.statistics.total_alerts,
                    'pending_alerts': self.statistics.pending_alerts,
                    'resolved_alerts': self.statistics.resolved_alerts,
                    'escalated_alerts': self.statistics.escalated_alerts
                },
                'alerts_by_severity': {
                    severity.value: count
                    for severity, count in self.statistics.alerts_by_severity.items()
                },
                'alerts_by_type': {
                    alert_type.value: count
                    for alert_type, count in self.statistics.alerts_by_type.items()
                },
                'top_assets_with_alerts': self._get_top_assets_with_alerts(10),
                'recent_activity': {
                    'last_alert_time': self.statistics.last_alert_time.isoformat() if self.statistics.last_alert_time else None,
                    'alerts_last_hour': self._count_recent_alerts(hours=1),
                    'alerts_last_24h': self._count_recent_alerts(hours=24)
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التنبيهات: {str(e)}")
            return {}

    def _get_top_assets_with_alerts(self, limit: int) -> List[Dict[str, Any]]:
        """الحصول على أكثر الأصول التي لديها تنبيهات"""
        try:
            sorted_assets = sorted(
                self.statistics.alerts_by_asset.items(),
                key=lambda x: x[1],
                reverse=True
            )

            return [
                {'asset': asset, 'alert_count': count}
                for asset, count in sorted_assets[:limit]
                if count > 0
            ]

        except Exception as e:
            logger.error(f"خطأ في جلب أكثر الأصول تنبيهاً: {str(e)}")
            return []

    def _count_recent_alerts(self, hours: int) -> int:
        """عد التنبيهات الحديثة"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            return len([
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ])

        except Exception as e:
            logger.error(f"خطأ في عد التنبيهات الحديثة: {str(e)}")
            return 0

    def get_active_alerts(self, severity: AlertSeverity = None) -> List[Dict[str, Any]]:
        """الحصول على التنبيهات النشطة"""
        try:
            active_alerts = []

            for alert in self.active_alerts.values():
                if not alert.resolved and (not severity or alert.severity == severity):
                    active_alerts.append({
                        'alert_id': alert.alert_id,
                        'alert_type': alert.alert_type.value,
                        'severity': alert.severity.value,
                        'title': alert.title,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'asset': alert.asset,
                        'escalation_level': alert.escalation_level,
                        'acknowledged': alert.acknowledgment_time is not None
                    })

            # ترتيب حسب الخطورة والوقت
            severity_order = {
                AlertSeverity.EMERGENCY: 5,
                AlertSeverity.CRITICAL: 4,
                AlertSeverity.HIGH: 3,
                AlertSeverity.MEDIUM: 2,
                AlertSeverity.LOW: 1
            }

            active_alerts.sort(
                key=lambda x: (severity_order.get(AlertSeverity(x['severity']), 0), x['timestamp']),
                reverse=True
            )

            return active_alerts

        except Exception as e:
            logger.error(f"خطأ في جلب التنبيهات النشطة: {str(e)}")
            return []

    def get_alert_history(self, hours: int = 24, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على تاريخ التنبيهات"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            recent_alerts = [
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ]

            # ترتيب حسب الوقت (الأحدث أولاً)
            recent_alerts.sort(key=lambda x: x.timestamp, reverse=True)

            return [
                {
                    'alert_id': alert.alert_id,
                    'alert_type': alert.alert_type.value,
                    'severity': alert.severity.value,
                    'title': alert.title,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'asset': alert.asset,
                    'resolved': alert.resolved,
                    'resolution_time': alert.resolution_time.isoformat() if alert.resolution_time else None,
                    'escalation_level': alert.escalation_level
                }
                for alert in recent_alerts[:limit]
            ]

        except Exception as e:
            logger.error(f"خطأ في جلب تاريخ التنبيهات: {str(e)}")
            return []

    def configure_notification_channel(self, channel: AlertChannel, settings: Dict[str, Any]):
        """تكوين قناة إشعارات"""
        try:
            if channel == AlertChannel.EMAIL:
                self.notification_settings['email'].update(settings)
            elif channel == AlertChannel.WEBHOOK:
                self.notification_settings['webhook'].update(settings)
            elif channel == AlertChannel.TELEGRAM:
                self.notification_settings['telegram'].update(settings)

            logger.info(f"تم تكوين قناة الإشعارات: {channel.value}")

        except Exception as e:
            logger.error(f"خطأ في تكوين قناة الإشعارات: {str(e)}")

    def test_notification_channel(self, channel: AlertChannel) -> bool:
        """اختبار قناة إشعارات"""
        try:
            test_alert = Alert(
                alert_id="test_alert",
                rule_id="test_rule",
                alert_type=AlertType.SYSTEM_ERROR,
                severity=AlertSeverity.LOW,
                title="اختبار النظام",
                message="هذا اختبار لقناة الإشعارات",
                details={'test': True},
                timestamp=datetime.now()
            )

            if channel == AlertChannel.EMAIL:
                self._send_email_notification(test_alert)
            elif channel == AlertChannel.WEBHOOK:
                self._send_webhook_notification(test_alert)
            elif channel == AlertChannel.TELEGRAM:
                self._send_telegram_notification(test_alert)
            elif channel == AlertChannel.CONSOLE:
                self._send_console_notification(test_alert)
            elif channel == AlertChannel.LOG_FILE:
                self._send_log_file_notification(test_alert)

            logger.info(f"تم اختبار قناة الإشعارات: {channel.value}")
            return True

        except Exception as e:
            logger.error(f"خطأ في اختبار قناة الإشعارات {channel.value}: {str(e)}")
            return False

    def generate_alert_report(self, hours: int = 24) -> Dict[str, Any]:
        """إنشاء تقرير التنبيهات"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # فلترة التنبيهات في الفترة المحددة
            period_alerts = [
                alert for alert in self.alert_history
                if alert.timestamp > cutoff_time
            ]

            report = {
                'report_period_hours': hours,
                'generation_time': datetime.now().isoformat(),
                'summary': {
                    'total_alerts': len(period_alerts),
                    'resolved_alerts': len([a for a in period_alerts if a.resolved]),
                    'pending_alerts': len([a for a in period_alerts if not a.resolved]),
                    'escalated_alerts': len([a for a in period_alerts if a.escalation_level > 0])
                },
                'alerts_by_severity': {},
                'alerts_by_type': {},
                'alerts_by_asset': {},
                'resolution_times': [],
                'most_frequent_alerts': [],
                'recommendations': []
            }

            # تحليل التنبيهات
            for alert in period_alerts:
                # حسب الخطورة
                severity = alert.severity.value
                if severity not in report['alerts_by_severity']:
                    report['alerts_by_severity'][severity] = 0
                report['alerts_by_severity'][severity] += 1

                # حسب النوع
                alert_type = alert.alert_type.value
                if alert_type not in report['alerts_by_type']:
                    report['alerts_by_type'][alert_type] = 0
                report['alerts_by_type'][alert_type] += 1

                # حسب الأصل
                if alert.asset:
                    if alert.asset not in report['alerts_by_asset']:
                        report['alerts_by_asset'][alert.asset] = 0
                    report['alerts_by_asset'][alert.asset] += 1

                # أوقات الحل
                if alert.resolved and alert.resolution_time:
                    resolution_time = (alert.resolution_time - alert.timestamp).total_seconds()
                    report['resolution_times'].append(resolution_time)

            # حساب متوسط وقت الحل
            if report['resolution_times']:
                avg_resolution_time = sum(report['resolution_times']) / len(report['resolution_times'])
                report['avg_resolution_time_seconds'] = avg_resolution_time

            # أكثر التنبيهات تكراراً
            alert_type_counts = sorted(
                report['alerts_by_type'].items(),
                key=lambda x: x[1],
                reverse=True
            )
            report['most_frequent_alerts'] = alert_type_counts[:5]

            # إنشاء التوصيات
            report['recommendations'] = self._generate_alert_recommendations(report)

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير التنبيهات: {str(e)}")
            return {}

    def _generate_alert_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات بناءً على تقرير التنبيهات"""
        recommendations = []

        try:
            # فحص عدد التنبيهات
            total_alerts = report['summary']['total_alerts']
            if total_alerts > 100:
                recommendations.append(f"عدد التنبيهات مرتفع ({total_alerts}) - راجع قواعد التنبيهات")

            # فحص معدل الحل
            resolved = report['summary']['resolved_alerts']
            if total_alerts > 0:
                resolution_rate = (resolved / total_alerts) * 100
                if resolution_rate < 80:
                    recommendations.append(f"معدل حل التنبيهات منخفض ({resolution_rate:.1f}%)")

            # فحص التصعيد
            escalated = report['summary']['escalated_alerts']
            if escalated > total_alerts * 0.2:  # أكثر من 20%
                recommendations.append("معدل تصعيد التنبيهات مرتفع - راجع أوقات الاستجابة")

            # فحص أكثر التنبيهات تكراراً
            if report['most_frequent_alerts']:
                most_frequent = report['most_frequent_alerts'][0]
                if most_frequent[1] > total_alerts * 0.3:  # أكثر من 30%
                    recommendations.append(f"تنبيه متكرر: {most_frequent[0]} - راجع السبب الجذري")

            # فحص وقت الحل
            if 'avg_resolution_time_seconds' in report:
                avg_time = report['avg_resolution_time_seconds']
                if avg_time > 3600:  # أكثر من ساعة
                    recommendations.append(f"وقت حل التنبيهات طويل ({avg_time/60:.1f} دقيقة)")

            if not recommendations:
                recommendations.append("أداء التنبيهات جيد - استمر في المراقبة")

        except Exception as e:
            logger.error(f"خطأ في إنشاء توصيات التنبيهات: {str(e)}")
            recommendations.append("خطأ في تحليل البيانات")

        return recommendations

# إنشاء مثيل عام للاستخدام
advanced_alert_system = AdvancedAlertSystem()

# دوال مساعدة للوصول السريع
def start_alert_system() -> bool:
    """بدء نظام التنبيهات"""
    return advanced_alert_system.start_alert_system()

def stop_alert_system():
    """إيقاف نظام التنبيهات"""
    advanced_alert_system.stop_alert_system()

def trigger_alert(alert_type: AlertType, severity: AlertSeverity, title: str, message: str, **kwargs) -> Optional[str]:
    """إطلاق تنبيه"""
    return advanced_alert_system.trigger_alert(alert_type, severity, title, message, **kwargs)

def get_alert_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات التنبيهات"""
    return advanced_alert_system.get_alert_statistics()

def get_active_alerts(severity: AlertSeverity = None) -> List[Dict[str, Any]]:
    """الحصول على التنبيهات النشطة"""
    return advanced_alert_system.get_active_alerts(severity)

def resolve_alert(alert_id: str, resolution_message: str = "") -> bool:
    """حل تنبيه"""
    return advanced_alert_system.resolve_alert(alert_id, resolution_message)

def get_alert_report(hours: int = 24) -> Dict[str, Any]:
    """الحصول على تقرير التنبيهات"""
    return advanced_alert_system.generate_alert_report(hours)
