"""
نظام تسجيل العمليات المتقدم
Advanced Operations Logger System
"""

import os
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import gzip
import shutil

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("advanced_operations_logger")

class OperationType(Enum):
    """أنواع العمليات"""
    DATA_COLLECTION = "data_collection"
    INDICATOR_CALCULATION = "indicator_calculation"
    WEBSOCKET_CONNECTION = "websocket_connection"
    DATABASE_OPERATION = "database_operation"
    REDIS_OPERATION = "redis_operation"
    SYSTEM_HEALTH_CHECK = "system_health_check"
    ERROR_HANDLING = "error_handling"
    PERFORMANCE_MONITORING = "performance_monitoring"
    BACKUP_OPERATION = "backup_operation"
    RESTART_OPERATION = "restart_operation"

class LogLevel(Enum):
    """مستويات التسجيل"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    """فئات السجلات"""
    SYSTEM = "system"
    OPERATIONS = "operations"
    PERFORMANCE = "performance"
    ERRORS = "errors"
    AUDIT = "audit"
    SECURITY = "security"

@dataclass
class OperationLogEntry:
    """إدخال سجل عملية"""
    timestamp: datetime
    operation_type: OperationType
    level: LogLevel
    category: LogCategory
    asset: Optional[str] = None
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    duration_ms: Optional[float] = None
    success: bool = True
    error_details: Optional[str] = None
    correlation_id: Optional[str] = None

@dataclass
class LoggingStatistics:
    """إحصائيات التسجيل"""
    total_entries: int = 0
    entries_by_level: Dict[LogLevel, int] = field(default_factory=dict)
    entries_by_category: Dict[LogCategory, int] = field(default_factory=dict)
    entries_by_operation: Dict[OperationType, int] = field(default_factory=dict)
    errors_count: int = 0
    warnings_count: int = 0
    last_entry_time: Optional[datetime] = None
    log_files_created: int = 0
    total_log_size_mb: float = 0.0

class AdvancedOperationsLogger:
    """نظام تسجيل العمليات المتقدم"""
    
    def __init__(self):
        self.config = TradingConfig()
        
        # إعدادات التسجيل
        self.logs_directory = "logs"
        self.max_log_file_size = 50 * 1024 * 1024  # 50 MB
        self.max_log_files = 10
        self.log_retention_days = 30
        
        # حالة النظام
        self.logging_active = False
        self.buffer_size = 1000
        self.flush_interval = 30  # ثانية
        
        # مخازن السجلات
        self.log_buffer: List[OperationLogEntry] = []
        self.buffer_lock = threading.Lock()
        
        # إحصائيات
        self.statistics = LoggingStatistics()
        
        # معالجات السجلات
        self.log_handlers: Dict[LogCategory, logging.Logger] = {}
        
        # خيط التنظيف
        self.cleanup_thread = None
        self.flush_thread = None
        
        # تهيئة النظام
        self._initialize_logging_system()
        
        logger.info("تم تهيئة نظام تسجيل العمليات المتقدم")

    def _initialize_logging_system(self):
        """تهيئة نظام التسجيل"""
        try:
            # إنشاء مجلد السجلات
            os.makedirs(self.logs_directory, exist_ok=True)
            
            # إنشاء معالجات السجلات لكل فئة
            for category in LogCategory:
                self._create_category_logger(category)
            
            # تهيئة الإحصائيات
            for level in LogLevel:
                self.statistics.entries_by_level[level] = 0
            
            for category in LogCategory:
                self.statistics.entries_by_category[category] = 0
            
            for operation in OperationType:
                self.statistics.entries_by_operation[operation] = 0
            
            logger.debug("تم تهيئة نظام التسجيل بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة نظام التسجيل: {str(e)}")

    def _create_category_logger(self, category: LogCategory):
        """إنشاء معالج سجلات لفئة معينة"""
        try:
            # إنشاء logger منفصل لكل فئة
            category_logger = logging.getLogger(f"scalping_{category.value}")
            category_logger.setLevel(logging.DEBUG)
            
            # إزالة المعالجات الموجودة
            for handler in category_logger.handlers[:]:
                category_logger.removeHandler(handler)
            
            # إنشاء معالج ملف دوار
            log_file_path = os.path.join(self.logs_directory, f"{category.value}.log")
            
            file_handler = RotatingFileHandler(
                log_file_path,
                maxBytes=self.max_log_file_size,
                backupCount=self.max_log_files,
                encoding='utf-8'
            )
            
            # تنسيق السجلات
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            category_logger.addHandler(file_handler)
            category_logger.propagate = False
            
            self.log_handlers[category] = category_logger
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء معالج سجلات {category.value}: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_logging(self) -> bool:
        """بدء نظام التسجيل"""
        try:
            if self.logging_active:
                logger.warning("نظام التسجيل نشط بالفعل")
                return True
            
            logger.info("🔄 بدء نظام تسجيل العمليات المتقدم")
            
            self.logging_active = True
            
            # بدء خيط التنظيف الدوري
            self.flush_thread = threading.Thread(
                target=self._flush_loop,
                daemon=True,
                name="LogFlushThread"
            )
            self.flush_thread.start()
            
            # بدء خيط التنظيف
            self.cleanup_thread = threading.Thread(
                target=self._cleanup_loop,
                daemon=True,
                name="LogCleanupThread"
            )
            self.cleanup_thread.start()
            
            logger.info("✅ تم بدء نظام تسجيل العمليات المتقدم")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام التسجيل: {str(e)}")
            return False

    def stop_logging(self):
        """إيقاف نظام التسجيل"""
        try:
            logger.info("إيقاف نظام تسجيل العمليات...")
            
            self.logging_active = False
            
            # تفريغ المخزن المؤقت
            self._flush_buffer()
            
            # انتظار انتهاء الخيوط
            if self.flush_thread and self.flush_thread.is_alive():
                self.flush_thread.join(timeout=5)
            
            if self.cleanup_thread and self.cleanup_thread.is_alive():
                self.cleanup_thread.join(timeout=5)
            
            logger.info("✅ تم إيقاف نظام تسجيل العمليات")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام التسجيل: {str(e)}")

    def log_operation(self, 
                     operation_type: OperationType,
                     level: LogLevel,
                     category: LogCategory,
                     message: str,
                     asset: str = None,
                     details: Dict[str, Any] = None,
                     duration_ms: float = None,
                     success: bool = True,
                     error_details: str = None,
                     correlation_id: str = None):
        """تسجيل عملية"""
        try:
            # إنشاء إدخال السجل
            log_entry = OperationLogEntry(
                timestamp=datetime.now(),
                operation_type=operation_type,
                level=level,
                category=category,
                asset=asset,
                message=message,
                details=details or {},
                duration_ms=duration_ms,
                success=success,
                error_details=error_details,
                correlation_id=correlation_id
            )
            
            # إضافة للمخزن المؤقت
            with self.buffer_lock:
                self.log_buffer.append(log_entry)
                
                # تفريغ المخزن إذا امتلأ
                if len(self.log_buffer) >= self.buffer_size:
                    self._flush_buffer()
            
            # تحديث الإحصائيات
            self._update_statistics(log_entry)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل العملية: {str(e)}")

    def _update_statistics(self, log_entry: OperationLogEntry):
        """تحديث الإحصائيات"""
        try:
            self.statistics.total_entries += 1
            self.statistics.entries_by_level[log_entry.level] += 1
            self.statistics.entries_by_category[log_entry.category] += 1
            self.statistics.entries_by_operation[log_entry.operation_type] += 1
            self.statistics.last_entry_time = log_entry.timestamp
            
            if log_entry.level == LogLevel.ERROR:
                self.statistics.errors_count += 1
            elif log_entry.level == LogLevel.WARNING:
                self.statistics.warnings_count += 1
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def _flush_buffer(self):
        """تفريغ المخزن المؤقت للملفات"""
        try:
            if not self.log_buffer:
                return
            
            # نسخ المخزن وتنظيفه
            with self.buffer_lock:
                entries_to_flush = self.log_buffer.copy()
                self.log_buffer.clear()
            
            # تجميع الإدخالات حسب الفئة
            entries_by_category: Dict[LogCategory, List[OperationLogEntry]] = {}
            for entry in entries_to_flush:
                if entry.category not in entries_by_category:
                    entries_by_category[entry.category] = []
                entries_by_category[entry.category].append(entry)
            
            # كتابة كل فئة في ملفها
            for category, entries in entries_by_category.items():
                self._write_entries_to_file(category, entries)
            
        except Exception as e:
            logger.error(f"خطأ في تفريغ المخزن المؤقت: {str(e)}")

    def _write_entries_to_file(self, category: LogCategory, entries: List[OperationLogEntry]):
        """كتابة الإدخالات في ملف الفئة"""
        try:
            if category not in self.log_handlers:
                return
            
            category_logger = self.log_handlers[category]
            
            for entry in entries:
                # تحويل الإدخال لتنسيق JSON
                log_data = {
                    'timestamp': entry.timestamp.isoformat(),
                    'operation_type': entry.operation_type.value,
                    'level': entry.level.value,
                    'asset': entry.asset,
                    'message': entry.message,
                    'details': entry.details,
                    'duration_ms': entry.duration_ms,
                    'success': entry.success,
                    'error_details': entry.error_details,
                    'correlation_id': entry.correlation_id
                }
                
                # تحديد مستوى السجل
                log_level = getattr(logging, entry.level.value)
                
                # كتابة السجل
                category_logger.log(log_level, json.dumps(log_data, ensure_ascii=False, default=str))
            
        except Exception as e:
            logger.error(f"خطأ في كتابة السجلات لفئة {category.value}: {str(e)}")

    def _flush_loop(self):
        """حلقة تفريغ المخزن المؤقت"""
        try:
            while self.logging_active:
                try:
                    time.sleep(self.flush_interval)
                    self._flush_buffer()
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة تفريغ المخزن: {str(e)}")
                    time.sleep(10)
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة تفريغ المخزن: {str(e)}")

    def _cleanup_loop(self):
        """حلقة تنظيف الملفات القديمة"""
        try:
            while self.logging_active:
                try:
                    # تنظيف كل 6 ساعات
                    time.sleep(6 * 3600)
                    self._cleanup_old_logs()
                    self._update_log_size_statistics()
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة التنظيف: {str(e)}")
                    time.sleep(3600)  # انتظار ساعة عند الخطأ
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة التنظيف: {str(e)}")

    def _cleanup_old_logs(self):
        """تنظيف السجلات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.log_retention_days)
            
            for root, dirs, files in os.walk(self.logs_directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    try:
                        # فحص تاريخ الملف
                        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        if file_time < cutoff_date:
                            # ضغط الملف قبل الحذف إذا كان كبير
                            if os.path.getsize(file_path) > 1024 * 1024:  # أكبر من 1 MB
                                self._compress_log_file(file_path)
                            else:
                                os.remove(file_path)
                                logger.debug(f"تم حذف ملف السجل القديم: {file}")
                    
                    except Exception as e:
                        logger.error(f"خطأ في معالجة ملف {file}: {str(e)}")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف السجلات القديمة: {str(e)}")

    def _compress_log_file(self, file_path: str):
        """ضغط ملف السجل"""
        try:
            compressed_path = file_path + '.gz'
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # حذف الملف الأصلي
            os.remove(file_path)
            
            logger.debug(f"تم ضغط ملف السجل: {os.path.basename(file_path)}")
            
        except Exception as e:
            logger.error(f"خطأ في ضغط ملف السجل {file_path}: {str(e)}")

    def _update_log_size_statistics(self):
        """تحديث إحصائيات حجم السجلات"""
        try:
            total_size = 0
            files_count = 0
            
            for root, dirs, files in os.walk(self.logs_directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    files_count += 1
            
            self.statistics.total_log_size_mb = total_size / (1024 * 1024)
            self.statistics.log_files_created = files_count
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الحجم: {str(e)}")

    @handle_errors(default_return={}, log_error=True)
    def get_logging_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التسجيل"""
        try:
            return {
                'system_status': {
                    'logging_active': self.logging_active,
                    'buffer_size': len(self.log_buffer),
                    'max_buffer_size': self.buffer_size
                },
                'entries_statistics': {
                    'total_entries': self.statistics.total_entries,
                    'errors_count': self.statistics.errors_count,
                    'warnings_count': self.statistics.warnings_count,
                    'last_entry_time': self.statistics.last_entry_time.isoformat() if self.statistics.last_entry_time else None
                },
                'entries_by_level': {level.value: count for level, count in self.statistics.entries_by_level.items()},
                'entries_by_category': {category.value: count for category, count in self.statistics.entries_by_category.items()},
                'entries_by_operation': {operation.value: count for operation, count in self.statistics.entries_by_operation.items()},
                'file_statistics': {
                    'log_files_count': self.statistics.log_files_created,
                    'total_size_mb': round(self.statistics.total_log_size_mb, 2),
                    'logs_directory': self.logs_directory,
                    'retention_days': self.log_retention_days
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات التسجيل: {str(e)}")
            return {}

    def get_recent_logs(self, category: LogCategory, count: int = 100) -> List[Dict[str, Any]]:
        """الحصول على آخر السجلات لفئة معينة"""
        try:
            log_file_path = os.path.join(self.logs_directory, f"{category.value}.log")

            if not os.path.exists(log_file_path):
                return []

            recent_logs = []

            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

                # أخذ آخر السطور
                for line in lines[-count:]:
                    try:
                        # استخراج JSON من السطر
                        json_start = line.find('{')
                        if json_start != -1:
                            json_data = line[json_start:].strip()
                            log_entry = json.loads(json_data)
                            recent_logs.append(log_entry)
                    except json.JSONDecodeError:
                        continue

            return recent_logs

        except Exception as e:
            logger.error(f"خطأ في جلب السجلات الحديثة لفئة {category.value}: {str(e)}")
            return []

    def search_logs(self,
                   category: LogCategory,
                   search_term: str,
                   start_date: datetime = None,
                   end_date: datetime = None,
                   max_results: int = 1000) -> List[Dict[str, Any]]:
        """البحث في السجلات"""
        try:
            log_file_path = os.path.join(self.logs_directory, f"{category.value}.log")

            if not os.path.exists(log_file_path):
                return []

            matching_logs = []

            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        # استخراج JSON من السطر
                        json_start = line.find('{')
                        if json_start != -1:
                            json_data = line[json_start:].strip()
                            log_entry = json.loads(json_data)

                            # فحص التاريخ
                            log_time = datetime.fromisoformat(log_entry['timestamp'])
                            if start_date and log_time < start_date:
                                continue
                            if end_date and log_time > end_date:
                                continue

                            # فحص النص
                            if search_term.lower() in json.dumps(log_entry).lower():
                                matching_logs.append(log_entry)

                                if len(matching_logs) >= max_results:
                                    break

                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue

            return matching_logs

        except Exception as e:
            logger.error(f"خطأ في البحث في السجلات: {str(e)}")
            return []

    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """الحصول على ملخص الأخطاء"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            error_summary = {
                'total_errors': 0,
                'errors_by_operation': {},
                'errors_by_asset': {},
                'critical_errors': 0,
                'recent_errors': [],
                'error_trends': [],
                'timestamp': datetime.now().isoformat()
            }

            # البحث في سجلات الأخطاء
            error_logs = self.search_logs(
                LogCategory.ERRORS,
                "",
                start_date=cutoff_time,
                max_results=10000
            )

            for log_entry in error_logs:
                error_summary['total_errors'] += 1

                # تجميع حسب نوع العملية
                operation = log_entry.get('operation_type', 'unknown')
                if operation not in error_summary['errors_by_operation']:
                    error_summary['errors_by_operation'][operation] = 0
                error_summary['errors_by_operation'][operation] += 1

                # تجميع حسب الأصل
                asset = log_entry.get('asset')
                if asset:
                    if asset not in error_summary['errors_by_asset']:
                        error_summary['errors_by_asset'][asset] = 0
                    error_summary['errors_by_asset'][asset] += 1

                # عد الأخطاء الحرجة
                if log_entry.get('level') == 'CRITICAL':
                    error_summary['critical_errors'] += 1

                # إضافة للأخطاء الحديثة (أول 10)
                if len(error_summary['recent_errors']) < 10:
                    error_summary['recent_errors'].append({
                        'timestamp': log_entry['timestamp'],
                        'operation': operation,
                        'asset': asset,
                        'message': log_entry.get('message', ''),
                        'level': log_entry.get('level', 'ERROR')
                    })

            return error_summary

        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص الأخطاء: {str(e)}")
            return {}

    def generate_operations_report(self, hours: int = 24) -> Dict[str, Any]:
        """إنشاء تقرير العمليات"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            report = {
                'report_period_hours': hours,
                'generation_time': datetime.now().isoformat(),
                'summary': {
                    'total_operations': 0,
                    'successful_operations': 0,
                    'failed_operations': 0,
                    'success_rate_percentage': 0.0
                },
                'operations_by_type': {},
                'operations_by_asset': {},
                'performance_metrics': {
                    'avg_operation_duration_ms': 0.0,
                    'slowest_operations': [],
                    'fastest_operations': []
                },
                'error_analysis': self.get_error_summary(hours),
                'recommendations': []
            }

            # تحليل سجلات العمليات
            operations_logs = self.search_logs(
                LogCategory.OPERATIONS,
                "",
                start_date=cutoff_time,
                max_results=50000
            )

            total_duration = 0
            duration_count = 0
            operations_with_duration = []

            for log_entry in operations_logs:
                report['summary']['total_operations'] += 1

                # تحليل النجاح/الفشل
                if log_entry.get('success', True):
                    report['summary']['successful_operations'] += 1
                else:
                    report['summary']['failed_operations'] += 1

                # تجميع حسب نوع العملية
                operation_type = log_entry.get('operation_type', 'unknown')
                if operation_type not in report['operations_by_type']:
                    report['operations_by_type'][operation_type] = {'count': 0, 'success': 0, 'failed': 0}

                report['operations_by_type'][operation_type]['count'] += 1
                if log_entry.get('success', True):
                    report['operations_by_type'][operation_type]['success'] += 1
                else:
                    report['operations_by_type'][operation_type]['failed'] += 1

                # تجميع حسب الأصل
                asset = log_entry.get('asset')
                if asset:
                    if asset not in report['operations_by_asset']:
                        report['operations_by_asset'][asset] = {'count': 0, 'success': 0, 'failed': 0}

                    report['operations_by_asset'][asset]['count'] += 1
                    if log_entry.get('success', True):
                        report['operations_by_asset'][asset]['success'] += 1
                    else:
                        report['operations_by_asset'][asset]['failed'] += 1

                # تحليل الأداء
                duration = log_entry.get('duration_ms')
                if duration is not None:
                    total_duration += duration
                    duration_count += 1
                    operations_with_duration.append({
                        'operation': operation_type,
                        'asset': asset,
                        'duration_ms': duration,
                        'timestamp': log_entry['timestamp']
                    })

            # حساب معدل النجاح
            if report['summary']['total_operations'] > 0:
                report['summary']['success_rate_percentage'] = (
                    report['summary']['successful_operations'] /
                    report['summary']['total_operations'] * 100
                )

            # حساب متوسط المدة
            if duration_count > 0:
                report['performance_metrics']['avg_operation_duration_ms'] = total_duration / duration_count

            # أبطأ وأسرع العمليات
            if operations_with_duration:
                sorted_operations = sorted(operations_with_duration, key=lambda x: x['duration_ms'])
                report['performance_metrics']['fastest_operations'] = sorted_operations[:5]
                report['performance_metrics']['slowest_operations'] = sorted_operations[-5:]

            # إنشاء التوصيات
            report['recommendations'] = self._generate_operations_recommendations(report)

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير العمليات: {str(e)}")
            return {}

    def _generate_operations_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات بناءً على تقرير العمليات"""
        recommendations = []

        try:
            # فحص معدل النجاح
            success_rate = report['summary']['success_rate_percentage']
            if success_rate < 95:
                recommendations.append(f"تحسين معدل النجاح الحالي {success_rate:.1f}% إلى أكثر من 95%")

            # فحص الأداء
            avg_duration = report['performance_metrics']['avg_operation_duration_ms']
            if avg_duration > 1000:  # أكثر من ثانية
                recommendations.append(f"تحسين أداء العمليات - المتوسط الحالي {avg_duration:.0f}ms")

            # فحص الأخطاء
            error_count = report['error_analysis']['total_errors']
            if error_count > 100:
                recommendations.append(f"تقليل عدد الأخطاء - العدد الحالي {error_count}")

            # فحص العمليات الفاشلة حسب النوع
            for operation_type, stats in report['operations_by_type'].items():
                if stats['count'] > 0:
                    failure_rate = (stats['failed'] / stats['count']) * 100
                    if failure_rate > 10:
                        recommendations.append(f"تحسين عملية {operation_type} - معدل فشل {failure_rate:.1f}%")

            if not recommendations:
                recommendations.append("الأداء ممتاز - استمر في المراقبة")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            recommendations.append("خطأ في تحليل البيانات")

        return recommendations

# إنشاء مثيل عام للاستخدام
advanced_operations_logger = AdvancedOperationsLogger()

# دوال مساعدة للوصول السريع
def start_operations_logging() -> bool:
    """بدء نظام تسجيل العمليات"""
    return advanced_operations_logger.start_logging()

def stop_operations_logging():
    """إيقاف نظام تسجيل العمليات"""
    advanced_operations_logger.stop_logging()

def log_operation(operation_type: OperationType, level: LogLevel, category: LogCategory,
                 message: str, **kwargs):
    """تسجيل عملية"""
    advanced_operations_logger.log_operation(operation_type, level, category, message, **kwargs)

def get_logging_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات التسجيل"""
    return advanced_operations_logger.get_logging_statistics()

def get_operations_report(hours: int = 24) -> Dict[str, Any]:
    """الحصول على تقرير العمليات"""
    return advanced_operations_logger.generate_operations_report(hours)

def get_error_summary(hours: int = 24) -> Dict[str, Any]:
    """الحصول على ملخص الأخطاء"""
    return advanced_operations_logger.get_error_summary(hours)
