"""
نظام مراقبة الأداء المتقدم للـ70 زوج والمؤشرات
Advanced Performance Monitor for 70 Currency Pairs and Indicators
"""

import asyncio
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import psutil

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("advanced_performance_monitor")

class PerformanceMetricType(Enum):
    """أنواع مقاييس الأداء"""
    DATA_COLLECTION_SPEED = "data_collection_speed"
    INDICATOR_CALCULATION_TIME = "indicator_calculation_time"
    WEBSOCKET_LATENCY = "websocket_latency"
    DATABASE_RESPONSE_TIME = "database_response_time"
    REDIS_RESPONSE_TIME = "redis_response_time"
    MEMORY_USAGE_PER_ASSET = "memory_usage_per_asset"
    CPU_USAGE_PER_OPERATION = "cpu_usage_per_operation"
    THROUGHPUT_CANDLES_PER_SECOND = "throughput_candles_per_second"
    ERROR_RATE_PER_ASSET = "error_rate_per_asset"
    SYSTEM_EFFICIENCY = "system_efficiency"

class PerformanceLevel(Enum):
    """مستويات الأداء"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    CRITICAL = "critical"

@dataclass
class PerformanceMetric:
    """مقياس أداء واحد"""
    metric_type: PerformanceMetricType
    asset: Optional[str] = None
    value: float = 0.0
    unit: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    level: PerformanceLevel = PerformanceLevel.GOOD
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AssetPerformanceProfile:
    """ملف أداء لأصل واحد"""
    asset: str
    data_collection_avg_time: float = 0.0
    indicator_calculation_avg_time: float = 0.0
    websocket_latency_avg: float = 0.0
    error_count_last_hour: int = 0
    success_rate_percentage: float = 100.0
    last_update: datetime = field(default_factory=datetime.now)
    performance_score: float = 100.0
    bottlenecks: List[str] = field(default_factory=list)

@dataclass
class SystemPerformanceReport:
    """تقرير أداء النظام الشامل"""
    timestamp: datetime
    overall_performance_score: float
    total_assets_monitored: int
    assets_performance: Dict[str, AssetPerformanceProfile]
    system_metrics: Dict[PerformanceMetricType, PerformanceMetric]
    performance_trends: Dict[str, List[float]]
    recommendations: List[str]
    critical_issues: List[str]

class AdvancedPerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self):
        self.config = TradingConfig()
        
        # حالة المراقبة
        self.monitoring_active = False
        self.monitoring_interval = 60  # دقيقة واحدة
        self.detailed_monitoring_interval = 10  # 10 ثوان للمراقبة التفصيلية
        
        # بيانات الأداء
        self.assets_performance: Dict[str, AssetPerformanceProfile] = {}
        self.system_metrics: Dict[PerformanceMetricType, List[PerformanceMetric]] = {}
        self.performance_history: Dict[str, List[float]] = {}
        
        # إعدادات التنبيهات
        self.performance_thresholds = {
            PerformanceMetricType.DATA_COLLECTION_SPEED: {'warning': 5.0, 'critical': 10.0},  # ثواني
            PerformanceMetricType.INDICATOR_CALCULATION_TIME: {'warning': 2.0, 'critical': 5.0},  # ثواني
            PerformanceMetricType.WEBSOCKET_LATENCY: {'warning': 1000, 'critical': 3000},  # ميلي ثانية
            PerformanceMetricType.DATABASE_RESPONSE_TIME: {'warning': 500, 'critical': 2000},  # ميلي ثانية
            PerformanceMetricType.ERROR_RATE_PER_ASSET: {'warning': 5.0, 'critical': 15.0},  # نسبة مئوية
            PerformanceMetricType.SYSTEM_EFFICIENCY: {'warning': 70.0, 'critical': 50.0}  # نسبة مئوية
        }
        
        # إحصائيات المراقبة
        self.monitoring_stats = {
            'total_measurements': 0,
            'alerts_generated': 0,
            'performance_reports_created': 0,
            'last_comprehensive_check': None
        }
        
        # خيوط المراقبة
        self.monitoring_thread = None
        self.detailed_monitoring_thread = None
        
        # تهيئة ملفات الأداء للأصول
        self._initialize_assets_performance()
        
        logger.info("تم تهيئة مراقب الأداء المتقدم")

    def _initialize_assets_performance(self):
        """تهيئة ملفات الأداء لجميع الأصول"""
        try:
            for asset in CURRENCY_PAIRS_70:
                self.assets_performance[asset] = AssetPerformanceProfile(asset=asset)
            
            # تهيئة قوائم المقاييس
            for metric_type in PerformanceMetricType:
                self.system_metrics[metric_type] = []
                self.performance_history[metric_type.value] = []
            
            logger.debug(f"تم تهيئة ملفات الأداء لـ {len(CURRENCY_PAIRS_70)} أصل")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة ملفات الأداء: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_monitoring(self) -> bool:
        """بدء مراقبة الأداء"""
        try:
            if self.monitoring_active:
                logger.warning("مراقبة الأداء نشطة بالفعل")
                return True
            
            logger.info("🔄 بدء مراقبة الأداء المتقدم")
            
            self.monitoring_active = True
            
            # بدء خيط المراقبة الرئيسي
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="AdvancedPerformanceMonitor"
            )
            self.monitoring_thread.start()
            
            # بدء خيط المراقبة التفصيلية
            self.detailed_monitoring_thread = threading.Thread(
                target=self._detailed_monitoring_loop,
                daemon=True,
                name="DetailedPerformanceMonitor"
            )
            self.detailed_monitoring_thread.start()
            
            logger.info("✅ تم بدء مراقبة الأداء المتقدم")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة الأداء: {str(e)}")
            return False

    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        try:
            logger.info("إيقاف مراقبة الأداء المتقدم...")
            
            self.monitoring_active = False
            
            # انتظار انتهاء الخيوط
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            if self.detailed_monitoring_thread and self.detailed_monitoring_thread.is_alive():
                self.detailed_monitoring_thread.join(timeout=5)
            
            logger.info("✅ تم إيقاف مراقبة الأداء المتقدم")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف مراقبة الأداء: {str(e)}")

    def record_performance_metric(self, metric_data):
        """تسجيل مقياس أداء"""
        try:
            # إذا كان المدخل قاموس، تحويله لكائن PerformanceMetric
            if isinstance(metric_data, dict):
                metric = PerformanceMetric(
                    timestamp=datetime.now(),
                    metric_type=metric_data['metric_type'],
                    value=float(metric_data['value']),
                    asset=metric_data.get('asset'),
                    unit=metric_data.get('unit', ''),
                    details=metric_data.get('details', {})
                )
            else:
                metric = metric_data

            # إضافة المقياس للقائمة
            if metric.metric_type not in self.system_metrics:
                self.system_metrics[metric.metric_type] = []

            self.system_metrics[metric.metric_type].append(metric)
            
            # الاحتفاظ بآخر 1000 مقياس فقط
            if len(self.system_metrics[metric.metric_type]) > 1000:
                self.system_metrics[metric.metric_type] = self.system_metrics[metric.metric_type][-1000:]
            
            # تحديث التاريخ
            if metric.metric_type.value not in self.performance_history:
                self.performance_history[metric.metric_type.value] = []
            
            self.performance_history[metric.metric_type.value].append(metric.value)
            
            # الاحتفاظ بآخر 100 قيمة
            if len(self.performance_history[metric.metric_type.value]) > 100:
                self.performance_history[metric.metric_type.value] = self.performance_history[metric.metric_type.value][-100:]
            
            # تحديث ملف الأداء للأصل إذا كان محدد
            if metric.asset and metric.asset in self.assets_performance:
                self._update_asset_performance(metric)
            
            # فحص التنبيهات
            self._check_performance_alerts(metric)
            
            self.monitoring_stats['total_measurements'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل مقياس الأداء: {str(e)}")

    def _update_asset_performance(self, metric: PerformanceMetric):
        """تحديث ملف أداء الأصل"""
        try:
            asset_profile = self.assets_performance[metric.asset]
            
            # تحديث المقاييس حسب النوع
            if metric.metric_type == PerformanceMetricType.DATA_COLLECTION_SPEED:
                asset_profile.data_collection_avg_time = self._calculate_moving_average(
                    asset_profile.data_collection_avg_time, metric.value, 10
                )
            elif metric.metric_type == PerformanceMetricType.INDICATOR_CALCULATION_TIME:
                asset_profile.indicator_calculation_avg_time = self._calculate_moving_average(
                    asset_profile.indicator_calculation_avg_time, metric.value, 10
                )
            elif metric.metric_type == PerformanceMetricType.WEBSOCKET_LATENCY:
                asset_profile.websocket_latency_avg = self._calculate_moving_average(
                    asset_profile.websocket_latency_avg, metric.value, 10
                )
            elif metric.metric_type == PerformanceMetricType.ERROR_RATE_PER_ASSET:
                if metric.level in [PerformanceLevel.POOR, PerformanceLevel.CRITICAL]:
                    asset_profile.error_count_last_hour += 1
            
            # تحديث وقت آخر تحديث
            asset_profile.last_update = datetime.now()
            
            # إعادة حساب نقاط الأداء
            asset_profile.performance_score = self._calculate_asset_performance_score(asset_profile)
            
            # تحديث قائمة الاختناقات
            asset_profile.bottlenecks = self._identify_asset_bottlenecks(asset_profile)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث ملف أداء الأصل {metric.asset}: {str(e)}")

    def _calculate_moving_average(self, current_avg: float, new_value: float, window_size: int) -> float:
        """حساب المتوسط المتحرك"""
        try:
            if current_avg == 0:
                return new_value
            
            # متوسط متحرك بسيط
            weight = 1.0 / window_size
            return current_avg * (1 - weight) + new_value * weight
            
        except Exception:
            return new_value

    def _calculate_asset_performance_score(self, profile: AssetPerformanceProfile) -> float:
        """حساب نقاط أداء الأصل"""
        try:
            score = 100.0
            
            # خصم نقاط حسب أوقات الاستجابة
            if profile.data_collection_avg_time > 5.0:
                score -= min(20, profile.data_collection_avg_time * 2)
            
            if profile.indicator_calculation_avg_time > 2.0:
                score -= min(15, profile.indicator_calculation_avg_time * 5)
            
            if profile.websocket_latency_avg > 1000:
                score -= min(10, (profile.websocket_latency_avg - 1000) / 100)
            
            # خصم نقاط حسب الأخطاء
            if profile.error_count_last_hour > 0:
                score -= min(25, profile.error_count_last_hour * 5)
            
            return max(0, score)
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الأداء: {str(e)}")
            return 50.0

    def _identify_asset_bottlenecks(self, profile: AssetPerformanceProfile) -> List[str]:
        """تحديد اختناقات الأداء للأصل"""
        bottlenecks = []
        
        try:
            if profile.data_collection_avg_time > 5.0:
                bottlenecks.append("جمع البيانات بطيء")
            
            if profile.indicator_calculation_avg_time > 2.0:
                bottlenecks.append("حساب المؤشرات بطيء")
            
            if profile.websocket_latency_avg > 1000:
                bottlenecks.append("زمن استجابة WebSocket مرتفع")
            
            if profile.error_count_last_hour > 5:
                bottlenecks.append("معدل أخطاء مرتفع")
            
            if profile.performance_score < 70:
                bottlenecks.append("أداء عام منخفض")
            
        except Exception as e:
            logger.error(f"خطأ في تحديد الاختناقات: {str(e)}")
        
        return bottlenecks

    def _check_performance_alerts(self, metric: PerformanceMetric):
        """فحص التنبيهات للمقياس"""
        try:
            if metric.metric_type not in self.performance_thresholds:
                return

            thresholds = self.performance_thresholds[metric.metric_type]

            # تحديد مستوى التنبيه
            if metric.value >= thresholds.get('critical', float('inf')):
                alert_level = "CRITICAL"
                metric.level = PerformanceLevel.CRITICAL
            elif metric.value >= thresholds.get('warning', float('inf')):
                alert_level = "WARNING"
                metric.level = PerformanceLevel.POOR
            else:
                return  # لا يوجد تنبيه

            # إنشاء التنبيه
            alert_message = f"تنبيه أداء {alert_level}: {metric.metric_type.value}"
            if metric.asset:
                alert_message += f" للأصل {metric.asset}"
            alert_message += f" - القيمة: {metric.value} {metric.unit}"

            logger.warning(alert_message)
            self.monitoring_stats['alerts_generated'] += 1

        except Exception as e:
            logger.error(f"خطأ في فحص التنبيهات: {str(e)}")

    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        try:
            while self.monitoring_active:
                try:
                    # إجراء فحص شامل للأداء
                    report = self.generate_comprehensive_performance_report()

                    if report:
                        self._log_performance_summary(report)
                        self.monitoring_stats['performance_reports_created'] += 1
                        self.monitoring_stats['last_comprehensive_check'] = datetime.now()

                    # انتظار حتى الفحص التالي
                    time.sleep(self.monitoring_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة المراقبة: {str(e)}")
                    time.sleep(60)

        except Exception as e:
            logger.error(f"خطأ في حلقة المراقبة: {str(e)}")

    def _detailed_monitoring_loop(self):
        """حلقة المراقبة التفصيلية"""
        try:
            while self.monitoring_active:
                try:
                    # مراقبة موارد النظام
                    self._monitor_system_resources()

                    # مراقبة أداء قاعدة البيانات
                    asyncio.run(self._monitor_database_performance())

                    # تنظيف البيانات القديمة
                    self._cleanup_old_performance_data()

                    # انتظار حتى الفحص التالي
                    time.sleep(self.detailed_monitoring_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة المراقبة التفصيلية: {str(e)}")
                    time.sleep(30)

        except Exception as e:
            logger.error(f"خطأ في حلقة المراقبة التفصيلية: {str(e)}")

    def _monitor_system_resources(self):
        """مراقبة موارد النظام"""
        try:
            # مراقبة استخدام المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_metric = PerformanceMetric(
                metric_type=PerformanceMetricType.CPU_USAGE_PER_OPERATION,
                value=cpu_percent,
                unit="%",
                details={'process_count': len(psutil.pids())}
            )
            self.record_performance_metric(cpu_metric)

            # مراقبة استخدام الذاكرة
            memory = psutil.virtual_memory()
            memory_per_asset = memory.used / len(CURRENCY_PAIRS_70) / (1024 * 1024)  # MB per asset
            memory_metric = PerformanceMetric(
                metric_type=PerformanceMetricType.MEMORY_USAGE_PER_ASSET,
                value=memory_per_asset,
                unit="MB",
                details={'total_memory_gb': memory.total / (1024**3), 'memory_percent': memory.percent}
            )
            self.record_performance_metric(memory_metric)

        except Exception as e:
            logger.error(f"خطأ في مراقبة موارد النظام: {str(e)}")

    async def _monitor_database_performance(self):
        """مراقبة أداء قاعدة البيانات"""
        try:
            from database.connection_manager import db_manager

            # قياس وقت استجابة قاعدة البيانات
            start_time = time.time()

            # اختبار استعلام بسيط
            connection_test = db_manager.test_connection()

            response_time = (time.time() - start_time) * 1000  # ميلي ثانية

            db_metric = PerformanceMetric(
                metric_type=PerformanceMetricType.DATABASE_RESPONSE_TIME,
                value=response_time,
                unit="ms",
                details={'connection_successful': connection_test}
            )
            self.record_performance_metric(db_metric)

        except Exception as e:
            logger.error(f"خطأ في مراقبة أداء قاعدة البيانات: {str(e)}")

    def _cleanup_old_performance_data(self):
        """تنظيف البيانات القديمة"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=24)  # الاحتفاظ بـ24 ساعة

            # تنظيف المقاييس القديمة
            for metric_type, metrics_list in self.system_metrics.items():
                self.system_metrics[metric_type] = [
                    metric for metric in metrics_list
                    if metric.timestamp > cutoff_time
                ]

            # إعادة تعيين عدادات الأخطاء للساعة الماضية
            for asset_profile in self.assets_performance.values():
                if current_time - asset_profile.last_update > timedelta(hours=1):
                    asset_profile.error_count_last_hour = 0

        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")

    def _log_performance_summary(self, report: SystemPerformanceReport):
        """طباعة ملخص الأداء"""
        try:
            logger.info("=" * 60)
            logger.info("📊 ملخص أداء النظام")
            logger.info("=" * 60)
            logger.info(f"النقاط الإجمالية: {report.overall_performance_score:.1f}/100")
            logger.info(f"الأصول المراقبة: {report.total_assets_monitored}")

            # أفضل وأسوأ الأصول
            if report.assets_performance:
                best_asset = max(report.assets_performance.values(), key=lambda x: x.performance_score)
                worst_asset = min(report.assets_performance.values(), key=lambda x: x.performance_score)

                logger.info(f"أفضل أصل: {best_asset.asset} ({best_asset.performance_score:.1f})")
                logger.info(f"أسوأ أصل: {worst_asset.asset} ({worst_asset.performance_score:.1f})")

            # المشاكل الحرجة
            if report.critical_issues:
                logger.warning("🚨 مشاكل حرجة:")
                for issue in report.critical_issues[:3]:  # أول 3 مشاكل
                    logger.warning(f"  - {issue}")

            # التوصيات
            if report.recommendations:
                logger.info("💡 توصيات:")
                for rec in report.recommendations[:3]:  # أول 3 توصيات
                    logger.info(f"  - {rec}")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"خطأ في طباعة ملخص الأداء: {str(e)}")

    @handle_errors(default_return=None, log_error=True)
    def generate_comprehensive_performance_report(self) -> Optional[SystemPerformanceReport]:
        """إنشاء تقرير أداء شامل"""
        try:
            current_time = datetime.now()

            # حساب النقاط الإجمالية
            overall_score = self._calculate_overall_performance_score()

            # جمع مقاييس النظام الحالية
            current_system_metrics = {}
            for metric_type, metrics_list in self.system_metrics.items():
                if metrics_list:
                    # أخذ آخر مقياس
                    current_system_metrics[metric_type] = metrics_list[-1]

            # إنشاء اتجاهات الأداء
            performance_trends = {}
            for metric_name, values in self.performance_history.items():
                if len(values) >= 2:
                    # حساب الاتجاه (متزايد/متناقص)
                    recent_avg = sum(values[-5:]) / len(values[-5:]) if len(values) >= 5 else values[-1]
                    older_avg = sum(values[-10:-5]) / 5 if len(values) >= 10 else values[0] if values else 0
                    performance_trends[metric_name] = [older_avg, recent_avg]

            # إنشاء التوصيات والمشاكل الحرجة
            recommendations = self._generate_performance_recommendations()
            critical_issues = self._identify_critical_performance_issues()

            # إنشاء التقرير
            report = SystemPerformanceReport(
                timestamp=current_time,
                overall_performance_score=overall_score,
                total_assets_monitored=len(self.assets_performance),
                assets_performance=self.assets_performance.copy(),
                system_metrics=current_system_metrics,
                performance_trends=performance_trends,
                recommendations=recommendations,
                critical_issues=critical_issues
            )

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الأداء: {str(e)}")
            return None

    def _calculate_overall_performance_score(self) -> float:
        """حساب النقاط الإجمالية للأداء"""
        try:
            if not self.assets_performance:
                return 0.0

            # متوسط نقاط جميع الأصول
            total_score = sum(profile.performance_score for profile in self.assets_performance.values())
            avg_asset_score = total_score / len(self.assets_performance)

            # تعديل حسب مقاييس النظام
            system_penalty = 0

            # فحص استخدام المعالج
            cpu_metrics = self.system_metrics.get(PerformanceMetricType.CPU_USAGE_PER_OPERATION, [])
            if cpu_metrics:
                latest_cpu = cpu_metrics[-1].value
                if latest_cpu > 80:
                    system_penalty += 10
                elif latest_cpu > 60:
                    system_penalty += 5

            # فحص استخدام الذاكرة
            memory_metrics = self.system_metrics.get(PerformanceMetricType.MEMORY_USAGE_PER_ASSET, [])
            if memory_metrics:
                latest_memory = memory_metrics[-1].value
                if latest_memory > 100:  # أكثر من 100 MB لكل أصل
                    system_penalty += 10
                elif latest_memory > 50:
                    system_penalty += 5

            return max(0, avg_asset_score - system_penalty)

        except Exception as e:
            logger.error(f"خطأ في حساب النقاط الإجمالية: {str(e)}")
            return 50.0

    def _generate_performance_recommendations(self) -> List[str]:
        """إنشاء توصيات تحسين الأداء"""
        recommendations = []

        try:
            # تحليل الأصول ذات الأداء المنخفض
            poor_performance_assets = [
                asset for asset, profile in self.assets_performance.items()
                if profile.performance_score < 70
            ]

            if len(poor_performance_assets) > 10:
                recommendations.append(f"تحسين أداء {len(poor_performance_assets)} أصل ذات أداء منخفض")

            # تحليل أوقات الاستجابة
            slow_data_collection = [
                asset for asset, profile in self.assets_performance.items()
                if profile.data_collection_avg_time > 5.0
            ]

            if slow_data_collection:
                recommendations.append("تحسين سرعة جمع البيانات للأصول البطيئة")

            # تحليل حساب المؤشرات
            slow_indicators = [
                asset for asset, profile in self.assets_performance.items()
                if profile.indicator_calculation_avg_time > 2.0
            ]

            if slow_indicators:
                recommendations.append("تحسين خوارزميات حساب المؤشرات")

            # تحليل استخدام الموارد
            memory_metrics = self.system_metrics.get(PerformanceMetricType.MEMORY_USAGE_PER_ASSET, [])
            if memory_metrics and memory_metrics[-1].value > 50:
                recommendations.append("تحسين استخدام الذاكرة وتنظيف البيانات القديمة")

            cpu_metrics = self.system_metrics.get(PerformanceMetricType.CPU_USAGE_PER_OPERATION, [])
            if cpu_metrics and cpu_metrics[-1].value > 70:
                recommendations.append("تحسين استخدام المعالج وتوزيع العمليات")

            # إذا لم توجد مشاكل
            if not recommendations:
                recommendations.append("الأداء ممتاز - استمر في المراقبة")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            recommendations.append("خطأ في تحليل الأداء")

        return recommendations

    def _identify_critical_performance_issues(self) -> List[str]:
        """تحديد المشاكل الحرجة في الأداء"""
        critical_issues = []

        try:
            # فحص الأصول المتوقفة
            current_time = datetime.now()
            stalled_assets = [
                asset for asset, profile in self.assets_performance.items()
                if (current_time - profile.last_update).total_seconds() > 300  # 5 دقائق
            ]

            if stalled_assets:
                critical_issues.append(f"{len(stalled_assets)} أصل متوقف عن التحديث")

            # فحص معدل الأخطاء المرتفع
            high_error_assets = [
                asset for asset, profile in self.assets_performance.items()
                if profile.error_count_last_hour > 10
            ]

            if high_error_assets:
                critical_issues.append(f"{len(high_error_assets)} أصل بمعدل أخطاء مرتفع")

            # فحص استخدام الموارد الحرج
            cpu_metrics = self.system_metrics.get(PerformanceMetricType.CPU_USAGE_PER_OPERATION, [])
            if cpu_metrics and cpu_metrics[-1].value > 90:
                critical_issues.append("استخدام المعالج في المنطقة الحرجة")

            memory_metrics = self.system_metrics.get(PerformanceMetricType.MEMORY_USAGE_PER_ASSET, [])
            if memory_metrics and memory_metrics[-1].value > 200:  # 200 MB per asset
                critical_issues.append("استخدام الذاكرة مرتفع جداً")

            # فحص أوقات الاستجابة الحرجة
            db_metrics = self.system_metrics.get(PerformanceMetricType.DATABASE_RESPONSE_TIME, [])
            if db_metrics and db_metrics[-1].value > 5000:  # 5 ثوان
                critical_issues.append("وقت استجابة قاعدة البيانات حرج")

        except Exception as e:
            logger.error(f"خطأ في تحديد المشاكل الحرجة: {str(e)}")
            critical_issues.append("خطأ في تحليل المشاكل الحرجة")

        return critical_issues

    @handle_errors(default_return={}, log_error=True)
    def get_performance_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        try:
            current_time = datetime.now()

            # إحصائيات عامة
            stats = {
                'monitoring_status': {
                    'active': self.monitoring_active,
                    'total_measurements': self.monitoring_stats['total_measurements'],
                    'alerts_generated': self.monitoring_stats['alerts_generated'],
                    'reports_created': self.monitoring_stats['performance_reports_created'],
                    'last_check': self.monitoring_stats['last_comprehensive_check']
                },
                'assets_summary': {
                    'total_assets': len(self.assets_performance),
                    'excellent_performance': len([p for p in self.assets_performance.values() if p.performance_score >= 90]),
                    'good_performance': len([p for p in self.assets_performance.values() if 70 <= p.performance_score < 90]),
                    'poor_performance': len([p for p in self.assets_performance.values() if p.performance_score < 70])
                },
                'system_health': {
                    'overall_score': self._calculate_overall_performance_score(),
                    'critical_issues_count': len(self._identify_critical_performance_issues()),
                    'recommendations_count': len(self._generate_performance_recommendations())
                },
                'performance_trends': self.performance_history.copy(),
                'timestamp': current_time.isoformat()
            }

            return stats

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الأداء: {str(e)}")
            return {}

    def get_asset_performance_details(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل أداء أصل معين"""
        try:
            if asset not in self.assets_performance:
                return None

            profile = self.assets_performance[asset]

            return {
                'asset': asset,
                'performance_score': profile.performance_score,
                'data_collection_time': profile.data_collection_avg_time,
                'indicator_calculation_time': profile.indicator_calculation_avg_time,
                'websocket_latency': profile.websocket_latency_avg,
                'error_count_last_hour': profile.error_count_last_hour,
                'success_rate': profile.success_rate_percentage,
                'last_update': profile.last_update.isoformat(),
                'bottlenecks': profile.bottlenecks,
                'performance_level': self._get_performance_level(profile.performance_score)
            }

        except Exception as e:
            logger.error(f"خطأ في جلب تفاصيل أداء {asset}: {str(e)}")
            return None

    def _get_performance_level(self, score: float) -> str:
        """تحديد مستوى الأداء حسب النقاط"""
        if score >= 90:
            return "ممتاز"
        elif score >= 80:
            return "جيد جداً"
        elif score >= 70:
            return "جيد"
        elif score >= 60:
            return "مقبول"
        else:
            return "ضعيف"

# إنشاء مثيل عام للاستخدام
advanced_performance_monitor = AdvancedPerformanceMonitor()

# دوال مساعدة للوصول السريع
def start_performance_monitoring() -> bool:
    """بدء مراقبة الأداء المتقدم"""
    return advanced_performance_monitor.start_monitoring()

def stop_performance_monitoring():
    """إيقاف مراقبة الأداء المتقدم"""
    advanced_performance_monitor.stop_monitoring()

def record_metric(metric_type: PerformanceMetricType, value: float, asset: str = None, unit: str = "", details: Dict[str, Any] = None):
    """تسجيل مقياس أداء"""
    metric = PerformanceMetric(
        metric_type=metric_type,
        asset=asset,
        value=value,
        unit=unit,
        details=details or {}
    )
    advanced_performance_monitor.record_performance_metric(metric)

def get_performance_report() -> Optional[SystemPerformanceReport]:
    """الحصول على تقرير أداء شامل"""
    return advanced_performance_monitor.generate_comprehensive_performance_report()

def get_performance_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات الأداء"""
    return advanced_performance_monitor.get_performance_statistics()

def get_asset_performance(asset: str) -> Optional[Dict[str, Any]]:
    """الحصول على أداء أصل معين"""
    return advanced_performance_monitor.get_asset_performance_details(asset)
