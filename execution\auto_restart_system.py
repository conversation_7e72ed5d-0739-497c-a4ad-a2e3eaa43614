"""
نظام إعادة التشغيل التلقائي - إعادة تشغيل النظام تلقائياً عند انقطاع الكهرباء أو إغلاق الجهاز
"""

import os
import sys
import time
import signal
import atexit
import subprocess
import threading
import json
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import platform

from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, ScalpingError

logger = scalping_logger.get_logger("auto_restart_system")

class RestartReason(Enum):
    """أسباب إعادة التشغيل"""
    SYSTEM_CRASH = "system_crash"
    POWER_OUTAGE = "power_outage"
    MEMORY_LEAK = "memory_leak"
    HIGH_CPU = "high_cpu"
    MANUAL_RESTART = "manual_restart"
    SCHEDULED_RESTART = "scheduled_restart"
    ERROR_THRESHOLD = "error_threshold"
    HEALTH_CHECK_FAILURE = "health_check_failure"

class SystemState(Enum):
    """حالات النظام"""
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    RESTARTING = "restarting"
    FAILED = "failed"

@dataclass
class RestartEvent:
    """حدث إعادة تشغيل"""
    timestamp: datetime
    reason: RestartReason
    details: Dict[str, Any] = field(default_factory=dict)
    success: bool = False
    restart_time_seconds: float = 0.0
    error_message: str = ""

@dataclass
class SystemStateInfo:
    """معلومات حالة النظام"""
    state: SystemState
    pid: int
    start_time: datetime
    uptime_seconds: float = 0.0
    restart_count: int = 0
    last_restart: Optional[datetime] = None
    memory_usage_mb: float = 0.0
    cpu_percent: float = 0.0

class AutoRestartSystem:
    """نظام إعادة التشغيل التلقائي"""
    
    def __init__(self, main_script_path: str = None):
        self.config = TradingConfig()
        
        # مسار السكريبت الرئيسي
        self.main_script_path = main_script_path or sys.argv[0]
        self.python_executable = sys.executable
        
        # معلومات النظام
        self.system_info = SystemStateInfo(
            state=SystemState.RUNNING,
            pid=os.getpid(),
            start_time=datetime.now()
        )
        
        # إعدادات إعادة التشغيل
        self.auto_restart_enabled = True
        self.max_restart_attempts = 5
        self.restart_delay_seconds = 30
        self.restart_cooldown_minutes = 5
        
        # عتبات إعادة التشغيل
        self.memory_threshold_mb = 2048  # 2GB
        self.cpu_threshold_percent = 90.0
        self.error_threshold_count = 50
        self.health_check_failures_threshold = 3
        
        # مراقبة النظام
        self.monitoring_active = False
        self.monitoring_interval = 60  # ثانية
        
        # تاريخ إعادة التشغيل
        self.restart_history: List[RestartEvent] = []
        self.max_history_size = 100
        
        # عدادات الأخطاء
        self.error_counts = {
            'total_errors': 0,
            'consecutive_failures': 0,
            'health_check_failures': 0
        }
        
        # callbacks
        self.before_restart_callbacks: List[Callable] = []
        self.after_restart_callbacks: List[Callable] = []
        
        # ملف حالة النظام
        self.state_file = "logs/system_state.json"
        self.lock_file = "logs/system.lock"
        
        # تسجيل معالجات الإشارات
        self._register_signal_handlers()
        
        # تسجيل دالة التنظيف عند الخروج
        atexit.register(self._cleanup_on_exit)
        
        # تحميل الحالة السابقة
        self._load_previous_state()
        
        logger.info(f"تم تهيئة نظام إعادة التشغيل التلقائي")
        logger.info(f"السكريبت الرئيسي: {self.main_script_path}")
        logger.info(f"PID: {self.system_info.pid}")

    def _register_signal_handlers(self):
        """تسجيل معالجات الإشارات"""
        try:
            # معالج إشارة الإغلاق
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
            
            # في Windows
            if platform.system() == "Windows":
                signal.signal(signal.SIGBREAK, self._signal_handler)
            else:
                # في Linux/Unix
                signal.signal(signal.SIGHUP, self._signal_handler)
                signal.signal(signal.SIGUSR1, self._signal_handler)
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل معالجات الإشارات: {str(e)}")

    def _signal_handler(self, signum, frame):
        """معالج الإشارات"""
        try:
            logger.info(f"تم استلام إشارة: {signum}")
            
            if signum in [signal.SIGTERM, signal.SIGINT]:
                logger.info("بدء إغلاق النظام بأمان...")
                self._graceful_shutdown()
            elif signum == signal.SIGUSR1:  # إشارة إعادة التشغيل المخصصة
                logger.info("تم طلب إعادة التشغيل عبر الإشارة")
                self.restart_system(RestartReason.MANUAL_RESTART)
                
        except Exception as e:
            logger.error(f"خطأ في معالج الإشارات: {str(e)}")

    @handle_errors(default_return=None, log_error=True)
    def _load_previous_state(self):
        """تحميل الحالة السابقة من الملف"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                # تحديث عداد إعادة التشغيل
                self.system_info.restart_count = state_data.get('restart_count', 0)
                
                # تحميل تاريخ إعادة التشغيل
                restart_history = state_data.get('restart_history', [])
                for event_data in restart_history[-10:]:  # آخر 10 أحداث فقط
                    event = RestartEvent(
                        timestamp=datetime.fromisoformat(event_data['timestamp']),
                        reason=RestartReason(event_data['reason']),
                        details=event_data.get('details', {}),
                        success=event_data.get('success', False),
                        restart_time_seconds=event_data.get('restart_time_seconds', 0.0),
                        error_message=event_data.get('error_message', '')
                    )
                    self.restart_history.append(event)
                
                logger.info(f"تم تحميل الحالة السابقة: {self.system_info.restart_count} إعادة تشغيل")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل الحالة السابقة: {str(e)}")

    @handle_errors(default_return=None, log_error=True)
    def _save_current_state(self):
        """حفظ الحالة الحالية في الملف"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            
            state_data = {
                'timestamp': datetime.now().isoformat(),
                'pid': self.system_info.pid,
                'state': self.system_info.state.value,
                'start_time': self.system_info.start_time.isoformat(),
                'restart_count': self.system_info.restart_count,
                'last_restart': self.system_info.last_restart.isoformat() if self.system_info.last_restart else None,
                'restart_history': []
            }
            
            # إضافة تاريخ إعادة التشغيل
            for event in self.restart_history[-10:]:  # آخر 10 أحداث فقط
                event_data = {
                    'timestamp': event.timestamp.isoformat(),
                    'reason': event.reason.value,
                    'details': event.details,
                    'success': event.success,
                    'restart_time_seconds': event.restart_time_seconds,
                    'error_message': event.error_message
                }
                state_data['restart_history'].append(event_data)
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ الحالة: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def create_lock_file(self) -> bool:
        """إنشاء ملف القفل لمنع تشغيل عدة نسخ"""
        try:
            if os.path.exists(self.lock_file):
                # فحص ما إذا كانت العملية ما زالت تعمل
                with open(self.lock_file, 'r') as f:
                    old_pid = int(f.read().strip())
                
                if psutil.pid_exists(old_pid):
                    logger.warning(f"النظام يعمل بالفعل مع PID: {old_pid}")
                    return False
                else:
                    logger.info(f"إزالة ملف قفل قديم لعملية منتهية: {old_pid}")
                    os.remove(self.lock_file)
            
            # إنشاء ملف القفل الجديد
            os.makedirs(os.path.dirname(self.lock_file), exist_ok=True)
            with open(self.lock_file, 'w') as f:
                f.write(str(self.system_info.pid))
            
            logger.info(f"تم إنشاء ملف القفل: {self.lock_file}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء ملف القفل: {str(e)}")
            return False

    def remove_lock_file(self):
        """إزالة ملف القفل"""
        try:
            if os.path.exists(self.lock_file):
                os.remove(self.lock_file)
                logger.info("تم إزالة ملف القفل")
        except Exception as e:
            logger.error(f"خطأ في إزالة ملف القفل: {str(e)}")

    def add_before_restart_callback(self, callback: Callable):
        """إضافة callback قبل إعادة التشغيل"""
        self.before_restart_callbacks.append(callback)

    def add_after_restart_callback(self, callback: Callable):
        """إضافة callback بعد إعادة التشغيل"""
        self.after_restart_callbacks.append(callback)

    @handle_errors(default_return=None, log_error=True)
    def update_system_metrics(self):
        """تحديث مقاييس النظام"""
        try:
            current_process = psutil.Process(self.system_info.pid)
            
            # تحديث المقاييس
            self.system_info.memory_usage_mb = current_process.memory_info().rss / (1024 * 1024)
            self.system_info.cpu_percent = current_process.cpu_percent()
            self.system_info.uptime_seconds = (datetime.now() - self.system_info.start_time).total_seconds()
            
        except Exception as e:
            logger.error(f"خطأ في تحديث مقاييس النظام: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def should_restart_system(self) -> tuple[bool, RestartReason, str]:
        """فحص ما إذا كان النظام يحتاج إعادة تشغيل"""
        try:
            self.update_system_metrics()
            
            # فحص استخدام الذاكرة
            if self.system_info.memory_usage_mb > self.memory_threshold_mb:
                return True, RestartReason.MEMORY_LEAK, f"استخدام ذاكرة مرتفع: {self.system_info.memory_usage_mb:.1f}MB"
            
            # فحص استخدام المعالج
            if self.system_info.cpu_percent > self.cpu_threshold_percent:
                return True, RestartReason.HIGH_CPU, f"استخدام معالج مرتفع: {self.system_info.cpu_percent:.1f}%"
            
            # فحص عدد الأخطاء
            if self.error_counts['total_errors'] > self.error_threshold_count:
                return True, RestartReason.ERROR_THRESHOLD, f"تجاوز عتبة الأخطاء: {self.error_counts['total_errors']}"
            
            # فحص فشل الفحوصات الصحية
            if self.error_counts['health_check_failures'] > self.health_check_failures_threshold:
                return True, RestartReason.HEALTH_CHECK_FAILURE, f"فشل متكرر في الفحوصات الصحية: {self.error_counts['health_check_failures']}"
            
            return False, None, ""
            
        except Exception as e:
            logger.error(f"خطأ في فحص الحاجة لإعادة التشغيل: {str(e)}")
            return False, None, ""

    @handle_errors(default_return=False, log_error=True)
    def restart_system(self, reason: RestartReason, details: Dict[str, Any] = None) -> bool:
        """إعادة تشغيل النظام"""
        try:
            if not self.auto_restart_enabled:
                logger.warning("إعادة التشغيل التلقائي معطلة")
                return False
            
            # فحص عدد محاولات إعادة التشغيل
            recent_restarts = [
                event for event in self.restart_history
                if event.timestamp > datetime.now() - timedelta(minutes=self.restart_cooldown_minutes)
            ]
            
            if len(recent_restarts) >= self.max_restart_attempts:
                logger.error(f"تجاوز الحد الأقصى لمحاولات إعادة التشغيل: {len(recent_restarts)}")
                return False
            
            logger.info(f"🔄 بدء إعادة تشغيل النظام - السبب: {reason.value}")
            
            restart_start_time = time.time()
            event = RestartEvent(
                timestamp=datetime.now(),
                reason=reason,
                details=details or {}
            )
            
            try:
                # تشغيل callbacks قبل إعادة التشغيل
                for callback in self.before_restart_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        logger.error(f"خطأ في callback قبل إعادة التشغيل: {str(e)}")
                
                # تحديث الحالة
                self.system_info.state = SystemState.RESTARTING
                self.system_info.restart_count += 1
                self.system_info.last_restart = datetime.now()
                
                # حفظ الحالة
                self._save_current_state()
                
                # انتظار قصير
                time.sleep(self.restart_delay_seconds)
                
                # تشغيل النظام الجديد
                cmd = [self.python_executable, self.main_script_path] + sys.argv[1:]
                
                logger.info(f"تشغيل الأمر: {' '.join(cmd)}")
                
                # بدء العملية الجديدة
                subprocess.Popen(
                    cmd,
                    cwd=os.getcwd(),
                    env=os.environ.copy(),
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if platform.system() == "Windows" else 0
                )
                
                # تسجيل نجاح إعادة التشغيل
                event.success = True
                event.restart_time_seconds = time.time() - restart_start_time
                
                logger.info(f"✅ تم بدء العملية الجديدة بنجاح")
                
                # إنهاء العملية الحالية
                self._graceful_shutdown()
                
                return True
                
            except Exception as e:
                event.success = False
                event.error_message = str(e)
                event.restart_time_seconds = time.time() - restart_start_time
                
                logger.error(f"❌ فشل في إعادة تشغيل النظام: {str(e)}")
                return False
                
            finally:
                # إضافة الحدث إلى التاريخ
                self.restart_history.append(event)
                if len(self.restart_history) > self.max_history_size:
                    self.restart_history.pop(0)
                
        except Exception as e:
            logger.error(f"خطأ في إعادة تشغيل النظام: {str(e)}")
            return False

    def _graceful_shutdown(self):
        """إغلاق النظام بأمان"""
        try:
            logger.info("🔄 بدء الإغلاق الآمن للنظام")

            # تحديث الحالة
            self.system_info.state = SystemState.STOPPING

            # إيقاف المراقبة
            self.stop_monitoring()

            # تشغيل callbacks بعد إعادة التشغيل
            for callback in self.after_restart_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"خطأ في callback بعد إعادة التشغيل: {str(e)}")

            # حفظ الحالة النهائية
            self.system_info.state = SystemState.STOPPED
            self._save_current_state()

            # إزالة ملف القفل
            self.remove_lock_file()

            logger.info("✅ تم إغلاق النظام بأمان")

            # إنهاء العملية
            os._exit(0)

        except Exception as e:
            logger.error(f"خطأ في الإغلاق الآمن: {str(e)}")
            os._exit(1)

    def _cleanup_on_exit(self):
        """تنظيف عند الخروج"""
        try:
            self.remove_lock_file()
        except:
            pass

    @handle_errors(default_return=False, log_error=True)
    def start_monitoring(self) -> bool:
        """بدء مراقبة النظام لإعادة التشغيل التلقائي"""
        try:
            if self.monitoring_active:
                logger.warning("مراقبة إعادة التشغيل تعمل بالفعل")
                return False

            self.monitoring_active = True
            logger.info(f"🔍 بدء مراقبة إعادة التشغيل التلقائي كل {self.monitoring_interval} ثانية")

            # بدء خيط المراقبة
            monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            monitoring_thread.start()

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة إعادة التشغيل: {str(e)}")
            return False

    def _monitoring_loop(self):
        """حلقة مراقبة إعادة التشغيل"""
        try:
            while self.monitoring_active:
                try:
                    # فحص الحاجة لإعادة التشغيل
                    should_restart, reason, message = self.should_restart_system()

                    if should_restart:
                        logger.warning(f"⚠️ النظام يحتاج إعادة تشغيل: {message}")

                        # محاولة إعادة التشغيل
                        success = self.restart_system(reason, {'message': message})

                        if not success:
                            logger.error("❌ فشل في إعادة تشغيل النظام")
                            # زيادة عداد الفشل المتتالي
                            self.error_counts['consecutive_failures'] += 1
                        else:
                            # إعادة تعيين عداد الفشل
                            self.error_counts['consecutive_failures'] = 0

                    # انتظار حتى الفحص التالي
                    time.sleep(self.monitoring_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة مراقبة إعادة التشغيل: {str(e)}")
                    time.sleep(60)  # انتظار دقيقة عند الخطأ

        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة إعادة التشغيل: {str(e)}")

    def stop_monitoring(self):
        """إيقاف مراقبة إعادة التشغيل"""
        self.monitoring_active = False
        logger.info("تم إيقاف مراقبة إعادة التشغيل")

    def increment_error_count(self, error_type: str = 'general'):
        """زيادة عداد الأخطاء"""
        self.error_counts['total_errors'] += 1
        if error_type == 'health_check':
            self.error_counts['health_check_failures'] += 1

    def reset_error_counts(self):
        """إعادة تعيين عدادات الأخطاء"""
        self.error_counts = {
            'total_errors': 0,
            'consecutive_failures': 0,
            'health_check_failures': 0
        }
        logger.info("تم إعادة تعيين عدادات الأخطاء")

    @handle_errors(default_return={}, log_error=True)
    def get_restart_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات إعادة التشغيل"""
        try:
            self.update_system_metrics()

            # حساب الإحصائيات
            successful_restarts = sum(1 for event in self.restart_history if event.success)
            failed_restarts = len(self.restart_history) - successful_restarts

            # متوسط وقت إعادة التشغيل
            restart_times = [event.restart_time_seconds for event in self.restart_history if event.success]
            avg_restart_time = sum(restart_times) / len(restart_times) if restart_times else 0

            # إحصائيات الأسباب
            reason_counts = {}
            for event in self.restart_history:
                reason = event.reason.value
                reason_counts[reason] = reason_counts.get(reason, 0) + 1

            stats = {
                'system_info': {
                    'pid': self.system_info.pid,
                    'state': self.system_info.state.value,
                    'uptime_seconds': self.system_info.uptime_seconds,
                    'uptime_hours': self.system_info.uptime_seconds / 3600,
                    'start_time': self.system_info.start_time.isoformat(),
                    'memory_usage_mb': self.system_info.memory_usage_mb,
                    'cpu_percent': self.system_info.cpu_percent
                },
                'restart_info': {
                    'total_restarts': self.system_info.restart_count,
                    'successful_restarts': successful_restarts,
                    'failed_restarts': failed_restarts,
                    'last_restart': self.system_info.last_restart.isoformat() if self.system_info.last_restart else None,
                    'average_restart_time_seconds': avg_restart_time,
                    'reason_counts': reason_counts
                },
                'monitoring': {
                    'auto_restart_enabled': self.auto_restart_enabled,
                    'monitoring_active': self.monitoring_active,
                    'monitoring_interval': self.monitoring_interval,
                    'max_restart_attempts': self.max_restart_attempts,
                    'restart_cooldown_minutes': self.restart_cooldown_minutes
                },
                'thresholds': {
                    'memory_threshold_mb': self.memory_threshold_mb,
                    'cpu_threshold_percent': self.cpu_threshold_percent,
                    'error_threshold_count': self.error_threshold_count,
                    'health_check_failures_threshold': self.health_check_failures_threshold
                },
                'error_counts': self.error_counts.copy(),
                'recent_events': [
                    {
                        'timestamp': event.timestamp.isoformat(),
                        'reason': event.reason.value,
                        'success': event.success,
                        'restart_time_seconds': event.restart_time_seconds,
                        'error_message': event.error_message
                    }
                    for event in self.restart_history[-5:]  # آخر 5 أحداث
                ]
            }

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات إعادة التشغيل: {str(e)}")
            return {}

    @handle_errors(default_return=False, log_error=True)
    def schedule_restart(self, delay_minutes: int = 5, reason: str = "صيانة مجدولة") -> bool:
        """جدولة إعادة تشغيل بعد فترة محددة"""
        try:
            logger.info(f"📅 تم جدولة إعادة التشغيل بعد {delay_minutes} دقيقة - السبب: {reason}")

            def delayed_restart():
                time.sleep(delay_minutes * 60)
                self.restart_system(
                    RestartReason.SCHEDULED_RESTART,
                    {'scheduled_reason': reason, 'delay_minutes': delay_minutes}
                )

            # بدء خيط إعادة التشغيل المجدولة
            restart_thread = threading.Thread(target=delayed_restart, daemon=True)
            restart_thread.start()

            return True

        except Exception as e:
            logger.error(f"خطأ في جدولة إعادة التشغيل: {str(e)}")
            return False

    def force_restart(self, reason: str = "إعادة تشغيل قسرية"):
        """إعادة تشغيل قسرية فورية"""
        logger.warning(f"🚨 إعادة تشغيل قسرية: {reason}")
        self.restart_system(
            RestartReason.MANUAL_RESTART,
            {'force_restart': True, 'reason': reason}
        )

    def disable_auto_restart(self):
        """تعطيل إعادة التشغيل التلقائي"""
        self.auto_restart_enabled = False
        logger.warning("⚠️ تم تعطيل إعادة التشغيل التلقائي")

    def enable_auto_restart(self):
        """تفعيل إعادة التشغيل التلقائي"""
        self.auto_restart_enabled = True
        logger.info("✅ تم تفعيل إعادة التشغيل التلقائي")

# إنشاء مثيل عام لنظام إعادة التشغيل
auto_restart_system = AutoRestartSystem()
