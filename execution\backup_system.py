"""
نظام النسخ الاحتياطي والاستعادة - نظام لعمل نسخ احتياطية واستعادة البيانات عند الحاجة
"""

import os
import shutil
import zipfile
import json
import threading
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import schedule

from config.trading_config import TradingConfig
from database.connection_manager import db_manager
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, ScalpingError

logger = scalping_logger.get_logger("backup_system")

class BackupType(Enum):
    """أنواع النسخ الاحتياطية"""
    FULL = "full"           # نسخة كاملة
    INCREMENTAL = "incremental"  # نسخة تزايدية
    DIFFERENTIAL = "differential"  # نسخة تفاضلية
    CONFIGURATION = "configuration"  # إعدادات فقط
    DATABASE = "database"   # قاعدة البيانات فقط

class BackupStatus(Enum):
    """حالات النسخ الاحتياطي"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class BackupJob:
    """مهمة نسخ احتياطي"""
    job_id: str
    backup_type: BackupType
    source_paths: List[str]
    destination_path: str
    status: BackupStatus = BackupStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    file_count: int = 0
    total_size_mb: float = 0.0
    compressed_size_mb: float = 0.0
    compression_ratio: float = 0.0
    checksum: str = ""
    error_message: str = ""
    
    @property
    def duration_seconds(self) -> float:
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return 0.0

@dataclass
class RestoreJob:
    """مهمة استعادة"""
    job_id: str
    backup_file: str
    restore_path: str
    status: BackupStatus = BackupStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    files_restored: int = 0
    error_message: str = ""

class BackupSystem:
    """نظام النسخ الاحتياطي والاستعادة"""
    
    def __init__(self, backup_directory: str = "backups"):
        self.config = TradingConfig()
        self.backup_directory = backup_directory
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(self.backup_directory, exist_ok=True)
        
        # إعدادات النسخ الاحتياطي
        self.auto_backup_enabled = True
        self.backup_retention_days = 30
        self.max_backup_size_gb = 10.0
        self.compression_enabled = True
        self.encryption_enabled = False  # يمكن تفعيله لاحقاً
        
        # جدولة النسخ الاحتياطية
        self.scheduled_backups = {
            'daily_full': {'time': '02:00', 'type': BackupType.FULL, 'enabled': True},
            'hourly_incremental': {'time': 'hourly', 'type': BackupType.INCREMENTAL, 'enabled': True},
            'weekly_database': {'time': 'sunday_03:00', 'type': BackupType.DATABASE, 'enabled': True}
        }
        
        # تتبع المهام
        self.backup_jobs: Dict[str, BackupJob] = {}
        self.restore_jobs: Dict[str, RestoreJob] = {}
        self.active_jobs: Dict[str, threading.Thread] = {}
        
        # إحصائيات
        self.backup_stats = {
            'total_backups': 0,
            'successful_backups': 0,
            'failed_backups': 0,
            'total_size_backed_up_gb': 0.0,
            'last_backup': None,
            'last_successful_backup': None
        }
        
        # مسارات النسخ الاحتياطي الافتراضية
        self.default_backup_paths = [
            'config/',
            'logs/',
            'data/',
            '.env',
            'requirements.txt'
        ]
        
        # استثناءات النسخ الاحتياطي
        self.backup_exclusions = [
            '*.pyc',
            '__pycache__/',
            '*.log',
            'temp/',
            'cache/',
            '.git/',
            'scalping_env/'
        ]
        
        # callbacks
        self.backup_callbacks: List[Callable] = []
        self.restore_callbacks: List[Callable] = []
        
        # قفل للعمليات المتزامنة
        self._lock = threading.Lock()
        
        # تحميل الإحصائيات السابقة
        self._load_backup_history()
        
        logger.info(f"تم تهيئة نظام النسخ الاحتياطي")
        logger.info(f"مجلد النسخ الاحتياطية: {self.backup_directory}")
        logger.info(f"النسخ التلقائي: {'مفعل' if self.auto_backup_enabled else 'معطل'}")

    @handle_errors(default_return=None, log_error=True)
    def _load_backup_history(self):
        """تحميل تاريخ النسخ الاحتياطية"""
        try:
            history_file = os.path.join(self.backup_directory, 'backup_history.json')
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                
                self.backup_stats.update(history_data.get('stats', {}))
                
                # تحويل التواريخ
                if self.backup_stats.get('last_backup'):
                    self.backup_stats['last_backup'] = datetime.fromisoformat(self.backup_stats['last_backup'])
                if self.backup_stats.get('last_successful_backup'):
                    self.backup_stats['last_successful_backup'] = datetime.fromisoformat(self.backup_stats['last_successful_backup'])
                
                logger.info(f"تم تحميل تاريخ النسخ الاحتياطية: {self.backup_stats['total_backups']} نسخة")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل تاريخ النسخ الاحتياطية: {str(e)}")

    @handle_errors(default_return=None, log_error=True)
    def _save_backup_history(self):
        """حفظ تاريخ النسخ الاحتياطية"""
        try:
            history_file = os.path.join(self.backup_directory, 'backup_history.json')
            
            # تحويل التواريخ إلى strings
            stats_copy = self.backup_stats.copy()
            if stats_copy.get('last_backup'):
                stats_copy['last_backup'] = stats_copy['last_backup'].isoformat()
            if stats_copy.get('last_successful_backup'):
                stats_copy['last_successful_backup'] = stats_copy['last_successful_backup'].isoformat()
            
            history_data = {
                'stats': stats_copy,
                'updated_at': datetime.now().isoformat()
            }
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ تاريخ النسخ الاحتياطية: {str(e)}")

    def _calculate_checksum(self, file_path: str) -> str:
        """حساب checksum للملف"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"خطأ في حساب checksum: {str(e)}")
            return ""

    def _should_exclude_path(self, path: str) -> bool:
        """فحص ما إذا كان المسار يجب استثناؤه"""
        for exclusion in self.backup_exclusions:
            if exclusion.endswith('/'):
                if exclusion[:-1] in path:
                    return True
            elif exclusion.startswith('*.'):
                if path.endswith(exclusion[1:]):
                    return True
            elif exclusion in path:
                return True
        return False

    @handle_errors(default_return=None, log_error=True)
    def create_backup_job(self, backup_type: BackupType, source_paths: List[str] = None,
                         destination_name: str = None) -> Optional[str]:
        """إنشاء مهمة نسخ احتياطي"""
        try:
            # إنشاء معرف فريد للمهمة
            job_id = f"backup_{backup_type.value}_{int(time.time())}"
            
            # تحديد المسارات المصدر
            if not source_paths:
                source_paths = self.default_backup_paths.copy()
            
            # تحديد اسم الملف الوجهة
            if not destination_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                destination_name = f"{backup_type.value}_backup_{timestamp}.zip"
            
            destination_path = os.path.join(self.backup_directory, destination_name)
            
            # إنشاء مهمة النسخ الاحتياطي
            job = BackupJob(
                job_id=job_id,
                backup_type=backup_type,
                source_paths=source_paths,
                destination_path=destination_path
            )
            
            with self._lock:
                self.backup_jobs[job_id] = job
            
            logger.info(f"تم إنشاء مهمة نسخ احتياطي: {job_id} ({backup_type.value})")
            return job_id
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء مهمة النسخ الاحتياطي: {str(e)}")
            return None

    @handle_errors(default_return=False, log_error=True)
    def execute_backup_job(self, job_id: str) -> bool:
        """تنفيذ مهمة نسخ احتياطي"""
        try:
            with self._lock:
                if job_id not in self.backup_jobs:
                    logger.error(f"مهمة النسخ الاحتياطي غير موجودة: {job_id}")
                    return False
                
                job = self.backup_jobs[job_id]
            
            if job.status != BackupStatus.PENDING:
                logger.warning(f"مهمة النسخ الاحتياطي ليست في حالة انتظار: {job_id}")
                return False
            
            logger.info(f"🔄 بدء تنفيذ النسخ الاحتياطي: {job_id}")
            
            # تحديث حالة المهمة
            job.status = BackupStatus.RUNNING
            job.started_at = datetime.now()
            
            try:
                # إنشاء النسخة الاحتياطية
                with zipfile.ZipFile(job.destination_path, 'w', 
                                   zipfile.ZIP_DEFLATED if self.compression_enabled else zipfile.ZIP_STORED) as zipf:
                    
                    total_size = 0
                    file_count = 0
                    
                    for source_path in job.source_paths:
                        if os.path.exists(source_path):
                            if os.path.isfile(source_path):
                                # ملف واحد
                                if not self._should_exclude_path(source_path):
                                    zipf.write(source_path)
                                    total_size += os.path.getsize(source_path)
                                    file_count += 1
                            elif os.path.isdir(source_path):
                                # مجلد
                                for root, dirs, files in os.walk(source_path):
                                    # تصفية المجلدات المستثناة
                                    dirs[:] = [d for d in dirs if not self._should_exclude_path(os.path.join(root, d))]
                                    
                                    for file in files:
                                        file_path = os.path.join(root, file)
                                        if not self._should_exclude_path(file_path):
                                            arcname = os.path.relpath(file_path)
                                            zipf.write(file_path, arcname)
                                            total_size += os.path.getsize(file_path)
                                            file_count += 1
                
                # حساب معلومات النسخة الاحتياطية
                compressed_size = os.path.getsize(job.destination_path)
                compression_ratio = (1 - compressed_size / total_size) * 100 if total_size > 0 else 0
                
                # حساب checksum
                checksum = self._calculate_checksum(job.destination_path)
                
                # تحديث معلومات المهمة
                job.file_count = file_count
                job.total_size_mb = total_size / (1024 * 1024)
                job.compressed_size_mb = compressed_size / (1024 * 1024)
                job.compression_ratio = compression_ratio
                job.checksum = checksum
                job.status = BackupStatus.COMPLETED
                job.completed_at = datetime.now()
                
                # تحديث الإحصائيات
                self.backup_stats['total_backups'] += 1
                self.backup_stats['successful_backups'] += 1
                self.backup_stats['total_size_backed_up_gb'] += job.compressed_size_mb / 1024
                self.backup_stats['last_backup'] = job.completed_at
                self.backup_stats['last_successful_backup'] = job.completed_at
                
                # حفظ التاريخ
                self._save_backup_history()
                
                # تشغيل callbacks
                for callback in self.backup_callbacks:
                    try:
                        callback(job)
                    except Exception as e:
                        logger.error(f"خطأ في callback النسخ الاحتياطي: {str(e)}")
                
                logger.info(f"✅ اكتمل النسخ الاحتياطي: {job_id}")
                logger.info(f"   - الملفات: {file_count}")
                logger.info(f"   - الحجم الأصلي: {job.total_size_mb:.1f}MB")
                logger.info(f"   - الحجم المضغوط: {job.compressed_size_mb:.1f}MB")
                logger.info(f"   - نسبة الضغط: {compression_ratio:.1f}%")
                
                return True
                
            except Exception as e:
                # فشل في النسخ الاحتياطي
                job.status = BackupStatus.FAILED
                job.error_message = str(e)
                job.completed_at = datetime.now()
                
                # تحديث الإحصائيات
                self.backup_stats['total_backups'] += 1
                self.backup_stats['failed_backups'] += 1
                self.backup_stats['last_backup'] = job.completed_at
                
                # حذف الملف الناقص إذا وجد
                if os.path.exists(job.destination_path):
                    try:
                        os.remove(job.destination_path)
                    except:
                        pass
                
                logger.error(f"❌ فشل النسخ الاحتياطي: {job_id} - {str(e)}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تنفيذ النسخ الاحتياطي: {str(e)}")
            return False

    @handle_errors(default_return=None, log_error=True)
    def create_backup_async(self, backup_type: BackupType, source_paths: List[str] = None) -> Optional[str]:
        """إنشاء نسخة احتياطية بشكل غير متزامن"""
        try:
            job_id = self.create_backup_job(backup_type, source_paths)
            if not job_id:
                return None

            # تشغيل المهمة في خيط منفصل
            def backup_worker():
                self.execute_backup_job(job_id)
                # إزالة الخيط من القائمة النشطة
                with self._lock:
                    if job_id in self.active_jobs:
                        del self.active_jobs[job_id]

            backup_thread = threading.Thread(target=backup_worker, daemon=True)
            backup_thread.start()

            with self._lock:
                self.active_jobs[job_id] = backup_thread

            return job_id

        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخ الاحتياطي غير المتزامن: {str(e)}")
            return None

    @handle_errors(default_return=None, log_error=True)
    def create_restore_job(self, backup_file: str, restore_path: str = ".") -> Optional[str]:
        """إنشاء مهمة استعادة"""
        try:
            if not os.path.exists(backup_file):
                logger.error(f"ملف النسخة الاحتياطية غير موجود: {backup_file}")
                return None

            job_id = f"restore_{int(time.time())}"

            job = RestoreJob(
                job_id=job_id,
                backup_file=backup_file,
                restore_path=restore_path
            )

            with self._lock:
                self.restore_jobs[job_id] = job

            logger.info(f"تم إنشاء مهمة استعادة: {job_id}")
            return job_id

        except Exception as e:
            logger.error(f"خطأ في إنشاء مهمة الاستعادة: {str(e)}")
            return None

    @handle_errors(default_return=False, log_error=True)
    def execute_restore_job(self, job_id: str) -> bool:
        """تنفيذ مهمة استعادة"""
        try:
            with self._lock:
                if job_id not in self.restore_jobs:
                    logger.error(f"مهمة الاستعادة غير موجودة: {job_id}")
                    return False

                job = self.restore_jobs[job_id]

            if job.status != BackupStatus.PENDING:
                logger.warning(f"مهمة الاستعادة ليست في حالة انتظار: {job_id}")
                return False

            logger.info(f"🔄 بدء تنفيذ الاستعادة: {job_id}")

            # تحديث حالة المهمة
            job.status = BackupStatus.RUNNING
            job.started_at = datetime.now()

            try:
                # استخراج النسخة الاحتياطية
                with zipfile.ZipFile(job.backup_file, 'r') as zipf:
                    # إنشاء مجلد الاستعادة إذا لم يكن موجوداً
                    os.makedirs(job.restore_path, exist_ok=True)

                    # استخراج جميع الملفات
                    zipf.extractall(job.restore_path)

                    # عد الملفات المستعادة
                    job.files_restored = len(zipf.namelist())

                # تحديث حالة المهمة
                job.status = BackupStatus.COMPLETED
                job.completed_at = datetime.now()

                # تشغيل callbacks
                for callback in self.restore_callbacks:
                    try:
                        callback(job)
                    except Exception as e:
                        logger.error(f"خطأ في callback الاستعادة: {str(e)}")

                logger.info(f"✅ اكتملت الاستعادة: {job_id}")
                logger.info(f"   - الملفات المستعادة: {job.files_restored}")

                return True

            except Exception as e:
                # فشل في الاستعادة
                job.status = BackupStatus.FAILED
                job.error_message = str(e)
                job.completed_at = datetime.now()

                logger.error(f"❌ فشلت الاستعادة: {job_id} - {str(e)}")
                return False

        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعادة: {str(e)}")
            return False

    @handle_errors(default_return=[], log_error=True)
    def list_available_backups(self) -> List[Dict[str, Any]]:
        """قائمة النسخ الاحتياطية المتاحة"""
        try:
            backups = []

            if not os.path.exists(self.backup_directory):
                return backups

            for filename in os.listdir(self.backup_directory):
                if filename.endswith('.zip'):
                    file_path = os.path.join(self.backup_directory, filename)

                    try:
                        stat = os.stat(file_path)

                        backup_info = {
                            'filename': filename,
                            'path': file_path,
                            'size_mb': stat.st_size / (1024 * 1024),
                            'created_at': datetime.fromtimestamp(stat.st_ctime),
                            'modified_at': datetime.fromtimestamp(stat.st_mtime),
                            'checksum': self._calculate_checksum(file_path)
                        }

                        # محاولة تحديد نوع النسخة الاحتياطية من اسم الملف
                        if 'full' in filename:
                            backup_info['type'] = BackupType.FULL.value
                        elif 'incremental' in filename:
                            backup_info['type'] = BackupType.INCREMENTAL.value
                        elif 'database' in filename:
                            backup_info['type'] = BackupType.DATABASE.value
                        else:
                            backup_info['type'] = 'unknown'

                        backups.append(backup_info)

                    except Exception as e:
                        logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية {filename}: {str(e)}")
                        continue

            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)

            return backups

        except Exception as e:
            logger.error(f"خطأ في قائمة النسخ الاحتياطية: {str(e)}")
            return []

    @handle_errors(default_return=False, log_error=True)
    def cleanup_old_backups(self) -> bool:
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            logger.info("🧹 بدء تنظيف النسخ الاحتياطية القديمة")

            cutoff_date = datetime.now() - timedelta(days=self.backup_retention_days)
            deleted_count = 0
            freed_space = 0

            for backup in self.list_available_backups():
                if backup['created_at'] < cutoff_date:
                    try:
                        file_size = os.path.getsize(backup['path'])
                        os.remove(backup['path'])
                        deleted_count += 1
                        freed_space += file_size
                        logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['filename']}")
                    except Exception as e:
                        logger.error(f"خطأ في حذف {backup['filename']}: {str(e)}")

            if deleted_count > 0:
                logger.info(f"✅ تم حذف {deleted_count} نسخة احتياطية قديمة، تحرير {freed_space / (1024*1024):.1f}MB")
            else:
                logger.info("لا توجد نسخ احتياطية قديمة للحذف")

            return True

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")
            return False

    @handle_errors(default_return=False, log_error=True)
    def setup_scheduled_backups(self) -> bool:
        """إعداد النسخ الاحتياطية المجدولة"""
        try:
            logger.info("📅 إعداد النسخ الاحتياطية المجدولة")

            # مسح الجدولة السابقة
            schedule.clear()

            # النسخة الاحتياطية اليومية الكاملة
            if self.scheduled_backups['daily_full']['enabled']:
                schedule.every().day.at(self.scheduled_backups['daily_full']['time']).do(
                    self._scheduled_backup_worker, BackupType.FULL
                )
                logger.info(f"تم جدولة النسخة الاحتياطية اليومية الكاملة في {self.scheduled_backups['daily_full']['time']}")

            # النسخة الاحتياطية التزايدية كل ساعة
            if self.scheduled_backups['hourly_incremental']['enabled']:
                schedule.every().hour.do(
                    self._scheduled_backup_worker, BackupType.INCREMENTAL
                )
                logger.info("تم جدولة النسخة الاحتياطية التزايدية كل ساعة")

            # النسخة الاحتياطية الأسبوعية لقاعدة البيانات
            if self.scheduled_backups['weekly_database']['enabled']:
                schedule.every().sunday.at("03:00").do(
                    self._scheduled_backup_worker, BackupType.DATABASE
                )
                logger.info("تم جدولة النسخة الاحتياطية الأسبوعية لقاعدة البيانات")

            # بدء خيط الجدولة
            def scheduler_worker():
                while self.auto_backup_enabled:
                    try:
                        schedule.run_pending()
                        time.sleep(60)  # فحص كل دقيقة
                    except Exception as e:
                        logger.error(f"خطأ في جدولة النسخ الاحتياطية: {str(e)}")
                        time.sleep(300)  # انتظار 5 دقائق عند الخطأ

            scheduler_thread = threading.Thread(target=scheduler_worker, daemon=True)
            scheduler_thread.start()

            return True

        except Exception as e:
            logger.error(f"خطأ في إعداد النسخ الاحتياطية المجدولة: {str(e)}")
            return False

    def _scheduled_backup_worker(self, backup_type: BackupType):
        """عامل النسخ الاحتياطية المجدولة"""
        try:
            logger.info(f"🕐 بدء النسخة الاحتياطية المجدولة: {backup_type.value}")

            # تنظيف النسخ القديمة أولاً
            self.cleanup_old_backups()

            # إنشاء النسخة الاحتياطية
            job_id = self.create_backup_async(backup_type)

            if job_id:
                logger.info(f"تم بدء النسخة الاحتياطية المجدولة: {job_id}")
            else:
                logger.error("فشل في إنشاء النسخة الاحتياطية المجدولة")

        except Exception as e:
            logger.error(f"خطأ في النسخة الاحتياطية المجدولة: {str(e)}")

    def add_backup_callback(self, callback: Callable):
        """إضافة callback للنسخ الاحتياطي"""
        self.backup_callbacks.append(callback)

    def add_restore_callback(self, callback: Callable):
        """إضافة callback للاستعادة"""
        self.restore_callbacks.append(callback)

    @handle_errors(default_return={}, log_error=True)
    def get_backup_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطي"""
        try:
            available_backups = self.list_available_backups()

            stats = {
                'backup_stats': self.backup_stats.copy(),
                'available_backups': len(available_backups),
                'total_backup_size_mb': sum(backup['size_mb'] for backup in available_backups),
                'oldest_backup': available_backups[-1]['created_at'].isoformat() if available_backups else None,
                'newest_backup': available_backups[0]['created_at'].isoformat() if available_backups else None,
                'active_backup_jobs': len(self.active_jobs),
                'pending_backup_jobs': len([job for job in self.backup_jobs.values() if job.status == BackupStatus.PENDING]),
                'running_backup_jobs': len([job for job in self.backup_jobs.values() if job.status == BackupStatus.RUNNING]),
                'settings': {
                    'auto_backup_enabled': self.auto_backup_enabled,
                    'backup_retention_days': self.backup_retention_days,
                    'max_backup_size_gb': self.max_backup_size_gb,
                    'compression_enabled': self.compression_enabled,
                    'backup_directory': self.backup_directory
                }
            }

            # تحويل التواريخ إلى strings
            if stats['backup_stats'].get('last_backup'):
                stats['backup_stats']['last_backup'] = stats['backup_stats']['last_backup'].isoformat()
            if stats['backup_stats'].get('last_successful_backup'):
                stats['backup_stats']['last_successful_backup'] = stats['backup_stats']['last_successful_backup'].isoformat()

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النسخ الاحتياطي: {str(e)}")
            return {}

# إنشاء مثيل عام لنظام النسخ الاحتياطي
backup_system = BackupSystem()
