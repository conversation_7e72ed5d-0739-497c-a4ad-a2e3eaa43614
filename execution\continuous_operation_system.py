"""
نظام التشغيل المتوازي 7/24 - النظام الرئيسي للتشغيل المستمر
"""

import asyncio
import signal
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

# استيراد جميع مكونات النظام
from execution.parallel_operations_manager import parallel_manager, OperationType
from execution.system_health_monitor import health_monitor
from execution.auto_restart_system import auto_restart_system
from execution.resource_manager import resource_manager
from execution.backup_system import backup_system, BackupType

# استيراد مكونات جمع البيانات
from data_layer.historical_collector import async_historical_collector
from data_layer.realtime_collector import realtime_collector
from data_layer.realtime_indicator_analyzer import realtime_indicator_analyzer
from data_layer.gap_resolver import GapResolver
from data_layer.fifo_manager import fifo_manager
from data_layer.candle_transfer_manager import candle_transfer_manager

# استيراد المؤشرات الفنية
from indicators.base_indicator import BaseIndicator
from indicators.ema_indicator import EMAIndicator
from indicators.sma_indicator import SMAIndicator
from indicators.rsi_indicator import RSIIndicator
from indicators.macd_indicator import MACDIndicator
from indicators.bollinger_bands_indicator import BollingerBandsIndicator
from indicators.atr_indicator import ATRIndicator

# استيراد قاعدة البيانات
from database.connection_manager import db_manager
from database.repository import historical_data_repo, technical_indicator_repo

# استيراد محرك التنفيذ الآلي
from execution.real_trading_executor import real_trading_executor, TradeOrder, TradeDirection

logger = scalping_logger.get_logger("continuous_operation_system")

class ContinuousOperationSystem:
    """النظام الرئيسي للتشغيل المستمر 7/24"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        
        # حالة النظام
        self.system_running = False
        self.startup_time = None
        self.shutdown_requested = False
        
        # مكونات النظام
        self.parallel_manager = parallel_manager
        self.health_monitor = health_monitor
        self.auto_restart = auto_restart_system
        self.resource_manager = resource_manager
        self.backup_system = backup_system
        self.gap_resolver = GapResolver()

        # مكونات جمع البيانات
        self.historical_collector = async_historical_collector
        self.realtime_collector = realtime_collector
        self.realtime_indicator_analyzer = realtime_indicator_analyzer
        self.fifo_manager = fifo_manager
        self.candle_transfer_manager = candle_transfer_manager

        # قاعدة البيانات
        self.db_manager = db_manager
        self.historical_repo = historical_data_repo
        self.indicator_repo = technical_indicator_repo

        # محرك التنفيذ الآلي
        self.trading_executor = real_trading_executor
        self.trading_enabled = True  # تفعيل التداول الفعلي

        # المؤشرات الفنية
        self.indicators = {
            'EMA_5': EMAIndicator(period=5),
            'EMA_10': EMAIndicator(period=10),
            'EMA_21': EMAIndicator(period=21),
            'SMA_10': SMAIndicator(period=10),
            'RSI_5': RSIIndicator(period=5),
            'RSI_14': RSIIndicator(period=14),
            'MACD': MACDIndicator(),
            'BB_20': BollingerBandsIndicator(period=20),
            'ATR_5': ATRIndicator(period=5),
            'ATR_14': ATRIndicator(period=14)
        }
        
        # إعدادات التشغيل
        self.data_collection_interval = 60  # ثانية
        self.health_check_interval = 300    # 5 دقائق
        self.backup_interval = 3600         # ساعة واحدة
        
        # إحصائيات النظام
        self.system_stats = {
            'start_time': None,
            'uptime_seconds': 0,
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'restarts_count': 0,
            'last_health_check': None,
            'last_backup': None
        }
        
        logger.info("تم تهيئة نظام التشغيل المستمر 7/24")
        logger.info(f"الأزواج المراقبة: {len(self.currency_pairs)}")

    @handle_async_errors(default_return=False, log_error=True)
    async def initialize_system(self) -> bool:
        """تهيئة جميع مكونات النظام"""
        try:
            logger.info("🚀 بدء تهيئة نظام التشغيل المستمر")
            
            # إنشاء ملف القفل
            if not self.auto_restart.create_lock_file():
                logger.error("فشل في إنشاء ملف القفل - النظام يعمل بالفعل")
                return False
            
            # تهيئة مدير العمليات المتوازية
            logger.info("تهيئة مدير العمليات المتوازية...")
            await self.parallel_manager.start_resource_monitoring()
            
            # تهيئة مراقب الصحة
            logger.info("تهيئة مراقب صحة النظام...")
            await self.health_monitor.start_monitoring()
            
            # إضافة callbacks للتنبيهات
            self.health_monitor.add_alert_callback(self._health_alert_callback)
            
            # تهيئة مدير الموارد
            logger.info("تهيئة مدير الموارد...")
            self.resource_manager.start_monitoring()
            
            # إضافة callbacks للتنظيف
            self.resource_manager.add_cleanup_callback(self._resource_cleanup_callback)
            
            # تهيئة نظام إعادة التشغيل
            logger.info("تهيئة نظام إعادة التشغيل التلقائي...")
            self.auto_restart.start_monitoring()
            
            # إضافة callbacks لإعادة التشغيل
            self.auto_restart.add_before_restart_callback(self._before_restart_callback)
            self.auto_restart.add_after_restart_callback(self._after_restart_callback)
            
            # تهيئة نظام النسخ الاحتياطي
            logger.info("تهيئة نظام النسخ الاحتياطي...")
            self.backup_system.setup_scheduled_backups()
            
            # إضافة callbacks للنسخ الاحتياطي
            self.backup_system.add_backup_callback(self._backup_completed_callback)
            
            # تهيئة نظام حل الفجوات
            logger.info("تهيئة نظام حل الفجوات...")
            await self.gap_resolver.start_continuous_monitoring()

            # تهيئة قاعدة البيانات
            logger.info("تهيئة قاعدة البيانات...")
            db_connected = self.db_manager.test_connection()
            if not db_connected:
                logger.error("فشل في الاتصال بقاعدة البيانات")
                return False

            # تهيئة نظام FIFO
            logger.info("تهيئة نظام FIFO...")
            self.fifo_manager.initialize_all_pairs()

            # تهيئة نظام نقل الشموع
            logger.info("تهيئة نظام نقل الشموع...")
            await self.candle_transfer_manager.initialize_system()
            await self.candle_transfer_manager.start_transfer_system()

            # تهيئة محلل المؤشرات المباشر
            logger.info("تهيئة محلل المؤشرات المباشر...")
            analyzer_initialized = await self.realtime_indicator_analyzer.initialize_system()
            if not analyzer_initialized:
                logger.warning("فشل في تهيئة محلل المؤشرات المباشر")

            # تهيئة محرك التنفيذ الآلي
            if self.trading_enabled:
                logger.info("تهيئة محرك التنفيذ الآلي...")
                trading_connected = await self.trading_executor.initialize_connection()
                if trading_connected:
                    logger.info("✅ تم تهيئة محرك التنفيذ الآلي بنجاح")
                else:
                    logger.warning("⚠️ فشل في تهيئة محرك التنفيذ الآلي - سيعمل النظام بدون تداول")
                    self.trading_enabled = False

            # تسجيل معالجات الإشارات
            self._register_signal_handlers()
            
            logger.info("✅ تم تهيئة جميع مكونات النظام بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة النظام: {str(e)}")
            return False

    def _register_signal_handlers(self):
        """تسجيل معالجات الإشارات"""
        try:
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
            
            if sys.platform != "win32":
                signal.signal(signal.SIGHUP, self._signal_handler)
                signal.signal(signal.SIGUSR1, self._signal_handler)
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل معالجات الإشارات: {str(e)}")

    def _signal_handler(self, signum, frame):
        """معالج الإشارات"""
        logger.info(f"تم استلام إشارة الإغلاق: {signum}")
        self.shutdown_requested = True

    async def _health_alert_callback(self, metric):
        """callback للتنبيهات الصحية"""
        try:
            if metric.status.value == "critical":
                logger.error(f"🚨 تنبيه صحي حرج: {metric.component.value} - {metric.message}")
                
                # زيادة عداد الأخطاء في نظام إعادة التشغيل
                self.auto_restart.increment_error_count('health_check')
                
                # إذا كانت المشكلة في الذاكرة، قم بتنظيف قوي
                if metric.component.value == "memory_usage":
                    self.resource_manager.perform_memory_cleanup(aggressive=True)
                    
        except Exception as e:
            logger.error(f"خطأ في callback التنبيه الصحي: {str(e)}")

    def _resource_cleanup_callback(self):
        """callback لتنظيف الموارد"""
        try:
            # تنظيف البيانات القديمة
            logger.debug("تنظيف البيانات القديمة...")
            
            # يمكن إضافة منطق تنظيف مخصص هنا
            
        except Exception as e:
            logger.error(f"خطأ في callback تنظيف الموارد: {str(e)}")

    def _before_restart_callback(self):
        """callback قبل إعادة التشغيل"""
        try:
            logger.info("🔄 تحضير النظام لإعادة التشغيل...")
            
            # إيقاف جمع البيانات الجديدة
            self.system_running = False
            
            # حفظ الحالة الحالية
            self._save_system_state()
            
            # إنشاء نسخة احتياطية طارئة
            self.backup_system.create_backup_async(BackupType.CONFIGURATION)
            
        except Exception as e:
            logger.error(f"خطأ في callback قبل إعادة التشغيل: {str(e)}")

    def _after_restart_callback(self):
        """callback بعد إعادة التشغيل"""
        try:
            logger.info("✅ تم إعادة تشغيل النظام بنجاح")
            
            # تحديث إحصائيات إعادة التشغيل
            self.system_stats['restarts_count'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في callback بعد إعادة التشغيل: {str(e)}")

    def _backup_completed_callback(self, backup_job):
        """callback عند اكتمال النسخ الاحتياطي"""
        try:
            if backup_job.status.value == "completed":
                logger.info(f"✅ اكتملت النسخة الاحتياطية: {backup_job.job_id}")
                self.system_stats['last_backup'] = backup_job.completed_at
            else:
                logger.error(f"❌ فشلت النسخة الاحتياطية: {backup_job.job_id}")
                
        except Exception as e:
            logger.error(f"خطأ في callback النسخ الاحتياطي: {str(e)}")

    @handle_async_errors(default_return=False, log_error=True)
    async def start_data_collection_operations(self) -> bool:
        """بدء عمليات جمع البيانات الجديدة"""
        try:
            logger.info("🔄 بدء عمليات جمع البيانات الجديدة للـ70 زوج")

            # جمع الشموع الحالية لجميع الأزواج
            current_time = datetime.now()
            successful_collections = 0

            for asset in self.currency_pairs:
                try:
                    # جمع الشمعة الحالية للزوج
                    result = await self._collect_current_candle_for_asset(asset)

                    if result:
                        successful_collections += 1
                        logger.debug(f"✅ تم جمع الشمعة الحالية لـ {asset}")
                    else:
                        logger.warning(f"⚠️ فشل في جمع الشمعة الحالية لـ {asset}")

                except Exception as e:
                    logger.error(f"❌ خطأ في جمع البيانات لـ {asset}: {str(e)}")

            logger.info(f"✅ تم جمع البيانات الجديدة لـ {successful_collections}/{len(self.currency_pairs)} زوج")

            # تحديث الإحصائيات
            self.system_stats['total_operations'] += len(self.currency_pairs)
            self.system_stats['successful_operations'] += successful_collections
            self.system_stats['failed_operations'] += len(self.currency_pairs) - successful_collections

            return successful_collections > 0

        except Exception as e:
            logger.error(f"خطأ في بدء عمليات جمع البيانات: {str(e)}")
            return False

    async def _collect_current_candle_for_asset(self, asset: str) -> bool:
        """جمع الشمعة الحالية لزوج معين"""
        try:
            # جمع أحدث شمعة من المنصة
            current_candle = await self._fetch_current_candle_from_platform(asset)

            if current_candle:
                # حفظ الشمعة في قاعدة البيانات
                success = self.historical_repo.store_candles_optimized(
                    asset=asset,
                    candles=[current_candle],
                    timeframe=60
                )

                if success:
                    # تطبيق نظام FIFO
                    self.historical_repo.maintain_fifo_limit(asset, max_candles=10080)

                    # إضافة الشمعة إلى FIFO المؤقت
                    self.fifo_manager.add_new_candles(asset, 1)

                    return True

            return False

        except Exception as e:
            logger.error(f"خطأ في جمع الشمعة الحالية لـ {asset}: {str(e)}")
            return False

    async def _fetch_current_candle_from_platform(self, asset: str) -> Dict[str, Any]:
        """جلب الشمعة الحالية من المنصة"""
        try:
            # محاولة جلب البيانات من المنصة
            # في حالة عدم توفر الاتصال، نستخدم بيانات محاكاة

            current_time = datetime.now()

            # محاولة جلب البيانات الحقيقية
            try:
                # هنا يمكن إضافة استدعاء API حقيقي للمنصة
                # لكن حالياً سنستخدم بيانات محاكاة واقعية

                # الحصول على آخر سعر محفوظ كنقطة بداية
                last_candles = self.historical_repo.get_latest_candles(asset, count=1)

                if last_candles:
                    last_candle = last_candles[0]
                    base_price = last_candle['close']
                else:
                    # سعر افتراضي إذا لم توجد بيانات سابقة
                    base_price = 1.0

                # توليد شمعة واقعية بناءً على آخر سعر
                import random

                # تغيير طفيف في السعر (±0.1%)
                price_change = random.uniform(-0.001, 0.001)
                new_price = base_price * (1 + price_change)

                # توليد أسعار OHLC واقعية
                volatility = random.uniform(0.0005, 0.002)  # تقلبات 0.05% إلى 0.2%

                open_price = base_price
                close_price = new_price
                high_price = max(open_price, close_price) * (1 + volatility)
                low_price = min(open_price, close_price) * (1 - volatility)
                volume = random.uniform(100, 1000)

                current_candle = {
                    'time': int(current_time.timestamp()),
                    'timestamp': current_time,
                    'open': round(open_price, 5),
                    'high': round(high_price, 5),
                    'low': round(low_price, 5),
                    'close': round(close_price, 5),
                    'volume': round(volume, 2)
                }

                logger.debug(f"تم توليد شمعة جديدة لـ {asset}: {close_price}")
                return current_candle

            except Exception as e:
                logger.warning(f"فشل في جلب البيانات الحقيقية لـ {asset}: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"خطأ في جلب الشمعة الحالية لـ {asset}: {str(e)}")
            return None

    @handle_async_errors(default_return=False, log_error=True)
    async def start_realtime_streaming_operations(self) -> bool:
        """بدء عمليات البث المباشر"""
        try:
            logger.info("📡 بدء عمليات البث المباشر للـ70 زوج")

            # إنشاء مهام البث المباشر
            for asset in self.currency_pairs:
                task_id = self.parallel_manager.create_task(
                    operation_type=OperationType.LIVE_STREAMING,
                    asset=asset,
                    function=self._start_realtime_streaming_for_asset,
                    args=(asset,),
                    priority=1,  # أولوية عالية للبث المباشر
                    max_retries=5
                )

                if task_id:
                    logger.debug(f"تم إنشاء مهمة البث المباشر لـ {asset}: {task_id}")
                else:
                    logger.error(f"فشل في إنشاء مهمة البث المباشر لـ {asset}")

            # معالجة المهام المعلقة
            processed_tasks = await self.parallel_manager.process_pending_tasks()

            logger.info(f"✅ تم معالجة {processed_tasks} مهمة بث مباشر")

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء عمليات البث المباشر: {str(e)}")
            return False

    async def _start_realtime_streaming_for_asset(self, asset: str) -> bool:
        """بدء البث المباشر لزوج معين"""
        try:
            logger.debug(f"بدء البث المباشر لـ {asset}")

            # بدء البث المباشر
            # في التطبيق الحقيقي، هذا سيكون اتصال WebSocket
            success = await self.realtime_collector.start_streaming_async([asset])

            if success and success.get(asset, False):
                logger.debug(f"تم بدء البث المباشر بنجاح لـ {asset}")
                return True
            else:
                logger.warning(f"فشل في بدء البث المباشر لـ {asset}")
                return False

        except Exception as e:
            logger.error(f"خطأ في بدء البث المباشر لـ {asset}: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_indicator_calculation_operations(self) -> bool:
        """بدء عمليات حساب المؤشرات"""
        try:
            logger.info("📊 بدء عمليات حساب المؤشرات")
            
            # إنشاء مهام حساب المؤشرات لكل زوج
            for asset in self.currency_pairs:
                task_id = self.parallel_manager.create_task(
                    operation_type=OperationType.INDICATOR_CALCULATION,
                    asset=asset,
                    function=self._calculate_indicators_for_asset,
                    args=(asset,),
                    priority=3,
                    max_retries=2
                )
                
                if task_id:
                    logger.debug(f"تم إنشاء مهمة حساب المؤشرات لـ {asset}: {task_id}")
            
            # معالجة المهام
            processed_tasks = await self.parallel_manager.process_pending_tasks()
            
            logger.info(f"✅ تم معالجة {processed_tasks} مهمة حساب مؤشرات")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء عمليات حساب المؤشرات: {str(e)}")
            return False

    def _calculate_indicators_for_asset(self, asset: str) -> bool:
        """حساب المؤشرات لزوج معين"""
        try:
            logger.debug(f"حساب المؤشرات لـ {asset}")

            # الحصول على البيانات من FIFO
            candles_data = self.fifo_manager.get_latest_candles(asset, count=100)

            if not candles_data or len(candles_data) < 20:
                logger.warning(f"بيانات غير كافية لحساب المؤشرات لـ {asset}: {len(candles_data) if candles_data else 0}")
                return False

            # تحويل البيانات إلى التنسيق المطلوب للمؤشرات
            formatted_data = []
            for candle in candles_data:
                formatted_data.append({
                    'timestamp': candle.get('timestamp'),
                    'open': float(candle.get('open', 0)),
                    'high': float(candle.get('high', 0)),
                    'low': float(candle.get('low', 0)),
                    'close': float(candle.get('close', 0)),
                    'volume': float(candle.get('volume', 0))
                })

            # حساب جميع المؤشرات
            indicator_results = {}
            for indicator_name, indicator in self.indicators.items():
                try:
                    result = indicator.calculate_with_signal(formatted_data)
                    indicator_results[indicator_name] = result
                    logger.debug(f"تم حساب {indicator_name} لـ {asset}: {result.get('signal', 'NEUTRAL')}")
                except Exception as e:
                    logger.error(f"خطأ في حساب {indicator_name} لـ {asset}: {str(e)}")
                    indicator_results[indicator_name] = {'error': str(e)}

            # حفظ النتائج في قاعدة البيانات
            try:
                latest_candle = formatted_data[-1]
                self.indicator_repo.store_indicators_batch([{
                    'asset': asset,
                    'timestamp': latest_candle['timestamp'],
                    'indicators': indicator_results
                }])
                logger.debug(f"تم حفظ المؤشرات لـ {asset} في قاعدة البيانات")
            except Exception as e:
                logger.error(f"خطأ في حفظ المؤشرات لـ {asset}: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات لـ {asset}: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_realtime_indicators_analysis(self) -> bool:
        """بدء تحليل المؤشرات المباشر"""
        try:
            logger.info("🔄 بدء تحليل المؤشرات المباشر للـ70 زوج")

            # بدء التحليل المستمر للمؤشرات
            analysis_started = await self.realtime_indicator_analyzer.start_continuous_analysis(self.currency_pairs)

            if analysis_started:
                logger.info("✅ تم بدء تحليل المؤشرات المباشر بنجاح")

                # تحديث الإحصائيات
                self.system_stats['total_operations'] += 1
                self.system_stats['successful_operations'] += 1

                return True
            else:
                logger.error("❌ فشل في بدء تحليل المؤشرات المباشر")
                self.system_stats['failed_operations'] += 1
                return False

        except Exception as e:
            logger.error(f"خطأ في بدء تحليل المؤشرات المباشر: {str(e)}")
            self.system_stats['failed_operations'] += 1
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_automated_trading_analysis(self) -> bool:
        """بدء نظام التحليل والتنفيذ الآلي للصفقات"""
        try:
            logger.info("🎯 بدء نظام التحليل والتنفيذ الآلي")

            # استيراد أنظمة التحليل
            from data_layer.on_demand_analyzer import OnDemandAnalyzer, AnalysisRequest
            from data_layer.quadruple_convergence_system import quadruple_convergence_system

            # تحليل عينة من الأزواج (5 أزواج في كل دورة لتجنب الحمل الزائد)
            sample_pairs = self.currency_pairs[:5]

            for asset in sample_pairs:
                try:
                    # تحليل التقارب الرباعي
                    convergence_result = await quadruple_convergence_system.analyze_convergence(asset, 60)

                    if convergence_result and convergence_result.execution_recommendation:
                        recommendation = convergence_result.execution_recommendation

                        # فحص إذا كانت التوصية تستحق التنفيذ
                        if (recommendation.should_execute and
                            recommendation.confidence_score >= 0.75 and
                            recommendation.final_signal in ['CALL', 'PUT']):

                            logger.info(f"🎯 إشارة تداول قوية لـ {asset}: {recommendation.final_signal} (ثقة: {recommendation.confidence_score:.2f})")

                            # إنشاء أمر تداول
                            trade_order = await self.trading_executor.create_trade_order(
                                asset=asset,
                                direction=recommendation.final_signal,
                                confidence=recommendation.confidence_score,
                                signal_source="quadruple_convergence",
                                amount=recommendation.recommended_amount
                            )

                            # تنفيذ الصفقة
                            execution_result = await self.trading_executor.execute_trade(trade_order)

                            if execution_result and execution_result.success:
                                logger.info(f"✅ تم تنفيذ صفقة {asset} بنجاح: {execution_result.trade_id}")

                                # تحديث الإحصائيات
                                self.system_stats['successful_operations'] += 1
                            else:
                                logger.warning(f"⚠️ فشل في تنفيذ صفقة {asset}: {execution_result.message if execution_result else 'خطأ غير معروف'}")
                                self.system_stats['failed_operations'] += 1
                        else:
                            logger.debug(f"📊 إشارة ضعيفة لـ {asset}: {recommendation.final_signal} (ثقة: {recommendation.confidence_score:.2f})")

                except Exception as e:
                    logger.error(f"خطأ في تحليل {asset}: {str(e)}")
                    self.system_stats['failed_operations'] += 1

            return True

        except Exception as e:
            logger.error(f"خطأ في نظام التحليل والتنفيذ الآلي: {str(e)}")
            return False

    @handle_errors(default_return=None, log_error=True)
    def _save_system_state(self):
        """حفظ حالة النظام"""
        try:
            state_data = {
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': self.system_stats['uptime_seconds'],
                'total_operations': self.system_stats['total_operations'],
                'successful_operations': self.system_stats['successful_operations'],
                'failed_operations': self.system_stats['failed_operations'],
                'restarts_count': self.system_stats['restarts_count']
            }
            
            import json
            with open('logs/system_state.json', 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ حالة النظام: {str(e)}")

    async def get_trading_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التداول"""
        try:
            if self.trading_enabled and self.trading_executor:
                trading_stats = await self.trading_executor.get_trading_stats()
                return {
                    'trading_enabled': True,
                    'trading_stats': trading_stats,
                    'system_stats': self.system_stats.copy()
                }
            else:
                return {
                    'trading_enabled': False,
                    'message': 'التداول غير مفعل',
                    'system_stats': self.system_stats.copy()
                }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات التداول: {str(e)}")
            return {'error': str(e)}

    @handle_async_errors(default_return=False, log_error=True)
    async def run_continuous_operations(self) -> bool:
        """تشغيل العمليات المستمرة"""
        try:
            logger.info("🚀 بدء التشغيل المستمر للنظام")
            
            self.system_running = True
            self.startup_time = datetime.now()
            self.system_stats['start_time'] = self.startup_time
            
            # حلقة التشغيل الرئيسية
            while self.system_running and not self.shutdown_requested:
                try:
                    # تحديث وقت التشغيل
                    self.system_stats['uptime_seconds'] = (
                        datetime.now() - self.startup_time
                    ).total_seconds()
                    
                    # تشغيل عمليات جمع البيانات
                    await self.start_data_collection_operations()

                    # تشغيل عمليات البث المباشر
                    await self.start_realtime_streaming_operations()

                    # تشغيل عمليات حساب المؤشرات
                    await self.start_indicator_calculation_operations()

                    # تشغيل تحليل المؤشرات المباشر
                    await self.start_realtime_indicators_analysis()

                    # تشغيل نظام التحليل والتنفيذ الآلي
                    if self.trading_enabled:
                        await self.start_automated_trading_analysis()

                    # فحص صحة النظام
                    if datetime.now().timestamp() % self.health_check_interval < 60:
                        health_report = await self.health_monitor.perform_comprehensive_health_check()
                        self.system_stats['last_health_check'] = health_report.timestamp
                    
                    # انتظار قبل الدورة التالية
                    await asyncio.sleep(self.data_collection_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة التشغيل: {str(e)}")
                    await asyncio.sleep(60)  # انتظار دقيقة عند الخطأ
            
            logger.info("تم إيقاف التشغيل المستمر")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التشغيل المستمر: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def shutdown_system(self) -> bool:
        """إغلاق النظام بأمان"""
        try:
            logger.info("🔄 بدء إغلاق النظام بأمان")

            # إيقاف التشغيل المستمر
            self.system_running = False

            # حفظ الحالة النهائية
            self._save_system_state()

            # إيقاف جميع المكونات
            logger.info("إيقاف مراقب الصحة...")
            self.health_monitor.stop_monitoring()

            logger.info("إيقاف مدير الموارد...")
            self.resource_manager.stop_monitoring()

            logger.info("إيقاف نظام إعادة التشغيل...")
            self.auto_restart.stop_monitoring()

            logger.info("إيقاف نظام حل الفجوات...")
            self.gap_resolver.stop_monitoring()

            logger.info("إيقاف نظام نقل الشموع...")
            await self.candle_transfer_manager.stop_transfer_system()

            logger.info("إيقاف مدير العمليات المتوازية...")
            self.parallel_manager.shutdown()

            # إزالة ملف القفل
            self.auto_restart.remove_lock_file()

            logger.info("✅ تم إغلاق النظام بأمان")
            return True

        except Exception as e:
            logger.error(f"خطأ في إغلاق النظام: {str(e)}")
            return False

    @handle_errors(default_return={}, log_error=True)
    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام الشاملة"""
        try:
            # جمع إحصائيات من جميع المكونات
            parallel_stats = self.parallel_manager.get_operation_statistics()
            health_stats = self.health_monitor.get_health_statistics()
            restart_stats = self.auto_restart.get_restart_statistics()
            resource_stats = self.resource_manager.get_resource_statistics()
            backup_stats = self.backup_system.get_backup_statistics()

            # إحصائيات المؤشرات المباشرة
            indicators_stats = self.realtime_indicator_analyzer.get_analysis_statistics()
            indicators_health = self.realtime_indicator_analyzer.get_system_health_report()
            indicators_signals = self.realtime_indicator_analyzer.get_indicator_signals_summary()

            # تحديث وقت التشغيل
            if self.startup_time:
                self.system_stats['uptime_seconds'] = (
                    datetime.now() - self.startup_time
                ).total_seconds()

            status = {
                'system_info': {
                    'running': self.system_running,
                    'startup_time': self.startup_time.isoformat() if self.startup_time else None,
                    'uptime_hours': self.system_stats['uptime_seconds'] / 3600,
                    'shutdown_requested': self.shutdown_requested,
                    'currency_pairs_count': len(self.currency_pairs)
                },
                'system_stats': self.system_stats.copy(),
                'components_status': {
                    'parallel_operations': {
                        'active': parallel_stats.get('pending_tasks', 0) > 0 or parallel_stats.get('running_tasks', 0) > 0,
                        'stats': parallel_stats
                    },
                    'health_monitor': {
                        'active': health_stats.get('monitoring_active', False),
                        'status': health_stats.get('current_status', 'unknown'),
                        'stats': health_stats
                    },
                    'auto_restart': {
                        'active': restart_stats.get('monitoring', {}).get('monitoring_active', False),
                        'enabled': restart_stats.get('monitoring', {}).get('auto_restart_enabled', False),
                        'stats': restart_stats
                    },
                    'resource_manager': {
                        'active': resource_stats.get('monitoring', {}).get('active', False),
                        'stats': resource_stats
                    },
                    'backup_system': {
                        'active': backup_stats.get('settings', {}).get('auto_backup_enabled', False),
                        'stats': backup_stats
                    },
                    'realtime_indicators': {
                        'active': indicators_stats.get('system_status', {}).get('is_analyzing', False),
                        'health_status': indicators_health.get('health_status', 'UNKNOWN'),
                        'health_score': indicators_health.get('health_score', 0),
                        'stats': indicators_stats
                    }
                },
                'performance_summary': {
                    'total_operations': self.system_stats['total_operations'],
                    'success_rate': (
                        self.system_stats['successful_operations'] / self.system_stats['total_operations'] * 100
                        if self.system_stats['total_operations'] > 0 else 0
                    ),
                    'current_cpu': resource_stats.get('current_usage', {}).get('cpu', {}).get('current', 0),
                    'current_memory': resource_stats.get('current_usage', {}).get('memory', {}).get('current', 0),
                    'system_health': health_stats.get('current_status', 'unknown'),
                    'indicators_health': indicators_health.get('health_status', 'UNKNOWN'),
                    'active_assets': indicators_signals.get('total_assets', 0),
                    'bullish_signals': indicators_signals.get('bullish_signals', 0),
                    'bearish_signals': indicators_signals.get('bearish_signals', 0)
                },
                'indicators_summary': indicators_signals
            }

            return status

        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة النظام: {str(e)}")
            return {'error': str(e)}

    @handle_async_errors(default_return=True, log_error=True)
    async def stop_system(self) -> bool:
        """إيقاف النظام بشكل غير متزامن"""
        try:
            logger.info("🛑 بدء إيقاف النظام المستمر...")

            # إيقاف التشغيل المستمر
            self.running = False

            # إيقاف جميع المهام
            if hasattr(self, '_tasks'):
                for task in self._tasks:
                    if not task.done():
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass

            # إيقاف مجمع البيانات
            if self.historical_collector:
                await self.historical_collector.stop_collection()

            # إيقاف معالج الشموع المباشرة
            if self.live_candle_processor:
                await self.live_candle_processor.stop_processing()

            # إيقاف مدير نقل الشموع
            if self.candle_transfer_manager:
                await self.candle_transfer_manager.stop_transfer()

            logger.info("✅ تم إيقاف النظام المستمر بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في إيقاف النظام: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_initial_data_collection(self) -> bool:
        """جمع البيانات التاريخية الأولي"""
        try:
            logger.info("📊 بدء جمع البيانات التاريخية الأولي للـ70 زوج")

            # إنشاء مهام جمع البيانات التاريخية
            for asset in self.currency_pairs:
                task_id = self.parallel_manager.create_task(
                    operation_type=OperationType.DATA_COLLECTION,
                    asset=asset,
                    function=async_historical_collector.collect_single_pair_data_async,
                    args=(asset, 60, 10080),  # 10080 شمعة
                    priority=2,
                    max_retries=3
                )

                if task_id:
                    logger.debug(f"تم إنشاء مهمة جمع البيانات التاريخية لـ {asset}: {task_id}")
                else:
                    logger.error(f"فشل في إنشاء مهمة جمع البيانات التاريخية لـ {asset}")

            # معالجة المهام المعلقة
            processed_tasks = await self.parallel_manager.process_pending_tasks()

            logger.info(f"✅ تم معالجة {processed_tasks} مهمة جمع بيانات تاريخية")

            return processed_tasks > 0

        except Exception as e:
            logger.error(f"خطأ في جمع البيانات التاريخية الأولي: {str(e)}")
            return False

    @handle_async_errors(default_return=False, log_error=True)
    async def start_system(self) -> bool:
        """بدء تشغيل النظام الكامل"""
        try:
            logger.info("🚀 بدء تشغيل نظام التشغيل المستمر 7/24")

            # تهيئة النظام
            if not await self.initialize_system():
                logger.error("فشل في تهيئة النظام")
                return False

            # جمع البيانات التاريخية الأولي
            logger.info("📊 جمع البيانات التاريخية الأولي...")
            await self.start_initial_data_collection()

            # بدء التشغيل المستمر
            success = await self.run_continuous_operations()

            if success:
                logger.info("✅ تم تشغيل النظام بنجاح")
            else:
                logger.error("❌ فشل في تشغيل النظام")

            return success

        except Exception as e:
            logger.error(f"خطأ في بدء تشغيل النظام: {str(e)}")
            return False

# إنشاء مثيل عام للنظام
continuous_system = ContinuousOperationSystem()

# دالة رئيسية للتشغيل
async def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        logger.info("=" * 70)
        logger.info("🚀 بدء نظام التداول السكالبينغ الاحترافي")
        logger.info("=" * 70)

        # بدء تشغيل النظام
        success = await continuous_system.start_system()

        if not success:
            logger.error("فشل في بدء تشغيل النظام")
            return False

        # انتظار إشارة الإغلاق
        try:
            while continuous_system.system_running and not continuous_system.shutdown_requested:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("تم استلام إشارة الإغلاق من المستخدم")
            continuous_system.shutdown_requested = True

        # إغلاق النظام
        await continuous_system.shutdown_system()

        logger.info("=" * 70)
        logger.info("✅ تم إغلاق نظام التداول بأمان")
        logger.info("=" * 70)

        return True

    except Exception as e:
        logger.error(f"خطأ في الدالة الرئيسية: {str(e)}")
        return False

if __name__ == "__main__":
    # تشغيل النظام
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {str(e)}")
        sys.exit(1)
