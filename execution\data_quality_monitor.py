"""
نظام مراقبة جودة البيانات
Data Quality Monitor System
"""

import asyncio
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics
import json

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("data_quality_monitor")

class DataQualityIssue(Enum):
    """أنواع مشاكل جودة البيانات"""
    MISSING_DATA = "missing_data"
    DUPLICATE_DATA = "duplicate_data"
    INVALID_VALUES = "invalid_values"
    TIMESTAMP_GAPS = "timestamp_gaps"
    PRICE_ANOMALIES = "price_anomalies"
    VOLUME_ANOMALIES = "volume_anomalies"
    STALE_DATA = "stale_data"
    INCONSISTENT_DATA = "inconsistent_data"
    INCOMPLETE_CANDLES = "incomplete_candles"
    EXTREME_VOLATILITY = "extreme_volatility"

class DataQualitySeverity(Enum):
    """مستويات خطورة مشاكل البيانات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DataQualityCheck:
    """فحص جودة البيانات"""
    check_id: str
    asset: str
    issue_type: DataQualityIssue
    severity: DataQualitySeverity
    description: str
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class AssetDataQuality:
    """جودة البيانات لأصل واحد"""
    asset: str
    total_candles_expected: int = 0
    total_candles_received: int = 0
    missing_candles: int = 0
    duplicate_candles: int = 0
    invalid_candles: int = 0
    last_candle_time: Optional[datetime] = None
    data_freshness_seconds: float = 0.0
    quality_score: float = 100.0
    issues_count: int = 0
    last_quality_check: Optional[datetime] = None
    status: str = "good"  # good, warning, error, critical

@dataclass
class DataQualityReport:
    """تقرير جودة البيانات"""
    timestamp: datetime
    overall_quality_score: float
    total_assets_monitored: int
    assets_with_issues: int
    total_issues_found: int
    issues_by_type: Dict[DataQualityIssue, int]
    issues_by_severity: Dict[DataQualitySeverity, int]
    assets_quality: Dict[str, AssetDataQuality]
    recommendations: List[str]
    critical_issues: List[DataQualityCheck]

class DataQualityMonitor:
    """مراقب جودة البيانات"""
    
    def __init__(self):
        self.config = TradingConfig()
        
        # حالة النظام
        self.monitoring_active = False
        self.check_interval = 120  # دقيقتان
        self.quality_thresholds = {
            'missing_data_threshold': 5,  # نسبة مئوية
            'stale_data_threshold': 300,  # 5 دقائق
            'price_change_threshold': 10,  # 10% تغيير مفاجئ
            'volume_anomaly_threshold': 500,  # 500% تغيير في الحجم
            'minimum_quality_score': 80.0
        }
        
        # بيانات المراقبة
        self.assets_quality: Dict[str, AssetDataQuality] = {}
        self.quality_issues: List[DataQualityCheck] = []
        self.quality_history: Dict[str, List[float]] = {}
        
        # إحصائيات المراقبة
        self.monitoring_stats = {
            'total_checks_performed': 0,
            'issues_detected': 0,
            'issues_resolved': 0,
            'last_check_time': None,
            'check_errors': 0
        }
        
        # خيط المراقبة
        self.monitoring_thread = None
        
        # تهيئة النظام
        self._initialize_quality_monitoring()
        
        logger.info("تم تهيئة نظام مراقبة جودة البيانات")

    def _initialize_quality_monitoring(self):
        """تهيئة نظام مراقبة الجودة"""
        try:
            # تهيئة جودة البيانات لكل أصل
            for asset in CURRENCY_PAIRS_70:
                self.assets_quality[asset] = AssetDataQuality(asset=asset)
                self.quality_history[asset] = []
            
            logger.debug(f"تم تهيئة مراقبة الجودة لـ {len(CURRENCY_PAIRS_70)} أصل")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة مراقبة الجودة: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_quality_monitoring(self) -> bool:
        """بدء مراقبة جودة البيانات"""
        try:
            if self.monitoring_active:
                logger.warning("مراقبة جودة البيانات نشطة بالفعل")
                return True
            
            logger.info("🔄 بدء نظام مراقبة جودة البيانات")
            
            self.monitoring_active = True
            
            # بدء خيط المراقبة
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="DataQualityMonitor"
            )
            self.monitoring_thread.start()
            
            logger.info("✅ تم بدء نظام مراقبة جودة البيانات")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة الجودة: {str(e)}")
            return False

    def stop_quality_monitoring(self):
        """إيقاف مراقبة جودة البيانات"""
        try:
            logger.info("إيقاف نظام مراقبة جودة البيانات...")
            
            self.monitoring_active = False
            
            # انتظار انتهاء خيط المراقبة
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            logger.info("✅ تم إيقاف نظام مراقبة جودة البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف مراقبة الجودة: {str(e)}")

    async def check_asset_data_quality(self, asset: str) -> AssetDataQuality:
        """فحص جودة البيانات لأصل معين"""
        try:
            if asset not in self.assets_quality:
                return AssetDataQuality(asset=asset)
            
            asset_quality = self.assets_quality[asset]
            current_time = datetime.now()
            
            # جلب البيانات الحديثة للأصل
            recent_candles = await self._get_recent_candles(asset, 100)
            
            if not recent_candles:
                # لا توجد بيانات
                self._record_quality_issue(
                    asset, DataQualityIssue.MISSING_DATA, DataQualitySeverity.CRITICAL,
                    "لا توجد بيانات للأصل"
                )
                asset_quality.quality_score = 0.0
                asset_quality.status = "critical"
                return asset_quality
            
            # فحص اكتمال البيانات
            await self._check_data_completeness(asset, recent_candles, asset_quality)
            
            # فحص صحة البيانات
            await self._check_data_validity(asset, recent_candles, asset_quality)
            
            # فحص حداثة البيانات
            await self._check_data_freshness(asset, recent_candles, asset_quality)
            
            # فحص الشذوذ في الأسعار
            await self._check_price_anomalies(asset, recent_candles, asset_quality)
            
            # فحص الشذوذ في الحجم
            await self._check_volume_anomalies(asset, recent_candles, asset_quality)
            
            # حساب نقاط الجودة الإجمالية
            asset_quality.quality_score = self._calculate_quality_score(asset_quality)
            
            # تحديد الحالة
            asset_quality.status = self._determine_quality_status(asset_quality.quality_score)
            
            # تحديث وقت آخر فحص
            asset_quality.last_quality_check = current_time
            
            # حفظ في التاريخ
            self.quality_history[asset].append(asset_quality.quality_score)
            if len(self.quality_history[asset]) > 100:
                self.quality_history[asset] = self.quality_history[asset][-100:]
            
            return asset_quality
            
        except Exception as e:
            logger.error(f"خطأ في فحص جودة البيانات لـ {asset}: {str(e)}")
            self.monitoring_stats['check_errors'] += 1
            return AssetDataQuality(asset=asset, quality_score=0.0, status="error")

    async def _get_recent_candles(self, asset: str, count: int) -> List[Dict[str, Any]]:
        """جلب الشموع الحديثة للأصل"""
        try:
            # محاكاة بيانات للاختبار
            from datetime import datetime, timedelta
            import random

            # إنشاء بيانات تجريبية للاختبار
            candles = []
            base_time = datetime.now() - timedelta(minutes=count)
            base_price = 1.1000 + random.uniform(-0.01, 0.01)

            for i in range(count):
                timestamp = base_time + timedelta(minutes=i)
                price_change = random.uniform(-0.0005, 0.0005)
                open_price = base_price + price_change
                close_price = open_price + random.uniform(-0.0003, 0.0003)
                high_price = max(open_price, close_price) + random.uniform(0, 0.0002)
                low_price = min(open_price, close_price) - random.uniform(0, 0.0002)
                volume = random.uniform(100, 1000)

                candle = {
                    'timestamp': timestamp.isoformat(),
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volume
                }
                candles.append(candle)
                base_price = close_price

            return candles

        except Exception as e:
            logger.error(f"خطأ في جلب الشموع لـ {asset}: {str(e)}")
            return []

    async def _check_data_completeness(self, asset: str, candles: List[Dict[str, Any]], asset_quality: AssetDataQuality):
        """فحص اكتمال البيانات"""
        try:
            if not candles:
                return
            
            # حساب الشموع المتوقعة (شمعة كل دقيقة)
            first_candle_time = datetime.fromisoformat(candles[0]['timestamp'])
            last_candle_time = datetime.fromisoformat(candles[-1]['timestamp'])
            
            time_diff_minutes = (last_candle_time - first_candle_time).total_seconds() / 60
            expected_candles = int(time_diff_minutes) + 1
            received_candles = len(candles)
            
            asset_quality.total_candles_expected = expected_candles
            asset_quality.total_candles_received = received_candles
            asset_quality.missing_candles = max(0, expected_candles - received_candles)
            
            # فحص الفجوات الزمنية
            gaps_found = 0
            for i in range(1, len(candles)):
                prev_time = datetime.fromisoformat(candles[i-1]['timestamp'])
                curr_time = datetime.fromisoformat(candles[i]['timestamp'])
                gap_minutes = (curr_time - prev_time).total_seconds() / 60
                
                if gap_minutes > 2:  # فجوة أكثر من دقيقتين
                    gaps_found += 1
            
            # تسجيل المشاكل
            missing_percentage = (asset_quality.missing_candles / expected_candles) * 100 if expected_candles > 0 else 0
            
            if missing_percentage > self.quality_thresholds['missing_data_threshold']:
                severity = DataQualitySeverity.HIGH if missing_percentage > 20 else DataQualitySeverity.MEDIUM
                self._record_quality_issue(
                    asset, DataQualityIssue.MISSING_DATA, severity,
                    f"بيانات مفقودة: {missing_percentage:.1f}%",
                    {'missing_candles': asset_quality.missing_candles, 'expected': expected_candles}
                )
            
            if gaps_found > 0:
                self._record_quality_issue(
                    asset, DataQualityIssue.TIMESTAMP_GAPS, DataQualitySeverity.MEDIUM,
                    f"فجوات زمنية: {gaps_found}",
                    {'gaps_count': gaps_found}
                )
            
        except Exception as e:
            logger.error(f"خطأ في فحص اكتمال البيانات لـ {asset}: {str(e)}")

    async def _check_data_validity(self, asset: str, candles: List[Dict[str, Any]], asset_quality: AssetDataQuality):
        """فحص صحة البيانات"""
        try:
            invalid_count = 0
            duplicate_count = 0
            seen_timestamps = set()
            
            for candle in candles:
                # فحص القيم المطلوبة
                required_fields = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
                missing_fields = [field for field in required_fields if field not in candle or candle[field] is None]
                
                if missing_fields:
                    invalid_count += 1
                    continue
                
                # فحص صحة الأسعار
                try:
                    open_price = float(candle['open'])
                    high_price = float(candle['high'])
                    low_price = float(candle['low'])
                    close_price = float(candle['close'])
                    volume = float(candle['volume'])
                    
                    # فحص منطقية الأسعار
                    if not (low_price <= open_price <= high_price and 
                           low_price <= close_price <= high_price and
                           low_price <= high_price and
                           volume >= 0):
                        invalid_count += 1
                        continue
                    
                    # فحص القيم الصفرية أو السالبة
                    if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                        invalid_count += 1
                        continue
                    
                except (ValueError, TypeError):
                    invalid_count += 1
                    continue
                
                # فحص التكرارات
                timestamp = candle['timestamp']
                if timestamp in seen_timestamps:
                    duplicate_count += 1
                else:
                    seen_timestamps.add(timestamp)
            
            asset_quality.invalid_candles = invalid_count
            asset_quality.duplicate_candles = duplicate_count
            
            # تسجيل المشاكل
            if invalid_count > 0:
                severity = DataQualitySeverity.HIGH if invalid_count > len(candles) * 0.1 else DataQualitySeverity.MEDIUM
                self._record_quality_issue(
                    asset, DataQualityIssue.INVALID_VALUES, severity,
                    f"شموع غير صحيحة: {invalid_count}",
                    {'invalid_count': invalid_count, 'total_candles': len(candles)}
                )
            
            if duplicate_count > 0:
                self._record_quality_issue(
                    asset, DataQualityIssue.DUPLICATE_DATA, DataQualitySeverity.MEDIUM,
                    f"شموع مكررة: {duplicate_count}",
                    {'duplicate_count': duplicate_count}
                )
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة البيانات لـ {asset}: {str(e)}")

    async def _check_data_freshness(self, asset: str, candles: List[Dict[str, Any]], asset_quality: AssetDataQuality):
        """فحص حداثة البيانات"""
        try:
            if not candles:
                return
            
            # آخر شمعة
            last_candle = candles[-1]
            last_candle_time = datetime.fromisoformat(last_candle['timestamp'])
            current_time = datetime.now()
            
            freshness_seconds = (current_time - last_candle_time).total_seconds()
            
            asset_quality.last_candle_time = last_candle_time
            asset_quality.data_freshness_seconds = freshness_seconds
            
            # فحص إذا كانت البيانات قديمة
            if freshness_seconds > self.quality_thresholds['stale_data_threshold']:
                severity = DataQualitySeverity.CRITICAL if freshness_seconds > 1800 else DataQualitySeverity.HIGH
                self._record_quality_issue(
                    asset, DataQualityIssue.STALE_DATA, severity,
                    f"بيانات قديمة: {freshness_seconds/60:.1f} دقيقة",
                    {'freshness_seconds': freshness_seconds, 'last_candle_time': last_candle_time.isoformat()}
                )
            
        except Exception as e:
            logger.error(f"خطأ في فحص حداثة البيانات لـ {asset}: {str(e)}")

    async def _check_price_anomalies(self, asset: str, candles: List[Dict[str, Any]], asset_quality: AssetDataQuality):
        """فحص الشذوذ في الأسعار"""
        try:
            if len(candles) < 10:
                return
            
            # حساب التغييرات في الأسعار
            price_changes = []
            for i in range(1, len(candles)):
                prev_close = float(candles[i-1]['close'])
                curr_close = float(candles[i]['close'])
                
                if prev_close > 0:
                    change_percent = abs((curr_close - prev_close) / prev_close) * 100
                    price_changes.append(change_percent)
            
            if not price_changes:
                return
            
            # فحص التغييرات المفاجئة
            threshold = self.quality_thresholds['price_change_threshold']
            extreme_changes = [change for change in price_changes if change > threshold]
            
            if extreme_changes:
                max_change = max(extreme_changes)
                severity = DataQualitySeverity.HIGH if max_change > 20 else DataQualitySeverity.MEDIUM
                
                self._record_quality_issue(
                    asset, DataQualityIssue.PRICE_ANOMALIES, severity,
                    f"تغييرات سعرية مفاجئة: {len(extreme_changes)} حالة، أقصى تغيير: {max_change:.1f}%",
                    {'extreme_changes_count': len(extreme_changes), 'max_change': max_change}
                )
            
            # فحص التقلبات الشديدة
            if len(price_changes) >= 20:
                avg_change = statistics.mean(price_changes)
                std_change = statistics.stdev(price_changes) if len(price_changes) > 1 else 0
                
                if std_change > avg_change * 3:  # تقلبات شديدة
                    self._record_quality_issue(
                        asset, DataQualityIssue.EXTREME_VOLATILITY, DataQualitySeverity.MEDIUM,
                        f"تقلبات شديدة: انحراف معياري {std_change:.2f}%",
                        {'avg_change': avg_change, 'std_deviation': std_change}
                    )
            
        except Exception as e:
            logger.error(f"خطأ في فحص شذوذ الأسعار لـ {asset}: {str(e)}")

    async def _check_volume_anomalies(self, asset: str, candles: List[Dict[str, Any]], asset_quality: AssetDataQuality):
        """فحص الشذوذ في الحجم"""
        try:
            if len(candles) < 10:
                return
            
            volumes = [float(candle['volume']) for candle in candles if candle.get('volume', 0) > 0]
            
            if len(volumes) < 5:
                return
            
            avg_volume = statistics.mean(volumes)
            
            # فحص الحجم الشاذ
            threshold = self.quality_thresholds['volume_anomaly_threshold']
            anomalies = []
            
            for volume in volumes:
                if avg_volume > 0:
                    volume_ratio = (volume / avg_volume) * 100
                    if volume_ratio > threshold or volume_ratio < (100 / threshold):
                        anomalies.append(volume_ratio)
            
            if anomalies:
                self._record_quality_issue(
                    asset, DataQualityIssue.VOLUME_ANOMALIES, DataQualitySeverity.LOW,
                    f"شذوذ في الحجم: {len(anomalies)} حالة",
                    {'anomalies_count': len(anomalies), 'avg_volume': avg_volume}
                )
            
        except Exception as e:
            logger.error(f"خطأ في فحص شذوذ الحجم لـ {asset}: {str(e)}")

    def _record_quality_issue(self, asset: str, issue_type: DataQualityIssue, severity: DataQualitySeverity, 
                            description: str, details: Dict[str, Any] = None):
        """تسجيل مشكلة جودة البيانات"""
        try:
            issue = DataQualityCheck(
                check_id=f"check_{int(time.time() * 1000)}",
                asset=asset,
                issue_type=issue_type,
                severity=severity,
                description=description,
                timestamp=datetime.now(),
                details=details or {}
            )
            
            self.quality_issues.append(issue)
            
            # تحديث عداد المشاكل للأصل
            if asset in self.assets_quality:
                self.assets_quality[asset].issues_count += 1
            
            # تحديث الإحصائيات
            self.monitoring_stats['issues_detected'] += 1
            
            # طباعة المشكلة حسب الخطورة
            if severity in [DataQualitySeverity.HIGH, DataQualitySeverity.CRITICAL]:
                logger.warning(f"مشكلة جودة بيانات [{severity.value}] في {asset}: {description}")
            else:
                logger.debug(f"مشكلة جودة بيانات [{severity.value}] في {asset}: {description}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل مشكلة الجودة: {str(e)}")

    def _calculate_quality_score(self, asset_quality: AssetDataQuality) -> float:
        """حساب نقاط جودة البيانات"""
        try:
            score = 100.0
            
            # خصم نقاط حسب البيانات المفقودة
            if asset_quality.total_candles_expected > 0:
                missing_percentage = (asset_quality.missing_candles / asset_quality.total_candles_expected) * 100
                score -= min(30, missing_percentage * 2)
            
            # خصم نقاط حسب البيانات غير الصحيحة
            if asset_quality.total_candles_received > 0:
                invalid_percentage = (asset_quality.invalid_candles / asset_quality.total_candles_received) * 100
                score -= min(25, invalid_percentage * 3)
            
            # خصم نقاط حسب البيانات المكررة
            if asset_quality.total_candles_received > 0:
                duplicate_percentage = (asset_quality.duplicate_candles / asset_quality.total_candles_received) * 100
                score -= min(15, duplicate_percentage * 2)
            
            # خصم نقاط حسب قدم البيانات
            if asset_quality.data_freshness_seconds > self.quality_thresholds['stale_data_threshold']:
                staleness_minutes = asset_quality.data_freshness_seconds / 60
                score -= min(20, staleness_minutes)
            
            # خصم نقاط حسب عدد المشاكل
            score -= min(10, asset_quality.issues_count)
            
            return max(0, score)
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الجودة: {str(e)}")
            return 50.0

    def _determine_quality_status(self, quality_score: float) -> str:
        """تحديد حالة جودة البيانات"""
        if quality_score >= 90:
            return "excellent"
        elif quality_score >= 80:
            return "good"
        elif quality_score >= 60:
            return "warning"
        elif quality_score >= 40:
            return "error"
        else:
            return "critical"

    def _monitoring_loop(self):
        """حلقة مراقبة جودة البيانات"""
        try:
            while self.monitoring_active:
                try:
                    # فحص جودة البيانات لجميع الأصول
                    asyncio.run(self._perform_quality_checks())

                    # تنظيف المشاكل القديمة
                    self._cleanup_old_issues()

                    # تحديث الإحصائيات
                    self.monitoring_stats['total_checks_performed'] += 1
                    self.monitoring_stats['last_check_time'] = datetime.now()

                    # انتظار حتى الفحص التالي
                    time.sleep(self.check_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة مراقبة الجودة: {str(e)}")
                    self.monitoring_stats['check_errors'] += 1
                    time.sleep(60)

        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة الجودة: {str(e)}")

    async def _perform_quality_checks(self):
        """إجراء فحوصات الجودة لجميع الأصول"""
        try:
            # فحص عينة من الأصول في كل دورة لتوزيع الحمل
            assets_to_check = CURRENCY_PAIRS_70[::5]  # كل خامس أصل

            tasks = []
            for asset in assets_to_check:
                task = self.check_asset_data_quality(asset)
                tasks.append(task)

            # تشغيل الفحوصات بشكل متوازي
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # معالجة النتائج
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"خطأ في فحص {assets_to_check[i]}: {str(result)}")
                else:
                    self.assets_quality[assets_to_check[i]] = result

        except Exception as e:
            logger.error(f"خطأ في إجراء فحوصات الجودة: {str(e)}")

    def _cleanup_old_issues(self):
        """تنظيف المشاكل القديمة"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=24)

            # إزالة المشاكل القديمة
            old_issues_count = len(self.quality_issues)
            self.quality_issues = [
                issue for issue in self.quality_issues
                if issue.timestamp > cutoff_time
            ]

            removed_count = old_issues_count - len(self.quality_issues)
            if removed_count > 0:
                logger.debug(f"تم تنظيف {removed_count} مشكلة جودة قديمة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف المشاكل القديمة: {str(e)}")

    @handle_errors(default_return={}, log_error=True)
    def get_quality_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات جودة البيانات"""
        try:
            current_time = datetime.now()

            # إحصائيات عامة
            total_issues = len(self.quality_issues)
            resolved_issues = len([issue for issue in self.quality_issues if issue.resolved])

            # إحصائيات حسب النوع
            issues_by_type = {}
            for issue_type in DataQualityIssue:
                issues_by_type[issue_type.value] = len([
                    issue for issue in self.quality_issues
                    if issue.issue_type == issue_type
                ])

            # إحصائيات حسب الخطورة
            issues_by_severity = {}
            for severity in DataQualitySeverity:
                issues_by_severity[severity.value] = len([
                    issue for issue in self.quality_issues
                    if issue.severity == severity
                ])

            # إحصائيات الأصول
            assets_stats = {
                'total_assets': len(self.assets_quality),
                'excellent_quality': len([a for a in self.assets_quality.values() if a.status == 'excellent']),
                'good_quality': len([a for a in self.assets_quality.values() if a.status == 'good']),
                'warning_quality': len([a for a in self.assets_quality.values() if a.status == 'warning']),
                'error_quality': len([a for a in self.assets_quality.values() if a.status == 'error']),
                'critical_quality': len([a for a in self.assets_quality.values() if a.status == 'critical'])
            }

            # حساب متوسط نقاط الجودة
            quality_scores = [asset.quality_score for asset in self.assets_quality.values()]
            avg_quality_score = statistics.mean(quality_scores) if quality_scores else 0

            return {
                'monitoring_status': {
                    'active': self.monitoring_active,
                    'check_interval': self.check_interval,
                    'last_check_time': self.monitoring_stats['last_check_time'].isoformat() if self.monitoring_stats['last_check_time'] else None
                },
                'quality_overview': {
                    'avg_quality_score': round(avg_quality_score, 2),
                    'total_issues': total_issues,
                    'resolved_issues': resolved_issues,
                    'pending_issues': total_issues - resolved_issues,
                    'resolution_rate': (resolved_issues / max(total_issues, 1)) * 100
                },
                'issues_breakdown': {
                    'by_type': issues_by_type,
                    'by_severity': issues_by_severity
                },
                'assets_breakdown': assets_stats,
                'monitoring_stats': {
                    'total_checks_performed': self.monitoring_stats['total_checks_performed'],
                    'issues_detected': self.monitoring_stats['issues_detected'],
                    'issues_resolved': self.monitoring_stats['issues_resolved'],
                    'check_errors': self.monitoring_stats['check_errors']
                },
                'timestamp': current_time.isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الجودة: {str(e)}")
            return {}

    def get_asset_quality_details(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفاصيل جودة أصل معين"""
        try:
            if asset not in self.assets_quality:
                return None

            asset_quality = self.assets_quality[asset]

            # جلب المشاكل المتعلقة بالأصل
            asset_issues = [
                {
                    'issue_type': issue.issue_type.value,
                    'severity': issue.severity.value,
                    'description': issue.description,
                    'timestamp': issue.timestamp.isoformat(),
                    'resolved': issue.resolved,
                    'details': issue.details
                }
                for issue in self.quality_issues
                if issue.asset == asset and not issue.resolved
            ]

            # جلب تاريخ الجودة
            quality_history = self.quality_history.get(asset, [])

            return {
                'asset': asset,
                'current_quality': {
                    'score': asset_quality.quality_score,
                    'status': asset_quality.status,
                    'last_check': asset_quality.last_quality_check.isoformat() if asset_quality.last_quality_check else None
                },
                'data_completeness': {
                    'expected_candles': asset_quality.total_candles_expected,
                    'received_candles': asset_quality.total_candles_received,
                    'missing_candles': asset_quality.missing_candles,
                    'completeness_rate': (
                        (asset_quality.total_candles_received / max(asset_quality.total_candles_expected, 1)) * 100
                    )
                },
                'data_validity': {
                    'invalid_candles': asset_quality.invalid_candles,
                    'duplicate_candles': asset_quality.duplicate_candles,
                    'validity_rate': (
                        ((asset_quality.total_candles_received - asset_quality.invalid_candles) /
                         max(asset_quality.total_candles_received, 1)) * 100
                    )
                },
                'data_freshness': {
                    'last_candle_time': asset_quality.last_candle_time.isoformat() if asset_quality.last_candle_time else None,
                    'freshness_seconds': asset_quality.data_freshness_seconds,
                    'is_stale': asset_quality.data_freshness_seconds > self.quality_thresholds['stale_data_threshold']
                },
                'active_issues': asset_issues,
                'issues_count': asset_quality.issues_count,
                'quality_history': quality_history[-20:],  # آخر 20 نقطة
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب تفاصيل جودة {asset}: {str(e)}")
            return None

    def generate_quality_report(self) -> DataQualityReport:
        """إنشاء تقرير جودة البيانات الشامل"""
        try:
            current_time = datetime.now()

            # حساب النقاط الإجمالية
            quality_scores = [asset.quality_score for asset in self.assets_quality.values()]
            overall_score = statistics.mean(quality_scores) if quality_scores else 0

            # إحصائيات المشاكل
            issues_by_type = {}
            issues_by_severity = {}

            for issue_type in DataQualityIssue:
                issues_by_type[issue_type] = len([
                    issue for issue in self.quality_issues
                    if issue.issue_type == issue_type and not issue.resolved
                ])

            for severity in DataQualitySeverity:
                issues_by_severity[severity] = len([
                    issue for issue in self.quality_issues
                    if issue.severity == severity and not issue.resolved
                ])

            # المشاكل الحرجة
            critical_issues = [
                issue for issue in self.quality_issues
                if issue.severity == DataQualitySeverity.CRITICAL and not issue.resolved
            ]

            # الأصول التي بها مشاكل
            assets_with_issues = len([
                asset for asset in self.assets_quality.values()
                if asset.issues_count > 0
            ])

            # إنشاء التوصيات
            recommendations = self._generate_quality_recommendations()

            return DataQualityReport(
                timestamp=current_time,
                overall_quality_score=overall_score,
                total_assets_monitored=len(self.assets_quality),
                assets_with_issues=assets_with_issues,
                total_issues_found=len([issue for issue in self.quality_issues if not issue.resolved]),
                issues_by_type=issues_by_type,
                issues_by_severity=issues_by_severity,
                assets_quality=self.assets_quality.copy(),
                recommendations=recommendations,
                critical_issues=critical_issues
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الجودة: {str(e)}")
            return DataQualityReport(
                timestamp=datetime.now(),
                overall_quality_score=0,
                total_assets_monitored=0,
                assets_with_issues=0,
                total_issues_found=0,
                issues_by_type={},
                issues_by_severity={},
                assets_quality={},
                recommendations=["خطأ في إنشاء التقرير"],
                critical_issues=[]
            )

    def _generate_quality_recommendations(self) -> List[str]:
        """إنشاء توصيات تحسين الجودة"""
        recommendations = []

        try:
            # فحص المشاكل الشائعة
            active_issues = [issue for issue in self.quality_issues if not issue.resolved]

            # مشاكل البيانات المفقودة
            missing_data_issues = len([issue for issue in active_issues if issue.issue_type == DataQualityIssue.MISSING_DATA])
            if missing_data_issues > 5:
                recommendations.append(f"إصلاح مشاكل البيانات المفقودة في {missing_data_issues} أصل")

            # مشاكل البيانات القديمة
            stale_data_issues = len([issue for issue in active_issues if issue.issue_type == DataQualityIssue.STALE_DATA])
            if stale_data_issues > 3:
                recommendations.append(f"تحديث البيانات القديمة في {stale_data_issues} أصل")

            # مشاكل القيم غير الصحيحة
            invalid_data_issues = len([issue for issue in active_issues if issue.issue_type == DataQualityIssue.INVALID_VALUES])
            if invalid_data_issues > 2:
                recommendations.append(f"تنظيف البيانات غير الصحيحة في {invalid_data_issues} أصل")

            # فحص الأصول ذات الجودة المنخفضة
            poor_quality_assets = [
                asset for asset in self.assets_quality.values()
                if asset.quality_score < self.quality_thresholds['minimum_quality_score']
            ]

            if len(poor_quality_assets) > 10:
                recommendations.append(f"تحسين جودة البيانات في {len(poor_quality_assets)} أصل")

            # فحص النقاط الإجمالية
            quality_scores = [asset.quality_score for asset in self.assets_quality.values()]
            if quality_scores:
                avg_score = statistics.mean(quality_scores)
                if avg_score < 85:
                    recommendations.append(f"تحسين الجودة الإجمالية - النقاط الحالية: {avg_score:.1f}")

            if not recommendations:
                recommendations.append("جودة البيانات ممتازة - استمر في المراقبة")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            recommendations.append("خطأ في تحليل البيانات")

        return recommendations

    def resolve_quality_issue(self, check_id: str, resolution_note: str = "") -> bool:
        """حل مشكلة جودة البيانات"""
        try:
            for issue in self.quality_issues:
                if issue.check_id == check_id:
                    issue.resolved = True
                    issue.resolution_time = datetime.now()
                    if resolution_note:
                        issue.details['resolution_note'] = resolution_note

                    self.monitoring_stats['issues_resolved'] += 1
                    logger.info(f"تم حل مشكلة الجودة: {check_id}")
                    return True

            return False

        except Exception as e:
            logger.error(f"خطأ في حل مشكلة الجودة {check_id}: {str(e)}")
            return False

# إنشاء مثيل عام للاستخدام
data_quality_monitor = DataQualityMonitor()

# دوال مساعدة للوصول السريع
def start_quality_monitoring() -> bool:
    """بدء مراقبة جودة البيانات"""
    return data_quality_monitor.start_quality_monitoring()

def stop_quality_monitoring():
    """إيقاف مراقبة جودة البيانات"""
    data_quality_monitor.stop_quality_monitoring()

async def check_asset_quality(asset: str) -> AssetDataQuality:
    """فحص جودة أصل معين"""
    return await data_quality_monitor.check_asset_data_quality(asset)

def get_quality_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات الجودة"""
    return data_quality_monitor.get_quality_statistics()

def get_asset_quality_details(asset: str) -> Optional[Dict[str, Any]]:
    """الحصول على تفاصيل جودة أصل"""
    return data_quality_monitor.get_asset_quality_details(asset)

def get_quality_report() -> DataQualityReport:
    """الحصول على تقرير الجودة"""
    return data_quality_monitor.generate_quality_report()

def resolve_issue(check_id: str, resolution_note: str = "") -> bool:
    """حل مشكلة جودة"""
    return data_quality_monitor.resolve_quality_issue(check_id, resolution_note)
