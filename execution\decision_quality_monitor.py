"""
مراقب جودة القرارات المتكامل
Decision Quality Monitor - Integrated with Main System
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70

# استيراد أنظمة اتخاذ القرارات
from data_layer.quadruple_convergence_system import quadruple_convergence_system, ConvergenceResult
from execution.real_trading_executor import real_trading_executor, TradeOrder, TradeStatus
from ai_models.ai_predictor_engine import ai_predictor_engine
from ai_models.prediction_tracker import prediction_tracker

logger = scalping_logger.get_logger("decision_quality_monitor")

class DecisionType(Enum):
    """أنواع القرارات"""
    CALL_DECISION = "CALL"
    PUT_DECISION = "PUT"
    NEUTRAL_DECISION = "NEUTRAL"
    NO_TRADE_DECISION = "NO_TRADE"

class DecisionQuality(Enum):
    """مستويات جودة القرارات"""
    EXCELLENT = "excellent"  # 90%+
    GOOD = "good"           # 70-89%
    AVERAGE = "average"     # 50-69%
    POOR = "poor"          # 30-49%
    VERY_POOR = "very_poor" # <30%

@dataclass
class DecisionRecord:
    """سجل قرار واحد"""
    decision_id: str
    asset: str
    decision_type: DecisionType
    confidence_score: float
    convergence_score: float
    layer_signals: Dict[str, Any]
    timestamp: datetime
    
    # نتائج التنفيذ
    executed: bool = False
    execution_result: Optional[Any] = None
    actual_outcome: Optional[str] = None  # 'correct', 'incorrect', 'pending'
    
    # تحليل الجودة
    quality_score: float = 0.0
    quality_level: Optional[DecisionQuality] = None
    quality_factors: Dict[str, float] = field(default_factory=dict)
    
    # معلومات إضافية
    market_conditions: Dict[str, Any] = field(default_factory=dict)
    risk_factors: Dict[str, Any] = field(default_factory=dict)
    verification_time: Optional[datetime] = None

@dataclass
class QualityMetrics:
    """مقاييس جودة القرارات"""
    total_decisions: int = 0
    correct_decisions: int = 0
    incorrect_decisions: int = 0
    pending_decisions: int = 0
    
    # معدلات الدقة
    overall_accuracy: float = 0.0
    call_accuracy: float = 0.0
    put_accuracy: float = 0.0
    neutral_accuracy: float = 0.0
    
    # توزيع الجودة
    excellent_decisions: int = 0
    good_decisions: int = 0
    average_decisions: int = 0
    poor_decisions: int = 0
    very_poor_decisions: int = 0
    
    # مقاييس الثقة
    avg_confidence: float = 0.0
    avg_convergence: float = 0.0
    confidence_accuracy_correlation: float = 0.0
    
    # مقاييس زمنية
    avg_decision_time: float = 0.0
    avg_verification_time: float = 0.0
    
    last_update: Optional[datetime] = None

class DecisionQualityMonitor:
    """مراقب جودة القرارات المتكامل"""
    
    def __init__(self):
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.is_monitoring = False
        self.monitoring_interval = 60  # دقيقة واحدة
        
        # مخزن القرارات
        self.decision_records = {}  # {decision_id: DecisionRecord}
        self.asset_decisions = {}   # {asset: [decision_ids]}
        self.pending_verifications = []
        
        # مقاييس الجودة
        self.quality_metrics = QualityMetrics()
        self.asset_quality_metrics = {}  # {asset: QualityMetrics}
        
        # إعدادات المراقبة
        self.monitoring_settings = {
            'verification_timeout_minutes': 5,  # مهلة التحقق من النتائج
            'quality_thresholds': {
                'excellent': 0.90,
                'good': 0.70,
                'average': 0.50,
                'poor': 0.30
            },
            'confidence_weight': 0.4,
            'convergence_weight': 0.3,
            'outcome_weight': 0.3
        }
        
        # إحصائيات النظام
        self.system_stats = {
            'total_monitoring_cycles': 0,
            'decisions_monitored': 0,
            'verifications_completed': 0,
            'quality_assessments': 0,
            'last_monitoring_time': None,
            'monitoring_duration_avg': 0.0
        }
        
        logger.info("تم تهيئة مراقب جودة القرارات")

    @handle_async_errors(default_return=False, log_error=True)
    async def start_quality_monitoring(self) -> bool:
        """بدء مراقبة جودة القرارات"""
        try:
            if self.is_monitoring:
                logger.warning("مراقب جودة القرارات يعمل بالفعل")
                return True
            
            self.is_monitoring = True
            logger.info("🚀 بدء مراقبة جودة القرارات المستمرة")
            
            # بدء حلقة المراقبة
            asyncio.create_task(self._monitoring_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة جودة القرارات: {str(e)}")
            return False

    async def _monitoring_loop(self):
        """حلقة المراقبة المستمرة"""
        try:
            while self.is_monitoring:
                monitoring_start = time.time()
                
                # مراقبة شاملة للجودة
                await self._perform_quality_monitoring()
                
                # تحديث الإحصائيات
                monitoring_duration = time.time() - monitoring_start
                self._update_system_stats(monitoring_duration)
                
                # انتظار حتى الدورة التالية
                await asyncio.sleep(self.monitoring_interval)
                
        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة الجودة: {str(e)}")

    async def _perform_quality_monitoring(self):
        """مراقبة شاملة لجودة القرارات"""
        try:
            logger.info("📊 بدء مراقبة جودة القرارات الشاملة")
            
            # جمع القرارات الجديدة
            await self._collect_new_decisions()
            
            # التحقق من النتائج المعلقة
            await self._verify_pending_decisions()
            
            # تقييم جودة القرارات
            await self._assess_decision_quality()
            
            # تحديث المقاييس
            await self._update_quality_metrics()
            
            # تحليل الأنماط
            await self._analyze_decision_patterns()
            
            # حفظ النتائج
            await self._save_monitoring_results()
            
            logger.info("✅ اكتملت مراقبة جودة القرارات")
            
        except Exception as e:
            logger.error(f"خطأ في مراقبة الجودة الشاملة: {str(e)}")

    async def _collect_new_decisions(self):
        """جمع القرارات الجديدة من النظام"""
        try:
            # جمع قرارات التقارب الرباعي الحديثة
            convergence_decisions = await self._collect_convergence_decisions()
            
            # جمع قرارات التداول المنفذة
            trading_decisions = await self._collect_trading_decisions()
            
            # جمع قرارات الذكاء الاصطناعي
            ai_decisions = await self._collect_ai_decisions()
            
            # دمج ومعالجة القرارات
            all_decisions = convergence_decisions + trading_decisions + ai_decisions
            
            for decision_data in all_decisions:
                await self._process_new_decision(decision_data)
            
            logger.debug(f"تم جمع {len(all_decisions)} قرار جديد")
            
        except Exception as e:
            logger.error(f"خطأ في جمع القرارات الجديدة: {str(e)}")

    async def _collect_convergence_decisions(self) -> List[Dict[str, Any]]:
        """جمع قرارات التقارب الرباعي"""
        try:
            decisions = []
            
            # الحصول على إحصائيات التقارب الحديثة
            convergence_stats = quadruple_convergence_system.stats.copy()
            
            # محاكاة قرارات حديثة (في التطبيق الفعلي، ستأتي من النظام)
            for asset in self.currency_pairs[:5]:  # أول 5 أصول للاختبار
                decision_data = {
                    'type': 'convergence',
                    'asset': asset,
                    'decision_type': DecisionType.CALL_DECISION,
                    'confidence_score': 0.75,
                    'convergence_score': 0.80,
                    'layer_signals': {
                        'technical': {'signal': 'CALL', 'confidence': 0.7},
                        'behavioral': {'signal': 'CALL', 'confidence': 0.8},
                        'quantitative': {'signal': 'NEUTRAL', 'confidence': 0.6},
                        'ai': {'signal': 'CALL', 'confidence': 0.9}
                    },
                    'timestamp': datetime.now()
                }
                decisions.append(decision_data)
            
            return decisions
            
        except Exception as e:
            logger.error(f"خطأ في جمع قرارات التقارب: {str(e)}")
            return []

    async def _collect_trading_decisions(self) -> List[Dict[str, Any]]:
        """جمع قرارات التداول المنفذة"""
        try:
            decisions = []
            
            # جمع الصفقات الحديثة
            recent_trades = real_trading_executor.executed_trades[-10:]  # آخر 10 صفقات
            
            for trade in recent_trades:
                if hasattr(trade, 'execution_time') and trade.execution_time:
                    # التحقق من أن الصفقة حديثة (آخر ساعة)
                    if (datetime.now() - trade.execution_time).total_seconds() < 3600:
                        decision_data = {
                            'type': 'trading',
                            'asset': trade.asset,
                            'decision_type': DecisionType.CALL_DECISION if trade.direction.value == 'CALL' else DecisionType.PUT_DECISION,
                            'confidence_score': trade.confidence,
                            'convergence_score': 0.75,  # قيمة افتراضية
                            'trade_order': trade,
                            'timestamp': trade.execution_time
                        }
                        decisions.append(decision_data)
            
            return decisions
            
        except Exception as e:
            logger.error(f"خطأ في جمع قرارات التداول: {str(e)}")
            return []

    async def _collect_ai_decisions(self) -> List[Dict[str, Any]]:
        """جمع قرارات الذكاء الاصطناعي"""
        try:
            decisions = []
            
            # جمع إحصائيات الذكاء الاصطناعي
            ai_stats = ai_predictor_engine.performance_stats.copy()
            
            # محاكاة قرارات ذكاء اصطناعي حديثة
            for asset in self.currency_pairs[:3]:  # أول 3 أصول للاختبار
                decision_data = {
                    'type': 'ai_prediction',
                    'asset': asset,
                    'decision_type': DecisionType.PUT_DECISION,
                    'confidence_score': 0.85,
                    'convergence_score': 0.70,
                    'ai_models': {
                        'xgboost': {'prediction': 'PUT', 'confidence': 0.8},
                        'lstm': {'prediction': 'PUT', 'confidence': 0.9},
                        'random_forest': {'prediction': 'NEUTRAL', 'confidence': 0.6}
                    },
                    'timestamp': datetime.now()
                }
                decisions.append(decision_data)
            
            return decisions
            
        except Exception as e:
            logger.error(f"خطأ في جمع قرارات الذكاء الاصطناعي: {str(e)}")
            return []

    async def _process_new_decision(self, decision_data: Dict[str, Any]):
        """معالجة قرار جديد"""
        try:
            # إنشاء معرف فريد للقرار
            decision_id = f"{decision_data['type']}_{decision_data['asset']}_{int(time.time())}"
            
            # إنشاء سجل القرار
            decision_record = DecisionRecord(
                decision_id=decision_id,
                asset=decision_data['asset'],
                decision_type=decision_data['decision_type'],
                confidence_score=decision_data['confidence_score'],
                convergence_score=decision_data['convergence_score'],
                layer_signals=decision_data.get('layer_signals', {}),
                timestamp=decision_data['timestamp']
            )
            
            # إضافة معلومات إضافية
            if 'trade_order' in decision_data:
                decision_record.executed = True
                decision_record.execution_result = decision_data['trade_order']
            
            # حفظ السجل
            self.decision_records[decision_id] = decision_record
            
            # تنظيم حسب الأصل
            if decision_data['asset'] not in self.asset_decisions:
                self.asset_decisions[decision_data['asset']] = []
            self.asset_decisions[decision_data['asset']].append(decision_id)
            
            # إضافة للتحقق المعلق
            self.pending_verifications.append(decision_id)
            
            logger.debug(f"تم معالجة قرار جديد: {decision_id}")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة القرار الجديد: {str(e)}")

    async def _verify_pending_decisions(self):
        """التحقق من نتائج القرارات المعلقة"""
        try:
            verified_decisions = []
            
            for decision_id in self.pending_verifications.copy():
                decision = self.decision_records.get(decision_id)
                if not decision:
                    continue
                
                # التحقق من انتهاء مهلة التحقق
                time_elapsed = (datetime.now() - decision.timestamp).total_seconds() / 60
                if time_elapsed >= self.monitoring_settings['verification_timeout_minutes']:
                    
                    # محاولة التحقق من النتيجة
                    verification_result = await self._verify_decision_outcome(decision)
                    
                    if verification_result['verified']:
                        decision.actual_outcome = verification_result['outcome']
                        decision.verification_time = datetime.now()
                        verified_decisions.append(decision_id)
                        
                        logger.debug(f"تم التحقق من القرار {decision_id}: {verification_result['outcome']}")
            
            # إزالة القرارات المتحقق منها
            for decision_id in verified_decisions:
                if decision_id in self.pending_verifications:
                    self.pending_verifications.remove(decision_id)
            
            logger.debug(f"تم التحقق من {len(verified_decisions)} قرار")
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من القرارات المعلقة: {str(e)}")

    async def _verify_decision_outcome(self, decision: DecisionRecord) -> Dict[str, Any]:
        """التحقق من نتيجة قرار واحد"""
        try:
            # إذا كان القرار منفذ كصفقة
            if decision.executed and decision.execution_result:
                trade = decision.execution_result
                
                # البحث عن نتيجة الصفقة
                for executed_trade in real_trading_executor.executed_trades:
                    if (hasattr(executed_trade, 'trade_id') and 
                        hasattr(trade, 'trade_id') and 
                        executed_trade.trade_id == trade.trade_id):
                        
                        if executed_trade.status == TradeStatus.WON:
                            return {'verified': True, 'outcome': 'correct'}
                        elif executed_trade.status == TradeStatus.LOST:
                            return {'verified': True, 'outcome': 'incorrect'}
            
            # محاكاة التحقق للقرارات غير المنفذة
            # في التطبيق الفعلي، سيتم مقارنة القرار مع حركة السعر الفعلية
            import random
            simulated_outcome = 'correct' if random.random() > 0.4 else 'incorrect'
            
            return {'verified': True, 'outcome': simulated_outcome}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من نتيجة القرار: {str(e)}")
            return {'verified': False, 'outcome': 'unknown'}

    async def _assess_decision_quality(self):
        """تقييم جودة القرارات"""
        try:
            assessed_count = 0

            for decision_id, decision in self.decision_records.items():
                if decision.actual_outcome and not decision.quality_level:
                    # حساب نقاط الجودة
                    quality_score = await self._calculate_quality_score(decision)
                    decision.quality_score = quality_score

                    # تحديد مستوى الجودة
                    decision.quality_level = self._determine_quality_level(quality_score)

                    # حساب عوامل الجودة
                    decision.quality_factors = await self._analyze_quality_factors(decision)

                    assessed_count += 1

            logger.debug(f"تم تقييم جودة {assessed_count} قرار")

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة القرارات: {str(e)}")

    async def _calculate_quality_score(self, decision: DecisionRecord) -> float:
        """حساب نقاط جودة القرار"""
        try:
            score = 0.0

            # نقاط النتيجة (30%)
            outcome_score = 1.0 if decision.actual_outcome == 'correct' else 0.0
            score += outcome_score * self.monitoring_settings['outcome_weight']

            # نقاط الثقة (40%)
            confidence_score = decision.confidence_score
            score += confidence_score * self.monitoring_settings['confidence_weight']

            # نقاط التقارب (30%)
            convergence_score = decision.convergence_score
            score += convergence_score * self.monitoring_settings['convergence_weight']

            # مكافآت إضافية
            if decision.confidence_score > 0.8 and decision.actual_outcome == 'correct':
                score += 0.1  # مكافأة للثقة العالية مع النتيجة الصحيحة

            if decision.convergence_score > 0.8 and decision.actual_outcome == 'correct':
                score += 0.1  # مكافأة للتقارب العالي مع النتيجة الصحيحة

            return min(1.0, score)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الجودة: {str(e)}")
            return 0.0

    def _determine_quality_level(self, quality_score: float) -> DecisionQuality:
        """تحديد مستوى جودة القرار"""
        try:
            thresholds = self.monitoring_settings['quality_thresholds']

            if quality_score >= thresholds['excellent']:
                return DecisionQuality.EXCELLENT
            elif quality_score >= thresholds['good']:
                return DecisionQuality.GOOD
            elif quality_score >= thresholds['average']:
                return DecisionQuality.AVERAGE
            elif quality_score >= thresholds['poor']:
                return DecisionQuality.POOR
            else:
                return DecisionQuality.VERY_POOR

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى الجودة: {str(e)}")
            return DecisionQuality.VERY_POOR

    async def _analyze_quality_factors(self, decision: DecisionRecord) -> Dict[str, float]:
        """تحليل عوامل جودة القرار"""
        try:
            factors = {}

            # عامل دقة التنبؤ
            factors['prediction_accuracy'] = 1.0 if decision.actual_outcome == 'correct' else 0.0

            # عامل مستوى الثقة
            factors['confidence_level'] = decision.confidence_score

            # عامل التقارب
            factors['convergence_level'] = decision.convergence_score

            # عامل توافق الطبقات
            if decision.layer_signals:
                layer_agreement = self._calculate_layer_agreement(decision.layer_signals)
                factors['layer_agreement'] = layer_agreement

            # عامل التوقيت
            if decision.verification_time and decision.timestamp:
                verification_delay = (decision.verification_time - decision.timestamp).total_seconds() / 60
                factors['timing_factor'] = max(0.0, 1.0 - (verification_delay / 60))  # تقليل النقاط مع التأخير

            # عامل ظروف السوق
            factors['market_conditions'] = await self._assess_market_conditions_factor(decision)

            return factors

        except Exception as e:
            logger.error(f"خطأ في تحليل عوامل الجودة: {str(e)}")
            return {}

    def _calculate_layer_agreement(self, layer_signals: Dict[str, Any]) -> float:
        """حساب مستوى توافق الطبقات"""
        try:
            if not layer_signals:
                return 0.0

            signals = []
            for layer, signal_data in layer_signals.items():
                if isinstance(signal_data, dict) and 'signal' in signal_data:
                    signals.append(signal_data['signal'])

            if not signals:
                return 0.0

            # حساب نسبة التوافق
            most_common_signal = max(set(signals), key=signals.count)
            agreement_count = signals.count(most_common_signal)
            agreement_ratio = agreement_count / len(signals)

            return agreement_ratio

        except Exception as e:
            logger.error(f"خطأ في حساب توافق الطبقات: {str(e)}")
            return 0.0

    async def _assess_market_conditions_factor(self, decision: DecisionRecord) -> float:
        """تقييم عامل ظروف السوق"""
        try:
            # محاكاة تقييم ظروف السوق
            # في التطبيق الفعلي، سيتم تحليل التقلبات والاتجاهات

            # عوامل افتراضية
            volatility_factor = 0.8  # تقلبات معتدلة
            trend_factor = 0.7       # اتجاه واضح
            volume_factor = 0.9      # حجم تداول جيد

            market_score = (volatility_factor + trend_factor + volume_factor) / 3
            return market_score

        except Exception as e:
            logger.error(f"خطأ في تقييم ظروف السوق: {str(e)}")
            return 0.5

    async def _update_quality_metrics(self):
        """تحديث مقاييس الجودة"""
        try:
            # إعادة تعيين المقاييس
            self.quality_metrics = QualityMetrics()

            # حساب المقاييس العامة
            for decision in self.decision_records.values():
                if decision.actual_outcome:
                    self.quality_metrics.total_decisions += 1

                    if decision.actual_outcome == 'correct':
                        self.quality_metrics.correct_decisions += 1
                    elif decision.actual_outcome == 'incorrect':
                        self.quality_metrics.incorrect_decisions += 1
                    else:
                        self.quality_metrics.pending_decisions += 1

                    # تحديث حسب نوع القرار
                    if decision.decision_type == DecisionType.CALL_DECISION:
                        if decision.actual_outcome == 'correct':
                            self.quality_metrics.call_accuracy += 1
                    elif decision.decision_type == DecisionType.PUT_DECISION:
                        if decision.actual_outcome == 'correct':
                            self.quality_metrics.put_accuracy += 1

                    # تحديث حسب مستوى الجودة
                    if decision.quality_level:
                        if decision.quality_level == DecisionQuality.EXCELLENT:
                            self.quality_metrics.excellent_decisions += 1
                        elif decision.quality_level == DecisionQuality.GOOD:
                            self.quality_metrics.good_decisions += 1
                        elif decision.quality_level == DecisionQuality.AVERAGE:
                            self.quality_metrics.average_decisions += 1
                        elif decision.quality_level == DecisionQuality.POOR:
                            self.quality_metrics.poor_decisions += 1
                        elif decision.quality_level == DecisionQuality.VERY_POOR:
                            self.quality_metrics.very_poor_decisions += 1

            # حساب النسب
            if self.quality_metrics.total_decisions > 0:
                self.quality_metrics.overall_accuracy = (
                    self.quality_metrics.correct_decisions / self.quality_metrics.total_decisions
                )

            # تحديث مقاييس الأصول
            await self._update_asset_quality_metrics()

            self.quality_metrics.last_update = datetime.now()

        except Exception as e:
            logger.error(f"خطأ في تحديث مقاييس الجودة: {str(e)}")

    async def _update_asset_quality_metrics(self):
        """تحديث مقاييس جودة الأصول"""
        try:
            self.asset_quality_metrics = {}

            for asset, decision_ids in self.asset_decisions.items():
                asset_metrics = QualityMetrics()

                for decision_id in decision_ids:
                    decision = self.decision_records.get(decision_id)
                    if decision and decision.actual_outcome:
                        asset_metrics.total_decisions += 1

                        if decision.actual_outcome == 'correct':
                            asset_metrics.correct_decisions += 1
                        elif decision.actual_outcome == 'incorrect':
                            asset_metrics.incorrect_decisions += 1

                # حساب دقة الأصل
                if asset_metrics.total_decisions > 0:
                    asset_metrics.overall_accuracy = (
                        asset_metrics.correct_decisions / asset_metrics.total_decisions
                    )

                self.asset_quality_metrics[asset] = asset_metrics

        except Exception as e:
            logger.error(f"خطأ في تحديث مقاييس جودة الأصول: {str(e)}")

    async def _analyze_decision_patterns(self):
        """تحليل أنماط القرارات"""
        try:
            # تحليل الأنماط الزمنية
            hourly_patterns = await self._analyze_hourly_patterns()

            # تحليل أنماط الثقة
            confidence_patterns = await self._analyze_confidence_patterns()

            # تحليل أنماط التقارب
            convergence_patterns = await self._analyze_convergence_patterns()

            # حفظ نتائج التحليل
            pattern_analysis = {
                'hourly_patterns': hourly_patterns,
                'confidence_patterns': confidence_patterns,
                'convergence_patterns': convergence_patterns,
                'analysis_timestamp': datetime.now().isoformat()
            }

            # يمكن حفظ هذا التحليل للاستخدام في التحسينات المستقبلية
            logger.debug("تم تحليل أنماط القرارات")

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط القرارات: {str(e)}")

    async def _analyze_hourly_patterns(self) -> Dict[str, Any]:
        """تحليل الأنماط الزمنية للقرارات"""
        try:
            hourly_stats = {}

            for decision in self.decision_records.values():
                if decision.actual_outcome:
                    hour = decision.timestamp.hour

                    if hour not in hourly_stats:
                        hourly_stats[hour] = {'total': 0, 'correct': 0}

                    hourly_stats[hour]['total'] += 1
                    if decision.actual_outcome == 'correct':
                        hourly_stats[hour]['correct'] += 1

            # حساب دقة كل ساعة
            for hour, stats in hourly_stats.items():
                if stats['total'] > 0:
                    stats['accuracy'] = stats['correct'] / stats['total']
                else:
                    stats['accuracy'] = 0.0

            return hourly_stats

        except Exception as e:
            logger.error(f"خطأ في تحليل الأنماط الزمنية: {str(e)}")
            return {}

    async def _analyze_confidence_patterns(self) -> Dict[str, Any]:
        """تحليل أنماط الثقة"""
        try:
            confidence_ranges = {
                'high': {'min': 0.8, 'max': 1.0, 'total': 0, 'correct': 0},
                'medium': {'min': 0.6, 'max': 0.8, 'total': 0, 'correct': 0},
                'low': {'min': 0.0, 'max': 0.6, 'total': 0, 'correct': 0}
            }

            for decision in self.decision_records.values():
                if decision.actual_outcome:
                    confidence = decision.confidence_score

                    for range_name, range_data in confidence_ranges.items():
                        if range_data['min'] <= confidence < range_data['max']:
                            range_data['total'] += 1
                            if decision.actual_outcome == 'correct':
                                range_data['correct'] += 1
                            break

            # حساب دقة كل نطاق ثقة
            for range_data in confidence_ranges.values():
                if range_data['total'] > 0:
                    range_data['accuracy'] = range_data['correct'] / range_data['total']
                else:
                    range_data['accuracy'] = 0.0

            return confidence_ranges

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الثقة: {str(e)}")
            return {}

    async def _analyze_convergence_patterns(self) -> Dict[str, Any]:
        """تحليل أنماط التقارب"""
        try:
            convergence_ranges = {
                'high': {'min': 0.8, 'max': 1.0, 'total': 0, 'correct': 0},
                'medium': {'min': 0.6, 'max': 0.8, 'total': 0, 'correct': 0},
                'low': {'min': 0.0, 'max': 0.6, 'total': 0, 'correct': 0}
            }

            for decision in self.decision_records.values():
                if decision.actual_outcome:
                    convergence = decision.convergence_score

                    for range_name, range_data in convergence_ranges.items():
                        if range_data['min'] <= convergence < range_data['max']:
                            range_data['total'] += 1
                            if decision.actual_outcome == 'correct':
                                range_data['correct'] += 1
                            break

            # حساب دقة كل نطاق تقارب
            for range_data in convergence_ranges.values():
                if range_data['total'] > 0:
                    range_data['accuracy'] = range_data['correct'] / range_data['total']
                else:
                    range_data['accuracy'] = 0.0

            return convergence_ranges

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط التقارب: {str(e)}")
            return {}

    async def _save_monitoring_results(self):
        """حفظ نتائج المراقبة"""
        try:
            # إعداد البيانات للحفظ
            monitoring_data = {
                'timestamp': datetime.now().isoformat(),
                'quality_metrics': {
                    'total_decisions': self.quality_metrics.total_decisions,
                    'correct_decisions': self.quality_metrics.correct_decisions,
                    'incorrect_decisions': self.quality_metrics.incorrect_decisions,
                    'overall_accuracy': self.quality_metrics.overall_accuracy,
                    'excellent_decisions': self.quality_metrics.excellent_decisions,
                    'good_decisions': self.quality_metrics.good_decisions,
                    'average_decisions': self.quality_metrics.average_decisions,
                    'poor_decisions': self.quality_metrics.poor_decisions,
                    'very_poor_decisions': self.quality_metrics.very_poor_decisions
                },
                'asset_metrics': {},
                'system_stats': self.system_stats.copy()
            }

            # إضافة مقاييس الأصول
            for asset, metrics in self.asset_quality_metrics.items():
                monitoring_data['asset_metrics'][asset] = {
                    'total_decisions': metrics.total_decisions,
                    'overall_accuracy': metrics.overall_accuracy
                }

            # حفظ في ملف JSON
            with open('logs/decision_quality_monitoring.json', 'w', encoding='utf-8') as f:
                json.dump(monitoring_data, f, ensure_ascii=False, indent=2)

            logger.debug("تم حفظ نتائج مراقبة جودة القرارات")

        except Exception as e:
            logger.error(f"خطأ في حفظ نتائج المراقبة: {str(e)}")

    def _update_system_stats(self, monitoring_duration: float):
        """تحديث إحصائيات النظام"""
        try:
            self.system_stats['total_monitoring_cycles'] += 1
            self.system_stats['last_monitoring_time'] = datetime.now()

            # تحديث متوسط مدة المراقبة
            current_avg = self.system_stats['monitoring_duration_avg']
            total_cycles = self.system_stats['total_monitoring_cycles']

            new_avg = ((current_avg * (total_cycles - 1)) + monitoring_duration) / total_cycles
            self.system_stats['monitoring_duration_avg'] = new_avg

            self.system_stats['decisions_monitored'] = len(self.decision_records)
            self.system_stats['verifications_completed'] = len([
                d for d in self.decision_records.values() if d.actual_outcome
            ])
            self.system_stats['quality_assessments'] = len([
                d for d in self.decision_records.values() if d.quality_level
            ])

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات النظام: {str(e)}")

    async def get_quality_report(self, asset: str = None) -> Dict[str, Any]:
        """الحصول على تقرير جودة القرارات"""
        try:
            if asset and asset in self.asset_quality_metrics:
                # تقرير أصل محدد
                asset_metrics = self.asset_quality_metrics[asset]
                asset_decisions = [
                    self.decision_records[decision_id]
                    for decision_id in self.asset_decisions.get(asset, [])
                    if decision_id in self.decision_records
                ]

                return {
                    'asset': asset,
                    'total_decisions': asset_metrics.total_decisions,
                    'overall_accuracy': asset_metrics.overall_accuracy,
                    'recent_decisions': len([d for d in asset_decisions if d.actual_outcome]),
                    'quality_distribution': self._get_asset_quality_distribution(asset_decisions),
                    'recommendations': self._generate_asset_recommendations(asset, asset_decisions)
                }

            # تقرير شامل
            return {
                'overall_metrics': {
                    'total_decisions': self.quality_metrics.total_decisions,
                    'overall_accuracy': self.quality_metrics.overall_accuracy,
                    'correct_decisions': self.quality_metrics.correct_decisions,
                    'incorrect_decisions': self.quality_metrics.incorrect_decisions
                },
                'quality_distribution': {
                    'excellent': self.quality_metrics.excellent_decisions,
                    'good': self.quality_metrics.good_decisions,
                    'average': self.quality_metrics.average_decisions,
                    'poor': self.quality_metrics.poor_decisions,
                    'very_poor': self.quality_metrics.very_poor_decisions
                },
                'system_stats': self.system_stats.copy(),
                'monitoring_active': self.is_monitoring,
                'last_update': self.quality_metrics.last_update,
                'top_performing_assets': self._get_top_performing_assets(),
                'recommendations': self._generate_system_recommendations()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على تقرير الجودة: {str(e)}")
            return {'error': str(e)}

    def _get_asset_quality_distribution(self, decisions: List[DecisionRecord]) -> Dict[str, int]:
        """الحصول على توزيع جودة قرارات الأصل"""
        try:
            distribution = {
                'excellent': 0,
                'good': 0,
                'average': 0,
                'poor': 0,
                'very_poor': 0
            }

            for decision in decisions:
                if decision.quality_level:
                    quality_key = decision.quality_level.value
                    if quality_key in distribution:
                        distribution[quality_key] += 1

            return distribution

        except Exception as e:
            logger.error(f"خطأ في حساب توزيع الجودة: {str(e)}")
            return {}

    def _generate_asset_recommendations(self, asset: str, decisions: List[DecisionRecord]) -> List[str]:
        """توليد توصيات للأصل"""
        try:
            recommendations = []

            if not decisions:
                recommendations.append(f"لا توجد قرارات كافية لـ {asset} - انتظر المزيد من البيانات")
                return recommendations

            # تحليل الأداء
            correct_decisions = [d for d in decisions if d.actual_outcome == 'correct']
            accuracy = len(correct_decisions) / len(decisions) if decisions else 0

            if accuracy < 0.5:
                recommendations.append(f"دقة منخفضة لـ {asset} ({accuracy:.1%}) - راجع معايير التحليل")

            # تحليل الثقة
            avg_confidence = sum(d.confidence_score for d in decisions) / len(decisions)
            if avg_confidence < 0.7:
                recommendations.append(f"متوسط ثقة منخفض لـ {asset} - تحسين نماذج التنبؤ مطلوب")

            # تحليل التقارب
            avg_convergence = sum(d.convergence_score for d in decisions) / len(decisions)
            if avg_convergence < 0.7:
                recommendations.append(f"تقارب ضعيف لـ {asset} - راجع توافق الطبقات")

            if not recommendations:
                recommendations.append(f"أداء جيد لـ {asset} - استمر في المراقبة")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في توليد توصيات الأصل: {str(e)}")
            return ["خطأ في توليد التوصيات"]

    def _get_top_performing_assets(self) -> List[Tuple[str, float]]:
        """الحصول على أفضل الأصول أداءً"""
        try:
            asset_performance = []

            for asset, metrics in self.asset_quality_metrics.items():
                if metrics.total_decisions >= 3:  # حد أدنى من القرارات
                    asset_performance.append((asset, metrics.overall_accuracy))

            # ترتيب حسب الدقة
            asset_performance.sort(key=lambda x: x[1], reverse=True)

            return asset_performance[:10]  # أفضل 10 أصول

        except Exception as e:
            logger.error(f"خطأ في الحصول على أفضل الأصول: {str(e)}")
            return []

    def _generate_system_recommendations(self) -> List[str]:
        """توليد توصيات النظام"""
        try:
            recommendations = []

            # تحليل الأداء العام
            if self.quality_metrics.overall_accuracy < 0.6:
                recommendations.append("دقة النظام العامة منخفضة - راجع جميع طبقات التحليل")

            # تحليل توزيع الجودة
            total_decisions = self.quality_metrics.total_decisions
            if total_decisions > 0:
                poor_ratio = (self.quality_metrics.poor_decisions + self.quality_metrics.very_poor_decisions) / total_decisions
                if poor_ratio > 0.3:
                    recommendations.append("نسبة عالية من القرارات ضعيفة الجودة - تحسين المعايير مطلوب")

            # تحليل عدد القرارات
            if total_decisions < 10:
                recommendations.append("عدد القرارات قليل - انتظر المزيد من البيانات للتحليل الدقيق")

            if not recommendations:
                recommendations.append("جودة القرارات مقبولة - استمر في المراقبة والتحسين")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في توليد توصيات النظام: {str(e)}")
            return ["خطأ في توليد التوصيات"]

    async def stop_monitoring(self):
        """إيقاف مراقبة جودة القرارات"""
        try:
            self.is_monitoring = False
            logger.info("تم إيقاف مراقب جودة القرارات")

        except Exception as e:
            logger.error(f"خطأ في إيقاف المراقبة: {str(e)}")

    async def record_manual_decision(self, asset: str, decision_type: DecisionType,
                                   confidence: float, convergence: float,
                                   layer_signals: Dict[str, Any] = None) -> str:
        """تسجيل قرار يدوي للمراقبة"""
        try:
            decision_id = f"manual_{asset}_{int(time.time())}"

            decision_record = DecisionRecord(
                decision_id=decision_id,
                asset=asset,
                decision_type=decision_type,
                confidence_score=confidence,
                convergence_score=convergence,
                layer_signals=layer_signals or {},
                timestamp=datetime.now()
            )

            # حفظ السجل
            self.decision_records[decision_id] = decision_record

            # تنظيم حسب الأصل
            if asset not in self.asset_decisions:
                self.asset_decisions[asset] = []
            self.asset_decisions[asset].append(decision_id)

            # إضافة للتحقق المعلق
            self.pending_verifications.append(decision_id)

            logger.info(f"تم تسجيل قرار يدوي: {decision_id}")
            return decision_id

        except Exception as e:
            logger.error(f"خطأ في تسجيل القرار اليدوي: {str(e)}")
            return ""

# إنشاء مثيل عام
decision_quality_monitor = DecisionQualityMonitor()
