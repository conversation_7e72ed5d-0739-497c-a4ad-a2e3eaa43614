"""
نظام التقارير التفصيلية المتكامل
Detailed Reporting System - Comprehensive Layer Performance Reports
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import base64

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70

# استيراد أنظمة المراقبة والإحصائيات
from execution.advanced_performance_monitor import advanced_performance_monitor
from execution.performance_statistics_system import performance_statistics_system
from execution.strategy_performance_analyzer import strategy_performance_analyzer
from execution.decision_quality_monitor import decision_quality_monitor
from execution.advanced_operations_logger import advanced_operations_logger

# استيراد طبقات النظام
from data_layer.quadruple_convergence_system import quadruple_convergence_system
from data_layer.technical_signals_engine import technical_signals_engine
from data_layer.realtime_behavior_analyzer import realtime_behavior_analyzer
from ai_models.ai_predictor_engine import ai_predictor_engine
from ai_models.unified_prediction_interface import unified_prediction_interface
from execution.real_trading_executor import real_trading_executor

logger = scalping_logger.get_logger("detailed_reporting_system")

class ReportFormat(Enum):
    """تنسيقات التقارير"""
    JSON = "json"
    HTML = "html"
    TEXT = "text"
    CSV = "csv"

class ReportType(Enum):
    """أنواع التقارير"""
    COMPREHENSIVE = "comprehensive"
    LAYER_SPECIFIC = "layer_specific"
    ASSET_SPECIFIC = "asset_specific"
    PERFORMANCE_SUMMARY = "performance_summary"
    ERROR_ANALYSIS = "error_analysis"
    TRADING_SUMMARY = "trading_summary"

class ReportPeriod(Enum):
    """فترات التقارير"""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"

@dataclass
class LayerPerformanceData:
    """بيانات أداء طبقة واحدة"""
    layer_name: str
    total_analyses: int = 0
    successful_analyses: int = 0
    failed_analyses: int = 0
    success_rate: float = 0.0
    avg_processing_time: float = 0.0
    accuracy_rate: float = 0.0
    
    # إحصائيات مفصلة
    signal_distribution: Dict[str, int] = field(default_factory=dict)
    confidence_distribution: Dict[str, int] = field(default_factory=dict)
    error_types: Dict[str, int] = field(default_factory=dict)
    performance_trends: List[float] = field(default_factory=list)
    
    # معلومات إضافية
    last_update: Optional[datetime] = None
    recommendations: List[str] = field(default_factory=list)
    critical_issues: List[str] = field(default_factory=list)

@dataclass
class ComprehensiveReport:
    """التقرير الشامل"""
    report_id: str
    generation_time: datetime
    report_period: ReportPeriod
    period_start: datetime
    period_end: datetime
    
    # أداء الطبقات
    technical_layer: LayerPerformanceData
    behavioral_layer: LayerPerformanceData
    quantitative_layer: LayerPerformanceData
    ai_layer: LayerPerformanceData
    
    # إحصائيات عامة
    system_overview: Dict[str, Any] = field(default_factory=dict)
    trading_performance: Dict[str, Any] = field(default_factory=dict)
    decision_quality: Dict[str, Any] = field(default_factory=dict)
    
    # تحليلات متقدمة
    convergence_analysis: Dict[str, Any] = field(default_factory=dict)
    performance_comparison: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    
    # معلومات التقرير
    total_assets_analyzed: int = 0
    report_size_mb: float = 0.0
    generation_duration_seconds: float = 0.0

class DetailedReportingSystem:
    """نظام التقارير التفصيلية المتكامل"""
    
    def __init__(self):
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.reports_directory = "reports"
        self.is_generating = False
        
        # إعدادات التقارير
        self.report_settings = {
            'auto_generation_enabled': True,
            'auto_generation_interval_hours': 24,
            'max_report_history': 30,
            'include_charts': True,
            'include_raw_data': False,
            'compression_enabled': True
        }
        
        # إحصائيات النظام
        self.system_stats = {
            'total_reports_generated': 0,
            'last_report_time': None,
            'avg_generation_time': 0.0,
            'reports_by_type': {},
            'total_data_processed_mb': 0.0
        }
        
        # تهيئة مجلد التقارير
        self._initialize_reports_directory()
        
        logger.info("تم تهيئة نظام التقارير التفصيلية")

    def _initialize_reports_directory(self):
        """تهيئة مجلد التقارير"""
        try:
            os.makedirs(self.reports_directory, exist_ok=True)
            os.makedirs(f"{self.reports_directory}/comprehensive", exist_ok=True)
            os.makedirs(f"{self.reports_directory}/layer_specific", exist_ok=True)
            os.makedirs(f"{self.reports_directory}/asset_specific", exist_ok=True)
            os.makedirs(f"{self.reports_directory}/archives", exist_ok=True)
            
            logger.debug("تم تهيئة مجلدات التقارير")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة مجلدات التقارير: {str(e)}")

    @handle_async_errors(default_return=None, log_error=True)
    async def generate_comprehensive_report(self, period: ReportPeriod = ReportPeriod.DAILY,
                                          custom_start: datetime = None,
                                          custom_end: datetime = None,
                                          formats: List[ReportFormat] = None) -> Optional[str]:
        """إنشاء تقرير شامل"""
        try:
            if self.is_generating:
                logger.warning("يتم إنشاء تقرير آخر حالياً")
                return None
            
            self.is_generating = True
            generation_start = datetime.now()
            
            logger.info(f"🚀 بدء إنشاء التقرير الشامل - فترة: {period.value}")
            
            # تحديد فترة التقرير
            period_start, period_end = self._calculate_report_period(period, custom_start, custom_end)
            
            # إنشاء معرف التقرير
            report_id = f"comprehensive_{period.value}_{generation_start.strftime('%Y%m%d_%H%M%S')}"
            
            # جمع البيانات من جميع الطبقات
            logger.info("📊 جمع بيانات الأداء من جميع الطبقات...")
            
            technical_data = await self._collect_technical_layer_data(period_start, period_end)
            behavioral_data = await self._collect_behavioral_layer_data(period_start, period_end)
            quantitative_data = await self._collect_quantitative_layer_data(period_start, period_end)
            ai_data = await self._collect_ai_layer_data(period_start, period_end)
            
            # جمع البيانات العامة
            system_overview = await self._collect_system_overview(period_start, period_end)
            trading_performance = await self._collect_trading_performance(period_start, period_end)
            decision_quality = await self._collect_decision_quality_data(period_start, period_end)
            
            # تحليلات متقدمة
            convergence_analysis = await self._analyze_convergence_patterns(period_start, period_end)
            performance_comparison = await self._compare_layer_performance(
                technical_data, behavioral_data, quantitative_data, ai_data
            )
            
            # توليد التوصيات
            recommendations = await self._generate_comprehensive_recommendations(
                technical_data, behavioral_data, quantitative_data, ai_data,
                system_overview, trading_performance, decision_quality
            )
            
            # إنشاء التقرير
            generation_duration = (datetime.now() - generation_start).total_seconds()
            
            comprehensive_report = ComprehensiveReport(
                report_id=report_id,
                generation_time=generation_start,
                report_period=period,
                period_start=period_start,
                period_end=period_end,
                technical_layer=technical_data,
                behavioral_layer=behavioral_data,
                quantitative_layer=quantitative_data,
                ai_layer=ai_data,
                system_overview=system_overview,
                trading_performance=trading_performance,
                decision_quality=decision_quality,
                convergence_analysis=convergence_analysis,
                performance_comparison=performance_comparison,
                recommendations=recommendations,
                total_assets_analyzed=len(self.currency_pairs),
                generation_duration_seconds=generation_duration
            )
            
            # حفظ التقرير بالتنسيقات المطلوبة
            if formats is None:
                formats = [ReportFormat.JSON, ReportFormat.HTML]
            
            saved_files = []
            for format_type in formats:
                file_path = await self._save_report(comprehensive_report, format_type)
                if file_path:
                    saved_files.append(file_path)
            
            # تحديث الإحصائيات
            self._update_system_stats(report_id, generation_duration, saved_files)
            
            logger.info(f"✅ تم إنشاء التقرير الشامل بنجاح: {report_id}")
            logger.info(f"📁 الملفات المحفوظة: {len(saved_files)}")
            
            return report_id
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الشامل: {str(e)}")
            return None
        finally:
            self.is_generating = False

    def _calculate_report_period(self, period: ReportPeriod, 
                               custom_start: datetime = None,
                               custom_end: datetime = None) -> tuple:
        """حساب فترة التقرير"""
        try:
            end_time = custom_end or datetime.now()
            
            if period == ReportPeriod.HOURLY:
                start_time = end_time - timedelta(hours=1)
            elif period == ReportPeriod.DAILY:
                start_time = end_time - timedelta(days=1)
            elif period == ReportPeriod.WEEKLY:
                start_time = end_time - timedelta(weeks=1)
            elif period == ReportPeriod.MONTHLY:
                start_time = end_time - timedelta(days=30)
            elif period == ReportPeriod.CUSTOM:
                start_time = custom_start or (end_time - timedelta(days=1))
            else:
                start_time = end_time - timedelta(days=1)
            
            return start_time, end_time
            
        except Exception as e:
            logger.error(f"خطأ في حساب فترة التقرير: {str(e)}")
            return datetime.now() - timedelta(days=1), datetime.now()

    async def _collect_technical_layer_data(self, start_time: datetime, end_time: datetime) -> LayerPerformanceData:
        """جمع بيانات طبقة التحليل الفني"""
        try:
            logger.debug("جمع بيانات طبقة التحليل الفني...")
            
            # جمع إحصائيات من محرك الإشارات الفنية
            technical_stats = technical_signals_engine.get_performance_statistics()
            
            # إنشاء بيانات الأداء
            technical_data = LayerPerformanceData(
                layer_name="Technical Analysis",
                total_analyses=technical_stats.get('total_analyses', 0),
                successful_analyses=technical_stats.get('successful_analyses', 0),
                success_rate=technical_stats.get('success_rate', 0.0),
                avg_processing_time=technical_stats.get('avg_processing_time', 0.0),
                accuracy_rate=technical_stats.get('accuracy_rate', 0.0),
                last_update=datetime.now()
            )
            
            # حساب الإحصائيات المشتقة
            if technical_data.total_analyses > 0:
                technical_data.failed_analyses = technical_data.total_analyses - technical_data.successful_analyses
                technical_data.success_rate = technical_data.successful_analyses / technical_data.total_analyses
            
            # جمع توزيع الإشارات
            technical_data.signal_distribution = technical_stats.get('signal_distribution', {
                'CALL': 0, 'PUT': 0, 'NEUTRAL': 0
            })
            
            # جمع توزيع الثقة
            technical_data.confidence_distribution = technical_stats.get('confidence_distribution', {
                'high': 0, 'medium': 0, 'low': 0
            })
            
            # جمع أنواع الأخطاء
            technical_data.error_types = technical_stats.get('error_types', {})
            
            # توليد التوصيات
            technical_data.recommendations = self._generate_technical_recommendations(technical_data)
            
            # تحديد المشاكل الحرجة
            technical_data.critical_issues = self._identify_technical_critical_issues(technical_data)
            
            return technical_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع بيانات طبقة التحليل الفني: {str(e)}")
            return LayerPerformanceData(layer_name="Technical Analysis")

    async def _collect_behavioral_layer_data(self, start_time: datetime, end_time: datetime) -> LayerPerformanceData:
        """جمع بيانات طبقة التحليل السلوكي"""
        try:
            logger.debug("جمع بيانات طبقة التحليل السلوكي...")
            
            # جمع إحصائيات من محلل السلوك اللحظي
            behavioral_stats = realtime_behavior_analyzer.get_performance_statistics()
            
            behavioral_data = LayerPerformanceData(
                layer_name="Behavioral Analysis",
                total_analyses=behavioral_stats.get('total_analyses', 0),
                successful_analyses=behavioral_stats.get('successful_analyses', 0),
                success_rate=behavioral_stats.get('success_rate', 0.0),
                avg_processing_time=behavioral_stats.get('avg_processing_time', 0.0),
                accuracy_rate=behavioral_stats.get('accuracy_rate', 0.0),
                last_update=datetime.now()
            )
            
            # حساب الإحصائيات المشتقة
            if behavioral_data.total_analyses > 0:
                behavioral_data.failed_analyses = behavioral_data.total_analyses - behavioral_data.successful_analyses
                behavioral_data.success_rate = behavioral_data.successful_analyses / behavioral_data.total_analyses
            
            # جمع البيانات المفصلة
            behavioral_data.signal_distribution = behavioral_stats.get('signal_distribution', {})
            behavioral_data.confidence_distribution = behavioral_stats.get('confidence_distribution', {})
            behavioral_data.error_types = behavioral_stats.get('error_types', {})
            
            # توليد التوصيات والمشاكل
            behavioral_data.recommendations = self._generate_behavioral_recommendations(behavioral_data)
            behavioral_data.critical_issues = self._identify_behavioral_critical_issues(behavioral_data)
            
            return behavioral_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع بيانات طبقة التحليل السلوكي: {str(e)}")
            return LayerPerformanceData(layer_name="Behavioral Analysis")

    async def _collect_quantitative_layer_data(self, start_time: datetime, end_time: datetime) -> LayerPerformanceData:
        """جمع بيانات طبقة التحليل الكمي"""
        try:
            logger.debug("جمع بيانات طبقة التحليل الكمي...")
            
            # جمع إحصائيات من نظام الإحصائيات
            quantitative_stats = performance_statistics_system.get_system_statistics()
            
            quantitative_data = LayerPerformanceData(
                layer_name="Quantitative Analysis",
                total_analyses=quantitative_stats.get('total_analyses', 0),
                successful_analyses=quantitative_stats.get('successful_analyses', 0),
                success_rate=quantitative_stats.get('success_rate', 0.0),
                avg_processing_time=quantitative_stats.get('avg_processing_time', 0.0),
                accuracy_rate=quantitative_stats.get('accuracy_rate', 0.0),
                last_update=datetime.now()
            )
            
            # حساب الإحصائيات المشتقة
            if quantitative_data.total_analyses > 0:
                quantitative_data.failed_analyses = quantitative_data.total_analyses - quantitative_data.successful_analyses
                quantitative_data.success_rate = quantitative_data.successful_analyses / quantitative_data.total_analyses
            
            # جمع البيانات المفصلة
            quantitative_data.signal_distribution = quantitative_stats.get('signal_distribution', {})
            quantitative_data.confidence_distribution = quantitative_stats.get('confidence_distribution', {})
            quantitative_data.error_types = quantitative_stats.get('error_types', {})
            
            # توليد التوصيات والمشاكل
            quantitative_data.recommendations = self._generate_quantitative_recommendations(quantitative_data)
            quantitative_data.critical_issues = self._identify_quantitative_critical_issues(quantitative_data)
            
            return quantitative_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع بيانات طبقة التحليل الكمي: {str(e)}")
            return LayerPerformanceData(layer_name="Quantitative Analysis")

    async def _collect_ai_layer_data(self, start_time: datetime, end_time: datetime) -> LayerPerformanceData:
        """جمع بيانات طبقة الذكاء الاصطناعي"""
        try:
            logger.debug("جمع بيانات طبقة الذكاء الاصطناعي...")
            
            # جمع إحصائيات من محرك الذكاء الاصطناعي
            ai_stats = ai_predictor_engine.performance_stats.copy()
            
            ai_data = LayerPerformanceData(
                layer_name="Artificial Intelligence",
                total_analyses=ai_stats.get('total_predictions', 0),
                successful_analyses=ai_stats.get('successful_predictions', 0),
                success_rate=ai_stats.get('success_rate', 0.0),
                avg_processing_time=ai_stats.get('avg_processing_time', 0.0),
                accuracy_rate=ai_stats.get('overall_accuracy', 0.0),
                last_update=datetime.now()
            )
            
            # حساب الإحصائيات المشتقة
            if ai_data.total_analyses > 0:
                ai_data.failed_analyses = ai_data.total_analyses - ai_data.successful_analyses
                ai_data.success_rate = ai_data.successful_analyses / ai_data.total_analyses
            
            # جمع البيانات المفصلة
            ai_data.signal_distribution = ai_stats.get('prediction_distribution', {})
            ai_data.confidence_distribution = ai_stats.get('confidence_distribution', {})
            ai_data.error_types = ai_stats.get('error_types', {})
            
            # توليد التوصيات والمشاكل
            ai_data.recommendations = self._generate_ai_recommendations(ai_data)
            ai_data.critical_issues = self._identify_ai_critical_issues(ai_data)
            
            return ai_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع بيانات طبقة الذكاء الاصطناعي: {str(e)}")
            return LayerPerformanceData(layer_name="Artificial Intelligence")

    async def _collect_system_overview(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """جمع نظرة عامة على النظام"""
        try:
            # جمع إحصائيات من مراقب الأداء المتقدم
            performance_stats = advanced_performance_monitor.get_performance_statistics()

            # جمع إحصائيات العمليات
            operations_stats = advanced_operations_logger.get_operations_summary(24)

            system_overview = {
                'system_uptime': performance_stats.get('system_uptime', '99.9%'),
                'total_assets_monitored': len(self.currency_pairs),
                'active_connections': performance_stats.get('active_connections', 0),
                'memory_usage_mb': performance_stats.get('memory_usage_mb', 0),
                'cpu_usage_percent': performance_stats.get('cpu_usage_percent', 0),
                'disk_usage_percent': performance_stats.get('disk_usage_percent', 0),
                'network_latency_ms': performance_stats.get('network_latency_ms', 0),
                'total_operations': operations_stats.get('total_operations', 0),
                'successful_operations': operations_stats.get('successful_operations', 0),
                'failed_operations': operations_stats.get('failed_operations', 0),
                'error_rate_percent': operations_stats.get('error_rate_percent', 0.0),
                'data_processed_mb': performance_stats.get('data_processed_mb', 0.0),
                'last_update': datetime.now().isoformat()
            }

            return system_overview

        except Exception as e:
            logger.error(f"خطأ في جمع نظرة عامة على النظام: {str(e)}")
            return {}

    async def _collect_trading_performance(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """جمع بيانات أداء التداول"""
        try:
            # جمع إحصائيات من محرك التداول
            trading_stats = await real_trading_executor.get_trading_stats()

            # جمع إحصائيات من محلل أداء الاستراتيجية
            strategy_report = await strategy_performance_analyzer.get_performance_report()

            trading_performance = {
                'total_trades': trading_stats.get('total_trades', 0),
                'winning_trades': trading_stats.get('winning_trades', 0),
                'losing_trades': trading_stats.get('losing_trades', 0),
                'win_rate_percent': trading_stats.get('win_rate', 0.0) * 100,
                'total_profit': trading_stats.get('total_profit', 0.0),
                'profit_factor': trading_stats.get('profit_factor', 0.0),
                'max_drawdown': trading_stats.get('max_drawdown', 0.0),
                'sharpe_ratio': trading_stats.get('sharpe_ratio', 0.0),
                'avg_trade_duration_minutes': trading_stats.get('avg_trade_duration', 0.0),
                'daily_trade_count': trading_stats.get('daily_trade_count', 0),
                'monthly_profit': trading_stats.get('monthly_profit', 0.0),
                'best_performing_asset': strategy_report.get('best_performing_assets', [('N/A', 0.0)])[0][0] if strategy_report.get('best_performing_assets') else 'N/A',
                'worst_performing_asset': strategy_report.get('worst_performing_assets', [('N/A', 0.0)])[-1][0] if strategy_report.get('worst_performing_assets') else 'N/A',
                'last_update': datetime.now().isoformat()
            }

            return trading_performance

        except Exception as e:
            logger.error(f"خطأ في جمع بيانات أداء التداول: {str(e)}")
            return {}

    async def _collect_decision_quality_data(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """جمع بيانات جودة القرارات"""
        try:
            # جمع تقرير جودة القرارات
            quality_report = await decision_quality_monitor.get_quality_report()

            decision_quality = {
                'total_decisions': quality_report.get('overall_metrics', {}).get('total_decisions', 0),
                'correct_decisions': quality_report.get('overall_metrics', {}).get('correct_decisions', 0),
                'incorrect_decisions': quality_report.get('overall_metrics', {}).get('incorrect_decisions', 0),
                'overall_accuracy_percent': quality_report.get('overall_metrics', {}).get('overall_accuracy', 0.0) * 100,
                'excellent_decisions': quality_report.get('quality_distribution', {}).get('excellent', 0),
                'good_decisions': quality_report.get('quality_distribution', {}).get('good', 0),
                'average_decisions': quality_report.get('quality_distribution', {}).get('average', 0),
                'poor_decisions': quality_report.get('quality_distribution', {}).get('poor', 0),
                'very_poor_decisions': quality_report.get('quality_distribution', {}).get('very_poor', 0),
                'top_performing_assets': quality_report.get('top_performing_assets', []),
                'system_recommendations': quality_report.get('recommendations', []),
                'monitoring_active': quality_report.get('monitoring_active', False),
                'last_update': quality_report.get('last_update')
            }

            return decision_quality

        except Exception as e:
            logger.error(f"خطأ في جمع بيانات جودة القرارات: {str(e)}")
            return {}

    async def _analyze_convergence_patterns(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """تحليل أنماط التقارب"""
        try:
            # جمع إحصائيات التقارب
            convergence_stats = quadruple_convergence_system.stats.copy()

            convergence_analysis = {
                'total_convergence_analyses': convergence_stats.get('total_analyses', 0),
                'convergent_signals': convergence_stats.get('convergent_signals', 0),
                'divergent_signals': convergence_stats.get('divergent_signals', 0),
                'neutral_signals': convergence_stats.get('neutral_signals', 0),
                'convergence_rate_percent': 0.0,
                'layer_agreement_analysis': {},
                'signal_strength_distribution': {},
                'convergence_trends': [],
                'pattern_insights': []
            }

            # حساب معدل التقارب
            total_analyses = convergence_stats.get('total_analyses', 0)
            if total_analyses > 0:
                convergence_analysis['convergence_rate_percent'] = (
                    convergence_stats.get('convergent_signals', 0) / total_analyses
                ) * 100

            # تحليل توافق الطبقات
            layer_performance = convergence_stats.get('layer_performance', {})
            for layer, performance in layer_performance.items():
                total = performance.get('total', 0)
                correct = performance.get('correct', 0)
                accuracy = (correct / total * 100) if total > 0 else 0.0

                convergence_analysis['layer_agreement_analysis'][layer] = {
                    'total_signals': total,
                    'correct_signals': correct,
                    'accuracy_percent': accuracy,
                    'contribution_weight': self._calculate_layer_weight(layer, accuracy)
                }

            # تحليل قوة الإشارات
            convergence_analysis['signal_strength_distribution'] = {
                'strong': convergence_stats.get('strong_signals', 0),
                'medium': convergence_stats.get('medium_signals', 0),
                'weak': convergence_stats.get('weak_signals', 0)
            }

            # رؤى الأنماط
            convergence_analysis['pattern_insights'] = self._generate_convergence_insights(convergence_stats)

            return convergence_analysis

        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط التقارب: {str(e)}")
            return {}

    def _calculate_layer_weight(self, layer: str, accuracy: float) -> float:
        """حساب وزن الطبقة بناء على الدقة"""
        try:
            base_weights = {
                'technical': 0.30,
                'behavioral': 0.25,
                'quantitative': 0.25,
                'ai': 0.20
            }

            base_weight = base_weights.get(layer, 0.25)

            # تعديل الوزن بناء على الدقة
            if accuracy >= 80:
                return base_weight * 1.2
            elif accuracy >= 60:
                return base_weight * 1.0
            elif accuracy >= 40:
                return base_weight * 0.8
            else:
                return base_weight * 0.6

        except Exception as e:
            logger.error(f"خطأ في حساب وزن الطبقة: {str(e)}")
            return 0.25

    def _generate_convergence_insights(self, convergence_stats: Dict[str, Any]) -> List[str]:
        """توليد رؤى أنماط التقارب"""
        try:
            insights = []

            total_analyses = convergence_stats.get('total_analyses', 0)
            convergent_signals = convergence_stats.get('convergent_signals', 0)

            if total_analyses > 0:
                convergence_rate = convergent_signals / total_analyses

                if convergence_rate > 0.8:
                    insights.append("معدل تقارب ممتاز - الطبقات تعمل بتناغم عالي")
                elif convergence_rate > 0.6:
                    insights.append("معدل تقارب جيد - توافق مقبول بين الطبقات")
                elif convergence_rate > 0.4:
                    insights.append("معدل تقارب متوسط - يحتاج تحسين في بعض الطبقات")
                else:
                    insights.append("معدل تقارب منخفض - مراجعة شاملة للطبقات مطلوبة")

            # تحليل أداء الطبقات
            layer_performance = convergence_stats.get('layer_performance', {})
            best_layer = max(layer_performance.items(),
                           key=lambda x: x[1].get('correct', 0) / max(x[1].get('total', 1), 1),
                           default=('unknown', {}))

            if best_layer[0] != 'unknown':
                insights.append(f"أفضل طبقة أداءً: {best_layer[0]}")

            return insights

        except Exception as e:
            logger.error(f"خطأ في توليد رؤى التقارب: {str(e)}")
            return []

    async def _compare_layer_performance(self, technical: LayerPerformanceData,
                                       behavioral: LayerPerformanceData,
                                       quantitative: LayerPerformanceData,
                                       ai: LayerPerformanceData) -> Dict[str, Any]:
        """مقارنة أداء الطبقات"""
        try:
            layers = [technical, behavioral, quantitative, ai]

            performance_comparison = {
                'accuracy_ranking': [],
                'speed_ranking': [],
                'reliability_ranking': [],
                'overall_ranking': [],
                'performance_matrix': {},
                'improvement_suggestions': {}
            }

            # ترتيب حسب الدقة
            accuracy_sorted = sorted(layers, key=lambda x: x.accuracy_rate, reverse=True)
            performance_comparison['accuracy_ranking'] = [
                {'layer': layer.layer_name, 'accuracy': layer.accuracy_rate}
                for layer in accuracy_sorted
            ]

            # ترتيب حسب السرعة
            speed_sorted = sorted(layers, key=lambda x: x.avg_processing_time)
            performance_comparison['speed_ranking'] = [
                {'layer': layer.layer_name, 'processing_time': layer.avg_processing_time}
                for layer in speed_sorted
            ]

            # ترتيب حسب الموثوقية
            reliability_sorted = sorted(layers, key=lambda x: x.success_rate, reverse=True)
            performance_comparison['reliability_ranking'] = [
                {'layer': layer.layer_name, 'success_rate': layer.success_rate}
                for layer in reliability_sorted
            ]

            # مصفوفة الأداء
            for layer in layers:
                performance_comparison['performance_matrix'][layer.layer_name] = {
                    'accuracy_score': layer.accuracy_rate * 100,
                    'speed_score': self._calculate_speed_score(layer.avg_processing_time),
                    'reliability_score': layer.success_rate * 100,
                    'overall_score': self._calculate_overall_layer_score(layer)
                }

            # الترتيب العام
            overall_sorted = sorted(layers,
                                  key=lambda x: self._calculate_overall_layer_score(x),
                                  reverse=True)
            performance_comparison['overall_ranking'] = [
                {'layer': layer.layer_name, 'overall_score': self._calculate_overall_layer_score(layer)}
                for layer in overall_sorted
            ]

            # اقتراحات التحسين
            for layer in layers:
                suggestions = self._generate_layer_improvement_suggestions(layer)
                if suggestions:
                    performance_comparison['improvement_suggestions'][layer.layer_name] = suggestions

            return performance_comparison

        except Exception as e:
            logger.error(f"خطأ في مقارنة أداء الطبقات: {str(e)}")
            return {}

    def _calculate_speed_score(self, processing_time: float) -> float:
        """حساب نقاط السرعة"""
        try:
            # كلما قل الوقت، زادت النقاط
            if processing_time <= 0.1:
                return 100.0
            elif processing_time <= 0.5:
                return 80.0
            elif processing_time <= 1.0:
                return 60.0
            elif processing_time <= 2.0:
                return 40.0
            else:
                return 20.0

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط السرعة: {str(e)}")
            return 0.0

    def _calculate_overall_layer_score(self, layer: LayerPerformanceData) -> float:
        """حساب النقاط الإجمالية للطبقة"""
        try:
            accuracy_weight = 0.4
            speed_weight = 0.3
            reliability_weight = 0.3

            accuracy_score = layer.accuracy_rate * 100
            speed_score = self._calculate_speed_score(layer.avg_processing_time)
            reliability_score = layer.success_rate * 100

            overall_score = (
                accuracy_score * accuracy_weight +
                speed_score * speed_weight +
                reliability_score * reliability_weight
            )

            return overall_score

        except Exception as e:
            logger.error(f"خطأ في حساب النقاط الإجمالية: {str(e)}")
            return 0.0

    def _generate_layer_improvement_suggestions(self, layer: LayerPerformanceData) -> List[str]:
        """توليد اقتراحات تحسين للطبقة"""
        try:
            suggestions = []

            # اقتراحات بناء على الدقة
            if layer.accuracy_rate < 0.6:
                suggestions.append(f"تحسين دقة {layer.layer_name} - مراجعة المعايير والخوارزميات")

            # اقتراحات بناء على السرعة
            if layer.avg_processing_time > 1.0:
                suggestions.append(f"تحسين سرعة {layer.layer_name} - تحسين الكود والخوارزميات")

            # اقتراحات بناء على معدل النجاح
            if layer.success_rate < 0.8:
                suggestions.append(f"تحسين موثوقية {layer.layer_name} - معالجة الأخطاء والاستثناءات")

            # اقتراحات بناء على الأخطاء
            if layer.error_types:
                most_common_error = max(layer.error_types.items(), key=lambda x: x[1], default=('unknown', 0))
                if most_common_error[1] > 0:
                    suggestions.append(f"معالجة خطأ {most_common_error[0]} في {layer.layer_name}")

            return suggestions

        except Exception as e:
            logger.error(f"خطأ في توليد اقتراحات التحسين: {str(e)}")
            return []

    def _generate_technical_recommendations(self, data: LayerPerformanceData) -> List[str]:
        """توليد توصيات للطبقة الفنية"""
        recommendations = []

        if data.accuracy_rate < 0.7:
            recommendations.append("مراجعة معايير المؤشرات الفنية وتحسين الإعدادات")

        if data.avg_processing_time > 0.5:
            recommendations.append("تحسين أداء حساب المؤشرات الفنية")

        if data.signal_distribution.get('NEUTRAL', 0) > data.total_analyses * 0.5:
            recommendations.append("تقليل الإشارات المحايدة وتحسين حساسية المؤشرات")

        return recommendations

    def _generate_behavioral_recommendations(self, data: LayerPerformanceData) -> List[str]:
        """توليد توصيات للطبقة السلوكية"""
        recommendations = []

        if data.accuracy_rate < 0.65:
            recommendations.append("تحسين نماذج التحليل السلوكي وأنماط السوق")

        if data.success_rate < 0.8:
            recommendations.append("مراجعة خوارزميات كشف الأنماط السلوكية")

        return recommendations

    def _generate_quantitative_recommendations(self, data: LayerPerformanceData) -> List[str]:
        """توليد توصيات للطبقة الكمية"""
        recommendations = []

        if data.accuracy_rate < 0.75:
            recommendations.append("تحسين النماذج الإحصائية والكمية")

        if data.avg_processing_time > 1.0:
            recommendations.append("تحسين كفاءة العمليات الحسابية الكمية")

        return recommendations

    def _generate_ai_recommendations(self, data: LayerPerformanceData) -> List[str]:
        """توليد توصيات لطبقة الذكاء الاصطناعي"""
        recommendations = []

        if data.accuracy_rate < 0.8:
            recommendations.append("إعادة تدريب نماذج الذكاء الاصطناعي ببيانات حديثة")

        if data.success_rate < 0.85:
            recommendations.append("تحسين معالجة الأخطاء في نماذج الذكاء الاصطناعي")

        return recommendations

    def _identify_technical_critical_issues(self, data: LayerPerformanceData) -> List[str]:
        """تحديد المشاكل الحرجة في الطبقة الفنية"""
        issues = []

        if data.success_rate < 0.5:
            issues.append("معدل فشل عالي في التحليل الفني")

        if data.avg_processing_time > 2.0:
            issues.append("بطء شديد في حساب المؤشرات الفنية")

        return issues

    def _identify_behavioral_critical_issues(self, data: LayerPerformanceData) -> List[str]:
        """تحديد المشاكل الحرجة في الطبقة السلوكية"""
        issues = []

        if data.accuracy_rate < 0.4:
            issues.append("دقة منخفضة جداً في التحليل السلوكي")

        return issues

    def _identify_quantitative_critical_issues(self, data: LayerPerformanceData) -> List[str]:
        """تحديد المشاكل الحرجة في الطبقة الكمية"""
        issues = []

        if data.success_rate < 0.6:
            issues.append("معدل فشل عالي في التحليل الكمي")

        return issues

    def _identify_ai_critical_issues(self, data: LayerPerformanceData) -> List[str]:
        """تحديد المشاكل الحرجة في طبقة الذكاء الاصطناعي"""
        issues = []

        if data.accuracy_rate < 0.6:
            issues.append("دقة منخفضة في نماذج الذكاء الاصطناعي")

        if data.total_analyses == 0:
            issues.append("عدم وجود تنبؤات من الذكاء الاصطناعي")

        return issues

    async def _generate_comprehensive_recommendations(self, technical: LayerPerformanceData,
                                                    behavioral: LayerPerformanceData,
                                                    quantitative: LayerPerformanceData,
                                                    ai: LayerPerformanceData,
                                                    system_overview: Dict[str, Any],
                                                    trading_performance: Dict[str, Any],
                                                    decision_quality: Dict[str, Any]) -> List[str]:
        """توليد توصيات شاملة للنظام"""
        try:
            recommendations = []

            # تحليل الأداء العام
            overall_accuracy = (technical.accuracy_rate + behavioral.accuracy_rate +
                              quantitative.accuracy_rate + ai.accuracy_rate) / 4

            if overall_accuracy < 0.7:
                recommendations.append("الأداء العام للنظام يحتاج تحسين - مراجعة شاملة مطلوبة")

            # تحليل أداء التداول
            win_rate = trading_performance.get('win_rate_percent', 0)
            if win_rate < 60:
                recommendations.append(f"معدل الفوز منخفض ({win_rate:.1f}%) - تحسين استراتيجية التداول")

            # تحليل جودة القرارات
            decision_accuracy = decision_quality.get('overall_accuracy_percent', 0)
            if decision_accuracy < 70:
                recommendations.append(f"جودة القرارات منخفضة ({decision_accuracy:.1f}%) - تحسين معايير اتخاذ القرارات")

            # تحليل استخدام الموارد
            cpu_usage = system_overview.get('cpu_usage_percent', 0)
            memory_usage = system_overview.get('memory_usage_mb', 0)

            if cpu_usage > 80:
                recommendations.append("استخدام عالي للمعالج - تحسين الأداء مطلوب")

            if memory_usage > 8000:  # أكثر من 8 جيجا
                recommendations.append("استخدام عالي للذاكرة - مراجعة إدارة الذاكرة")

            # توصيات خاصة بالطبقات
            layer_recommendations = []
            for layer in [technical, behavioral, quantitative, ai]:
                layer_recommendations.extend(layer.recommendations)

            recommendations.extend(layer_recommendations[:5])  # أهم 5 توصيات

            # توصيات عامة
            if not recommendations:
                recommendations.append("الأداء العام جيد - استمر في المراقبة والتحسين المستمر")

            return recommendations[:10]  # أهم 10 توصيات

        except Exception as e:
            logger.error(f"خطأ في توليد التوصيات الشاملة: {str(e)}")
            return ["خطأ في توليد التوصيات"]

    async def _save_report(self, report: ComprehensiveReport, format_type: ReportFormat) -> Optional[str]:
        """حفظ التقرير بالتنسيق المحدد"""
        try:
            timestamp = report.generation_time.strftime('%Y%m%d_%H%M%S')

            if format_type == ReportFormat.JSON:
                filename = f"{self.reports_directory}/comprehensive/{report.report_id}.json"
                await self._save_json_report(report, filename)

            elif format_type == ReportFormat.HTML:
                filename = f"{self.reports_directory}/comprehensive/{report.report_id}.html"
                await self._save_html_report(report, filename)

            elif format_type == ReportFormat.TEXT:
                filename = f"{self.reports_directory}/comprehensive/{report.report_id}.txt"
                await self._save_text_report(report, filename)

            elif format_type == ReportFormat.CSV:
                filename = f"{self.reports_directory}/comprehensive/{report.report_id}.csv"
                await self._save_csv_report(report, filename)

            else:
                logger.warning(f"تنسيق غير مدعوم: {format_type}")
                return None

            # حساب حجم الملف
            if os.path.exists(filename):
                file_size = os.path.getsize(filename) / (1024 * 1024)  # MB
                report.report_size_mb += file_size

                logger.info(f"تم حفظ التقرير: {filename} ({file_size:.2f} MB)")
                return filename

            return None

        except Exception as e:
            logger.error(f"خطأ في حفظ التقرير: {str(e)}")
            return None

    async def _save_json_report(self, report: ComprehensiveReport, filename: str):
        """حفظ التقرير بتنسيق JSON"""
        try:
            # تحويل التقرير إلى قاموس
            report_dict = {
                'report_id': report.report_id,
                'generation_time': report.generation_time.isoformat(),
                'report_period': report.report_period.value,
                'period_start': report.period_start.isoformat(),
                'period_end': report.period_end.isoformat(),
                'layers_performance': {
                    'technical': self._layer_to_dict(report.technical_layer),
                    'behavioral': self._layer_to_dict(report.behavioral_layer),
                    'quantitative': self._layer_to_dict(report.quantitative_layer),
                    'ai': self._layer_to_dict(report.ai_layer)
                },
                'system_overview': report.system_overview,
                'trading_performance': report.trading_performance,
                'decision_quality': report.decision_quality,
                'convergence_analysis': report.convergence_analysis,
                'performance_comparison': report.performance_comparison,
                'recommendations': report.recommendations,
                'metadata': {
                    'total_assets_analyzed': report.total_assets_analyzed,
                    'generation_duration_seconds': report.generation_duration_seconds,
                    'report_size_mb': report.report_size_mb
                }
            }

            # حفظ الملف
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"خطأ في حفظ تقرير JSON: {str(e)}")

    def _layer_to_dict(self, layer: LayerPerformanceData) -> Dict[str, Any]:
        """تحويل بيانات الطبقة إلى قاموس"""
        return {
            'layer_name': layer.layer_name,
            'total_analyses': layer.total_analyses,
            'successful_analyses': layer.successful_analyses,
            'failed_analyses': layer.failed_analyses,
            'success_rate': layer.success_rate,
            'avg_processing_time': layer.avg_processing_time,
            'accuracy_rate': layer.accuracy_rate,
            'signal_distribution': layer.signal_distribution,
            'confidence_distribution': layer.confidence_distribution,
            'error_types': layer.error_types,
            'performance_trends': layer.performance_trends,
            'recommendations': layer.recommendations,
            'critical_issues': layer.critical_issues,
            'last_update': layer.last_update.isoformat() if layer.last_update else None
        }

    async def _save_html_report(self, report: ComprehensiveReport, filename: str):
        """حفظ التقرير بتنسيق HTML"""
        try:
            html_content = self._generate_html_template(report)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            logger.error(f"خطأ في حفظ تقرير HTML: {str(e)}")

    def _generate_html_template(self, report: ComprehensiveReport) -> str:
        """توليد قالب HTML للتقرير"""
        try:
            html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأداء الشامل - {report.report_id}</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .section {{ margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
        .layer-card {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #e9ecef; border-radius: 5px; min-width: 150px; text-align: center; }}
        .good {{ color: #28a745; }} .warning {{ color: #ffc107; }} .danger {{ color: #dc3545; }}
        table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        th, td {{ padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #007bff; color: white; }}
        .recommendations {{ background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; }}
        .critical-issues {{ background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير الأداء الشامل</h1>
            <p><strong>معرف التقرير:</strong> {report.report_id}</p>
            <p><strong>تاريخ الإنشاء:</strong> {report.generation_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>فترة التقرير:</strong> {report.period_start.strftime('%Y-%m-%d')} إلى {report.period_end.strftime('%Y-%m-%d')}</p>
        </div>

        <div class="section">
            <h2>نظرة عامة على النظام</h2>
            <div class="metric">
                <strong>الأصول المراقبة:</strong><br>
                <span class="good">{report.total_assets_analyzed}</span>
            </div>
            <div class="metric">
                <strong>وقت التشغيل:</strong><br>
                <span class="good">{report.system_overview.get('system_uptime', 'N/A')}</span>
            </div>
            <div class="metric">
                <strong>استخدام المعالج:</strong><br>
                <span class="{'good' if report.system_overview.get('cpu_usage_percent', 0) < 70 else 'warning' if report.system_overview.get('cpu_usage_percent', 0) < 90 else 'danger'}">{report.system_overview.get('cpu_usage_percent', 0)}%</span>
            </div>
            <div class="metric">
                <strong>استخدام الذاكرة:</strong><br>
                <span class="good">{report.system_overview.get('memory_usage_mb', 0)} MB</span>
            </div>
        </div>

        <div class="section">
            <h2>أداء الطبقات</h2>
            {self._generate_layer_html(report.technical_layer)}
            {self._generate_layer_html(report.behavioral_layer)}
            {self._generate_layer_html(report.quantitative_layer)}
            {self._generate_layer_html(report.ai_layer)}
        </div>

        <div class="section">
            <h2>أداء التداول</h2>
            <table>
                <tr><th>المقياس</th><th>القيمة</th></tr>
                <tr><td>إجمالي الصفقات</td><td>{report.trading_performance.get('total_trades', 0)}</td></tr>
                <tr><td>الصفقات الرابحة</td><td>{report.trading_performance.get('winning_trades', 0)}</td></tr>
                <tr><td>معدل الفوز</td><td>{report.trading_performance.get('win_rate_percent', 0):.1f}%</td></tr>
                <tr><td>إجمالي الربح</td><td>${report.trading_performance.get('total_profit', 0):.2f}</td></tr>
                <tr><td>عامل الربح</td><td>{report.trading_performance.get('profit_factor', 0):.2f}</td></tr>
            </table>
        </div>

        <div class="section">
            <h2>جودة القرارات</h2>
            <div class="metric">
                <strong>إجمالي القرارات:</strong><br>
                <span>{report.decision_quality.get('total_decisions', 0)}</span>
            </div>
            <div class="metric">
                <strong>دقة القرارات:</strong><br>
                <span class="{'good' if report.decision_quality.get('overall_accuracy_percent', 0) > 70 else 'warning' if report.decision_quality.get('overall_accuracy_percent', 0) > 50 else 'danger'}">{report.decision_quality.get('overall_accuracy_percent', 0):.1f}%</span>
            </div>
        </div>

        <div class="section">
            <h2>التوصيات</h2>
            <div class="recommendations">
                <ul>
                    {''.join([f'<li>{rec}</li>' for rec in report.recommendations])}
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>معلومات التقرير</h2>
            <p><strong>مدة الإنشاء:</strong> {report.generation_duration_seconds:.2f} ثانية</p>
            <p><strong>حجم التقرير:</strong> {report.report_size_mb:.2f} MB</p>
            <p><strong>تم الإنشاء بواسطة:</strong> نظام التقارير التفصيلية المتكامل</p>
        </div>
    </div>
</body>
</html>
            """

            return html

        except Exception as e:
            logger.error(f"خطأ في توليد قالب HTML: {str(e)}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def _generate_layer_html(self, layer: LayerPerformanceData) -> str:
        """توليد HTML لطبقة واحدة"""
        try:
            accuracy_class = 'good' if layer.accuracy_rate > 0.7 else 'warning' if layer.accuracy_rate > 0.5 else 'danger'
            success_class = 'good' if layer.success_rate > 0.8 else 'warning' if layer.success_rate > 0.6 else 'danger'

            html = f"""
            <div class="layer-card">
                <h3>{layer.layer_name}</h3>
                <div class="metric">
                    <strong>إجمالي التحليلات:</strong><br>
                    <span>{layer.total_analyses}</span>
                </div>
                <div class="metric">
                    <strong>معدل النجاح:</strong><br>
                    <span class="{success_class}">{layer.success_rate:.1%}</span>
                </div>
                <div class="metric">
                    <strong>معدل الدقة:</strong><br>
                    <span class="{accuracy_class}">{layer.accuracy_rate:.1%}</span>
                </div>
                <div class="metric">
                    <strong>متوسط وقت المعالجة:</strong><br>
                    <span>{layer.avg_processing_time:.3f}s</span>
                </div>
            """

            if layer.recommendations:
                html += f"""
                <div class="recommendations">
                    <strong>التوصيات:</strong>
                    <ul>
                        {''.join([f'<li>{rec}</li>' for rec in layer.recommendations])}
                    </ul>
                </div>
                """

            if layer.critical_issues:
                html += f"""
                <div class="critical-issues">
                    <strong>المشاكل الحرجة:</strong>
                    <ul>
                        {''.join([f'<li>{issue}</li>' for issue in layer.critical_issues])}
                    </ul>
                </div>
                """

            html += "</div>"
            return html

        except Exception as e:
            logger.error(f"خطأ في توليد HTML للطبقة: {str(e)}")
            return f"<div class='layer-card'><h3>{layer.layer_name}</h3><p>خطأ في عرض البيانات</p></div>"

    async def _save_text_report(self, report: ComprehensiveReport, filename: str):
        """حفظ التقرير بتنسيق نصي"""
        try:
            text_content = f"""
=== تقرير الأداء الشامل ===
معرف التقرير: {report.report_id}
تاريخ الإنشاء: {report.generation_time.strftime('%Y-%m-%d %H:%M:%S')}
فترة التقرير: {report.period_start.strftime('%Y-%m-%d')} إلى {report.period_end.strftime('%Y-%m-%d')}

=== نظرة عامة على النظام ===
الأصول المراقبة: {report.total_assets_analyzed}
وقت التشغيل: {report.system_overview.get('system_uptime', 'N/A')}
استخدام المعالج: {report.system_overview.get('cpu_usage_percent', 0)}%
استخدام الذاكرة: {report.system_overview.get('memory_usage_mb', 0)} MB

=== أداء الطبقات ===
{self._generate_layer_text(report.technical_layer)}
{self._generate_layer_text(report.behavioral_layer)}
{self._generate_layer_text(report.quantitative_layer)}
{self._generate_layer_text(report.ai_layer)}

=== أداء التداول ===
إجمالي الصفقات: {report.trading_performance.get('total_trades', 0)}
الصفقات الرابحة: {report.trading_performance.get('winning_trades', 0)}
معدل الفوز: {report.trading_performance.get('win_rate_percent', 0):.1f}%
إجمالي الربح: ${report.trading_performance.get('total_profit', 0):.2f}
عامل الربح: {report.trading_performance.get('profit_factor', 0):.2f}

=== جودة القرارات ===
إجمالي القرارات: {report.decision_quality.get('total_decisions', 0)}
دقة القرارات: {report.decision_quality.get('overall_accuracy_percent', 0):.1f}%

=== التوصيات ===
{chr(10).join([f"- {rec}" for rec in report.recommendations])}

=== معلومات التقرير ===
مدة الإنشاء: {report.generation_duration_seconds:.2f} ثانية
حجم التقرير: {report.report_size_mb:.2f} MB
            """

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(text_content)

        except Exception as e:
            logger.error(f"خطأ في حفظ التقرير النصي: {str(e)}")

    def _generate_layer_text(self, layer: LayerPerformanceData) -> str:
        """توليد نص لطبقة واحدة"""
        try:
            text = f"""
--- {layer.layer_name} ---
إجمالي التحليلات: {layer.total_analyses}
التحليلات الناجحة: {layer.successful_analyses}
معدل النجاح: {layer.success_rate:.1%}
معدل الدقة: {layer.accuracy_rate:.1%}
متوسط وقت المعالجة: {layer.avg_processing_time:.3f}s
            """

            if layer.recommendations:
                text += f"\nالتوصيات:\n{chr(10).join([f'  - {rec}' for rec in layer.recommendations])}"

            if layer.critical_issues:
                text += f"\nالمشاكل الحرجة:\n{chr(10).join([f'  - {issue}' for issue in layer.critical_issues])}"

            return text

        except Exception as e:
            logger.error(f"خطأ في توليد نص الطبقة: {str(e)}")
            return f"--- {layer.layer_name} ---\nخطأ في عرض البيانات"

    async def _save_csv_report(self, report: ComprehensiveReport, filename: str):
        """حفظ التقرير بتنسيق CSV"""
        try:
            import csv

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # رأس الملف
                writer.writerow(['Layer', 'Total_Analyses', 'Success_Rate', 'Accuracy_Rate', 'Avg_Processing_Time'])

                # بيانات الطبقات
                layers = [report.technical_layer, report.behavioral_layer,
                         report.quantitative_layer, report.ai_layer]

                for layer in layers:
                    writer.writerow([
                        layer.layer_name,
                        layer.total_analyses,
                        f"{layer.success_rate:.3f}",
                        f"{layer.accuracy_rate:.3f}",
                        f"{layer.avg_processing_time:.3f}"
                    ])

        except Exception as e:
            logger.error(f"خطأ في حفظ تقرير CSV: {str(e)}")

    def _update_system_stats(self, report_id: str, generation_duration: float, saved_files: List[str]):
        """تحديث إحصائيات النظام"""
        try:
            self.system_stats['total_reports_generated'] += 1
            self.system_stats['last_report_time'] = datetime.now()

            # تحديث متوسط وقت الإنشاء
            current_avg = self.system_stats['avg_generation_time']
            total_reports = self.system_stats['total_reports_generated']

            new_avg = ((current_avg * (total_reports - 1)) + generation_duration) / total_reports
            self.system_stats['avg_generation_time'] = new_avg

            # تحديث إحصائيات التنسيقات
            for file_path in saved_files:
                file_extension = file_path.split('.')[-1].upper()
                if file_extension not in self.system_stats['reports_by_type']:
                    self.system_stats['reports_by_type'][file_extension] = 0
                self.system_stats['reports_by_type'][file_extension] += 1

            logger.debug(f"تم تحديث إحصائيات النظام للتقرير: {report_id}")

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات النظام: {str(e)}")

    async def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        try:
            return {
                'system_stats': self.system_stats.copy(),
                'report_settings': self.report_settings.copy(),
                'supported_formats': [format_type.value for format_type in ReportFormat],
                'supported_periods': [period.value for period in ReportPeriod],
                'reports_directory': self.reports_directory,
                'is_generating': self.is_generating
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النظام: {str(e)}")
            return {}

# إنشاء مثيل عام
detailed_reporting_system = DetailedReportingSystem()
