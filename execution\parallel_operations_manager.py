"""
نظام إدارة العمليات المتوازية - إدارة وتنظيم العمليات المتوازية للـ70 زوج
"""

import asyncio
import threading
import time
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from enum import Enum

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, ScalpingError

logger = scalping_logger.get_logger("parallel_operations_manager")

class OperationType(Enum):
    """أنواع العمليات المختلفة"""
    DATA_COLLECTION = "data_collection"
    INDICATOR_CALCULATION = "indicator_calculation"
    LIVE_STREAMING = "live_streaming"
    GAP_RESOLUTION = "gap_resolution"
    SYSTEM_MONITORING = "system_monitoring"
    BACKUP_OPERATION = "backup_operation"

class OperationStatus(Enum):
    """حالات العمليات"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

@dataclass
class OperationTask:
    """مهمة عملية واحدة"""
    task_id: str
    operation_type: OperationType
    asset: str
    function: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: int = 1  # 1 = عالي، 5 = منخفض
    max_retries: int = 3
    retry_count: int = 0
    status: OperationStatus = OperationStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    memory_usage: float = 0.0
    
    @property
    def is_completed(self) -> bool:
        return self.status in [OperationStatus.COMPLETED, OperationStatus.FAILED, OperationStatus.CANCELLED]
    
    @property
    def can_retry(self) -> bool:
        return self.status == OperationStatus.FAILED and self.retry_count < self.max_retries

@dataclass
class SystemResources:
    """موارد النظام الحالية"""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_available_gb: float = 0.0
    active_threads: int = 0
    active_processes: int = 0
    disk_usage_percent: float = 0.0
    network_io: Dict[str, float] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

class ParallelOperationsManager:
    """مدير العمليات المتوازية الرئيسي"""
    
    def __init__(self, max_workers: int = None, max_async_tasks: int = None):
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.config = TradingConfig()
        
        # تحديد عدد العمال بناءً على موارد النظام
        cpu_count = psutil.cpu_count()
        self.max_workers = max_workers or min(cpu_count * 2, 20)
        self.max_async_tasks = max_async_tasks or min(cpu_count * 4, 50)
        
        # مجمعات العمال
        self.thread_executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=min(cpu_count, 8))
        
        # إدارة المهام
        self.pending_tasks: Dict[str, OperationTask] = {}
        self.running_tasks: Dict[str, OperationTask] = {}
        self.completed_tasks: Dict[str, OperationTask] = {}
        self.failed_tasks: Dict[str, OperationTask] = {}
        
        # إدارة العمليات غير المتزامنة
        self.async_semaphore = asyncio.Semaphore(self.max_async_tasks)
        self.active_async_tasks: Set[asyncio.Task] = set()
        
        # مراقبة الموارد
        self.system_resources = SystemResources()
        self.resource_monitor_active = False
        self.resource_check_interval = 30  # ثانية
        
        # إحصائيات العمليات
        self.operation_stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0,
            'peak_memory_usage': 0.0,
            'peak_cpu_usage': 0.0,
            'start_time': datetime.now()
        }
        
        # إعدادات الأداء
        self.cpu_threshold = 80.0  # %
        self.memory_threshold = 85.0  # %
        self.auto_scaling = True
        self.performance_monitoring = True
        
        # قفل للعمليات المتزامنة
        self._lock = threading.Lock()
        
        logger.info(f"تم تهيئة مدير العمليات المتوازية")
        logger.info(f"العمال: {self.max_workers} threads, {min(cpu_count, 8)} processes")
        logger.info(f"المهام غير المتزامنة: {self.max_async_tasks}")
        logger.info(f"الأزواج المدعومة: {len(self.currency_pairs)}")

    @handle_errors(default_return=None, log_error=True)
    def create_task(self, operation_type: OperationType, asset: str, function: Callable,
                   args: tuple = (), kwargs: dict = None, priority: int = 1,
                   max_retries: int = 3) -> Optional[str]:
        """إنشاء مهمة جديدة"""
        try:
            task_id = f"{operation_type.value}_{asset}_{int(time.time() * 1000)}"
            
            task = OperationTask(
                task_id=task_id,
                operation_type=operation_type,
                asset=asset,
                function=function,
                args=args,
                kwargs=kwargs or {},
                priority=priority,
                max_retries=max_retries
            )
            
            with self._lock:
                self.pending_tasks[task_id] = task
                self.operation_stats['total_tasks'] += 1
            
            logger.debug(f"تم إنشاء مهمة: {task_id} ({operation_type.value}) لـ {asset}")
            return task_id
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المهمة: {str(e)}")
            return None

    @handle_async_errors(default_return=False, log_error=True)
    async def execute_task_async(self, task: OperationTask) -> bool:
        """تنفيذ مهمة بشكل غير متزامن"""
        async with self.async_semaphore:
            try:
                # تحديث حالة المهمة
                task.status = OperationStatus.RUNNING
                task.started_at = datetime.now()
                
                # مراقبة الذاكرة قبل التنفيذ
                process = psutil.Process()
                memory_before = process.memory_info().rss / 1024 / 1024  # MB
                
                # تنفيذ المهمة
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(task.function):
                    result = await task.function(*task.args, **task.kwargs)
                else:
                    # تشغيل الدالة المتزامنة في executor
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(
                        self.thread_executor, 
                        task.function, 
                        *task.args
                    )
                
                # حساب الإحصائيات
                task.execution_time = time.time() - start_time
                memory_after = process.memory_info().rss / 1024 / 1024  # MB
                task.memory_usage = memory_after - memory_before
                
                # تحديث النتيجة
                task.result = result
                task.status = OperationStatus.COMPLETED
                task.completed_at = datetime.now()
                
                # نقل المهمة إلى المكتملة
                with self._lock:
                    if task.task_id in self.running_tasks:
                        del self.running_tasks[task.task_id]
                    self.completed_tasks[task.task_id] = task
                    self.operation_stats['completed_tasks'] += 1
                    self.operation_stats['total_execution_time'] += task.execution_time
                
                logger.debug(f"✅ اكتملت المهمة: {task.task_id} في {task.execution_time:.2f}s")
                return True
                
            except Exception as e:
                # معالجة الخطأ
                task.error = str(e)
                task.status = OperationStatus.FAILED
                task.completed_at = datetime.now()
                
                # نقل إلى المهام الفاشلة
                with self._lock:
                    if task.task_id in self.running_tasks:
                        del self.running_tasks[task.task_id]
                    self.failed_tasks[task.task_id] = task
                    self.operation_stats['failed_tasks'] += 1
                
                logger.error(f"❌ فشلت المهمة: {task.task_id} - {str(e)}")
                
                # محاولة إعادة التشغيل إذا أمكن
                if task.can_retry:
                    await self._retry_task(task)
                
                return False

    @handle_async_errors(default_return=False, log_error=True)
    async def _retry_task(self, task: OperationTask) -> bool:
        """إعادة محاولة تنفيذ مهمة فاشلة"""
        try:
            task.retry_count += 1
            task.status = OperationStatus.RETRYING
            
            # تأخير متزايد قبل إعادة المحاولة
            delay = min(2 ** task.retry_count, 60)  # حد أقصى دقيقة واحدة
            await asyncio.sleep(delay)
            
            # نقل المهمة إلى قائمة الانتظار
            with self._lock:
                if task.task_id in self.failed_tasks:
                    del self.failed_tasks[task.task_id]
                task.status = OperationStatus.PENDING
                self.pending_tasks[task.task_id] = task
            
            logger.info(f"🔄 إعادة محاولة المهمة: {task.task_id} (محاولة {task.retry_count})")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إعادة محاولة المهمة {task.task_id}: {str(e)}")
            return False

    @handle_errors(default_return=[], log_error=True)
    def get_pending_tasks_by_priority(self) -> List[OperationTask]:
        """الحصول على المهام المعلقة مرتبة حسب الأولوية"""
        with self._lock:
            tasks = list(self.pending_tasks.values())
        
        # ترتيب حسب الأولوية (1 = أعلى أولوية)
        return sorted(tasks, key=lambda t: (t.priority, t.created_at))

    @handle_async_errors(default_return=0, log_error=True)
    async def process_pending_tasks(self, batch_size: int = None) -> int:
        """معالجة المهام المعلقة"""
        try:
            if not batch_size:
                batch_size = min(self.max_async_tasks, len(self.pending_tasks))
            
            # الحصول على المهام حسب الأولوية
            pending_tasks = self.get_pending_tasks_by_priority()
            
            if not pending_tasks:
                return 0
            
            # تحديد عدد المهام للمعالجة
            tasks_to_process = pending_tasks[:batch_size]
            
            logger.info(f"🚀 بدء معالجة {len(tasks_to_process)} مهمة")
            
            # نقل المهام إلى قائمة التشغيل
            with self._lock:
                for task in tasks_to_process:
                    if task.task_id in self.pending_tasks:
                        del self.pending_tasks[task.task_id]
                        self.running_tasks[task.task_id] = task
            
            # تنفيذ المهام بشكل متوازي
            async_tasks = []
            for task in tasks_to_process:
                async_task = asyncio.create_task(self.execute_task_async(task))
                async_tasks.append(async_task)
                self.active_async_tasks.add(async_task)
            
            # انتظار اكتمال جميع المهام
            results = await asyncio.gather(*async_tasks, return_exceptions=True)
            
            # تنظيف المهام المكتملة
            for async_task in async_tasks:
                self.active_async_tasks.discard(async_task)
            
            # حساب النتائج
            successful_tasks = sum(1 for result in results if result is True)
            
            logger.info(f"✅ اكتملت {successful_tasks}/{len(tasks_to_process)} مهمة بنجاح")
            
            return successful_tasks

        except Exception as e:
            logger.error(f"خطأ في معالجة المهام المعلقة: {str(e)}")
            return 0

    @handle_errors(default_return=None, log_error=True)
    def update_system_resources(self):
        """تحديث معلومات موارد النظام"""
        try:
            # معلومات المعالج والذاكرة
            self.system_resources.cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            self.system_resources.memory_percent = memory.percent
            self.system_resources.memory_available_gb = memory.available / (1024**3)

            # معلومات العمليات والخيوط
            self.system_resources.active_threads = threading.active_count()
            self.system_resources.active_processes = len(psutil.pids())

            # معلومات القرص
            disk = psutil.disk_usage('/')
            self.system_resources.disk_usage_percent = (disk.used / disk.total) * 100

            # معلومات الشبكة
            net_io = psutil.net_io_counters()
            self.system_resources.network_io = {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }

            self.system_resources.timestamp = datetime.now()

            # تحديث الإحصائيات
            self.operation_stats['peak_cpu_usage'] = max(
                self.operation_stats['peak_cpu_usage'],
                self.system_resources.cpu_percent
            )
            self.operation_stats['peak_memory_usage'] = max(
                self.operation_stats['peak_memory_usage'],
                self.system_resources.memory_percent
            )

        except Exception as e:
            logger.error(f"خطأ في تحديث موارد النظام: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def is_system_overloaded(self) -> bool:
        """فحص ما إذا كان النظام محمل بشكل مفرط"""
        try:
            self.update_system_resources()

            cpu_overload = self.system_resources.cpu_percent > self.cpu_threshold
            memory_overload = self.system_resources.memory_percent > self.memory_threshold

            if cpu_overload or memory_overload:
                logger.warning(f"⚠️ النظام محمل: CPU={self.system_resources.cpu_percent:.1f}%, "
                             f"Memory={self.system_resources.memory_percent:.1f}%")
                return True

            return False

        except Exception as e:
            logger.error(f"خطأ في فحص حمولة النظام: {str(e)}")
            return True  # افتراض الحمولة العالية عند الخطأ

    @handle_async_errors(default_return=False, log_error=True)
    async def start_resource_monitoring(self) -> bool:
        """بدء مراقبة موارد النظام"""
        try:
            if self.resource_monitor_active:
                logger.warning("مراقب الموارد يعمل بالفعل")
                return False

            self.resource_monitor_active = True
            logger.info(f"🔍 بدء مراقبة موارد النظام كل {self.resource_check_interval} ثانية")

            # بدء حلقة المراقبة
            asyncio.create_task(self._resource_monitoring_loop())

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة الموارد: {str(e)}")
            return False

    async def _resource_monitoring_loop(self):
        """حلقة مراقبة الموارد المستمرة"""
        try:
            while self.resource_monitor_active:
                try:
                    # تحديث موارد النظام
                    self.update_system_resources()

                    # فحص الحمولة العالية
                    if self.is_system_overloaded():
                        await self._handle_system_overload()

                    # تنظيف الذاكرة إذا لزم الأمر
                    if self.system_resources.memory_percent > 75:
                        await self._cleanup_memory()

                    # انتظار حتى الفحص التالي
                    await asyncio.sleep(self.resource_check_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة مراقبة الموارد: {str(e)}")
                    await asyncio.sleep(60)  # انتظار دقيقة عند الخطأ

        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة الموارد: {str(e)}")

    async def _handle_system_overload(self):
        """معالجة الحمولة العالية للنظام"""
        try:
            logger.warning("🚨 معالجة الحمولة العالية للنظام")

            # تقليل عدد المهام المتزامنة
            if self.auto_scaling:
                new_limit = max(5, self.max_async_tasks // 2)
                self.async_semaphore = asyncio.Semaphore(new_limit)
                logger.info(f"📉 تقليل المهام المتزامنة إلى {new_limit}")

            # إلغاء المهام غير الضرورية
            cancelled_count = await self._cancel_low_priority_tasks()
            if cancelled_count > 0:
                logger.info(f"🚫 تم إلغاء {cancelled_count} مهمة منخفضة الأولوية")

            # تنظيف الذاكرة
            await self._cleanup_memory()

        except Exception as e:
            logger.error(f"خطأ في معالجة الحمولة العالية: {str(e)}")

    async def _cancel_low_priority_tasks(self) -> int:
        """إلغاء المهام منخفضة الأولوية"""
        try:
            cancelled_count = 0

            with self._lock:
                # إلغاء المهام المعلقة منخفضة الأولوية (أولوية > 3)
                tasks_to_cancel = [
                    task_id for task_id, task in self.pending_tasks.items()
                    if task.priority > 3
                ]

                for task_id in tasks_to_cancel:
                    task = self.pending_tasks[task_id]
                    task.status = OperationStatus.CANCELLED
                    del self.pending_tasks[task_id]
                    cancelled_count += 1
                    self.operation_stats['cancelled_tasks'] += 1

            return cancelled_count

        except Exception as e:
            logger.error(f"خطأ في إلغاء المهام منخفضة الأولوية: {str(e)}")
            return 0

    async def _cleanup_memory(self):
        """تنظيف الذاكرة"""
        try:
            # تنظيف المهام المكتملة القديمة (أكثر من ساعة)
            cutoff_time = datetime.now() - timedelta(hours=1)

            with self._lock:
                old_completed = [
                    task_id for task_id, task in self.completed_tasks.items()
                    if task.completed_at and task.completed_at < cutoff_time
                ]

                for task_id in old_completed:
                    del self.completed_tasks[task_id]

                old_failed = [
                    task_id for task_id, task in self.failed_tasks.items()
                    if task.completed_at and task.completed_at < cutoff_time
                ]

                for task_id in old_failed:
                    del self.failed_tasks[task_id]

            # تشغيل garbage collector
            gc.collect()

            logger.debug(f"🧹 تم تنظيف {len(old_completed)} مهمة مكتملة و {len(old_failed)} مهمة فاشلة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الذاكرة: {str(e)}")

    def stop_resource_monitoring(self):
        """إيقاف مراقبة الموارد"""
        self.resource_monitor_active = False
        logger.info("تم إيقاف مراقبة الموارد")

    @handle_errors(default_return={}, log_error=True)
    def get_operation_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العمليات"""
        try:
            with self._lock:
                # حساب المتوسطات
                if self.operation_stats['completed_tasks'] > 0:
                    self.operation_stats['average_execution_time'] = (
                        self.operation_stats['total_execution_time'] /
                        self.operation_stats['completed_tasks']
                    )

                # إحصائيات الحالة الحالية
                current_stats = {
                    'pending_tasks': len(self.pending_tasks),
                    'running_tasks': len(self.running_tasks),
                    'completed_tasks': len(self.completed_tasks),
                    'failed_tasks': len(self.failed_tasks),
                    'active_async_tasks': len(self.active_async_tasks)
                }

                # دمج الإحصائيات
                stats = {
                    **self.operation_stats,
                    **current_stats,
                    'system_resources': {
                        'cpu_percent': self.system_resources.cpu_percent,
                        'memory_percent': self.system_resources.memory_percent,
                        'memory_available_gb': self.system_resources.memory_available_gb,
                        'active_threads': self.system_resources.active_threads,
                        'disk_usage_percent': self.system_resources.disk_usage_percent
                    },
                    'configuration': {
                        'max_workers': self.max_workers,
                        'max_async_tasks': self.max_async_tasks,
                        'cpu_threshold': self.cpu_threshold,
                        'memory_threshold': self.memory_threshold,
                        'auto_scaling': self.auto_scaling
                    }
                }

                return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}

    @handle_errors(default_return=False, log_error=True)
    def shutdown(self, timeout: int = 30) -> bool:
        """إغلاق مدير العمليات بأمان"""
        try:
            logger.info("🔄 بدء إغلاق مدير العمليات المتوازية")

            # إيقاف مراقبة الموارد
            self.stop_resource_monitoring()

            # إلغاء جميع المهام المعلقة
            with self._lock:
                for task in self.pending_tasks.values():
                    task.status = OperationStatus.CANCELLED
                self.pending_tasks.clear()

            # انتظار اكتمال المهام الجارية
            start_time = time.time()
            while self.running_tasks and (time.time() - start_time) < timeout:
                time.sleep(1)
                logger.info(f"انتظار اكتمال {len(self.running_tasks)} مهمة...")

            # إغلاق executors
            self.thread_executor.shutdown(wait=True)
            self.process_executor.shutdown(wait=True)

            # طباعة الإحصائيات النهائية
            final_stats = self.get_operation_statistics()
            logger.info("📊 الإحصائيات النهائية:")
            logger.info(f"   - إجمالي المهام: {final_stats.get('total_tasks', 0)}")
            logger.info(f"   - المهام المكتملة: {final_stats.get('completed_tasks', 0)}")
            logger.info(f"   - المهام الفاشلة: {final_stats.get('failed_tasks', 0)}")
            logger.info(f"   - متوسط وقت التنفيذ: {final_stats.get('average_execution_time', 0):.2f}s")

            logger.info("✅ تم إغلاق مدير العمليات المتوازية بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في إغلاق مدير العمليات: {str(e)}")
            return False

# إنشاء مثيل عام للمدير
parallel_manager = ParallelOperationsManager()
