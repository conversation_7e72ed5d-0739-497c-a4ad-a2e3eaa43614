"""
نظام إحصائيات الأداء للـ70 زوج
Performance Statistics System for 70 Currency Pairs
"""

import asyncio
import time
import threading
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import statistics
# import numpy as np  # سنستخدم statistics بدلاً من numpy
from collections import defaultdict, deque

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors

logger = scalping_logger.get_logger("performance_statistics_system")

class StatisticType(Enum):
    """أنواع الإحصائيات"""
    DATA_COLLECTION_RATE = "data_collection_rate"
    INDICATOR_CALCULATION_SPEED = "indicator_calculation_speed"
    WEBSOCKET_LATENCY = "websocket_latency"
    DATABASE_QUERY_TIME = "database_query_time"
    REDIS_RESPONSE_TIME = "redis_response_time"
    MEMORY_USAGE = "memory_usage"
    CPU_UTILIZATION = "cpu_utilization"
    ERROR_RATE = "error_rate"
    SUCCESS_RATE = "success_rate"
    THROUGHPUT = "throughput"

class TimeFrame(Enum):
    """الإطارات الزمنية للإحصائيات"""
    MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    HOUR = "1h"
    FOUR_HOURS = "4h"
    DAY = "1d"
    WEEK = "1w"

@dataclass
class StatisticPoint:
    """نقطة إحصائية واحدة"""
    timestamp: datetime
    value: float
    asset: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class StatisticSummary:
    """ملخص إحصائي"""
    statistic_type: StatisticType
    timeframe: TimeFrame
    asset: Optional[str] = None
    count: int = 0
    min_value: float = 0.0
    max_value: float = 0.0
    avg_value: float = 0.0
    median_value: float = 0.0
    std_deviation: float = 0.0
    percentile_95: float = 0.0
    percentile_99: float = 0.0
    trend_direction: str = "stable"  # up, down, stable
    trend_strength: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class AssetPerformanceStats:
    """إحصائيات أداء أصل واحد"""
    asset: str
    data_points_collected: int = 0
    indicators_calculated: int = 0
    errors_count: int = 0
    avg_collection_time: float = 0.0
    avg_calculation_time: float = 0.0
    success_rate: float = 100.0
    last_activity: Optional[datetime] = None
    performance_score: float = 100.0
    status: str = "active"  # active, slow, error, inactive

class PerformanceStatisticsSystem:
    """نظام إحصائيات الأداء"""
    
    def __init__(self):
        self.config = TradingConfig()
        
        # حالة النظام
        self.statistics_active = False
        self.collection_interval = 60  # ثانية
        self.retention_days = 30
        
        # مخازن البيانات
        self.statistics_data: Dict[StatisticType, Dict[str, deque]] = {}
        self.asset_statistics: Dict[str, AssetPerformanceStats] = {}
        self.summary_cache: Dict[str, StatisticSummary] = {}
        
        # إعدادات التجميع
        self.max_points_per_series = 10000
        self.aggregation_intervals = {
            TimeFrame.MINUTE: 60,
            TimeFrame.FIVE_MINUTES: 300,
            TimeFrame.FIFTEEN_MINUTES: 900,
            TimeFrame.HOUR: 3600,
            TimeFrame.FOUR_HOURS: 14400,
            TimeFrame.DAY: 86400,
            TimeFrame.WEEK: 604800
        }
        
        # خيوط المعالجة
        self.collection_thread = None
        self.aggregation_thread = None
        
        # إحصائيات النظام
        self.system_stats = {
            'total_data_points': 0,
            'statistics_calculated': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'last_collection_time': None,
            'collection_errors': 0
        }
        
        # تهيئة النظام
        self._initialize_statistics_system()
        
        logger.info("تم تهيئة نظام إحصائيات الأداء")

    def _initialize_statistics_system(self):
        """تهيئة نظام الإحصائيات"""
        try:
            # تهيئة مخازن البيانات
            for stat_type in StatisticType:
                self.statistics_data[stat_type] = {}
                
                # إنشاء مخزن لكل أصل + مخزن عام
                for asset in CURRENCY_PAIRS_70:
                    self.statistics_data[stat_type][asset] = deque(maxlen=self.max_points_per_series)
                
                # مخزن عام للنظام
                self.statistics_data[stat_type]['system'] = deque(maxlen=self.max_points_per_series)
            
            # تهيئة إحصائيات الأصول
            for asset in CURRENCY_PAIRS_70:
                self.asset_statistics[asset] = AssetPerformanceStats(asset=asset)
            
            logger.debug("تم تهيئة مخازن البيانات للإحصائيات")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة نظام الإحصائيات: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_statistics_collection(self) -> bool:
        """بدء جمع الإحصائيات"""
        try:
            if self.statistics_active:
                logger.warning("نظام الإحصائيات نشط بالفعل")
                return True
            
            logger.info("🔄 بدء نظام إحصائيات الأداء")
            
            self.statistics_active = True
            
            # بدء خيط الجمع
            self.collection_thread = threading.Thread(
                target=self._collection_loop,
                daemon=True,
                name="StatisticsCollectionThread"
            )
            self.collection_thread.start()
            
            # بدء خيط التجميع
            self.aggregation_thread = threading.Thread(
                target=self._aggregation_loop,
                daemon=True,
                name="StatisticsAggregationThread"
            )
            self.aggregation_thread.start()
            
            logger.info("✅ تم بدء نظام إحصائيات الأداء")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء نظام الإحصائيات: {str(e)}")
            return False

    def stop_statistics_collection(self):
        """إيقاف جمع الإحصائيات"""
        try:
            logger.info("إيقاف نظام إحصائيات الأداء...")
            
            self.statistics_active = False
            
            # انتظار انتهاء الخيوط
            if self.collection_thread and self.collection_thread.is_alive():
                self.collection_thread.join(timeout=5)
            
            if self.aggregation_thread and self.aggregation_thread.is_alive():
                self.aggregation_thread.join(timeout=5)
            
            logger.info("✅ تم إيقاف نظام إحصائيات الأداء")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف نظام الإحصائيات: {str(e)}")

    def record_statistic(self, 
                        stat_type: StatisticType,
                        value: float,
                        asset: str = None,
                        metadata: Dict[str, Any] = None):
        """تسجيل نقطة إحصائية"""
        try:
            timestamp = datetime.now()
            
            # إنشاء نقطة البيانات
            point = StatisticPoint(
                timestamp=timestamp,
                value=value,
                asset=asset,
                metadata=metadata or {}
            )
            
            # تحديد المخزن المناسب
            storage_key = asset if asset else 'system'
            
            if stat_type in self.statistics_data and storage_key in self.statistics_data[stat_type]:
                self.statistics_data[stat_type][storage_key].append(point)
                self.system_stats['total_data_points'] += 1
            
            # تحديث إحصائيات الأصل
            if asset and asset in self.asset_statistics:
                self._update_asset_statistics(asset, stat_type, value, timestamp)
            
            # إبطال الكاش للملخصات المتأثرة
            self._invalidate_cache(stat_type, asset)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الإحصائية: {str(e)}")
            self.system_stats['collection_errors'] += 1

    def _update_asset_statistics(self, asset: str, stat_type: StatisticType, value: float, timestamp: datetime):
        """تحديث إحصائيات الأصل"""
        try:
            asset_stats = self.asset_statistics[asset]
            asset_stats.last_activity = timestamp
            
            # تحديث العدادات حسب نوع الإحصائية
            if stat_type == StatisticType.DATA_COLLECTION_RATE:
                asset_stats.data_points_collected += 1
            elif stat_type == StatisticType.INDICATOR_CALCULATION_SPEED:
                asset_stats.indicators_calculated += 1
                # تحديث متوسط وقت الحساب
                if asset_stats.avg_calculation_time == 0:
                    asset_stats.avg_calculation_time = value
                else:
                    asset_stats.avg_calculation_time = (asset_stats.avg_calculation_time + value) / 2
            elif stat_type == StatisticType.ERROR_RATE:
                if value > 0:
                    asset_stats.errors_count += 1
            
            # تحديث معدل النجاح
            total_operations = asset_stats.data_points_collected + asset_stats.indicators_calculated
            if total_operations > 0:
                asset_stats.success_rate = ((total_operations - asset_stats.errors_count) / total_operations) * 100
            
            # تحديث نقاط الأداء
            asset_stats.performance_score = self._calculate_performance_score(asset_stats)
            
            # تحديث الحالة
            asset_stats.status = self._determine_asset_status(asset_stats)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الأصل {asset}: {str(e)}")

    def _calculate_performance_score(self, asset_stats: AssetPerformanceStats) -> float:
        """حساب نقاط الأداء للأصل"""
        try:
            score = 100.0
            
            # خصم نقاط حسب معدل الأخطاء
            if asset_stats.success_rate < 95:
                score -= (95 - asset_stats.success_rate) * 2
            
            # خصم نقاط حسب وقت الحساب
            if asset_stats.avg_calculation_time > 1000:  # أكثر من ثانية
                score -= min(20, (asset_stats.avg_calculation_time - 1000) / 100)
            
            # خصم نقاط حسب النشاط
            if asset_stats.last_activity:
                inactive_minutes = (datetime.now() - asset_stats.last_activity).total_seconds() / 60
                if inactive_minutes > 10:
                    score -= min(30, inactive_minutes)
            
            return max(0, score)
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الأداء: {str(e)}")
            return 50.0

    def _determine_asset_status(self, asset_stats: AssetPerformanceStats) -> str:
        """تحديد حالة الأصل"""
        try:
            if asset_stats.last_activity:
                inactive_minutes = (datetime.now() - asset_stats.last_activity).total_seconds() / 60
                if inactive_minutes > 30:
                    return "inactive"
            
            if asset_stats.success_rate < 80:
                return "error"
            elif asset_stats.avg_calculation_time > 2000:  # أكثر من ثانيتين
                return "slow"
            else:
                return "active"
                
        except Exception as e:
            logger.error(f"خطأ في تحديد حالة الأصل: {str(e)}")
            return "unknown"

    def _invalidate_cache(self, stat_type: StatisticType, asset: str = None):
        """إبطال الكاش للملخصات"""
        try:
            # إبطال الكاش للإحصائية المحددة
            cache_keys_to_remove = []
            
            for cache_key in self.summary_cache.keys():
                if stat_type.value in cache_key:
                    if not asset or asset in cache_key or 'system' in cache_key:
                        cache_keys_to_remove.append(cache_key)
            
            for key in cache_keys_to_remove:
                del self.summary_cache[key]
                self.system_stats['cache_misses'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في إبطال الكاش: {str(e)}")

    def get_statistic_summary(self, 
                            stat_type: StatisticType,
                            timeframe: TimeFrame,
                            asset: str = None) -> Optional[StatisticSummary]:
        """الحصول على ملخص إحصائي"""
        try:
            # إنشاء مفتاح الكاش
            cache_key = f"{stat_type.value}_{timeframe.value}_{asset or 'system'}"
            
            # فحص الكاش أولاً
            if cache_key in self.summary_cache:
                cached_summary = self.summary_cache[cache_key]
                # فحص إذا كان الكاش حديث (أقل من دقيقة)
                if (datetime.now() - cached_summary.last_updated).total_seconds() < 60:
                    self.system_stats['cache_hits'] += 1
                    return cached_summary
            
            # حساب الملخص الجديد
            summary = self._calculate_statistic_summary(stat_type, timeframe, asset)
            
            if summary:
                # حفظ في الكاش
                self.summary_cache[cache_key] = summary
                self.system_stats['statistics_calculated'] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على ملخص الإحصائية: {str(e)}")
            return None

    def _calculate_statistic_summary(self, 
                                   stat_type: StatisticType,
                                   timeframe: TimeFrame,
                                   asset: str = None) -> Optional[StatisticSummary]:
        """حساب ملخص إحصائي"""
        try:
            # تحديد المخزن والفترة الزمنية
            storage_key = asset if asset else 'system'
            
            if stat_type not in self.statistics_data or storage_key not in self.statistics_data[stat_type]:
                return None
            
            data_points = self.statistics_data[stat_type][storage_key]
            
            if not data_points:
                return None
            
            # فلترة البيانات حسب الإطار الزمني
            cutoff_time = datetime.now() - timedelta(seconds=self.aggregation_intervals[timeframe])
            filtered_points = [
                point for point in data_points
                if point.timestamp > cutoff_time
            ]
            
            if not filtered_points:
                return None
            
            # استخراج القيم
            values = [point.value for point in filtered_points]
            
            # حساب الإحصائيات
            summary = StatisticSummary(
                statistic_type=stat_type,
                timeframe=timeframe,
                asset=asset,
                count=len(values),
                min_value=min(values),
                max_value=max(values),
                avg_value=statistics.mean(values),
                median_value=statistics.median(values),
                last_updated=datetime.now()
            )
            
            # حساب الانحراف المعياري
            if len(values) > 1:
                summary.std_deviation = statistics.stdev(values)
            
            # حساب المئينات
            if len(values) >= 20:  # نحتاج عينة كافية
                sorted_values = sorted(values)
                summary.percentile_95 = sorted_values[int(len(sorted_values) * 0.95)]
                summary.percentile_99 = sorted_values[int(len(sorted_values) * 0.99)]
            
            # حساب الاتجاه
            trend_info = self._calculate_trend(values)
            summary.trend_direction = trend_info['direction']
            summary.trend_strength = trend_info['strength']
            
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في حساب ملخص الإحصائية: {str(e)}")
            return None

    def _calculate_trend(self, values: List[float]) -> Dict[str, Any]:
        """حساب اتجاه البيانات"""
        try:
            if len(values) < 10:
                return {'direction': 'stable', 'strength': 0.0}
            
            # تقسيم البيانات لنصفين
            mid_point = len(values) // 2
            first_half = values[:mid_point]
            second_half = values[mid_point:]
            
            first_avg = statistics.mean(first_half)
            second_avg = statistics.mean(second_half)
            
            # حساب التغيير النسبي
            if first_avg != 0:
                change_percent = ((second_avg - first_avg) / first_avg) * 100
            else:
                change_percent = 0
            
            # تحديد الاتجاه
            if abs(change_percent) < 5:
                direction = 'stable'
            elif change_percent > 0:
                direction = 'up'
            else:
                direction = 'down'
            
            # قوة الاتجاه
            strength = min(abs(change_percent) / 20, 1.0)  # تطبيع إلى 0-1
            
            return {
                'direction': direction,
                'strength': strength,
                'change_percent': change_percent
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب الاتجاه: {str(e)}")
            return {'direction': 'stable', 'strength': 0.0}

    def _collection_loop(self):
        """حلقة جمع الإحصائيات"""
        try:
            while self.statistics_active:
                try:
                    # جمع إحصائيات النظام
                    self._collect_system_statistics()
                    
                    # تحديث وقت آخر جمع
                    self.system_stats['last_collection_time'] = datetime.now()
                    
                    # انتظار حتى الجمع التالي
                    time.sleep(self.collection_interval)
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة جمع الإحصائيات: {str(e)}")
                    self.system_stats['collection_errors'] += 1
                    time.sleep(60)
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة جمع الإحصائيات: {str(e)}")

    def _aggregation_loop(self):
        """حلقة تجميع وتنظيف البيانات"""
        try:
            while self.statistics_active:
                try:
                    # تنظيف البيانات القديمة
                    self._cleanup_old_data()
                    
                    # تجميع البيانات للإطارات الزمنية الطويلة
                    self._aggregate_data()
                    
                    # انتظار (كل 10 دقائق)
                    time.sleep(600)
                    
                except Exception as e:
                    logger.error(f"خطأ في دورة التجميع: {str(e)}")
                    time.sleep(300)
                    
        except Exception as e:
            logger.error(f"خطأ في حلقة التجميع: {str(e)}")

    def _collect_system_statistics(self):
        """جمع إحصائيات النظام"""
        try:
            # هذه الدالة ستتكامل مع أنظمة المراقبة الأخرى
            # لجمع إحصائيات الأداء الحالية
            
            # محاكاة إحصائيات النظام للاختبار
            import random
            memory_usage = random.uniform(40, 80)  # محاكاة استخدام الذاكرة
            self.record_statistic(StatisticType.MEMORY_USAGE, memory_usage)

            cpu_usage = random.uniform(20, 60)  # محاكاة استخدام المعالج
            self.record_statistic(StatisticType.CPU_UTILIZATION, cpu_usage)
            
        except Exception as e:
            logger.error(f"خطأ في جمع إحصائيات النظام: {str(e)}")

    def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.retention_days)
            
            for stat_type in self.statistics_data:
                for storage_key in self.statistics_data[stat_type]:
                    data_queue = self.statistics_data[stat_type][storage_key]
                    
                    # إزالة النقاط القديمة
                    while data_queue and data_queue[0].timestamp < cutoff_time:
                        data_queue.popleft()
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")

    def _aggregate_data(self):
        """تجميع البيانات للإطارات الزمنية الطويلة"""
        try:
            # تجميع البيانات لتوفير مساحة وتحسين الأداء
            # يمكن تطوير هذه الدالة لاحقاً حسب الحاجة
            pass
            
        except Exception as e:
            logger.error(f"خطأ في تجميع البيانات: {str(e)}")

    @handle_errors(default_return={}, log_error=True)
    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        try:
            return {
                'system_status': {
                    'statistics_active': self.statistics_active,
                    'collection_interval': self.collection_interval,
                    'retention_days': self.retention_days
                },
                'data_statistics': {
                    'total_data_points': self.system_stats['total_data_points'],
                    'statistics_calculated': self.system_stats['statistics_calculated'],
                    'collection_errors': self.system_stats['collection_errors'],
                    'last_collection_time': self.system_stats['last_collection_time'].isoformat() if self.system_stats['last_collection_time'] else None
                },
                'cache_statistics': {
                    'cache_hits': self.system_stats['cache_hits'],
                    'cache_misses': self.system_stats['cache_misses'],
                    'cache_hit_ratio': (
                        self.system_stats['cache_hits'] /
                        max(self.system_stats['cache_hits'] + self.system_stats['cache_misses'], 1)
                    ) * 100,
                    'cached_summaries': len(self.summary_cache)
                },
                'assets_statistics': {
                    'total_assets': len(self.asset_statistics),
                    'active_assets': len([a for a in self.asset_statistics.values() if a.status == 'active']),
                    'slow_assets': len([a for a in self.asset_statistics.values() if a.status == 'slow']),
                    'error_assets': len([a for a in self.asset_statistics.values() if a.status == 'error']),
                    'inactive_assets': len([a for a in self.asset_statistics.values() if a.status == 'inactive'])
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات النظام: {str(e)}")
            return {}

    def get_asset_performance_overview(self) -> Dict[str, Any]:
        """الحصول على نظرة عامة على أداء الأصول"""
        try:
            overview = {
                'summary': {
                    'total_assets': len(self.asset_statistics),
                    'avg_performance_score': 0.0,
                    'total_data_points': 0,
                    'total_indicators_calculated': 0,
                    'total_errors': 0
                },
                'performance_distribution': {
                    'excellent': 0,  # 90-100
                    'good': 0,       # 70-89
                    'fair': 0,       # 50-69
                    'poor': 0        # 0-49
                },
                'status_distribution': {
                    'active': 0,
                    'slow': 0,
                    'error': 0,
                    'inactive': 0
                },
                'top_performers': [],
                'worst_performers': [],
                'most_active_assets': [],
                'timestamp': datetime.now().isoformat()
            }

            # جمع الإحصائيات
            total_score = 0
            asset_scores = []

            for asset, stats in self.asset_statistics.items():
                total_score += stats.performance_score
                overview['summary']['total_data_points'] += stats.data_points_collected
                overview['summary']['total_indicators_calculated'] += stats.indicators_calculated
                overview['summary']['total_errors'] += stats.errors_count

                # توزيع الأداء
                if stats.performance_score >= 90:
                    overview['performance_distribution']['excellent'] += 1
                elif stats.performance_score >= 70:
                    overview['performance_distribution']['good'] += 1
                elif stats.performance_score >= 50:
                    overview['performance_distribution']['fair'] += 1
                else:
                    overview['performance_distribution']['poor'] += 1

                # توزيع الحالة
                overview['status_distribution'][stats.status] += 1

                # إضافة للقوائم
                asset_scores.append({
                    'asset': asset,
                    'score': stats.performance_score,
                    'data_points': stats.data_points_collected,
                    'indicators': stats.indicators_calculated,
                    'status': stats.status
                })

            # حساب المتوسط
            if self.asset_statistics:
                overview['summary']['avg_performance_score'] = total_score / len(self.asset_statistics)

            # ترتيب الأصول
            asset_scores.sort(key=lambda x: x['score'], reverse=True)

            # أفضل وأسوأ الأصول
            overview['top_performers'] = asset_scores[:10]
            overview['worst_performers'] = asset_scores[-10:]

            # أكثر الأصول نشاطاً
            asset_scores.sort(key=lambda x: x['data_points'], reverse=True)
            overview['most_active_assets'] = asset_scores[:10]

            return overview

        except Exception as e:
            logger.error(f"خطأ في إنشاء نظرة عامة على الأداء: {str(e)}")
            return {}

    def get_detailed_asset_statistics(self, asset: str) -> Optional[Dict[str, Any]]:
        """الحصول على إحصائيات مفصلة لأصل معين"""
        try:
            if asset not in self.asset_statistics:
                return None

            asset_stats = self.asset_statistics[asset]

            # جمع الملخصات الإحصائية للأصل
            summaries = {}
            for stat_type in StatisticType:
                for timeframe in [TimeFrame.HOUR, TimeFrame.DAY]:
                    summary = self.get_statistic_summary(stat_type, timeframe, asset)
                    if summary:
                        key = f"{stat_type.value}_{timeframe.value}"
                        summaries[key] = {
                            'count': summary.count,
                            'avg': summary.avg_value,
                            'min': summary.min_value,
                            'max': summary.max_value,
                            'trend': summary.trend_direction
                        }

            return {
                'asset': asset,
                'basic_stats': {
                    'data_points_collected': asset_stats.data_points_collected,
                    'indicators_calculated': asset_stats.indicators_calculated,
                    'errors_count': asset_stats.errors_count,
                    'success_rate': asset_stats.success_rate,
                    'performance_score': asset_stats.performance_score,
                    'status': asset_stats.status,
                    'last_activity': asset_stats.last_activity.isoformat() if asset_stats.last_activity else None
                },
                'performance_metrics': {
                    'avg_collection_time': asset_stats.avg_collection_time,
                    'avg_calculation_time': asset_stats.avg_calculation_time
                },
                'detailed_statistics': summaries,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في جلب إحصائيات الأصل {asset}: {str(e)}")
            return None

    def get_comparative_analysis(self, assets: List[str], stat_type: StatisticType, timeframe: TimeFrame) -> Dict[str, Any]:
        """تحليل مقارن بين الأصول"""
        try:
            analysis = {
                'statistic_type': stat_type.value,
                'timeframe': timeframe.value,
                'assets_count': len(assets),
                'comparison_data': [],
                'summary': {
                    'best_performer': None,
                    'worst_performer': None,
                    'avg_value': 0.0,
                    'value_range': 0.0
                },
                'timestamp': datetime.now().isoformat()
            }

            asset_values = []

            for asset in assets:
                if asset in CURRENCY_PAIRS_70:
                    summary = self.get_statistic_summary(stat_type, timeframe, asset)
                    if summary:
                        asset_data = {
                            'asset': asset,
                            'avg_value': summary.avg_value,
                            'min_value': summary.min_value,
                            'max_value': summary.max_value,
                            'trend': summary.trend_direction,
                            'data_points': summary.count
                        }
                        analysis['comparison_data'].append(asset_data)
                        asset_values.append(summary.avg_value)

            # حساب الملخص
            if asset_values:
                analysis['summary']['avg_value'] = statistics.mean(asset_values)
                analysis['summary']['value_range'] = max(asset_values) - min(asset_values)

                # أفضل وأسوأ أداء
                sorted_data = sorted(analysis['comparison_data'], key=lambda x: x['avg_value'])
                analysis['summary']['worst_performer'] = sorted_data[0]['asset']
                analysis['summary']['best_performer'] = sorted_data[-1]['asset']

            return analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل المقارن: {str(e)}")
            return {}

    def generate_performance_report(self, timeframe: TimeFrame = TimeFrame.DAY) -> Dict[str, Any]:
        """إنشاء تقرير أداء شامل"""
        try:
            report = {
                'report_timeframe': timeframe.value,
                'generation_time': datetime.now().isoformat(),
                'system_overview': self.get_system_statistics(),
                'assets_overview': self.get_asset_performance_overview(),
                'key_metrics': {},
                'performance_trends': {},
                'recommendations': [],
                'alerts': []
            }

            # المقاييس الرئيسية
            key_metrics = {}
            for stat_type in [StatisticType.DATA_COLLECTION_RATE, StatisticType.INDICATOR_CALCULATION_SPEED,
                             StatisticType.ERROR_RATE, StatisticType.SUCCESS_RATE]:
                summary = self.get_statistic_summary(stat_type, timeframe)
                if summary:
                    key_metrics[stat_type.value] = {
                        'avg_value': summary.avg_value,
                        'trend': summary.trend_direction,
                        'trend_strength': summary.trend_strength
                    }

            report['key_metrics'] = key_metrics

            # اتجاهات الأداء
            trends = {}
            for stat_type in StatisticType:
                summary = self.get_statistic_summary(stat_type, timeframe)
                if summary and summary.trend_direction != 'stable':
                    trends[stat_type.value] = {
                        'direction': summary.trend_direction,
                        'strength': summary.trend_strength
                    }

            report['performance_trends'] = trends

            # التوصيات
            report['recommendations'] = self._generate_performance_recommendations(report)

            # التنبيهات
            report['alerts'] = self._generate_performance_alerts(report)

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الأداء: {str(e)}")
            return {}

    def _generate_performance_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات الأداء"""
        recommendations = []

        try:
            assets_overview = report.get('assets_overview', {})

            # فحص الأصول غير النشطة
            inactive_count = assets_overview.get('status_distribution', {}).get('inactive', 0)
            if inactive_count > 5:
                recommendations.append(f"إعادة تشغيل {inactive_count} أصل غير نشط")

            # فحص الأصول البطيئة
            slow_count = assets_overview.get('status_distribution', {}).get('slow', 0)
            if slow_count > 10:
                recommendations.append(f"تحسين أداء {slow_count} أصل بطيء")

            # فحص الأخطاء
            error_count = assets_overview.get('status_distribution', {}).get('error', 0)
            if error_count > 3:
                recommendations.append(f"إصلاح مشاكل {error_count} أصل بأخطاء")

            # فحص الأداء العام
            avg_score = assets_overview.get('summary', {}).get('avg_performance_score', 100)
            if avg_score < 80:
                recommendations.append(f"تحسين الأداء العام - النقاط الحالية: {avg_score:.1f}")

            if not recommendations:
                recommendations.append("الأداء ممتاز - استمر في المراقبة")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            recommendations.append("خطأ في تحليل البيانات")

        return recommendations

    def _generate_performance_alerts(self, report: Dict[str, Any]) -> List[str]:
        """إنشاء تنبيهات الأداء"""
        alerts = []

        try:
            # فحص الاتجاهات السلبية
            trends = report.get('performance_trends', {})
            for metric, trend_info in trends.items():
                if trend_info['direction'] == 'down' and trend_info['strength'] > 0.5:
                    alerts.append(f"انخفاض في {metric}")

            # فحص المقاييس الحرجة
            key_metrics = report.get('key_metrics', {})

            error_rate = key_metrics.get('error_rate', {}).get('avg_value', 0)
            if error_rate > 10:
                alerts.append(f"معدل أخطاء مرتفع: {error_rate:.1f}%")

            success_rate = key_metrics.get('success_rate', {}).get('avg_value', 100)
            if success_rate < 90:
                alerts.append(f"معدل نجاح منخفض: {success_rate:.1f}%")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التنبيهات: {str(e)}")
            alerts.append("خطأ في تحليل التنبيهات")

        return alerts

# إنشاء مثيل عام للاستخدام
performance_statistics_system = PerformanceStatisticsSystem()

# دوال مساعدة للوصول السريع
def start_statistics_collection() -> bool:
    """بدء جمع الإحصائيات"""
    return performance_statistics_system.start_statistics_collection()

def stop_statistics_collection():
    """إيقاف جمع الإحصائيات"""
    performance_statistics_system.stop_statistics_collection()

def record_statistic(stat_type: StatisticType, value: float, asset: str = None, metadata: Dict[str, Any] = None):
    """تسجيل إحصائية"""
    performance_statistics_system.record_statistic(stat_type, value, asset, metadata)

def get_statistic_summary(stat_type: StatisticType, timeframe: TimeFrame, asset: str = None) -> Optional[StatisticSummary]:
    """الحصول على ملخص إحصائي"""
    return performance_statistics_system.get_statistic_summary(stat_type, timeframe, asset)

def get_system_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات النظام"""
    return performance_statistics_system.get_system_statistics()

def get_asset_performance(asset: str) -> Optional[Dict[str, Any]]:
    """الحصول على أداء أصل معين"""
    return performance_statistics_system.get_detailed_asset_statistics(asset)

def get_performance_report(timeframe: TimeFrame = TimeFrame.DAY) -> Dict[str, Any]:
    """الحصول على تقرير الأداء"""
    return performance_statistics_system.generate_performance_report(timeframe)
