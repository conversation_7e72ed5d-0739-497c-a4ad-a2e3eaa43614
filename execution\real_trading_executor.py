"""
محرك التنفيذ الآلي الفعلي للصفقات - متكامل مع النظام الرئيسي
Real Trading Executor for Pocket Option Platform - Integrated with Main System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
import json
import websocket
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70

logger = scalping_logger.get_logger("real_trading_executor")

class TradeDirection(Enum):
    """اتجاه التداول"""
    CALL = "CALL"  # صاعد
    PUT = "PUT"    # هابط

class TradeStatus(Enum):
    """حالة الصفقة"""
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    WON = "WON"
    LOST = "LOST"
    CANCELLED = "CANCELLED"

@dataclass
class TradeOrder:
    """أمر التداول"""
    asset: str
    direction: TradeDirection
    amount: float
    expiry_time: int  # بالثواني
    confidence: float
    signal_source: str
    timestamp: datetime
    execution_time: Optional[datetime] = None
    trade_id: Optional[str] = None
    status: TradeStatus = TradeStatus.PENDING
    result: Optional[Dict[str, Any]] = None

@dataclass
class ExecutionResult:
    """نتيجة التنفيذ"""
    success: bool
    trade_id: Optional[str]
    message: str
    execution_time: datetime
    order: TradeOrder

class RealTradingExecutor:
    """محرك التنفيذ الآلي الفعلي"""
    
    def __init__(self):
        self.is_connected = False
        self.is_trading_enabled = False
        self.ws = None
        self.ssid = os.getenv('POCKET_OPTION_SSID', '')
        
        # إعدادات التداول
        self.min_trade_amount = 1.0
        self.max_trade_amount = 100.0
        self.default_expiry = 60
        self.min_confidence = 0.75
        
        # إدارة الصفقات
        self.pending_orders = []
        self.executed_trades = []
        self.last_trade_time = {}
        self.daily_trade_count = 0
        self.max_daily_trades = 50
        
        # إحصائيات
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'last_update': None
        }
        
        logger.info("تم تهيئة محرك التنفيذ الآلي الفعلي")

    async def initialize_connection(self) -> bool:
        """تهيئة الاتصال بالمنصة"""
        try:
            if not self.ssid:
                logger.error("SSID غير موجود في متغيرات البيئة")
                return False
            
            logger.info("🔌 بدء الاتصال بمنصة Pocket Option...")
            
            # إنشاء اتصال WebSocket
            await self._connect_websocket()
            
            if self.is_connected:
                logger.info("✅ تم الاتصال بمنصة Pocket Option بنجاح")
                return True
            else:
                logger.error("❌ فشل في الاتصال بمنصة Pocket Option")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تهيئة الاتصال: {str(e)}")
            return False

    async def _connect_websocket(self):
        """إنشاء اتصال WebSocket"""
        try:
            def on_message(ws, message):
                try:
                    data = json.loads(message)
                    asyncio.create_task(self._handle_message(data))
                except Exception as e:
                    logger.error(f"خطأ في معالجة الرسالة: {str(e)}")

            def on_error(ws, error):
                logger.error(f"خطأ في WebSocket: {str(error)}")
                self.is_connected = False

            def on_close(ws, close_status_code, close_msg):
                logger.warning("تم إغلاق اتصال WebSocket")
                self.is_connected = False

            def on_open(ws):
                logger.info("تم فتح اتصال WebSocket")
                self.is_connected = True
                # إرسال رسالة المصادقة
                auth_message = self.ssid
                ws.send(auth_message)

            # قائمة عناوين WebSocket للمحاولة
            websocket_urls = [
                "wss://ws2.pocketoption.com/echo/websocket",
                "wss://ws.pocketoption.com/echo/websocket",
                "wss://api.pocketoption.com/websocket"
            ]

            # محاولة الاتصال بكل عنوان
            for url in websocket_urls:
                try:
                    logger.info(f"🔄 محاولة الاتصال بـ {url}")

                    # إنشاء WebSocket
                    websocket.enableTrace(False)
                    self.ws = websocket.WebSocketApp(
                        url,
                        on_message=on_message,
                        on_error=on_error,
                        on_close=on_close,
                        on_open=on_open
                    )

                    # تشغيل WebSocket في thread منفصل
                    def run_websocket():
                        self.ws.run_forever()

                    ws_thread = threading.Thread(target=run_websocket, daemon=True)
                    ws_thread.start()

                    # انتظار الاتصال
                    for _ in range(5):  # انتظار 5 ثوان لكل محاولة
                        if self.is_connected:
                            logger.info(f"✅ نجح الاتصال بـ {url}")
                            return
                        await asyncio.sleep(1)

                    # إذا لم ينجح الاتصال، أغلق WebSocket وجرب التالي
                    if self.ws:
                        self.ws.close()

                except Exception as e:
                    logger.warning(f"⚠️ فشل الاتصال بـ {url}: {str(e)}")
                    continue

            # إذا فشلت جميع المحاولات
            logger.error("❌ فشل في الاتصال بجميع عناوين WebSocket")
            self.is_connected = False

        except Exception as e:
            logger.error(f"خطأ في إنشاء WebSocket: {str(e)}")
            self.is_connected = False

    async def _handle_message(self, data: Dict[str, Any]):
        """معالجة الرسائل الواردة من المنصة"""
        try:
            if isinstance(data, list) and len(data) >= 2:
                message_type = data[0]
                message_data = data[1] if len(data) > 1 else {}
                
                if message_type == "auth":
                    await self._handle_auth_response(message_data)
                elif message_type == "trade-result":
                    await self._handle_trade_result(message_data)
                elif message_type == "balance":
                    await self._handle_balance_update(message_data)
                elif message_type == "candles":
                    await self._handle_candle_data(message_data)
                    
        except Exception as e:
            logger.error(f"خطأ في معالجة الرسالة: {str(e)}")

    async def _handle_auth_response(self, data: Dict[str, Any]):
        """معالجة رد المصادقة"""
        try:
            if data.get('success'):
                logger.info("✅ تم تسجيل الدخول بنجاح")
                self.is_trading_enabled = True
                
                # طلب معلومات الحساب
                await self._request_account_info()
            else:
                logger.error("❌ فشل في تسجيل الدخول")
                self.is_trading_enabled = False
                
        except Exception as e:
            logger.error(f"خطأ في معالجة رد المصادقة: {str(e)}")

    async def _request_account_info(self):
        """طلب معلومات الحساب"""
        try:
            if self.ws and self.is_connected:
                account_request = json.dumps(["profile", {}])
                self.ws.send(account_request)
                
                balance_request = json.dumps(["balance", {}])
                self.ws.send(balance_request)
                
        except Exception as e:
            logger.error(f"خطأ في طلب معلومات الحساب: {str(e)}")

    async def _handle_trade_result(self, data: Dict[str, Any]):
        """معالجة نتائج الصفقات"""
        try:
            trade_id = data.get('id')
            result = data.get('result')  # 'win' أو 'lose'
            profit = data.get('profit', 0.0)
            
            # البحث عن الصفقة في القائمة المعلقة
            for order in self.pending_orders:
                if order.trade_id == trade_id:
                    if result == 'win':
                        order.status = TradeStatus.WON
                        self.stats['winning_trades'] += 1
                        self.stats['total_profit'] += profit
                        logger.info(f"🎉 ربح صفقة {order.asset}: +${profit:.2f}")
                    else:
                        order.status = TradeStatus.LOST
                        self.stats['losing_trades'] += 1
                        self.stats['total_profit'] -= order.amount
                        logger.info(f"💔 خسارة صفقة {order.asset}: -${order.amount:.2f}")
                    
                    order.result = data
                    self.executed_trades.append(order)
                    self.pending_orders.remove(order)
                    break
            
            # تحديث معدل الفوز
            total_completed = self.stats['winning_trades'] + self.stats['losing_trades']
            if total_completed > 0:
                self.stats['win_rate'] = self.stats['winning_trades'] / total_completed
            
            self.stats['last_update'] = datetime.now()
            
        except Exception as e:
            logger.error(f"خطأ في معالجة نتيجة الصفقة: {str(e)}")

    async def _handle_balance_update(self, data: Dict[str, Any]):
        """معالجة تحديث الرصيد"""
        try:
            balance = data.get('balance', 0.0)
            logger.info(f"💰 رصيد الحساب: ${balance:.2f}")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة تحديث الرصيد: {str(e)}")

    async def _handle_candle_data(self, data: Dict[str, Any]):
        """معالجة بيانات الشموع الواردة"""
        try:
            # يمكن استخدام هذه البيانات للتحليل اللحظي
            asset = data.get('asset')
            candle = data.get('candle')
            
            if asset and candle:
                logger.debug(f"📊 شمعة جديدة لـ {asset}: {candle}")
                
        except Exception as e:
            logger.error(f"خطأ في معالجة بيانات الشموع: {str(e)}")

    @handle_async_errors(default_return=None, log_error=True)
    async def execute_trade(self, order: TradeOrder) -> Optional[ExecutionResult]:
        """تنفيذ صفقة فعلية"""
        try:
            if not self.is_connected or not self.is_trading_enabled:
                return ExecutionResult(
                    success=False,
                    trade_id=None,
                    message="المنصة غير متصلة أو التداول غير مفعل",
                    execution_time=datetime.now(),
                    order=order
                )

            # فحص الشروط
            validation_result = await self._validate_trade_order(order)
            if not validation_result['valid']:
                return ExecutionResult(
                    success=False,
                    trade_id=None,
                    message=validation_result['reason'],
                    execution_time=datetime.now(),
                    order=order
                )

            logger.info(f"🎯 تنفيذ صفقة {order.direction.value} على {order.asset} بمبلغ ${order.amount}")

            # إنشاء رسالة التداول
            trade_message = self._create_trade_message(order)

            # إرسال الصفقة
            if self.ws and self.is_connected:
                self.ws.send(json.dumps(trade_message))

                # إضافة للصفقات المعلقة
                order.execution_time = datetime.now()
                order.status = TradeStatus.EXECUTED
                order.trade_id = f"trade_{int(time.time())}"
                self.pending_orders.append(order)

                # تحديث الإحصائيات
                self.daily_trade_count += 1
                self.stats['total_trades'] += 1

                # تسجيل آخر وقت تداول للأصل
                self.last_trade_time[order.asset] = datetime.now()

                logger.info(f"✅ تم إرسال صفقة {order.asset} بنجاح")

                return ExecutionResult(
                    success=True,
                    trade_id=order.trade_id,
                    message="تم تنفيذ الصفقة بنجاح",
                    execution_time=order.execution_time,
                    order=order
                )
            else:
                return ExecutionResult(
                    success=False,
                    trade_id=None,
                    message="فقدان الاتصال بالمنصة",
                    execution_time=datetime.now(),
                    order=order
                )

        except Exception as e:
            logger.error(f"خطأ في تنفيذ الصفقة: {str(e)}")
            return ExecutionResult(
                success=False,
                trade_id=None,
                message=f"خطأ في التنفيذ: {str(e)}",
                execution_time=datetime.now(),
                order=order
            )

    async def _validate_trade_order(self, order: TradeOrder) -> Dict[str, Any]:
        """التحقق من صحة أمر التداول"""
        try:
            # فحص الثقة
            if order.confidence < self.min_confidence:
                return {
                    'valid': False,
                    'reason': f"مستوى الثقة منخفض: {order.confidence:.2f} < {self.min_confidence}"
                }

            # فحص المبلغ
            if order.amount < self.min_trade_amount or order.amount > self.max_trade_amount:
                return {
                    'valid': False,
                    'reason': f"مبلغ الصفقة خارج النطاق المسموح: ${order.amount}"
                }

            # فحص الحد اليومي
            if self.daily_trade_count >= self.max_daily_trades:
                return {
                    'valid': False,
                    'reason': f"تم الوصول للحد الأقصى اليومي: {self.max_daily_trades} صفقة"
                }

            # فحص التداول المتكرر
            if order.asset in self.last_trade_time:
                time_diff = datetime.now() - self.last_trade_time[order.asset]
                if time_diff.total_seconds() < 60:  # منع التداول خلال دقيقة واحدة
                    return {
                        'valid': False,
                        'reason': f"تداول متكرر على {order.asset} - انتظر {60 - int(time_diff.total_seconds())} ثانية"
                    }

            # فحص الأصل
            if order.asset not in CURRENCY_PAIRS_70:
                return {
                    'valid': False,
                    'reason': f"الأصل {order.asset} غير مدعوم"
                }

            return {'valid': True, 'reason': 'صالح للتنفيذ'}

        except Exception as e:
            logger.error(f"خطأ في التحقق من الأمر: {str(e)}")
            return {'valid': False, 'reason': f"خطأ في التحقق: {str(e)}"}

    def _create_trade_message(self, order: TradeOrder) -> List[Any]:
        """إنشاء رسالة التداول"""
        try:
            # تحويل اتجاه التداول
            direction = "call" if order.direction == TradeDirection.CALL else "put"

            # إنشاء رسالة التداول
            trade_data = {
                "asset": order.asset,
                "amount": order.amount,
                "action": direction,
                "isDemo": 1,  # تداول تجريبي
                "requestId": int(time.time()),
                "optionType": 100,  # خيارات ثنائية
                "time": order.expiry_time
            }

            return ["openTrade", trade_data]

        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة التداول: {str(e)}")
            return ["error", {"message": str(e)}]

    async def create_trade_order(self, asset: str, direction: str, confidence: float,
                               signal_source: str, amount: float = None) -> TradeOrder:
        """إنشاء أمر تداول جديد"""
        try:
            # تحديد المبلغ إذا لم يتم تحديده
            if amount is None:
                # حساب المبلغ بناء على مستوى الثقة
                if confidence >= 0.9:
                    amount = 10.0
                elif confidence >= 0.8:
                    amount = 5.0
                else:
                    amount = 2.0

            # تحويل الاتجاه
            trade_direction = TradeDirection.CALL if direction.upper() in ['CALL', 'BUY', 'BULLISH'] else TradeDirection.PUT

            order = TradeOrder(
                asset=asset,
                direction=trade_direction,
                amount=amount,
                expiry_time=self.default_expiry,
                confidence=confidence,
                signal_source=signal_source,
                timestamp=datetime.now()
            )

            return order

        except Exception as e:
            logger.error(f"خطأ في إنشاء أمر التداول: {str(e)}")
            raise

    async def get_trading_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التداول"""
        try:
            return {
                'connection_status': self.is_connected,
                'trading_enabled': self.is_trading_enabled,
                'daily_trades': self.daily_trade_count,
                'pending_orders': len(self.pending_orders),
                'completed_trades': len(self.executed_trades),
                'stats': self.stats.copy(),
                'last_update': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}

    async def stop_trading(self):
        """إيقاف التداول"""
        try:
            self.is_trading_enabled = False

            if self.ws:
                self.ws.close()

            logger.info("تم إيقاف محرك التداول")

        except Exception as e:
            logger.error(f"خطأ في إيقاف التداول: {str(e)}")

# إنشاء مثيل عام
real_trading_executor = RealTradingExecutor()
