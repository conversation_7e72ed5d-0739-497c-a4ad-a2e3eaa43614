"""
نظام إدارة الذاكرة والموارد - إدارة استخدام الذاكرة والموارد بكفاءة للتشغيل المستمر
"""

import gc
import os
import sys
import psutil
import threading
import time
import weakref
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum

from config.trading_config import TradingConfig
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, ScalpingError

logger = scalping_logger.get_logger("resource_manager")

class ResourceType(Enum):
    """أنواع الموارد"""
    MEMORY = "memory"
    CPU = "cpu"
    DISK = "disk"
    NETWORK = "network"
    FILE_HANDLES = "file_handles"
    DATABASE_CONNECTIONS = "database_connections"

class OptimizationLevel(Enum):
    """مستويات التحسين"""
    CONSERVATIVE = "conservative"  # تحسين محافظ
    MODERATE = "moderate"         # تحسين متوسط
    AGGRESSIVE = "aggressive"     # تحسين قوي

@dataclass
class ResourceUsage:
    """استخدام الموارد"""
    resource_type: ResourceType
    current_usage: float
    peak_usage: float
    average_usage: float
    threshold_warning: float
    threshold_critical: float
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MemoryProfile:
    """ملف تعريف الذاكرة"""
    total_objects: int
    total_size_mb: float
    largest_objects: List[Dict[str, Any]]
    memory_leaks: List[Dict[str, Any]]
    gc_stats: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)

class ResourceManager:
    """مدير الموارد الرئيسي"""
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.MODERATE):
        self.config = TradingConfig()
        self.optimization_level = optimization_level
        
        # إعدادات المراقبة
        self.monitoring_active = False
        self.monitoring_interval = 30  # ثانية
        self.cleanup_interval = 300    # 5 دقائق
        
        # عتبات الموارد (محسنة للأداء)
        self.resource_thresholds = {
            ResourceType.MEMORY: {'warning': 70.0, 'critical': 85.0},  # تحسين
            ResourceType.CPU: {'warning': 65.0, 'critical': 80.0},     # تحسين
            ResourceType.DISK: {'warning': 75.0, 'critical': 90.0},    # تحسين
            ResourceType.FILE_HANDLES: {'warning': 75.0, 'critical': 90.0}  # تحسين
        }
        
        # تتبع استخدام الموارد
        self.resource_usage_history: Dict[ResourceType, deque] = {
            resource: deque(maxlen=100) for resource in ResourceType
        }
        
        # إحصائيات الذاكرة
        self.memory_stats = {
            'peak_usage_mb': 0.0,
            'gc_collections': 0,
            'objects_cleaned': 0,
            'memory_freed_mb': 0.0,
            'last_cleanup': None
        }
        
        # تتبع الكائنات الكبيرة
        self.large_objects: Set[weakref.ref] = set()
        self.object_size_threshold = 10 * 1024 * 1024  # 10MB
        
        # callbacks للتنظيف
        self.cleanup_callbacks: List[Callable] = []
        
        # قفل للعمليات المتزامنة
        self._lock = threading.Lock()
        
        # معلومات العملية
        self.process = psutil.Process()
        
        logger.info(f"تم تهيئة مدير الموارد")
        logger.info(f"مستوى التحسين: {optimization_level.value}")
        logger.info(f"فترة المراقبة: {self.monitoring_interval} ثانية")

    @handle_errors(default_return=None, log_error=True)
    def get_current_resource_usage(self) -> Dict[ResourceType, ResourceUsage]:
        """الحصول على استخدام الموارد الحالي"""
        try:
            usage_data = {}
            
            # استخدام الذاكرة
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            usage_data[ResourceType.MEMORY] = ResourceUsage(
                resource_type=ResourceType.MEMORY,
                current_usage=memory_percent,
                peak_usage=max(self.memory_stats['peak_usage_mb'], memory_mb),
                average_usage=self._calculate_average_usage(ResourceType.MEMORY),
                threshold_warning=self.resource_thresholds[ResourceType.MEMORY]['warning'],
                threshold_critical=self.resource_thresholds[ResourceType.MEMORY]['critical'],
                details={
                    'rss_mb': memory_mb,
                    'vms_mb': memory_info.vms / (1024 * 1024),
                    'available_mb': psutil.virtual_memory().available / (1024 * 1024)
                }
            )
            
            # تحديث الذروة
            self.memory_stats['peak_usage_mb'] = max(self.memory_stats['peak_usage_mb'], memory_mb)
            
            # استخدام المعالج
            cpu_percent = self.process.cpu_percent()
            usage_data[ResourceType.CPU] = ResourceUsage(
                resource_type=ResourceType.CPU,
                current_usage=cpu_percent,
                peak_usage=cpu_percent,  # يمكن تحسين هذا لتتبع الذروة
                average_usage=self._calculate_average_usage(ResourceType.CPU),
                threshold_warning=self.resource_thresholds[ResourceType.CPU]['warning'],
                threshold_critical=self.resource_thresholds[ResourceType.CPU]['critical'],
                details={
                    'num_threads': self.process.num_threads(),
                    'cpu_times': self.process.cpu_times()._asdict()
                }
            )
            
            # استخدام القرص
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            usage_data[ResourceType.DISK] = ResourceUsage(
                resource_type=ResourceType.DISK,
                current_usage=disk_percent,
                peak_usage=disk_percent,
                average_usage=self._calculate_average_usage(ResourceType.DISK),
                threshold_warning=self.resource_thresholds[ResourceType.DISK]['warning'],
                threshold_critical=self.resource_thresholds[ResourceType.DISK]['critical'],
                details={
                    'total_gb': disk_usage.total / (1024**3),
                    'used_gb': disk_usage.used / (1024**3),
                    'free_gb': disk_usage.free / (1024**3)
                }
            )
            
            # معالجات الملفات
            try:
                open_files = len(self.process.open_files())
                max_files = 1024  # افتراضي، يمكن الحصول عليه من النظام
                file_handle_percent = (open_files / max_files) * 100
                
                usage_data[ResourceType.FILE_HANDLES] = ResourceUsage(
                    resource_type=ResourceType.FILE_HANDLES,
                    current_usage=file_handle_percent,
                    peak_usage=file_handle_percent,
                    average_usage=self._calculate_average_usage(ResourceType.FILE_HANDLES),
                    threshold_warning=self.resource_thresholds[ResourceType.FILE_HANDLES]['warning'],
                    threshold_critical=self.resource_thresholds[ResourceType.FILE_HANDLES]['critical'],
                    details={
                        'open_files': open_files,
                        'max_files': max_files
                    }
                )
            except:
                pass  # قد لا يكون متاحاً في جميع الأنظمة
            
            # حفظ في التاريخ
            with self._lock:
                for resource_type, usage in usage_data.items():
                    self.resource_usage_history[resource_type].append(usage)
            
            return usage_data
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على استخدام الموارد: {str(e)}")
            return {}

    def _calculate_average_usage(self, resource_type: ResourceType) -> float:
        """حساب متوسط الاستخدام"""
        try:
            with self._lock:
                history = self.resource_usage_history[resource_type]
                if not history:
                    return 0.0
                
                return sum(usage.current_usage for usage in history) / len(history)
                
        except Exception as e:
            logger.error(f"خطأ في حساب متوسط الاستخدام: {str(e)}")
            return 0.0

    @handle_errors(default_return=0, log_error=True)
    def perform_memory_cleanup(self, aggressive: bool = False) -> int:
        """تنظيف الذاكرة"""
        try:
            logger.info(f"🧹 بدء تنظيف الذاكرة {'القوي' if aggressive else 'العادي'}")
            
            memory_before = self.process.memory_info().rss / (1024 * 1024)
            objects_before = len(gc.get_objects())
            
            # تنظيف المراجع الضعيفة المنتهية
            dead_refs = [ref for ref in self.large_objects if ref() is None]
            for ref in dead_refs:
                self.large_objects.discard(ref)
            
            # تشغيل callbacks التنظيف
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"خطأ في callback التنظيف: {str(e)}")
            
            # تنظيف garbage collector
            collected = 0
            for generation in range(3):
                collected += gc.collect(generation)
            
            # تنظيف إضافي في الوضع القوي (محسن)
            if aggressive:
                # تشغيل garbage collection عدة مرات
                for _ in range(3):
                    collected += gc.collect()

                # تنظيف الكاش الداخلي
                old_thresholds = gc.get_threshold()
                gc.set_threshold(100, 5, 5)  # تقليل عتبات GC مؤقتاً (محسن)
                collected += gc.collect()
                gc.set_threshold(*old_thresholds)  # إعادة تعيين العتبات الأصلية

                # تنظيف إضافي للكائنات المختلفة
                self._cleanup_temporary_objects()
                self._cleanup_medium_objects()
                self._defragment_memory()
            
            # حساب النتائج
            memory_after = self.process.memory_info().rss / (1024 * 1024)
            objects_after = len(gc.get_objects())
            
            memory_freed = memory_before - memory_after
            objects_cleaned = objects_before - objects_after
            
            # تحديث الإحصائيات
            self.memory_stats['gc_collections'] += 1
            self.memory_stats['objects_cleaned'] += objects_cleaned
            self.memory_stats['memory_freed_mb'] += memory_freed
            self.memory_stats['last_cleanup'] = datetime.now()
            
            logger.info(f"✅ تم تنظيف {objects_cleaned} كائن، تحرير {memory_freed:.1f}MB")
            
            return collected
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الذاكرة: {str(e)}")
            return 0

    @handle_errors(default_return=None, log_error=True)
    def generate_memory_profile(self) -> MemoryProfile:
        """إنتاج ملف تعريف الذاكرة"""
        try:
            logger.debug("📊 إنتاج ملف تعريف الذاكرة")
            
            # جمع جميع الكائنات
            all_objects = gc.get_objects()
            total_objects = len(all_objects)
            
            # تحليل الكائنات الكبيرة
            large_objects = []
            total_size = 0
            
            for obj in all_objects:
                try:
                    size = sys.getsizeof(obj)
                    total_size += size
                    
                    if size > self.object_size_threshold:
                        large_objects.append({
                            'type': type(obj).__name__,
                            'size_mb': size / (1024 * 1024),
                            'id': id(obj),
                            'repr': str(obj)[:100] + '...' if len(str(obj)) > 100 else str(obj)
                        })
                except:
                    continue
            
            # ترتيب الكائنات الكبيرة حسب الحجم
            large_objects.sort(key=lambda x: x['size_mb'], reverse=True)
            
            # إحصائيات garbage collector
            gc_stats = {
                'counts': gc.get_count(),
                'threshold': gc.get_threshold(),
                'stats': gc.get_stats() if hasattr(gc, 'get_stats') else {}
            }
            
            # البحث عن تسريبات الذاكرة المحتملة
            memory_leaks = self._detect_potential_memory_leaks()
            
            profile = MemoryProfile(
                total_objects=total_objects,
                total_size_mb=total_size / (1024 * 1024),
                largest_objects=large_objects[:10],  # أكبر 10 كائنات
                memory_leaks=memory_leaks,
                gc_stats=gc_stats
            )
            
            return profile
            
        except Exception as e:
            logger.error(f"خطأ في إنتاج ملف تعريف الذاكرة: {str(e)}")
            return MemoryProfile(0, 0.0, [], [], {})

    def _detect_potential_memory_leaks(self) -> List[Dict[str, Any]]:
        """اكتشاف تسريبات الذاكرة المحتملة"""
        try:
            leaks = []
            
            # فحص الكائنات التي لها مراجع دائرية
            for obj in gc.garbage:
                leaks.append({
                    'type': 'circular_reference',
                    'object_type': type(obj).__name__,
                    'id': id(obj),
                    'referrers_count': len(gc.get_referrers(obj))
                })
            
            # فحص الكائنات الكبيرة التي لم يتم تنظيفها
            current_large_objects = []
            for ref in self.large_objects:
                obj = ref()
                if obj is not None:
                    current_large_objects.append({
                        'type': 'large_object_not_cleaned',
                        'object_type': type(obj).__name__,
                        'size_mb': sys.getsizeof(obj) / (1024 * 1024),
                        'id': id(obj)
                    })
            
            leaks.extend(current_large_objects)
            
            return leaks
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف تسريبات الذاكرة: {str(e)}")
            return []

    def _cleanup_temporary_objects(self):
        """تنظيف الكائنات المؤقتة"""
        try:
            # تنظيف المتغيرات المحلية غير المستخدمة
            import sys
            frame = sys._getframe()
            while frame:
                if hasattr(frame, 'f_locals'):
                    # تنظيف المتغيرات المؤقتة
                    temp_vars = [k for k in frame.f_locals.keys() if k.startswith('_temp')]
                    for var in temp_vars:
                        if var in frame.f_locals:
                            del frame.f_locals[var]
                frame = frame.f_back
        except Exception as e:
            logger.debug(f"تنظيف الكائنات المؤقتة: {str(e)}")

    def _cleanup_medium_objects(self):
        """تنظيف الكائنات متوسطة الحجم"""
        try:
            # تنظيف الكائنات التي تزيد عن 1MB
            for obj in gc.get_objects():
                if hasattr(obj, '__dict__') and sys.getsizeof(obj) > 1024 * 1024:
                    # تنظيف الكاش الداخلي للكائن
                    if hasattr(obj, 'clear') and callable(getattr(obj, 'clear')):
                        try:
                            obj.clear()
                        except:
                            pass
        except Exception as e:
            logger.debug(f"تنظيف الكائنات المتوسطة: {str(e)}")

    def _defragment_memory(self):
        """إلغاء تجزئة الذاكرة"""
        try:
            # إجبار Python على إعادة تنظيم الذاكرة
            import ctypes
            if hasattr(ctypes, 'pythonapi'):
                # تشغيل compact للذاكرة
                for _ in range(5):
                    gc.collect()

            # تنظيف الكاش العام
            if hasattr(gc, 'set_threshold'):
                # تقليل عتبات GC مؤقتاً لتنظيف أكثر عدوانية
                old_thresholds = gc.get_threshold()
                gc.set_threshold(100, 5, 5)
                gc.collect()
                gc.set_threshold(*old_thresholds)

        except Exception as e:
            logger.debug(f"إلغاء تجزئة الذاكرة: {str(e)}")

    def add_cleanup_callback(self, callback: Callable):
        """إضافة callback للتنظيف"""
        self.cleanup_callbacks.append(callback)
        logger.info(f"تم إضافة callback للتنظيف: {callback.__name__}")

    def register_large_object(self, obj: Any):
        """تسجيل كائن كبير للمراقبة"""
        try:
            size = sys.getsizeof(obj)
            if size > self.object_size_threshold:
                self.large_objects.add(weakref.ref(obj))
                logger.debug(f"تم تسجيل كائن كبير: {type(obj).__name__} ({size / (1024 * 1024):.1f}MB)")
        except Exception as e:
            logger.error(f"خطأ في تسجيل كائن كبير: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def optimize_resources(self) -> bool:
        """تحسين الموارد بناءً على مستوى التحسين"""
        try:
            logger.info(f"🔧 بدء تحسين الموارد - المستوى: {self.optimization_level.value}")
            
            current_usage = self.get_current_resource_usage()
            
            # تحسين الذاكرة
            memory_usage = current_usage.get(ResourceType.MEMORY)
            if memory_usage:
                if memory_usage.current_usage > memory_usage.threshold_critical:
                    # تنظيف قوي
                    self.perform_memory_cleanup(aggressive=True)
                elif memory_usage.current_usage > memory_usage.threshold_warning:
                    # تنظيف عادي
                    self.perform_memory_cleanup(aggressive=False)
            
            # تحسين المعالج
            cpu_usage = current_usage.get(ResourceType.CPU)
            if cpu_usage and cpu_usage.current_usage > cpu_usage.threshold_warning:
                # تقليل أولوية العملية
                if self.optimization_level == OptimizationLevel.AGGRESSIVE:
                    try:
                        self.process.nice(1)  # تقليل الأولوية
                        logger.info("تم تقليل أولوية العملية")
                    except:
                        pass
            
            # تحسين القرص
            disk_usage = current_usage.get(ResourceType.DISK)
            if disk_usage and disk_usage.current_usage > disk_usage.threshold_warning:
                self._cleanup_temporary_files()
            
            logger.info("✅ تم تحسين الموارد")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحسين الموارد: {str(e)}")
            return False

    def _cleanup_temporary_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            temp_dirs = ['logs', 'temp', 'cache']
            cleaned_files = 0
            freed_space = 0

            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                # حذف الملفات القديمة (أكثر من يوم)
                                if os.path.getmtime(file_path) < time.time() - 86400:
                                    file_size = os.path.getsize(file_path)
                                    os.remove(file_path)
                                    cleaned_files += 1
                                    freed_space += file_size
                            except:
                                continue

            if cleaned_files > 0:
                logger.info(f"🗑️ تم تنظيف {cleaned_files} ملف مؤقت، تحرير {freed_space / (1024*1024):.1f}MB")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")

    @handle_errors(default_return=False, log_error=True)
    def start_monitoring(self) -> bool:
        """بدء مراقبة الموارد"""
        try:
            if self.monitoring_active:
                logger.warning("مراقبة الموارد تعمل بالفعل")
                return False

            self.monitoring_active = True
            logger.info(f"🔍 بدء مراقبة الموارد كل {self.monitoring_interval} ثانية")

            # بدء خيط المراقبة
            monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            monitoring_thread.start()

            # بدء خيط التنظيف الدوري
            cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
            cleanup_thread.start()

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة الموارد: {str(e)}")
            return False

    def _monitoring_loop(self):
        """حلقة مراقبة الموارد"""
        try:
            while self.monitoring_active:
                try:
                    # الحصول على استخدام الموارد
                    current_usage = self.get_current_resource_usage()

                    # فحص الحاجة للتحسين
                    needs_optimization = False
                    for resource_type, usage in current_usage.items():
                        if usage.current_usage > usage.threshold_warning:
                            needs_optimization = True
                            logger.warning(f"⚠️ استخدام مرتفع لـ {resource_type.value}: {usage.current_usage:.1f}%")

                    # تحسين الموارد إذا لزم الأمر
                    if needs_optimization:
                        self.optimize_resources()

                    # انتظار حتى الفحص التالي
                    time.sleep(self.monitoring_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة مراقبة الموارد: {str(e)}")
                    time.sleep(60)

        except Exception as e:
            logger.error(f"خطأ في حلقة مراقبة الموارد: {str(e)}")

    def _cleanup_loop(self):
        """حلقة التنظيف الدوري"""
        try:
            while self.monitoring_active:
                try:
                    # انتظار فترة التنظيف
                    time.sleep(self.cleanup_interval)

                    # تنظيف دوري للذاكرة
                    self.perform_memory_cleanup(aggressive=False)

                    # تنظيف الملفات المؤقتة
                    self._cleanup_temporary_files()

                except Exception as e:
                    logger.error(f"خطأ في دورة التنظيف: {str(e)}")
                    time.sleep(300)  # انتظار 5 دقائق عند الخطأ

        except Exception as e:
            logger.error(f"خطأ في حلقة التنظيف: {str(e)}")

    def stop_monitoring(self):
        """إيقاف مراقبة الموارد"""
        self.monitoring_active = False
        logger.info("تم إيقاف مراقبة الموارد")

    @handle_errors(default_return={}, log_error=True)
    def get_resource_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الموارد"""
        try:
            current_usage = self.get_current_resource_usage()

            stats = {
                'current_usage': {},
                'memory_stats': self.memory_stats.copy(),
                'monitoring': {
                    'active': self.monitoring_active,
                    'interval': self.monitoring_interval,
                    'cleanup_interval': self.cleanup_interval,
                    'optimization_level': self.optimization_level.value
                },
                'thresholds': {
                    resource.value: thresholds for resource, thresholds in self.resource_thresholds.items()
                },
                'large_objects_count': len(self.large_objects),
                'cleanup_callbacks_count': len(self.cleanup_callbacks)
            }

            # إضافة الاستخدام الحالي
            for resource_type, usage in current_usage.items():
                stats['current_usage'][resource_type.value] = {
                    'current': usage.current_usage,
                    'peak': usage.peak_usage,
                    'average': usage.average_usage,
                    'status': 'critical' if usage.current_usage > usage.threshold_critical
                             else 'warning' if usage.current_usage > usage.threshold_warning
                             else 'normal'
                }

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الموارد: {str(e)}")
            return {}

# إنشاء مثيل عام لمدير الموارد
resource_manager = ResourceManager()
