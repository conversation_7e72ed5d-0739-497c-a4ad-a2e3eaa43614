"""
محلل أداء الاستراتيجية المتكامل
Strategy Performance Analyzer - Integrated with Main System
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

from utils.logger import scalping_logger
from utils.error_handler import handle_async_errors
from config.currency_pairs import CURRENCY_PAIRS_70
from database.repository import historical_data_repo

# استيراد أنظمة المراقبة الموجودة
from execution.advanced_performance_monitor import advanced_performance_monitor
from execution.performance_statistics_system import performance_statistics_system
from execution.real_trading_executor import real_trading_executor

# استيراد أنظمة التحليل
from data_layer.quadruple_convergence_system import quadruple_convergence_system
from ai_models.ai_predictor_engine import ai_predictor_engine
from ai_models.prediction_tracker import prediction_tracker

logger = scalping_logger.get_logger("strategy_performance_analyzer")

class PerformanceMetric(Enum):
    """مقاييس الأداء"""
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"
    SHARPE_RATIO = "sharpe_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    AVERAGE_TRADE_DURATION = "avg_trade_duration"
    SIGNAL_ACCURACY = "signal_accuracy"
    CONVERGENCE_RATE = "convergence_rate"
    AI_PREDICTION_ACCURACY = "ai_prediction_accuracy"

@dataclass
class StrategyPerformanceMetrics:
    """مقاييس أداء الاستراتيجية"""
    # مقاييس التداول الأساسية
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    
    # مقاييس الربحية
    total_profit: float = 0.0
    total_loss: float = 0.0
    net_profit: float = 0.0
    profit_factor: float = 0.0
    
    # مقاييس المخاطر
    max_drawdown: float = 0.0
    max_drawdown_percent: float = 0.0
    consecutive_wins: int = 0
    consecutive_losses: int = 0
    max_consecutive_losses: int = 0
    
    # مقاييس الإشارات
    total_signals_generated: int = 0
    signals_executed: int = 0
    signal_execution_rate: float = 0.0
    
    # مقاييس التقارب
    convergent_signals: int = 0
    divergent_signals: int = 0
    convergence_rate: float = 0.0
    
    # مقاييس الذكاء الاصطناعي
    ai_predictions_total: int = 0
    ai_predictions_correct: int = 0
    ai_accuracy: float = 0.0
    
    # مقاييس الوقت
    avg_decision_time_ms: float = 0.0
    avg_execution_time_ms: float = 0.0
    
    # معلومات إضافية
    analysis_period_start: Optional[datetime] = None
    analysis_period_end: Optional[datetime] = None
    last_update: Optional[datetime] = None

@dataclass
class AssetPerformanceAnalysis:
    """تحليل أداء أصل واحد"""
    asset: str
    metrics: StrategyPerformanceMetrics
    layer_performance: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    recent_trades: List[Dict[str, Any]] = field(default_factory=list)
    performance_score: float = 0.0
    recommendations: List[str] = field(default_factory=list)

class StrategyPerformanceAnalyzer:
    """محلل أداء الاستراتيجية المتكامل"""
    
    def __init__(self):
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.is_analyzing = False
        self.analysis_interval = 300  # 5 دقائق
        
        # مخزن البيانات
        self.performance_data = {}
        self.historical_performance = {}
        self.analysis_cache = {}
        
        # إعدادات التحليل
        self.analysis_settings = {
            'lookback_hours': 24,
            'min_trades_for_analysis': 5,
            'performance_thresholds': {
                'excellent': 0.8,
                'good': 0.6,
                'average': 0.4,
                'poor': 0.2
            }
        }
        
        # إحصائيات النظام
        self.system_stats = {
            'total_analyses_performed': 0,
            'assets_analyzed': 0,
            'last_analysis_time': None,
            'analysis_duration_avg': 0.0
        }
        
        logger.info("تم تهيئة محلل أداء الاستراتيجية")

    @handle_async_errors(default_return=False, log_error=True)
    async def start_performance_analysis(self) -> bool:
        """بدء تحليل الأداء المستمر"""
        try:
            if self.is_analyzing:
                logger.warning("محلل الأداء يعمل بالفعل")
                return True
            
            self.is_analyzing = True
            logger.info("🚀 بدء تحليل أداء الاستراتيجية المستمر")
            
            # بدء حلقة التحليل
            asyncio.create_task(self._analysis_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء تحليل الأداء: {str(e)}")
            return False

    async def _analysis_loop(self):
        """حلقة التحليل المستمر"""
        try:
            while self.is_analyzing:
                analysis_start = time.time()
                
                # تحليل شامل للأداء
                await self._perform_comprehensive_analysis()
                
                # تحديث الإحصائيات
                analysis_duration = time.time() - analysis_start
                self._update_system_stats(analysis_duration)
                
                # انتظار حتى التحليل التالي
                await asyncio.sleep(self.analysis_interval)
                
        except Exception as e:
            logger.error(f"خطأ في حلقة تحليل الأداء: {str(e)}")

    async def _perform_comprehensive_analysis(self):
        """تحليل شامل للأداء"""
        try:
            logger.info("📊 بدء التحليل الشامل للأداء")
            
            # جمع البيانات من جميع المصادر
            trading_stats = await self._collect_trading_statistics()
            convergence_stats = await self._collect_convergence_statistics()
            ai_stats = await self._collect_ai_statistics()
            system_stats = await self._collect_system_statistics()
            
            # تحليل كل أصل على حدة
            for asset in self.currency_pairs[:10]:  # تحليل أول 10 أصول في كل دورة
                try:
                    asset_analysis = await self._analyze_asset_performance(
                        asset, trading_stats, convergence_stats, ai_stats, system_stats
                    )
                    
                    if asset_analysis:
                        self.performance_data[asset] = asset_analysis
                        
                except Exception as e:
                    logger.error(f"خطأ في تحليل أداء {asset}: {str(e)}")
            
            # تحليل الأداء العام
            overall_analysis = await self._analyze_overall_performance()
            self.performance_data['overall'] = overall_analysis
            
            # حفظ النتائج
            await self._save_analysis_results()
            
            logger.info("✅ اكتمل التحليل الشامل للأداء")
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل: {str(e)}")

    async def _collect_trading_statistics(self) -> Dict[str, Any]:
        """جمع إحصائيات التداول"""
        try:
            # جمع إحصائيات من محرك التداول
            trading_stats = await real_trading_executor.get_trading_stats()
            
            # إضافة إحصائيات مفصلة
            detailed_stats = {
                'basic_stats': trading_stats,
                'trade_history': real_trading_executor.executed_trades[-50:],  # آخر 50 صفقة
                'pending_orders': len(real_trading_executor.pending_orders),
                'daily_performance': self._calculate_daily_performance(real_trading_executor.executed_trades)
            }
            
            return detailed_stats
            
        except Exception as e:
            logger.error(f"خطأ في جمع إحصائيات التداول: {str(e)}")
            return {}

    async def _collect_convergence_statistics(self) -> Dict[str, Any]:
        """جمع إحصائيات التقارب"""
        try:
            # جمع إحصائيات من نظام التقارب الرباعي
            convergence_stats = quadruple_convergence_system.stats.copy()
            
            # إضافة تحليل مفصل
            detailed_stats = {
                'basic_stats': convergence_stats,
                'layer_performance': convergence_stats.get('layer_performance', {}),
                'convergence_rate': self._calculate_convergence_rate(convergence_stats),
                'signal_quality': self._analyze_signal_quality(convergence_stats)
            }
            
            return detailed_stats
            
        except Exception as e:
            logger.error(f"خطأ في جمع إحصائيات التقارب: {str(e)}")
            return {}

    async def _collect_ai_statistics(self) -> Dict[str, Any]:
        """جمع إحصائيات الذكاء الاصطناعي"""
        try:
            # جمع إحصائيات من محرك الذكاء الاصطناعي
            ai_stats = ai_predictor_engine.performance_stats.copy()
            
            # جمع إحصائيات من متتبع التنبؤات
            prediction_stats = prediction_tracker.get_performance_summary()
            
            detailed_stats = {
                'predictor_stats': ai_stats,
                'prediction_tracking': prediction_stats,
                'model_accuracy': self._calculate_model_accuracy(ai_stats),
                'prediction_trends': self._analyze_prediction_trends(prediction_stats)
            }
            
            return detailed_stats
            
        except Exception as e:
            logger.error(f"خطأ في جمع إحصائيات الذكاء الاصطناعي: {str(e)}")
            return {}

    async def _collect_system_statistics(self) -> Dict[str, Any]:
        """جمع إحصائيات النظام"""
        try:
            # جمع إحصائيات من أنظمة المراقبة
            performance_stats = advanced_performance_monitor.get_performance_statistics()
            system_stats = performance_statistics_system.get_system_statistics()
            
            detailed_stats = {
                'performance_monitor': performance_stats,
                'statistics_system': system_stats,
                'system_health': self._evaluate_system_health(performance_stats, system_stats)
            }
            
            return detailed_stats
            
        except Exception as e:
            logger.error(f"خطأ في جمع إحصائيات النظام: {str(e)}")
            return {}

    def _calculate_daily_performance(self, trades: List[Any]) -> Dict[str, Any]:
        """حساب الأداء اليومي"""
        try:
            today = datetime.now().date()
            today_trades = [t for t in trades if t.execution_time and t.execution_time.date() == today]
            
            if not today_trades:
                return {'trades': 0, 'profit': 0.0, 'win_rate': 0.0}
            
            winning_trades = [t for t in today_trades if t.status.value == 'WON']
            total_profit = sum(t.result.get('profit', 0) for t in today_trades if t.result)
            
            return {
                'trades': len(today_trades),
                'winning_trades': len(winning_trades),
                'profit': total_profit,
                'win_rate': len(winning_trades) / len(today_trades) if today_trades else 0.0
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب الأداء اليومي: {str(e)}")
            return {'trades': 0, 'profit': 0.0, 'win_rate': 0.0}

    def _calculate_convergence_rate(self, stats: Dict[str, Any]) -> float:
        """حساب معدل التقارب"""
        try:
            total_analyses = stats.get('total_analyses', 0)
            convergent_signals = stats.get('convergent_signals', 0)
            
            if total_analyses == 0:
                return 0.0
            
            return convergent_signals / total_analyses
            
        except Exception as e:
            logger.error(f"خطأ في حساب معدل التقارب: {str(e)}")
            return 0.0

    def _analyze_signal_quality(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل جودة الإشارات"""
        try:
            layer_performance = stats.get('layer_performance', {})
            
            quality_analysis = {}
            for layer, performance in layer_performance.items():
                total = performance.get('total', 0)
                correct = performance.get('correct', 0)
                
                accuracy = correct / total if total > 0 else 0.0
                quality_analysis[layer] = {
                    'accuracy': accuracy,
                    'total_signals': total,
                    'correct_signals': correct,
                    'quality_score': self._calculate_quality_score(accuracy, total)
                }
            
            return quality_analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل جودة الإشارات: {str(e)}")
            return {}

    def _calculate_quality_score(self, accuracy: float, total_signals: int) -> float:
        """حساب نقاط الجودة"""
        try:
            # نقاط الجودة تعتمد على الدقة وعدد الإشارات
            base_score = accuracy * 100

            # مكافأة للإشارات الكثيرة (موثوقية أكبر)
            volume_bonus = min(total_signals / 100, 0.2) * 100

            return min(base_score + volume_bonus, 100.0)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الجودة: {str(e)}")
            return 0.0

    def _calculate_model_accuracy(self, ai_stats: Dict[str, Any]) -> Dict[str, float]:
        """حساب دقة النماذج"""
        try:
            model_accuracy = {}

            for model_name in ['xgboost', 'lstm', 'random_forest']:
                model_stats = ai_stats.get('model_accuracy', {}).get(model_name, {})
                total = model_stats.get('total_predictions', 0)
                correct = model_stats.get('correct_predictions', 0)

                accuracy = correct / total if total > 0 else 0.0
                model_accuracy[model_name] = accuracy

            return model_accuracy

        except Exception as e:
            logger.error(f"خطأ في حساب دقة النماذج: {str(e)}")
            return {}

    def _analyze_prediction_trends(self, prediction_stats: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل اتجاهات التنبؤ"""
        try:
            trends = {
                'accuracy_trend': 'stable',
                'volume_trend': 'stable',
                'confidence_trend': 'stable',
                'recent_performance': 'good'
            }

            # تحليل الاتجاهات بناء على البيانات المتاحة
            recent_accuracy = prediction_stats.get('recent_accuracy', 0.0)
            overall_accuracy = prediction_stats.get('overall_accuracy', 0.0)

            if recent_accuracy > overall_accuracy + 0.05:
                trends['accuracy_trend'] = 'improving'
            elif recent_accuracy < overall_accuracy - 0.05:
                trends['accuracy_trend'] = 'declining'

            return trends

        except Exception as e:
            logger.error(f"خطأ في تحليل اتجاهات التنبؤ: {str(e)}")
            return {}

    def _evaluate_system_health(self, performance_stats: Dict[str, Any], system_stats: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم صحة النظام"""
        try:
            health_score = 100.0
            issues = []

            # فحص استخدام الموارد
            cpu_usage = performance_stats.get('system_resources', {}).get('cpu_usage', 0)
            memory_usage = performance_stats.get('system_resources', {}).get('memory_usage', 0)

            if cpu_usage > 80:
                health_score -= 20
                issues.append("استخدام عالي للمعالج")

            if memory_usage > 80:
                health_score -= 20
                issues.append("استخدام عالي للذاكرة")

            # فحص معدل الأخطاء
            error_rate = system_stats.get('error_rate', 0)
            if error_rate > 0.05:  # أكثر من 5% أخطاء
                health_score -= 30
                issues.append("معدل أخطاء عالي")

            return {
                'health_score': max(health_score, 0.0),
                'status': 'healthy' if health_score > 70 else 'warning' if health_score > 40 else 'critical',
                'issues': issues
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم صحة النظام: {str(e)}")
            return {'health_score': 0.0, 'status': 'unknown', 'issues': ['خطأ في التقييم']}

    async def _analyze_asset_performance(self, asset: str, trading_stats: Dict[str, Any],
                                       convergence_stats: Dict[str, Any], ai_stats: Dict[str, Any],
                                       system_stats: Dict[str, Any]) -> AssetPerformanceAnalysis:
        """تحليل أداء أصل واحد"""
        try:
            # إنشاء مقاييس الأداء للأصل
            metrics = StrategyPerformanceMetrics()

            # تحليل الصفقات الخاصة بالأصل
            asset_trades = [t for t in trading_stats.get('basic_stats', {}).get('completed_trades', [])
                           if hasattr(t, 'asset') and t.asset == asset]

            if asset_trades:
                metrics = self._calculate_asset_metrics(asset_trades, metrics)

            # تحليل أداء الطبقات للأصل
            layer_performance = self._analyze_asset_layer_performance(asset, convergence_stats, ai_stats)

            # حساب نقاط الأداء
            performance_score = self._calculate_asset_performance_score(metrics, layer_performance)

            # توليد التوصيات
            recommendations = self._generate_asset_recommendations(asset, metrics, layer_performance)

            return AssetPerformanceAnalysis(
                asset=asset,
                metrics=metrics,
                layer_performance=layer_performance,
                recent_trades=asset_trades[-10:],  # آخر 10 صفقات
                performance_score=performance_score,
                recommendations=recommendations
            )

        except Exception as e:
            logger.error(f"خطأ في تحليل أداء {asset}: {str(e)}")
            return AssetPerformanceAnalysis(asset=asset, metrics=StrategyPerformanceMetrics())

    def _calculate_asset_metrics(self, trades: List[Any], metrics: StrategyPerformanceMetrics) -> StrategyPerformanceMetrics:
        """حساب مقاييس الأداء للأصل"""
        try:
            metrics.total_trades = len(trades)

            winning_trades = [t for t in trades if t.status.value == 'WON']
            losing_trades = [t for t in trades if t.status.value == 'LOST']

            metrics.winning_trades = len(winning_trades)
            metrics.losing_trades = len(losing_trades)
            metrics.win_rate = len(winning_trades) / len(trades) if trades else 0.0

            # حساب الأرباح والخسائر
            total_profit = sum(t.result.get('profit', 0) for t in winning_trades if t.result)
            total_loss = sum(t.amount for t in losing_trades)

            metrics.total_profit = total_profit
            metrics.total_loss = total_loss
            metrics.net_profit = total_profit - total_loss
            metrics.profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

            # حساب الخسائر المتتالية
            consecutive_losses = 0
            max_consecutive = 0

            for trade in trades:
                if trade.status.value == 'LOST':
                    consecutive_losses += 1
                    max_consecutive = max(max_consecutive, consecutive_losses)
                else:
                    consecutive_losses = 0

            metrics.max_consecutive_losses = max_consecutive
            metrics.last_update = datetime.now()

            return metrics

        except Exception as e:
            logger.error(f"خطأ في حساب مقاييس الأصل: {str(e)}")
            return metrics

    def _analyze_asset_layer_performance(self, asset: str, convergence_stats: Dict[str, Any],
                                       ai_stats: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """تحليل أداء الطبقات للأصل"""
        try:
            layer_performance = {}

            # أداء طبقات التقارب
            convergence_layers = convergence_stats.get('layer_performance', {})
            for layer, stats in convergence_layers.items():
                layer_performance[layer] = {
                    'accuracy': stats.get('correct', 0) / stats.get('total', 1),
                    'total_signals': stats.get('total', 0),
                    'correct_signals': stats.get('correct', 0),
                    'performance_score': self._calculate_layer_score(stats)
                }

            # أداء الذكاء الاصطناعي للأصل
            asset_ai_stats = ai_stats.get('currency_pair_stats', {}).get(asset, {})
            if asset_ai_stats:
                layer_performance['ai'] = {
                    'accuracy': asset_ai_stats.get('accuracy', 0.0),
                    'total_predictions': asset_ai_stats.get('total_predictions', 0),
                    'correct_predictions': asset_ai_stats.get('correct_predictions', 0),
                    'performance_score': asset_ai_stats.get('accuracy', 0.0) * 100
                }

            return layer_performance

        except Exception as e:
            logger.error(f"خطأ في تحليل أداء طبقات {asset}: {str(e)}")
            return {}

    def _calculate_layer_score(self, stats: Dict[str, Any]) -> float:
        """حساب نقاط أداء الطبقة"""
        try:
            total = stats.get('total', 0)
            correct = stats.get('correct', 0)

            if total == 0:
                return 0.0

            accuracy = correct / total
            return accuracy * 100

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الطبقة: {str(e)}")
            return 0.0

    def _calculate_asset_performance_score(self, metrics: StrategyPerformanceMetrics,
                                         layer_performance: Dict[str, Dict[str, Any]]) -> float:
        """حساب نقاط أداء الأصل"""
        try:
            score = 0.0

            # نقاط من معدل الفوز (40%)
            win_rate_score = metrics.win_rate * 40

            # نقاط من عامل الربح (30%)
            profit_factor_score = min(metrics.profit_factor / 2.0, 1.0) * 30 if metrics.profit_factor != float('inf') else 30

            # نقاط من أداء الطبقات (30%)
            layer_scores = [layer.get('performance_score', 0) for layer in layer_performance.values()]
            avg_layer_score = sum(layer_scores) / len(layer_scores) if layer_scores else 0
            layer_score = avg_layer_score * 0.3

            score = win_rate_score + profit_factor_score + layer_score

            # خصم نقاط للخسائر المتتالية
            if metrics.max_consecutive_losses > 5:
                score -= (metrics.max_consecutive_losses - 5) * 2

            return max(min(score, 100.0), 0.0)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط أداء الأصل: {str(e)}")
            return 0.0

    def _generate_asset_recommendations(self, asset: str, metrics: StrategyPerformanceMetrics,
                                      layer_performance: Dict[str, Dict[str, Any]]) -> List[str]:
        """توليد توصيات للأصل"""
        try:
            recommendations = []

            # توصيات بناء على معدل الفوز
            if metrics.win_rate < 0.5:
                recommendations.append(f"معدل الفوز منخفض ({metrics.win_rate:.1%}) - راجع معايير الدخول")

            # توصيات بناء على الخسائر المتتالية
            if metrics.max_consecutive_losses > 7:
                recommendations.append(f"خسائر متتالية عالية ({metrics.max_consecutive_losses}) - فعل نظام إيقاف الخسائر")

            # توصيات بناء على أداء الطبقات
            for layer, performance in layer_performance.items():
                accuracy = performance.get('accuracy', 0)
                if accuracy < 0.6:
                    recommendations.append(f"دقة طبقة {layer} منخفضة ({accuracy:.1%}) - راجع معايير الطبقة")

            # توصيات عامة
            if metrics.total_trades < 10:
                recommendations.append("عدد الصفقات قليل - انتظر المزيد من البيانات للتحليل الدقيق")

            if not recommendations:
                recommendations.append("الأداء جيد - استمر في المراقبة")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في توليد توصيات {asset}: {str(e)}")
            return ["خطأ في توليد التوصيات"]

    async def _analyze_overall_performance(self) -> Dict[str, Any]:
        """تحليل الأداء العام للنظام"""
        try:
            overall_analysis = {
                'system_health': 'good',
                'total_assets_analyzed': len([k for k in self.performance_data.keys() if k != 'overall']),
                'best_performing_assets': [],
                'worst_performing_assets': [],
                'system_recommendations': [],
                'performance_summary': {},
                'analysis_timestamp': datetime.now().isoformat()
            }

            # تحليل أفضل وأسوأ الأصول
            asset_scores = {}
            for asset, analysis in self.performance_data.items():
                if asset != 'overall' and isinstance(analysis, AssetPerformanceAnalysis):
                    asset_scores[asset] = analysis.performance_score

            if asset_scores:
                sorted_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)
                overall_analysis['best_performing_assets'] = sorted_assets[:5]
                overall_analysis['worst_performing_assets'] = sorted_assets[-5:]

            # ملخص الأداء
            overall_analysis['performance_summary'] = await self._generate_performance_summary()

            # توصيات النظام
            overall_analysis['system_recommendations'] = self._generate_system_recommendations()

            return overall_analysis

        except Exception as e:
            logger.error(f"خطأ في تحليل الأداء العام: {str(e)}")
            return {}

    async def _generate_performance_summary(self) -> Dict[str, Any]:
        """توليد ملخص الأداء"""
        try:
            summary = {
                'total_trades': 0,
                'total_profit': 0.0,
                'overall_win_rate': 0.0,
                'active_assets': 0,
                'system_uptime': '99.9%',
                'last_24h_performance': {}
            }

            # جمع الإحصائيات من جميع الأصول
            total_trades = 0
            total_wins = 0
            total_profit = 0.0

            for asset, analysis in self.performance_data.items():
                if asset != 'overall' and isinstance(analysis, AssetPerformanceAnalysis):
                    metrics = analysis.metrics
                    total_trades += metrics.total_trades
                    total_wins += metrics.winning_trades
                    total_profit += metrics.net_profit

            if total_trades > 0:
                summary['total_trades'] = total_trades
                summary['total_profit'] = total_profit
                summary['overall_win_rate'] = total_wins / total_trades

            summary['active_assets'] = len([k for k in self.performance_data.keys() if k != 'overall'])

            return summary

        except Exception as e:
            logger.error(f"خطأ في توليد ملخص الأداء: {str(e)}")
            return {}

    def _generate_system_recommendations(self) -> List[str]:
        """توليد توصيات النظام"""
        try:
            recommendations = []

            # تحليل الأداء العام
            if len(self.performance_data) < 5:
                recommendations.append("قم بزيادة عدد الأصول المراقبة لتحسين التنويع")

            # فحص أداء النظام
            if self.system_stats['total_analyses_performed'] > 0:
                avg_duration = self.system_stats['analysis_duration_avg']
                if avg_duration > 60:  # أكثر من دقيقة
                    recommendations.append("وقت التحليل طويل - راجع أداء النظام")

            recommendations.append("راجع التقارير التفصيلية لكل أصل لتحسين الأداء")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في توليد توصيات النظام: {str(e)}")
            return ["خطأ في توليد التوصيات"]

    async def _save_analysis_results(self):
        """حفظ نتائج التحليل"""
        try:
            # حفظ في ملف JSON
            analysis_data = {
                'timestamp': datetime.now().isoformat(),
                'performance_data': {},
                'system_stats': self.system_stats.copy()
            }

            # تحويل البيانات للحفظ
            for asset, analysis in self.performance_data.items():
                if isinstance(analysis, AssetPerformanceAnalysis):
                    analysis_data['performance_data'][asset] = {
                        'asset': analysis.asset,
                        'performance_score': analysis.performance_score,
                        'metrics': {
                            'total_trades': analysis.metrics.total_trades,
                            'win_rate': analysis.metrics.win_rate,
                            'net_profit': analysis.metrics.net_profit,
                            'max_consecutive_losses': analysis.metrics.max_consecutive_losses
                        },
                        'recommendations': analysis.recommendations
                    }
                else:
                    analysis_data['performance_data'][asset] = analysis

            # حفظ في ملف
            import json
            with open('logs/strategy_performance_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)

            logger.debug("تم حفظ نتائج تحليل الأداء")

        except Exception as e:
            logger.error(f"خطأ في حفظ نتائج التحليل: {str(e)}")

    def _update_system_stats(self, analysis_duration: float):
        """تحديث إحصائيات النظام"""
        try:
            self.system_stats['total_analyses_performed'] += 1
            self.system_stats['last_analysis_time'] = datetime.now()

            # تحديث متوسط مدة التحليل
            current_avg = self.system_stats['analysis_duration_avg']
            total_analyses = self.system_stats['total_analyses_performed']

            new_avg = ((current_avg * (total_analyses - 1)) + analysis_duration) / total_analyses
            self.system_stats['analysis_duration_avg'] = new_avg

            self.system_stats['assets_analyzed'] = len([k for k in self.performance_data.keys() if k != 'overall'])

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات النظام: {str(e)}")

    async def get_performance_report(self, asset: str = None) -> Dict[str, Any]:
        """الحصول على تقرير الأداء"""
        try:
            if asset and asset in self.performance_data:
                # تقرير أصل محدد
                analysis = self.performance_data[asset]
                if isinstance(analysis, AssetPerformanceAnalysis):
                    return {
                        'asset': asset,
                        'performance_score': analysis.performance_score,
                        'metrics': analysis.metrics.__dict__,
                        'layer_performance': analysis.layer_performance,
                        'recommendations': analysis.recommendations,
                        'recent_trades_count': len(analysis.recent_trades)
                    }

            # تقرير شامل
            return {
                'overall_analysis': self.performance_data.get('overall', {}),
                'system_stats': self.system_stats.copy(),
                'total_assets': len([k for k in self.performance_data.keys() if k != 'overall']),
                'analysis_active': self.is_analyzing,
                'last_update': self.system_stats.get('last_analysis_time')
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على تقرير الأداء: {str(e)}")
            return {'error': str(e)}

    async def stop_analysis(self):
        """إيقاف تحليل الأداء"""
        try:
            self.is_analyzing = False
            logger.info("تم إيقاف محلل أداء الاستراتيجية")

        except Exception as e:
            logger.error(f"خطأ في إيقاف التحليل: {str(e)}")

# إنشاء مثيل عام
strategy_performance_analyzer = StrategyPerformanceAnalyzer()
