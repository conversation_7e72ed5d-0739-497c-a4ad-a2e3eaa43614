"""
نظام مراقبة صحة النظام - مراقبة صحة النظام والعمليات بشكل مستمر
"""

import asyncio
import psutil
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

from config.currency_pairs import CURRENCY_PAIRS_70
from config.trading_config import TradingConfig
from database.connection_manager import db_manager
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, handle_async_errors, ScalpingError

logger = scalping_logger.get_logger("system_health_monitor")

class HealthStatus(Enum):
    """حالات صحة النظام"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

class ComponentType(Enum):
    """أنواع مكونات النظام"""
    DATABASE = "database"
    REDIS = "redis"
    API_CONNECTION = "api_connection"
    DATA_COLLECTION = "data_collection"
    INDICATOR_CALCULATION = "indicator_calculation"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    DISK_USAGE = "disk_usage"
    NETWORK = "network"
    PROCESS_HEALTH = "process_health"

@dataclass
class HealthMetric:
    """مقياس صحة واحد"""
    component: ComponentType
    status: HealthStatus
    value: float
    threshold_warning: float
    threshold_critical: float
    message: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_healthy(self) -> bool:
        return self.status == HealthStatus.HEALTHY
    
    @property
    def needs_attention(self) -> bool:
        return self.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]

@dataclass
class SystemHealthReport:
    """تقرير صحة النظام الشامل"""
    overall_status: HealthStatus
    metrics: Dict[ComponentType, HealthMetric]
    timestamp: datetime = field(default_factory=datetime.now)
    uptime_seconds: float = 0.0
    total_errors: int = 0
    warnings_count: int = 0
    critical_issues: int = 0
    recommendations: List[str] = field(default_factory=list)

class SystemHealthMonitor:
    """مراقب صحة النظام الرئيسي"""
    
    def __init__(self, check_interval: int = 60):
        self.currency_pairs = CURRENCY_PAIRS_70.copy()
        self.config = TradingConfig()
        self.check_interval = check_interval
        
        # تاريخ بدء النظام
        self.system_start_time = datetime.now()
        
        # حالة المراقبة
        self.monitoring_active = False
        self.health_checks_enabled = True
        
        # تخزين المقاييس
        self.current_metrics: Dict[ComponentType, HealthMetric] = {}
        self.metrics_history: Dict[ComponentType, deque] = {
            component: deque(maxlen=100) for component in ComponentType
        }
        
        # عدادات الأخطاء
        self.error_counts = {
            'database_errors': 0,
            'api_errors': 0,
            'memory_warnings': 0,
            'cpu_warnings': 0,
            'disk_warnings': 0,
            'network_errors': 0
        }
        
        # إعدادات العتبات
        self.thresholds = {
            ComponentType.CPU_USAGE: {'warning': 70.0, 'critical': 85.0},
            ComponentType.MEMORY_USAGE: {'warning': 75.0, 'critical': 90.0},
            ComponentType.DISK_USAGE: {'warning': 80.0, 'critical': 95.0},
            ComponentType.DATABASE: {'warning': 1000.0, 'critical': 5000.0},  # response time ms
            ComponentType.API_CONNECTION: {'warning': 2000.0, 'critical': 10000.0},  # response time ms
            ComponentType.DATA_COLLECTION: {'warning': 10.0, 'critical': 25.0},  # error rate %
            ComponentType.INDICATOR_CALCULATION: {'warning': 5.0, 'critical': 15.0},  # error rate %
        }
        
        # callbacks للتنبيهات
        self.alert_callbacks: List[Callable] = []
        
        # تقارير الصحة
        self.latest_report: Optional[SystemHealthReport] = None
        self.reports_history: deque = deque(maxlen=24)  # آخر 24 تقرير
        
        logger.info(f"تم تهيئة مراقب صحة النظام")
        logger.info(f"فترة الفحص: {self.check_interval} ثانية")
        logger.info(f"مراقبة {len(self.currency_pairs)} زوج عملة")

    @handle_errors(default_return=None, log_error=True)
    def add_alert_callback(self, callback: Callable[[HealthMetric], None]):
        """إضافة callback للتنبيهات"""
        self.alert_callbacks.append(callback)
        logger.info(f"تم إضافة callback للتنبيهات: {callback.__name__}")

    @handle_async_errors(default_return=HealthMetric, log_error=True)
    async def check_database_health(self) -> HealthMetric:
        """فحص صحة قاعدة البيانات"""
        try:
            start_time = time.time()
            
            # اختبار الاتصال
            connection_test = await asyncio.to_thread(db_manager.test_connection)
            response_time = (time.time() - start_time) * 1000  # ms
            
            # تحديد الحالة
            if not connection_test:
                status = HealthStatus.CRITICAL
                message = "فشل في الاتصال بقاعدة البيانات"
                self.error_counts['database_errors'] += 1
            elif response_time > self.thresholds[ComponentType.DATABASE]['critical']:
                status = HealthStatus.CRITICAL
                message = f"استجابة قاعدة البيانات بطيئة جداً: {response_time:.1f}ms"
                # تشغيل تحسين تلقائي
                await self._optimize_database_performance()
            elif response_time > self.thresholds[ComponentType.DATABASE]['warning']:
                status = HealthStatus.WARNING
                message = f"استجابة قاعدة البيانات بطيئة: {response_time:.1f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"قاعدة البيانات تعمل بشكل طبيعي: {response_time:.1f}ms"
            
            metric = HealthMetric(
                component=ComponentType.DATABASE,
                status=status,
                value=response_time,
                threshold_warning=self.thresholds[ComponentType.DATABASE]['warning'],
                threshold_critical=self.thresholds[ComponentType.DATABASE]['critical'],
                message=message,
                details={'connection_test': connection_test, 'response_time_ms': response_time}
            )
            
            return metric
            
        except Exception as e:
            self.error_counts['database_errors'] += 1
            return HealthMetric(
                component=ComponentType.DATABASE,
                status=HealthStatus.CRITICAL,
                value=0.0,
                threshold_warning=self.thresholds[ComponentType.DATABASE]['warning'],
                threshold_critical=self.thresholds[ComponentType.DATABASE]['critical'],
                message=f"خطأ في فحص قاعدة البيانات: {str(e)}"
            )

    async def _optimize_database_performance(self):
        """تحسين أداء قاعدة البيانات تلقائياً"""
        try:
            logger.info("🔧 بدء تحسين أداء قاعدة البيانات...")

            # تنظيف الاتصالات المعلقة
            await asyncio.to_thread(db_manager.cleanup_connections)

            # إعادة تهيئة pool الاتصالات
            await asyncio.to_thread(db_manager.reset_connection_pool)

            logger.info("✅ تم تحسين أداء قاعدة البيانات")

        except Exception as e:
            logger.error(f"خطأ في تحسين قاعدة البيانات: {str(e)}")

    @handle_async_errors(default_return=HealthMetric, log_error=True)
    async def check_api_connection_health(self) -> HealthMetric:
        """فحص صحة اتصال API"""
        try:
            start_time = time.time()
            
            # محاولة اختبار الاتصال مع API
            # هنا يمكن إضافة اختبار حقيقي للاتصال مع Pocket Option
            await asyncio.sleep(0.1)  # محاكاة اختبار الاتصال
            response_time = (time.time() - start_time) * 1000  # ms
            
            # تحديد الحالة (محاكاة)
            if response_time > self.thresholds[ComponentType.API_CONNECTION]['critical']:
                status = HealthStatus.CRITICAL
                message = f"اتصال API بطيء جداً: {response_time:.1f}ms"
                self.error_counts['api_errors'] += 1
            elif response_time > self.thresholds[ComponentType.API_CONNECTION]['warning']:
                status = HealthStatus.WARNING
                message = f"اتصال API بطيء: {response_time:.1f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"اتصال API يعمل بشكل طبيعي: {response_time:.1f}ms"
            
            metric = HealthMetric(
                component=ComponentType.API_CONNECTION,
                status=status,
                value=response_time,
                threshold_warning=self.thresholds[ComponentType.API_CONNECTION]['warning'],
                threshold_critical=self.thresholds[ComponentType.API_CONNECTION]['critical'],
                message=message,
                details={'response_time_ms': response_time}
            )
            
            return metric
            
        except Exception as e:
            self.error_counts['api_errors'] += 1
            return HealthMetric(
                component=ComponentType.API_CONNECTION,
                status=HealthStatus.CRITICAL,
                value=0.0,
                threshold_warning=self.thresholds[ComponentType.API_CONNECTION]['warning'],
                threshold_critical=self.thresholds[ComponentType.API_CONNECTION]['critical'],
                message=f"خطأ في فحص اتصال API: {str(e)}"
            )

    @handle_errors(default_return=HealthMetric, log_error=True)
    def check_system_resources(self) -> List[HealthMetric]:
        """فحص موارد النظام (CPU, Memory, Disk)"""
        metrics = []
        
        try:
            # فحص المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.thresholds[ComponentType.CPU_USAGE]['critical']:
                cpu_status = HealthStatus.CRITICAL
                cpu_message = f"استخدام المعالج مرتفع جداً: {cpu_percent:.1f}%"
                self.error_counts['cpu_warnings'] += 1
            elif cpu_percent > self.thresholds[ComponentType.CPU_USAGE]['warning']:
                cpu_status = HealthStatus.WARNING
                cpu_message = f"استخدام المعالج مرتفع: {cpu_percent:.1f}%"
            else:
                cpu_status = HealthStatus.HEALTHY
                cpu_message = f"استخدام المعالج طبيعي: {cpu_percent:.1f}%"
            
            metrics.append(HealthMetric(
                component=ComponentType.CPU_USAGE,
                status=cpu_status,
                value=cpu_percent,
                threshold_warning=self.thresholds[ComponentType.CPU_USAGE]['warning'],
                threshold_critical=self.thresholds[ComponentType.CPU_USAGE]['critical'],
                message=cpu_message
            ))
            
            # فحص الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            if memory_percent > self.thresholds[ComponentType.MEMORY_USAGE]['critical']:
                memory_status = HealthStatus.CRITICAL
                memory_message = f"استخدام الذاكرة مرتفع جداً: {memory_percent:.1f}%"
                self.error_counts['memory_warnings'] += 1
            elif memory_percent > self.thresholds[ComponentType.MEMORY_USAGE]['warning']:
                memory_status = HealthStatus.WARNING
                memory_message = f"استخدام الذاكرة مرتفع: {memory_percent:.1f}%"
            else:
                memory_status = HealthStatus.HEALTHY
                memory_message = f"استخدام الذاكرة طبيعي: {memory_percent:.1f}%"
            
            metrics.append(HealthMetric(
                component=ComponentType.MEMORY_USAGE,
                status=memory_status,
                value=memory_percent,
                threshold_warning=self.thresholds[ComponentType.MEMORY_USAGE]['warning'],
                threshold_critical=self.thresholds[ComponentType.MEMORY_USAGE]['critical'],
                message=memory_message,
                details={'available_gb': memory.available / (1024**3)}
            ))
            
            # فحص القرص
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > self.thresholds[ComponentType.DISK_USAGE]['critical']:
                disk_status = HealthStatus.CRITICAL
                disk_message = f"استخدام القرص مرتفع جداً: {disk_percent:.1f}%"
                self.error_counts['disk_warnings'] += 1
            elif disk_percent > self.thresholds[ComponentType.DISK_USAGE]['warning']:
                disk_status = HealthStatus.WARNING
                disk_message = f"استخدام القرص مرتفع: {disk_percent:.1f}%"
            else:
                disk_status = HealthStatus.HEALTHY
                disk_message = f"استخدام القرص طبيعي: {disk_percent:.1f}%"
            
            metrics.append(HealthMetric(
                component=ComponentType.DISK_USAGE,
                status=disk_status,
                value=disk_percent,
                threshold_warning=self.thresholds[ComponentType.DISK_USAGE]['warning'],
                threshold_critical=self.thresholds[ComponentType.DISK_USAGE]['critical'],
                message=disk_message,
                details={'free_gb': disk.free / (1024**3)}
            ))
            
        except Exception as e:
            logger.error(f"خطأ في فحص موارد النظام: {str(e)}")
            
        return metrics

    @handle_errors(default_return=HealthMetric, log_error=True)
    def check_process_health(self) -> HealthMetric:
        """فحص صحة العمليات"""
        try:
            current_process = psutil.Process()
            
            # معلومات العملية الحالية
            process_info = {
                'pid': current_process.pid,
                'memory_mb': current_process.memory_info().rss / (1024 * 1024),
                'cpu_percent': current_process.cpu_percent(),
                'num_threads': current_process.num_threads(),
                'status': current_process.status(),
                'create_time': datetime.fromtimestamp(current_process.create_time())
            }
            
            # تحديد الحالة
            if process_info['status'] != 'running':
                status = HealthStatus.CRITICAL
                message = f"العملية في حالة غير طبيعية: {process_info['status']}"
            elif process_info['memory_mb'] > 2048:  # أكثر من 2GB
                status = HealthStatus.WARNING
                message = f"استخدام ذاكرة مرتفع: {process_info['memory_mb']:.1f}MB"
            else:
                status = HealthStatus.HEALTHY
                message = f"العملية تعمل بشكل طبيعي: {process_info['memory_mb']:.1f}MB"
            
            return HealthMetric(
                component=ComponentType.PROCESS_HEALTH,
                status=status,
                value=process_info['memory_mb'],
                threshold_warning=1024.0,  # 1GB
                threshold_critical=2048.0,  # 2GB
                message=message,
                details=process_info
            )
            
        except Exception as e:
            return HealthMetric(
                component=ComponentType.PROCESS_HEALTH,
                status=HealthStatus.CRITICAL,
                value=0.0,
                threshold_warning=1024.0,
                threshold_critical=2048.0,
                message=f"خطأ في فحص العملية: {str(e)}"
            )

    @handle_async_errors(default_return=None, log_error=True)
    async def perform_comprehensive_health_check(self) -> SystemHealthReport:
        """إجراء فحص شامل لصحة النظام"""
        try:
            logger.debug("🔍 بدء الفحص الشامل لصحة النظام")

            # تجميع جميع المقاييس
            all_metrics = {}

            # فحص قاعدة البيانات
            db_metric = await self.check_database_health()
            all_metrics[ComponentType.DATABASE] = db_metric

            # فحص اتصال API
            api_metric = await self.check_api_connection_health()
            all_metrics[ComponentType.API_CONNECTION] = api_metric

            # فحص موارد النظام
            resource_metrics = self.check_system_resources()
            for metric in resource_metrics:
                all_metrics[metric.component] = metric

            # فحص صحة العملية
            process_metric = self.check_process_health()
            all_metrics[ComponentType.PROCESS_HEALTH] = process_metric

            # تحديث المقاييس الحالية
            self.current_metrics = all_metrics

            # إضافة إلى التاريخ
            for component, metric in all_metrics.items():
                self.metrics_history[component].append(metric)

            # تحديد الحالة العامة
            overall_status = self._determine_overall_status(all_metrics)

            # حساب الإحصائيات
            uptime = (datetime.now() - self.system_start_time).total_seconds()
            warnings_count = sum(1 for m in all_metrics.values() if m.status == HealthStatus.WARNING)
            critical_issues = sum(1 for m in all_metrics.values() if m.status == HealthStatus.CRITICAL)
            total_errors = sum(self.error_counts.values())

            # إنتاج التوصيات
            recommendations = self._generate_recommendations(all_metrics)

            # إنشاء التقرير
            report = SystemHealthReport(
                overall_status=overall_status,
                metrics=all_metrics,
                uptime_seconds=uptime,
                total_errors=total_errors,
                warnings_count=warnings_count,
                critical_issues=critical_issues,
                recommendations=recommendations
            )

            # حفظ التقرير
            self.latest_report = report
            self.reports_history.append(report)

            # إرسال التنبيهات إذا لزم الأمر
            await self._send_alerts_if_needed(all_metrics)

            logger.debug(f"✅ اكتمل الفحص الشامل: {overall_status.value}")
            return report

        except Exception as e:
            logger.error(f"خطأ في الفحص الشامل: {str(e)}")
            return SystemHealthReport(
                overall_status=HealthStatus.UNKNOWN,
                metrics={},
                recommendations=["فشل في إجراء الفحص الشامل"]
            )

    def _determine_overall_status(self, metrics: Dict[ComponentType, HealthMetric]) -> HealthStatus:
        """تحديد الحالة العامة للنظام"""
        if not metrics:
            return HealthStatus.UNKNOWN

        # إذا كان هناك أي مشكلة حرجة
        if any(m.status == HealthStatus.CRITICAL for m in metrics.values()):
            return HealthStatus.CRITICAL

        # إذا كان هناك تحذيرات
        if any(m.status == HealthStatus.WARNING for m in metrics.values()):
            return HealthStatus.WARNING

        # إذا كانت جميع المقاييس صحية
        return HealthStatus.HEALTHY

    def _generate_recommendations(self, metrics: Dict[ComponentType, HealthMetric]) -> List[str]:
        """إنتاج توصيات بناءً على المقاييس"""
        recommendations = []

        for component, metric in metrics.items():
            if metric.status == HealthStatus.CRITICAL:
                if component == ComponentType.DATABASE:
                    recommendations.append("فحص اتصال قاعدة البيانات وإعادة تشغيل الخدمة إذا لزم الأمر")
                elif component == ComponentType.API_CONNECTION:
                    recommendations.append("فحص اتصال الإنترنت وإعدادات API")
                elif component == ComponentType.CPU_USAGE:
                    recommendations.append("تقليل عدد العمليات المتوازية أو ترقية المعالج")
                elif component == ComponentType.MEMORY_USAGE:
                    recommendations.append("إغلاق التطبيقات غير الضرورية أو إضافة ذاكرة")
                elif component == ComponentType.DISK_USAGE:
                    recommendations.append("تنظيف الملفات المؤقتة أو إضافة مساحة تخزين")

            elif metric.status == HealthStatus.WARNING:
                if component == ComponentType.CPU_USAGE:
                    recommendations.append("مراقبة استخدام المعالج وتحسين الأداء")
                elif component == ComponentType.MEMORY_USAGE:
                    recommendations.append("مراقبة استخدام الذاكرة وتنظيف البيانات غير المستخدمة")
                elif component == ComponentType.DISK_USAGE:
                    recommendations.append("تنظيف الملفات القديمة ومراقبة مساحة القرص")

        return recommendations

    async def _send_alerts_if_needed(self, metrics: Dict[ComponentType, HealthMetric]):
        """إرسال التنبيهات عند الحاجة"""
        try:
            for metric in metrics.values():
                if metric.needs_attention:
                    for callback in self.alert_callbacks:
                        try:
                            if asyncio.iscoroutinefunction(callback):
                                await callback(metric)
                            else:
                                callback(metric)
                        except Exception as e:
                            logger.error(f"خطأ في callback التنبيه: {str(e)}")

        except Exception as e:
            logger.error(f"خطأ في إرسال التنبيهات: {str(e)}")

    @handle_async_errors(default_return=False, log_error=True)
    async def start_monitoring(self) -> bool:
        """بدء مراقبة صحة النظام"""
        try:
            if self.monitoring_active:
                logger.warning("مراقب صحة النظام يعمل بالفعل")
                return False

            self.monitoring_active = True
            logger.info(f"🔍 بدء مراقبة صحة النظام كل {self.check_interval} ثانية")

            # بدء حلقة المراقبة
            asyncio.create_task(self._monitoring_loop())

            return True

        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة صحة النظام: {str(e)}")
            return False

    async def _monitoring_loop(self):
        """حلقة المراقبة المستمرة"""
        try:
            while self.monitoring_active:
                try:
                    # إجراء الفحص الشامل
                    report = await self.perform_comprehensive_health_check()

                    # طباعة ملخص الحالة
                    self._log_health_summary(report)

                    # انتظار حتى الفحص التالي
                    await asyncio.sleep(self.check_interval)

                except Exception as e:
                    logger.error(f"خطأ في دورة المراقبة: {str(e)}")
                    await asyncio.sleep(60)  # انتظار دقيقة عند الخطأ

        except Exception as e:
            logger.error(f"خطأ في حلقة المراقبة: {str(e)}")

    def _log_health_summary(self, report: SystemHealthReport):
        """طباعة ملخص حالة الصحة"""
        try:
            status_emoji = {
                HealthStatus.HEALTHY: "✅",
                HealthStatus.WARNING: "⚠️",
                HealthStatus.CRITICAL: "🚨",
                HealthStatus.UNKNOWN: "❓"
            }

            emoji = status_emoji.get(report.overall_status, "❓")

            if report.overall_status == HealthStatus.HEALTHY:
                logger.info(f"{emoji} النظام يعمل بشكل طبيعي")
            elif report.overall_status == HealthStatus.WARNING:
                logger.warning(f"{emoji} النظام يحتاج انتباه: {report.warnings_count} تحذير")
            elif report.overall_status == HealthStatus.CRITICAL:
                logger.error(f"{emoji} النظام في حالة حرجة: {report.critical_issues} مشكلة حرجة")

            # طباعة المشاكل الحرجة
            for component, metric in report.metrics.items():
                if metric.status == HealthStatus.CRITICAL:
                    logger.error(f"🚨 {component.value}: {metric.message}")
                elif metric.status == HealthStatus.WARNING:
                    logger.warning(f"⚠️ {component.value}: {metric.message}")

        except Exception as e:
            logger.error(f"خطأ في طباعة ملخص الصحة: {str(e)}")

    def stop_monitoring(self):
        """إيقاف مراقبة صحة النظام"""
        self.monitoring_active = False
        logger.info("تم إيقاف مراقبة صحة النظام")

    @handle_errors(default_return={}, log_error=True)
    def get_health_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الصحة"""
        try:
            uptime = (datetime.now() - self.system_start_time).total_seconds()

            stats = {
                'system_uptime_seconds': uptime,
                'system_uptime_hours': uptime / 3600,
                'monitoring_active': self.monitoring_active,
                'check_interval': self.check_interval,
                'total_reports': len(self.reports_history),
                'error_counts': self.error_counts.copy(),
                'current_status': self.latest_report.overall_status.value if self.latest_report else 'unknown',
                'last_check': self.latest_report.timestamp.isoformat() if self.latest_report else None,
                'components_status': {}
            }

            # إضافة حالة المكونات
            if self.current_metrics:
                for component, metric in self.current_metrics.items():
                    stats['components_status'][component.value] = {
                        'status': metric.status.value,
                        'value': metric.value,
                        'message': metric.message,
                        'last_check': metric.timestamp.isoformat()
                    }

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الصحة: {str(e)}")
            return {}

    @handle_errors(default_return=None, log_error=True)
    def save_health_report_to_file(self, filepath: str = None):
        """حفظ تقرير الصحة في ملف"""
        try:
            if not self.latest_report:
                logger.warning("لا يوجد تقرير صحة لحفظه")
                return

            if not filepath:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filepath = f"logs/health_report_{timestamp}.json"

            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # تحويل التقرير إلى قاموس
            report_dict = {
                'timestamp': self.latest_report.timestamp.isoformat(),
                'overall_status': self.latest_report.overall_status.value,
                'uptime_seconds': self.latest_report.uptime_seconds,
                'total_errors': self.latest_report.total_errors,
                'warnings_count': self.latest_report.warnings_count,
                'critical_issues': self.latest_report.critical_issues,
                'recommendations': self.latest_report.recommendations,
                'metrics': {}
            }

            # إضافة المقاييس
            for component, metric in self.latest_report.metrics.items():
                report_dict['metrics'][component.value] = {
                    'status': metric.status.value,
                    'value': metric.value,
                    'threshold_warning': metric.threshold_warning,
                    'threshold_critical': metric.threshold_critical,
                    'message': metric.message,
                    'timestamp': metric.timestamp.isoformat(),
                    'details': metric.details
                }

            # حفظ في الملف
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ تقرير الصحة في: {filepath}")

        except Exception as e:
            logger.error(f"خطأ في حفظ تقرير الصحة: {str(e)}")

# إنشاء مثيل عام للمراقب
health_monitor = SystemHealthMonitor()
