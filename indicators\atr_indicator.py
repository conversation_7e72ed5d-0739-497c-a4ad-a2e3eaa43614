"""
مؤشر متوسط المدى الحقيقي (ATR)
Average True Range Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class ATRIndicator(BaseIndicator):
    """مؤشر متوسط المدى الحقيقي - يقيس التقلب في السوق"""
    
    def __init__(self, period: int = 14):
        """
        تهيئة مؤشر ATR
        
        Args:
            period: فترة المؤشر (افتراضي 14)
        """
        super().__init__(period, f"ATR_{period}")
    
    def calculate_true_range(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب المدى الحقيقي (True Range) لكل شمعة
        
        True Range = max(
            High - Low,
            abs(High - Previous Close),
            abs(Low - Previous Close)
        )
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم المدى الحقيقي
        """
        if not data or len(data) < 2:
            return []
        
        true_ranges = []
        
        for i in range(1, len(data)):
            current = data[i]
            previous = data[i - 1]
            
            high = float(current['high'])
            low = float(current['low'])
            prev_close = float(previous['close'])
            
            # حساب المدى الحقيقي
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        return true_ranges
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب ATR باستخدام المتوسط المتحرك الأسي المنعم (Wilder's smoothing)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم ATR
        """
        if not data or len(data) < self.period + 1:
            return []
        
        # حساب المدى الحقيقي
        true_ranges = self.calculate_true_range(data)
        
        if not true_ranges or len(true_ranges) < self.period:
            return []
        
        atr_values = []
        
        # حساب أول ATR باستخدام المتوسط البسيط
        first_atr = sum(true_ranges[:self.period]) / self.period
        atr_values.append(first_atr)
        
        # حساب باقي قيم ATR باستخدام Wilder's smoothing
        # ATR = ((Previous ATR * (period - 1)) + Current TR) / period
        for i in range(self.period, len(true_ranges)):
            current_tr = true_ranges[i]
            previous_atr = atr_values[-1]
            
            atr = ((previous_atr * (self.period - 1)) + current_tr) / self.period
            atr_values.append(atr)
        
        return atr_values
    
    def calculate_simple_atr(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب ATR باستخدام المتوسط المتحرك البسيط
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم ATR البسيط
        """
        if not data or len(data) < self.period + 1:
            return []
        
        true_ranges = self.calculate_true_range(data)
        
        if not true_ranges or len(true_ranges) < self.period:
            return []
        
        atr_values = []
        
        # حساب ATR باستخدام المتوسط المتحرك البسيط
        for i in range(self.period - 1, len(true_ranges)):
            atr = sum(true_ranges[i - self.period + 1:i + 1]) / self.period
            atr_values.append(atr)
        
        return atr_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة ATR (بناءً على مستوى التقلب)
        
        Args:
            values: قيم ATR
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        # إشارات بناءً على تغير التقلب
        if current > previous * 1.2:
            return 'HIGH_VOLATILITY'  # تقلب عالي
        elif current < previous * 0.8:
            return 'LOW_VOLATILITY'   # تقلب منخفض
        elif current > previous:
            return 'INCREASING_VOLATILITY'  # تقلب متزايد
        elif current < previous:
            return 'DECREASING_VOLATILITY'  # تقلب متناقص
        else:
            return 'NEUTRAL'
    
    def get_volatility_level(self, values: List[float]) -> str:
        """
        تحديد مستوى التقلب
        
        Args:
            values: قيم ATR
            
        Returns:
            str: مستوى التقلب
        """
        if not values or len(values) < 10:
            return 'UNKNOWN'
        
        # حساب متوسط آخر 10 قيم
        recent_values = values[-10:]
        current_atr = values[-1]
        avg_atr = sum(recent_values) / len(recent_values)
        
        # تصنيف مستوى التقلب
        if current_atr >= avg_atr * 1.5:
            return 'VERY_HIGH'
        elif current_atr >= avg_atr * 1.2:
            return 'HIGH'
        elif current_atr >= avg_atr * 0.8:
            return 'NORMAL'
        elif current_atr >= avg_atr * 0.5:
            return 'LOW'
        else:
            return 'VERY_LOW'
    
    def calculate_atr_bands(self, data: List[Dict[str, Any]], multiplier: float = 2.0) -> Dict[str, List[float]]:
        """
        حساب نطاقات ATR (مشابه لنطاقات بولينجر لكن باستخدام ATR)
        
        Args:
            data: بيانات الشموع
            multiplier: مضاعف ATR
            
        Returns:
            Dict: نطاقات ATR
        """
        if not data or len(data) < self.period + 1:
            return {
                'upper_band': [],
                'middle_band': [],
                'lower_band': []
            }
        
        atr_values = self.calculate(data)
        
        if not atr_values:
            return {
                'upper_band': [],
                'middle_band': [],
                'lower_band': []
            }
        
        # حساب المتوسط المتحرك للأسعار
        start_index = len(data) - len(atr_values)
        prices = [float(candle['close']) for candle in data[start_index:]]
        
        if len(prices) != len(atr_values):
            # محاذاة البيانات
            min_length = min(len(prices), len(atr_values))
            prices = prices[-min_length:]
            atr_values = atr_values[-min_length:]
        
        # حساب النطاقات
        upper_band = []
        lower_band = []
        middle_band = prices.copy()
        
        for i in range(len(atr_values)):
            if i < len(prices):
                upper = prices[i] + (atr_values[i] * multiplier)
                lower = prices[i] - (atr_values[i] * multiplier)
                upper_band.append(upper)
                lower_band.append(lower)
        
        return {
            'upper_band': upper_band,
            'middle_band': middle_band,
            'lower_band': lower_band
        }
    
    def calculate_atr_percentage(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب ATR كنسبة مئوية من السعر
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم ATR كنسبة مئوية
        """
        atr_values = self.calculate(data)
        
        if not atr_values or len(data) < len(atr_values) + 1:
            return []
        
        # الحصول على الأسعار المقابلة
        start_index = len(data) - len(atr_values)
        prices = [float(candle['close']) for candle in data[start_index:]]
        
        atr_percentages = []
        for i in range(len(atr_values)):
            if i < len(prices) and prices[i] != 0:
                percentage = (atr_values[i] / prices[i]) * 100
                atr_percentages.append(percentage)
            else:
                atr_percentages.append(0.0)
        
        return atr_percentages
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل لـ ATR
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: تحليل شامل
        """
        if not data or len(data) < self.period + 1:
            return {
                'atr_values': [],
                'true_ranges': [],
                'signal': 'NEUTRAL',
                'volatility_level': 'UNKNOWN',
                'atr_bands': {},
                'atr_percentage': []
            }
        
        # حساب القيم
        atr_values = self.calculate(data)
        true_ranges = self.calculate_true_range(data)
        atr_bands = self.calculate_atr_bands(data)
        atr_percentage = self.calculate_atr_percentage(data)
        
        # تحليل الإشارات
        signal = self.get_signal(atr_values)
        volatility_level = self.get_volatility_level(atr_values)
        
        return {
            'atr_values': atr_values,
            'true_ranges': true_ranges,
            'signal': signal,
            'volatility_level': volatility_level,
            'atr_bands': atr_bands,
            'atr_percentage': atr_percentage,
            'current_atr': atr_values[-1] if atr_values else None,
            'current_volatility_percent': atr_percentage[-1] if atr_percentage else None
        }

# إنشاء المؤشرات المطلوبة
class ATR5(ATRIndicator):
    """ATR بفترة 5"""
    def __init__(self):
        super().__init__(5)

class ATR14(ATRIndicator):
    """ATR بفترة 14"""
    def __init__(self):
        super().__init__(14)
