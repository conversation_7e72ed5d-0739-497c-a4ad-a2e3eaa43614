"""
الكلاس الأساسي لجميع المؤشرات الفنية
Base Indicator Class for all Technical Indicators
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseIndicator(ABC):
    """الكلاس الأساسي لجميع المؤشرات الفنية"""

    def __init__(self, period: int, name: str = None):
        """
        تهيئة المؤشر الأساسي

        Args:
            period (int): فترة المؤشر
            name (str): اسم المؤشر (اختياري)
        """
        self.period = period
        self.name = name or self.__class__.__name__
        self.last_calculation_time = None
        self.calculation_count = 0

        # التحقق من صحة المدخلات
        if period <= 0:
            raise ValueError(f"Period must be positive, got {period}")

    @abstractmethod
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب قيم المؤشر

        Args:
            data: قائمة من الشموع (OHLCV)

        Returns:
            List[float]: قائمة قيم المؤشر
        """
        pass

    def validate_data(self, data: List[Dict[str, Any]]) -> bool:
        """
        التحقق من صحة البيانات

        Args:
            data: البيانات المراد التحقق منها

        Returns:
            bool: True إذا كانت البيانات صحيحة
        """
        if not data:
            logger.warning(f"{self.name}: No data provided")
            return False

        if len(data) < self.period:
            logger.warning(f"{self.name}: Insufficient data. Need {self.period}, got {len(data)}")
            return False

        # التحقق من وجود الحقول المطلوبة
        required_fields = ['open', 'high', 'low', 'close']
        for candle in data[:3]:  # فحص أول 3 شموع فقط للسرعة
            for field in required_fields:
                if field not in candle:
                    logger.error(f"{self.name}: Missing required field '{field}' in data")
                    return False

        return True

    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        الحصول على إشارة التداول

        Args:
            values: قيم المؤشر
            current_price: السعر الحالي (اختياري)

        Returns:
            str: إشارة التداول (BULLISH, BEARISH, NEUTRAL)
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'

        current = values[-1]
        previous = values[-2]

        # إشارة بسيطة بناءً على الاتجاه
        if current > previous:
            return 'BULLISH'
        elif current < previous:
            return 'BEARISH'
        else:
            return 'NEUTRAL'

    def calculate_with_validation(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        حساب المؤشر مع التحقق من صحة البيانات وقياس الأداء

        Args:
            data: بيانات الشموع

        Returns:
            Dict: نتائج الحساب مع معلومات إضافية
        """
        start_time = datetime.now()

        # التحقق من صحة البيانات
        if not self.validate_data(data):
            return {
                'values': [],
                'signal': 'NEUTRAL',
                'error': 'Invalid data',
                'calculation_time_ms': 0
            }

        try:
            # حساب المؤشر
            values = self.calculate(data)

            # حساب الإشارة
            signal = self.get_signal(values, data[-1]['close'] if data else None)

            # حساب وقت التنفيذ
            end_time = datetime.now()
            calculation_time = (end_time - start_time).total_seconds() * 1000

            # تحديث الإحصائيات
            self.last_calculation_time = calculation_time
            self.calculation_count += 1

            return {
                'values': values,
                'signal': signal,
                'calculation_time_ms': round(calculation_time, 2),
                'data_points': len(data),
                'indicator_name': self.name,
                'period': self.period
            }

        except Exception as e:
            logger.error(f"{self.name}: Calculation error - {str(e)}")
            return {
                'values': [],
                'signal': 'NEUTRAL',
                'error': str(e),
                'calculation_time_ms': 0
            }

    def get_latest_value(self, data: List[Dict[str, Any]]) -> Optional[float]:
        """
        الحصول على آخر قيمة للمؤشر

        Args:
            data: بيانات الشموع

        Returns:
            Optional[float]: آخر قيمة أو None
        """
        result = self.calculate_with_validation(data)
        values = result.get('values', [])
        return values[-1] if values else None

    def __str__(self) -> str:
        """تمثيل نصي للمؤشر"""
        return f"{self.name}(period={self.period})"

    def __repr__(self) -> str:
        """تمثيل تقني للمؤشر"""
        return f"{self.__class__.__name__}(period={self.period}, name='{self.name}')"