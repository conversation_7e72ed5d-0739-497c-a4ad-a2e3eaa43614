"""
مؤشر نطاقات بولينجر (Bollinger Bands)
Bollinger Bands Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any, Tuple
from indicators.base_indicator import BaseIndicator
import numpy as np

class BollingerBandsIndicator(BaseIndicator):
    """مؤشر نطاقات بولينجر - يقيس التقلب والمستويات المحتملة للدعم والمقاومة"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        """
        تهيئة مؤشر نطاقات بولينجر
        
        Args:
            period: فترة المتوسط المتحرك (افتراضي 20)
            std_dev: عدد الانحرافات المعيارية (افتراضي 2.0)
        """
        super().__init__(period, f"BollingerBands_{period}_{std_dev}")
        self.std_dev = std_dev
        
        # التحقق من صحة المعاملات
        if std_dev <= 0:
            raise ValueError("Standard deviation multiplier must be positive")
    
    def calculate_sma(self, prices: List[float]) -> List[float]:
        """
        حساب المتوسط المتحرك البسيط
        
        Args:
            prices: قائمة الأسعار
            
        Returns:
            List[float]: قيم SMA
        """
        if len(prices) < self.period:
            return []
        
        sma_values = []
        for i in range(self.period - 1, len(prices)):
            sma = sum(prices[i - self.period + 1:i + 1]) / self.period
            sma_values.append(sma)
        
        return sma_values
    
    def calculate_standard_deviation(self, prices: List[float], sma_values: List[float]) -> List[float]:
        """
        حساب الانحراف المعياري المتحرك
        
        Args:
            prices: قائمة الأسعار
            sma_values: قيم المتوسط المتحرك
            
        Returns:
            List[float]: قيم الانحراف المعياري
        """
        if len(prices) < self.period or not sma_values:
            return []
        
        std_values = []
        start_index = self.period - 1
        
        for i, sma in enumerate(sma_values):
            # أخذ آخر period من الأسعار
            period_prices = prices[start_index + i - self.period + 1:start_index + i + 1]
            
            # حساب الانحراف المعياري
            variance = sum((price - sma) ** 2 for price in period_prices) / self.period
            std_dev = variance ** 0.5
            std_values.append(std_dev)
        
        return std_values
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب الخط الأوسط (SMA) لنطاقات بولينجر
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم الخط الأوسط
        """
        if not data or len(data) < self.period:
            return []
        
        prices = [float(candle['close']) for candle in data]
        return self.calculate_sma(prices)
    
    def calculate_full_bands(self, data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        حساب نطاقات بولينجر الكاملة
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: يحتوي على Upper Band, Middle Band (SMA), Lower Band
        """
        if not data or len(data) < self.period:
            return {
                'upper_band': [],
                'middle_band': [],
                'lower_band': []
            }
        
        prices = [float(candle['close']) for candle in data]
        
        # حساب المتوسط المتحرك
        middle_band = self.calculate_sma(prices)
        
        if not middle_band:
            return {
                'upper_band': [],
                'middle_band': [],
                'lower_band': []
            }
        
        # حساب الانحراف المعياري
        std_values = self.calculate_standard_deviation(prices, middle_band)
        
        if not std_values:
            return {
                'upper_band': [],
                'middle_band': middle_band,
                'lower_band': []
            }
        
        # حساب النطاقات العلوية والسفلية
        upper_band = []
        lower_band = []
        
        for i in range(len(middle_band)):
            if i < len(std_values):
                upper = middle_band[i] + (std_values[i] * self.std_dev)
                lower = middle_band[i] - (std_values[i] * self.std_dev)
                upper_band.append(upper)
                lower_band.append(lower)
        
        return {
            'upper_band': upper_band,
            'middle_band': middle_band,
            'lower_band': lower_band
        }
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة بولينجر باندز بناءً على الخط الأوسط
        
        Args:
            values: قيم الخط الأوسط
            current_price: السعر الحالي
            
        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        if current_price is None:
            # إشارة بناءً على اتجاه الخط الأوسط
            current = values[-1]
            previous = values[-2]
            
            if current > previous:
                return 'BULLISH'
            elif current < previous:
                return 'BEARISH'
            else:
                return 'NEUTRAL'
        else:
            # إشارة بناءً على موقع السعر من الخط الأوسط
            middle_line = values[-1]
            
            if current_price > middle_line:
                return 'BULLISH'
            elif current_price < middle_line:
                return 'BEARISH'
            else:
                return 'NEUTRAL'
    
    def get_band_signal(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        إشارة شاملة لنطاقات بولينجر
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: إشارات مفصلة
        """
        bands = self.calculate_full_bands(data)
        
        if not bands['upper_band'] or not bands['lower_band'] or not data:
            return {
                'signal': 'NEUTRAL',
                'position': 'MIDDLE',
                'squeeze': False,
                'expansion': False,
                'bands': bands
            }
        
        current_price = float(data[-1]['close'])
        upper_band = bands['upper_band'][-1]
        middle_band = bands['middle_band'][-1]
        lower_band = bands['lower_band'][-1]
        
        # تحديد موقع السعر
        position = 'MIDDLE'
        signal = 'NEUTRAL'
        
        if current_price >= upper_band:
            position = 'ABOVE_UPPER'
            signal = 'OVERBOUGHT'
        elif current_price <= lower_band:
            position = 'BELOW_LOWER'
            signal = 'OVERSOLD'
        elif current_price > middle_band:
            position = 'UPPER_HALF'
            signal = 'BULLISH'
        elif current_price < middle_band:
            position = 'LOWER_HALF'
            signal = 'BEARISH'
        
        # كشف الضغط والتوسع
        squeeze = False
        expansion = False
        
        if len(bands['upper_band']) >= 2:
            current_width = upper_band - lower_band
            previous_width = bands['upper_band'][-2] - bands['lower_band'][-2]
            
            # الضغط: النطاق يضيق
            if current_width < previous_width * 0.95:
                squeeze = True
            # التوسع: النطاق يتوسع
            elif current_width > previous_width * 1.05:
                expansion = True
        
        return {
            'signal': signal,
            'position': position,
            'squeeze': squeeze,
            'expansion': expansion,
            'bands': bands,
            'current_price': current_price,
            'band_width': upper_band - lower_band,
            'price_position_percent': ((current_price - lower_band) / (upper_band - lower_band)) * 100
        }
    
    def calculate_bandwidth(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب عرض النطاق (Bandwidth)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم عرض النطاق
        """
        bands = self.calculate_full_bands(data)
        
        if not bands['upper_band'] or not bands['lower_band'] or not bands['middle_band']:
            return []
        
        bandwidth_values = []
        for i in range(len(bands['upper_band'])):
            if i < len(bands['lower_band']) and i < len(bands['middle_band']):
                width = bands['upper_band'][i] - bands['lower_band'][i]
                middle = bands['middle_band'][i]
                
                if middle != 0:
                    bandwidth = (width / middle) * 100
                    bandwidth_values.append(bandwidth)
                else:
                    bandwidth_values.append(0.0)
        
        return bandwidth_values
    
    def calculate_percent_b(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب %B (موقع السعر النسبي في النطاق)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم %B
        """
        bands = self.calculate_full_bands(data)
        
        if not bands['upper_band'] or not bands['lower_band'] or len(data) < self.period:
            return []
        
        percent_b_values = []
        start_index = self.period - 1
        
        for i in range(len(bands['upper_band'])):
            if start_index + i < len(data):
                price = float(data[start_index + i]['close'])
                upper = bands['upper_band'][i]
                lower = bands['lower_band'][i]
                
                if upper != lower:
                    percent_b = (price - lower) / (upper - lower)
                    percent_b_values.append(percent_b)
                else:
                    percent_b_values.append(0.5)  # في المنتصف إذا كان النطاق صفر
        
        return percent_b_values

# إنشاء المؤشر المطلوب
class BollingerBands(BollingerBandsIndicator):
    """نطاقات بولينجر بالمعاملات التقليدية (20, 2)"""
    def __init__(self):
        super().__init__(20, 2.0)
