"""
مؤشر المتوسط المتحرك الأسي (EMA)
Exponential Moving Average
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class EMAIndicator(BaseIndicator):
    """مؤشر المتوسط المتحرك الأسي"""
    
    def __init__(self, period: int):
        super().__init__(period, f"EMA_{period}")
        self.multiplier = 2 / (period + 1)
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """حساب EMA"""
        if not data or len(data) < self.period:
            return []

        prices = [float(candle['close']) for candle in data]
        if len(prices) < self.period:
            return []
        
        ema_values = []
        
        # البداية بـ SMA للفترة الأولى
        sma = sum(prices[:self.period]) / self.period
        ema_values.append(sma)
        
        # حساب EMA للفترات التالية
        for i in range(self.period, len(prices)):
            ema = (prices[i] * self.multiplier) + (ema_values[-1] * (1 - self.multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """إشارة EMA المخصصة"""
        if not values or len(values) < 2:
            return "NEUTRAL"
        
        current_ema = values[-1]
        previous_ema = values[-2]
        
        # إشارة بناءً على اتجاه EMA
        if current_ema > previous_ema:
            # إذا كان السعر الحالي أعلى من EMA = إشارة صعود قوية
            if current_price and current_price > current_ema:
                return "STRONG_BULLISH"
            else:
                return "BULLISH"
        elif current_ema < previous_ema:
            # إذا كان السعر الحالي أقل من EMA = إشارة هبوط قوية
            if current_price and current_price < current_ema:
                return "STRONG_BEARISH"
            else:
                return "BEARISH"
        else:
            return "NEUTRAL"

# إنشاء المؤشرات المطلوبة
class EMA5(EMAIndicator):
    """EMA بفترة 5"""
    def __init__(self):
        super().__init__(5)

class EMA10(EMAIndicator):
    """EMA بفترة 10"""
    def __init__(self):
        super().__init__(10)

class EMA21(EMAIndicator):
    """EMA بفترة 21"""
    def __init__(self):
        super().__init__(21)

# اختبار المؤشر
def test_ema_indicator():
    """اختبار مؤشر EMA"""
    print("🔄 اختبار مؤشر EMA...")
    
    # بيانات تجريبية
    test_data = [
        {'open': 1.1000, 'high': 1.1010, 'low': 1.0990, 'close': 1.1005},
        {'open': 1.1005, 'high': 1.1015, 'low': 1.0995, 'close': 1.1010},
        {'open': 1.1010, 'high': 1.1020, 'low': 1.1000, 'close': 1.1015},
        {'open': 1.1015, 'high': 1.1025, 'low': 1.1005, 'close': 1.1020},
        {'open': 1.1020, 'high': 1.1030, 'low': 1.1010, 'close': 1.1025},
        {'open': 1.1025, 'high': 1.1035, 'low': 1.1015, 'close': 1.1030},
        {'open': 1.1030, 'high': 1.1040, 'low': 1.1020, 'close': 1.1035},
        {'open': 1.1035, 'high': 1.1045, 'low': 1.1025, 'close': 1.1040},
        {'open': 1.1040, 'high': 1.1050, 'low': 1.1030, 'close': 1.1045},
        {'open': 1.1045, 'high': 1.1055, 'low': 1.1035, 'close': 1.1050},
    ]
    
    # اختبار EMA5
    ema5 = EMA5()
    values5 = ema5.calculate(test_data)
    
    if values5:
        print(f"✅ EMA5 يعمل - عدد القيم: {len(values5)}")
        print(f"   آخر قيمة: {values5[-1]:.5f}")
        print(f"   الإشارة: {ema5.get_signal(values5, test_data[-1]['close'])}")
    else:
        print("❌ EMA5 فشل")
    
    # اختبار EMA10
    ema10 = EMA10()
    values10 = ema10.calculate(test_data)
    
    if values10:
        print(f"✅ EMA10 يعمل - عدد القيم: {len(values10)}")
        print(f"   آخر قيمة: {values10[-1]:.5f}")
        print(f"   الإشارة: {ema10.get_signal(values10, test_data[-1]['close'])}")
    else:
        print("❌ EMA10 فشل")
    
    # اختبار دقة الحساب
    print("\n🧮 اختبار دقة الحساب:")
    
    # حساب يدوي لـ EMA5
    prices = [d['close'] for d in test_data]
    manual_ema = []
    multiplier = 2 / (5 + 1)
    
    # SMA للفترة الأولى
    sma = sum(prices[:5]) / 5
    manual_ema.append(sma)
    
    # EMA للفترات التالية
    for i in range(5, len(prices)):
        ema = (prices[i] * multiplier) + (manual_ema[-1] * (1 - multiplier))
        manual_ema.append(ema)
    
    # مقارنة النتائج
    if len(values5) == len(manual_ema):
        differences = [abs(a - b) for a, b in zip(values5, manual_ema)]
        max_diff = max(differences)
        
        if max_diff < 0.00001:  # دقة 5 خانات عشرية
            print("✅ دقة الحساب 100% - الفرق الأقصى: {:.8f}".format(max_diff))
        else:
            print(f"❌ خطأ في الحساب - الفرق الأقصى: {max_diff:.8f}")
    else:
        print("❌ عدد القيم غير متطابق")
    
    return len(values5) > 0 and len(values10) > 0

if __name__ == "__main__":
    test_ema_indicator()
