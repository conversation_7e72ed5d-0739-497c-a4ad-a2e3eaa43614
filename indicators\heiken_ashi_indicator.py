"""
مؤشر شموع هايكن آشي (<PERSON><PERSON><PERSON>)
Heiken Ashi Candlesticks Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any, Tuple
from indicators.base_indicator import BaseIndicator
import numpy as np

class HeikenAshiIndicator(BaseIndicator):
    """مؤشر شموع هايكن آشي - يقلل الضوضاء ويوضح الاتجاهات"""
    
    def __init__(self):
        """تهيئة مؤشر هايكن آشي"""
        super().__init__(1, "HeikenAshi")  # لا يحتاج فترة محددة
    
    def calculate_heiken_ashi_candle(self, current_candle: Dict[str, Any], 
                                   previous_ha_candle: Dict[str, Any] = None) -> Dict[str, float]:
        """
        حساب شمعة هايكن آشي واحدة
        
        Formulas:
        HA_Close = (Open + High + Low + Close) / 4
        HA_Open = (Previous_HA_Open + Previous_HA_Close) / 2
        HA_High = max(High, HA_Open, HA_Close)
        HA_Low = min(Low, HA_Open, HA_Close)
        
        Args:
            current_candle: الشمعة الحالية
            previous_ha_candle: شمعة هايكن آشي السابقة
            
        Returns:
            Dict: شمعة هايكن آشي
        """
        open_price = float(current_candle['open'])
        high_price = float(current_candle['high'])
        low_price = float(current_candle['low'])
        close_price = float(current_candle['close'])
        
        # حساب HA_Close
        ha_close = (open_price + high_price + low_price + close_price) / 4
        
        # حساب HA_Open
        if previous_ha_candle is None:
            # للشمعة الأولى
            ha_open = (open_price + close_price) / 2
        else:
            ha_open = (previous_ha_candle['ha_open'] + previous_ha_candle['ha_close']) / 2
        
        # حساب HA_High و HA_Low
        ha_high = max(high_price, ha_open, ha_close)
        ha_low = min(low_price, ha_open, ha_close)
        
        return {
            'ha_open': ha_open,
            'ha_high': ha_high,
            'ha_low': ha_low,
            'ha_close': ha_close,
            'timestamp': current_candle.get('timestamp', ''),
            'original_open': open_price,
            'original_high': high_price,
            'original_low': low_price,
            'original_close': close_price
        }
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب قيم الإغلاق لشموع هايكن آشي (للتوافق مع BaseIndicator)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم إغلاق هايكن آشي
        """
        ha_candles = self.calculate_full_heiken_ashi(data)
        return [candle['ha_close'] for candle in ha_candles]
    
    def calculate_full_heiken_ashi(self, data: List[Dict[str, Any]]) -> List[Dict[str, float]]:
        """
        حساب جميع شموع هايكن آشي
        
        Args:
            data: بيانات الشموع الأصلية
            
        Returns:
            List[Dict]: قائمة شموع هايكن آشي
        """
        if not data:
            return []
        
        ha_candles = []
        previous_ha_candle = None
        
        for candle in data:
            ha_candle = self.calculate_heiken_ashi_candle(candle, previous_ha_candle)
            ha_candles.append(ha_candle)
            previous_ha_candle = ha_candle
        
        return ha_candles
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة هايكن آشي بناءً على قيم الإغلاق
        
        Args:
            values: قيم إغلاق هايكن آشي
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        if current > previous:
            return 'BULLISH'
        elif current < previous:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def get_candle_signal(self, ha_candles: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        إشارة شاملة بناءً على شموع هايكن آشي
        
        Args:
            ha_candles: شموع هايكن آشي
            
        Returns:
            Dict: إشارات مفصلة
        """
        if not ha_candles or len(ha_candles) < 3:
            return {
                'signal': 'NEUTRAL',
                'trend': 'NEUTRAL',
                'candle_type': 'UNKNOWN',
                'strength': 'WEAK',
                'consecutive_count': 0
            }
        
        current_candle = ha_candles[-1]
        previous_candle = ha_candles[-2]
        
        # تحديد نوع الشمعة
        candle_type = self.classify_candle(current_candle)
        
        # تحديد الاتجاه
        trend = self.determine_trend(ha_candles[-5:] if len(ha_candles) >= 5 else ha_candles)
        
        # تحديد الإشارة الرئيسية
        signal = 'NEUTRAL'
        if candle_type == 'BULLISH':
            signal = 'BULLISH'
        elif candle_type == 'BEARISH':
            signal = 'BEARISH'
        elif candle_type == 'DOJI':
            signal = 'NEUTRAL'
        
        # حساب قوة الإشارة
        strength = self.calculate_signal_strength(ha_candles[-3:])
        
        # عدد الشموع المتتالية في نفس الاتجاه
        consecutive_count = self.count_consecutive_candles(ha_candles)
        
        return {
            'signal': signal,
            'trend': trend,
            'candle_type': candle_type,
            'strength': strength,
            'consecutive_count': consecutive_count,
            'current_candle': current_candle,
            'trend_change': self.detect_trend_change(ha_candles[-3:])
        }
    
    def classify_candle(self, candle: Dict[str, float]) -> str:
        """
        تصنيف نوع شمعة هايكن آشي
        
        Args:
            candle: شمعة هايكن آشي
            
        Returns:
            str: نوع الشمعة
        """
        ha_open = candle['ha_open']
        ha_close = candle['ha_close']
        ha_high = candle['ha_high']
        ha_low = candle['ha_low']
        
        body_size = abs(ha_close - ha_open)
        total_range = ha_high - ha_low
        
        if total_range == 0:
            return 'DOJI'
        
        body_ratio = body_size / total_range
        
        # شمعة دوجي (جسم صغير)
        if body_ratio < 0.1:
            return 'DOJI'
        
        # شمعة صاعدة
        elif ha_close > ha_open:
            if body_ratio > 0.8:
                return 'STRONG_BULLISH'  # شمعة صاعدة قوية
            else:
                return 'BULLISH'
        
        # شمعة هابطة
        elif ha_close < ha_open:
            if body_ratio > 0.8:
                return 'STRONG_BEARISH'  # شمعة هابطة قوية
            else:
                return 'BEARISH'
        
        else:
            return 'DOJI'
    
    def determine_trend(self, candles: List[Dict[str, float]]) -> str:
        """
        تحديد الاتجاه العام
        
        Args:
            candles: شموع هايكن آشي
            
        Returns:
            str: الاتجاه
        """
        if not candles or len(candles) < 2:
            return 'NEUTRAL'
        
        bullish_count = 0
        bearish_count = 0
        
        for candle in candles:
            if candle['ha_close'] > candle['ha_open']:
                bullish_count += 1
            elif candle['ha_close'] < candle['ha_open']:
                bearish_count += 1
        
        if bullish_count > bearish_count * 1.5:
            return 'UPTREND'
        elif bearish_count > bullish_count * 1.5:
            return 'DOWNTREND'
        else:
            return 'SIDEWAYS'
    
    def calculate_signal_strength(self, candles: List[Dict[str, float]]) -> str:
        """
        حساب قوة الإشارة
        
        Args:
            candles: آخر شموع هايكن آشي
            
        Returns:
            str: قوة الإشارة
        """
        if not candles:
            return 'WEAK'
        
        total_strength = 0
        for candle in candles:
            body_size = abs(candle['ha_close'] - candle['ha_open'])
            total_range = candle['ha_high'] - candle['ha_low']
            
            if total_range > 0:
                strength = body_size / total_range
                total_strength += strength
        
        avg_strength = total_strength / len(candles)
        
        if avg_strength > 0.7:
            return 'VERY_STRONG'
        elif avg_strength > 0.5:
            return 'STRONG'
        elif avg_strength > 0.3:
            return 'MODERATE'
        else:
            return 'WEAK'
    
    def count_consecutive_candles(self, candles: List[Dict[str, float]]) -> int:
        """
        عدد الشموع المتتالية في نفس الاتجاه
        
        Args:
            candles: شموع هايكن آشي
            
        Returns:
            int: عدد الشموع المتتالية
        """
        if not candles or len(candles) < 2:
            return 0
        
        current_direction = None
        if candles[-1]['ha_close'] > candles[-1]['ha_open']:
            current_direction = 'bullish'
        elif candles[-1]['ha_close'] < candles[-1]['ha_open']:
            current_direction = 'bearish'
        else:
            return 0
        
        count = 1
        for i in range(len(candles) - 2, -1, -1):
            candle = candles[i]
            if current_direction == 'bullish' and candle['ha_close'] > candle['ha_open']:
                count += 1
            elif current_direction == 'bearish' and candle['ha_close'] < candle['ha_open']:
                count += 1
            else:
                break
        
        return count
    
    def detect_trend_change(self, candles: List[Dict[str, float]]) -> bool:
        """
        كشف تغيير الاتجاه
        
        Args:
            candles: آخر 3 شموع هايكن آشي
            
        Returns:
            bool: True إذا كان هناك تغيير في الاتجاه
        """
        if not candles or len(candles) < 3:
            return False
        
        # تحديد اتجاه الشمعتين الأوليين
        first_direction = 'bullish' if candles[0]['ha_close'] > candles[0]['ha_open'] else 'bearish'
        second_direction = 'bullish' if candles[1]['ha_close'] > candles[1]['ha_open'] else 'bearish'
        third_direction = 'bullish' if candles[2]['ha_close'] > candles[2]['ha_open'] else 'bearish'
        
        # تغيير الاتجاه إذا كانت الشمعة الأخيرة مختلفة عن السابقتين
        if first_direction == second_direction and third_direction != first_direction:
            return True
        
        return False
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل لهايكن آشي
        
        Args:
            data: بيانات الشموع الأصلية
            
        Returns:
            Dict: تحليل شامل
        """
        if not data:
            return {
                'ha_candles': [],
                'signal': 'NEUTRAL',
                'trend': 'NEUTRAL',
                'strength': 'WEAK',
                'consecutive_count': 0,
                'trend_change': False
            }
        
        # حساب شموع هايكن آشي
        ha_candles = self.calculate_full_heiken_ashi(data)
        
        # تحليل الإشارات
        signal_analysis = self.get_candle_signal(ha_candles)
        
        return {
            'ha_candles': ha_candles,
            'signal': signal_analysis['signal'],
            'trend': signal_analysis['trend'],
            'candle_type': signal_analysis['candle_type'],
            'strength': signal_analysis['strength'],
            'consecutive_count': signal_analysis['consecutive_count'],
            'trend_change': signal_analysis['trend_change'],
            'current_candle': signal_analysis['current_candle'] if ha_candles else None
        }

# إنشاء المؤشر المطلوب
class HeikenAshi(HeikenAshiIndicator):
    """شموع هايكن آشي"""
    def __init__(self):
        super().__init__()
