"""
مؤشر MACD (Moving Average Convergence Divergence)
تقارب وتباعد المتوسطات المتحركة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any, Tuple
from indicators.base_indicator import BaseIndicator
import numpy as np

class MACDIndicator(BaseIndicator):
    """مؤشر MACD - تقارب وتباعد المتوسطات المتحركة"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """
        تهيئة مؤشر MACD
        
        Args:
            fast_period: فترة EMA السريع (افتراضي 12)
            slow_period: فترة EMA البطيء (افتراضي 26)
            signal_period: فترة خط الإشارة (افتراضي 9)
        """
        super().__init__(slow_period, f"MACD_{fast_period}_{slow_period}_{signal_period}")
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
        # التحقق من صحة المعاملات
        if fast_period >= slow_period:
            raise ValueError("Fast period must be less than slow period")
        if signal_period <= 0:
            raise ValueError("Signal period must be positive")
    
    def calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """
        حساب المتوسط المتحرك الأسي
        
        Args:
            prices: قائمة الأسعار
            period: فترة EMA
            
        Returns:
            List[float]: قيم EMA
        """
        if len(prices) < period:
            return []
        
        multiplier = 2.0 / (period + 1)
        ema_values = []
        
        # البداية بـ SMA للفترة الأولى
        sma = sum(prices[:period]) / period
        ema_values.append(sma)
        
        # حساب EMA للفترات التالية
        for i in range(period, len(prices)):
            ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب MACD
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم MACD Line
        """
        if not data or len(data) < self.slow_period:
            return []
        
        prices = [float(candle['close']) for candle in data]
        
        # حساب EMA السريع والبطيء
        fast_ema = self.calculate_ema(prices, self.fast_period)
        slow_ema = self.calculate_ema(prices, self.slow_period)
        
        if not fast_ema or not slow_ema:
            return []
        
        # محاذاة القوائم (slow_ema أقصر)
        start_index = len(fast_ema) - len(slow_ema)
        aligned_fast_ema = fast_ema[start_index:]
        
        # حساب MACD Line
        macd_line = []
        for i in range(len(slow_ema)):
            macd_value = aligned_fast_ema[i] - slow_ema[i]
            macd_line.append(macd_value)
        
        return macd_line
    
    def calculate_full_macd(self, data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        حساب MACD كاملاً (MACD Line, Signal Line, Histogram)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: يحتوي على MACD Line, Signal Line, Histogram
        """
        if not data or len(data) < self.slow_period + self.signal_period:
            return {
                'macd_line': [],
                'signal_line': [],
                'histogram': []
            }
        
        # حساب MACD Line
        macd_line = self.calculate(data)
        
        if not macd_line or len(macd_line) < self.signal_period:
            return {
                'macd_line': macd_line,
                'signal_line': [],
                'histogram': []
            }
        
        # حساب Signal Line (EMA من MACD Line)
        signal_line = self.calculate_ema(macd_line, self.signal_period)
        
        # محاذاة القوائم لحساب Histogram
        if signal_line:
            start_index = len(macd_line) - len(signal_line)
            aligned_macd = macd_line[start_index:]
            
            # حساب Histogram
            histogram = []
            for i in range(len(signal_line)):
                hist_value = aligned_macd[i] - signal_line[i]
                histogram.append(hist_value)
        else:
            histogram = []
        
        return {
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram
        }
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة MACD بناءً على MACD Line فقط
        
        Args:
            values: قيم MACD Line
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        # إشارات بناءً على اتجاه MACD
        if current > 0 and previous <= 0:
            return 'BULLISH'  # عبور فوق الصفر
        elif current < 0 and previous >= 0:
            return 'BEARISH'  # عبور تحت الصفر
        elif current > previous and current > 0:
            return 'BULLISH'  # اتجاه صاعد فوق الصفر
        elif current < previous and current < 0:
            return 'BEARISH'  # اتجاه هابط تحت الصفر
        else:
            return 'NEUTRAL'
    
    def get_full_signal(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        إشارة MACD كاملة بناءً على جميع المكونات
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: إشارات مفصلة
        """
        macd_data = self.calculate_full_macd(data)
        
        if not macd_data['macd_line'] or not macd_data['signal_line'] or not macd_data['histogram']:
            return {
                'signal': 'NEUTRAL',
                'macd_line': macd_data['macd_line'],
                'signal_line': macd_data['signal_line'],
                'histogram': macd_data['histogram'],
                'crossover': 'NONE',
                'divergence': 'NONE'
            }
        
        macd_line = macd_data['macd_line']
        signal_line = macd_data['signal_line']
        histogram = macd_data['histogram']
        
        # تحديد الإشارة الرئيسية
        main_signal = 'NEUTRAL'
        crossover = 'NONE'
        
        if len(histogram) >= 2:
            current_hist = histogram[-1]
            previous_hist = histogram[-2]
            
            # إشارات العبور
            if current_hist > 0 and previous_hist <= 0:
                main_signal = 'BULLISH'
                crossover = 'BULLISH_CROSSOVER'
            elif current_hist < 0 and previous_hist >= 0:
                main_signal = 'BEARISH'
                crossover = 'BEARISH_CROSSOVER'
            elif current_hist > previous_hist and current_hist > 0:
                main_signal = 'BULLISH'
            elif current_hist < previous_hist and current_hist < 0:
                main_signal = 'BEARISH'
        
        # كشف التباعد (مبسط)
        divergence = 'NONE'
        if len(macd_line) >= 5 and len(data) >= 5:
            recent_prices = [float(candle['close']) for candle in data[-5:]]
            recent_macd = macd_line[-5:]
            
            if (recent_prices[-1] < recent_prices[0] and 
                recent_macd[-1] > recent_macd[0]):
                divergence = 'BULLISH_DIVERGENCE'
            elif (recent_prices[-1] > recent_prices[0] and 
                  recent_macd[-1] < recent_macd[0]):
                divergence = 'BEARISH_DIVERGENCE'
        
        return {
            'signal': main_signal,
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram,
            'crossover': crossover,
            'divergence': divergence
        }

# إنشاء المؤشر المطلوب
class MACD(MACDIndicator):
    """MACD بالمعاملات التقليدية (12, 26, 9)"""
    def __init__(self):
        super().__init__(12, 26, 9)
