"""
مؤشر الزخم (Momentum)
Momentum Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class MomentumIndicator(BaseIndicator):
    """مؤشر الزخم - يقيس معدل تغير السعر"""
    
    def __init__(self, period: int = 10):
        """
        تهيئة مؤشر الزخم
        
        Args:
            period: فترة المؤشر (افتراضي 10)
        """
        super().__init__(period, f"Momentum_{period}")
        self.neutral_level = 100  # المستوى المحايد للزخم
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب مؤشر الزخم
        
        Formula: Momentum = (Current Price / Price n periods ago) * 100
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم الزخم
        """
        if not data or len(data) < self.period + 1:
            return []
        
        prices = [float(candle['close']) for candle in data]
        momentum_values = []
        
        # حساب الزخم لكل نقطة
        for i in range(self.period, len(prices)):
            current_price = prices[i]
            past_price = prices[i - self.period]
            
            if past_price != 0:
                momentum = (current_price / past_price) * 100
                momentum_values.append(momentum)
            else:
                # تجنب القسمة على صفر
                momentum_values.append(100.0)
        
        return momentum_values
    
    def calculate_rate_of_change(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب معدل التغير (Rate of Change) - نسخة بديلة من الزخم
        
        Formula: ROC = ((Current Price - Price n periods ago) / Price n periods ago) * 100
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم معدل التغير
        """
        if not data or len(data) < self.period + 1:
            return []
        
        prices = [float(candle['close']) for candle in data]
        roc_values = []
        
        # حساب معدل التغير لكل نقطة
        for i in range(self.period, len(prices)):
            current_price = prices[i]
            past_price = prices[i - self.period]
            
            if past_price != 0:
                roc = ((current_price - past_price) / past_price) * 100
                roc_values.append(roc)
            else:
                roc_values.append(0.0)
        
        return roc_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة الزخم
        
        Args:
            values: قيم الزخم
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        # إشارات بناءً على مستوى 100 والاتجاه
        if current > self.neutral_level and previous <= self.neutral_level:
            return 'BULLISH'  # عبور فوق المستوى المحايد
        elif current < self.neutral_level and previous >= self.neutral_level:
            return 'BEARISH'  # عبور تحت المستوى المحايد
        elif current > previous and current > self.neutral_level:
            return 'BULLISH'  # زخم صاعد قوي
        elif current < previous and current < self.neutral_level:
            return 'BEARISH'  # زخم هابط قوي
        elif current > previous:
            return 'BULLISH'  # زخم صاعد
        elif current < previous:
            return 'BEARISH'  # زخم هابط
        else:
            return 'NEUTRAL'
    
    def get_momentum_strength(self, values: List[float]) -> str:
        """
        تحديد قوة الزخم
        
        Args:
            values: قيم الزخم
            
        Returns:
            str: قوة الزخم
        """
        if not values:
            return 'NEUTRAL'
        
        current = values[-1]
        
        # تصنيف قوة الزخم
        if current >= 110:
            return 'VERY_STRONG_BULLISH'
        elif current >= 105:
            return 'STRONG_BULLISH'
        elif current >= 102:
            return 'MODERATE_BULLISH'
        elif current >= 98:
            return 'NEUTRAL'
        elif current >= 95:
            return 'MODERATE_BEARISH'
        elif current >= 90:
            return 'STRONG_BEARISH'
        else:
            return 'VERY_STRONG_BEARISH'
    
    def detect_momentum_divergence(self, momentum_values: List[float], price_values: List[float]) -> str:
        """
        كشف التباعد في الزخم
        
        Args:
            momentum_values: قيم الزخم
            price_values: قيم الأسعار
            
        Returns:
            str: نوع التباعد
        """
        if len(momentum_values) < 5 or len(price_values) < 5:
            return 'NONE'
        
        # فحص آخر 5 نقاط
        recent_momentum = momentum_values[-5:]
        recent_prices = price_values[-5:]
        
        # التباعد الصاعد: السعر ينخفض والزخم يرتفع
        if (recent_prices[-1] < recent_prices[0] and 
            recent_momentum[-1] > recent_momentum[0]):
            return 'BULLISH_DIVERGENCE'
        
        # التباعد الهابط: السعر يرتفع والزخم ينخفض
        elif (recent_prices[-1] > recent_prices[0] and 
              recent_momentum[-1] < recent_momentum[0]):
            return 'BEARISH_DIVERGENCE'
        
        return 'NONE'
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل للزخم
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: تحليل شامل
        """
        if not data or len(data) < self.period + 1:
            return {
                'momentum_values': [],
                'roc_values': [],
                'signal': 'NEUTRAL',
                'strength': 'NEUTRAL',
                'divergence': 'NONE',
                'trend': 'NEUTRAL'
            }
        
        # حساب القيم
        momentum_values = self.calculate(data)
        roc_values = self.calculate_rate_of_change(data)
        prices = [float(candle['close']) for candle in data]
        
        # تحليل الإشارات
        signal = self.get_signal(momentum_values)
        strength = self.get_momentum_strength(momentum_values)
        divergence = self.detect_momentum_divergence(momentum_values, prices)
        
        # تحديد الاتجاه العام
        trend = 'NEUTRAL'
        if momentum_values:
            recent_momentum = momentum_values[-min(5, len(momentum_values)):]
            avg_momentum = sum(recent_momentum) / len(recent_momentum)
            
            if avg_momentum > 105:
                trend = 'UPTREND'
            elif avg_momentum < 95:
                trend = 'DOWNTREND'
            else:
                trend = 'SIDEWAYS'
        
        return {
            'momentum_values': momentum_values,
            'roc_values': roc_values,
            'signal': signal,
            'strength': strength,
            'divergence': divergence,
            'trend': trend,
            'current_momentum': momentum_values[-1] if momentum_values else None,
            'neutral_level': self.neutral_level
        }
    
    def calculate_momentum_oscillator(self, data: List[Dict[str, Any]], smoothing_period: int = 3) -> List[float]:
        """
        حساب مذبذب الزخم المنعم
        
        Args:
            data: بيانات الشموع
            smoothing_period: فترة التنعيم
            
        Returns:
            List[float]: قيم المذبذب المنعم
        """
        momentum_values = self.calculate(data)
        
        if not momentum_values or len(momentum_values) < smoothing_period:
            return []
        
        # تطبيق متوسط متحرك بسيط للتنعيم
        smoothed_values = []
        for i in range(smoothing_period - 1, len(momentum_values)):
            avg = sum(momentum_values[i - smoothing_period + 1:i + 1]) / smoothing_period
            smoothed_values.append(avg)
        
        return smoothed_values

# إنشاء المؤشر المطلوب
class Momentum(MomentumIndicator):
    """مؤشر الزخم بفترة 10"""
    def __init__(self):
        super().__init__(10)
