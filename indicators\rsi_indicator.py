"""
مؤشر القوة النسبية (RSI)
Relative Strength Index
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class RSIIndicator(BaseIndicator):
    """مؤشر القوة النسبية"""

    def __init__(self, period: int):
        super().__init__(period, f"RSI_{period}")
        self.overbought_level = 70 if period == 14 else 80  # مستوى التشبع الشرائي
        self.oversold_level = 30 if period == 14 else 20    # مستوى التشبع البيعي
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب RSI باستخدام خوارزمية محسنة

        Args:
            data: بيانات الشموع

        Returns:
            List[float]: قيم RSI
        """
        if not data or len(data) < self.period + 1:
            return []

        prices = [float(candle['close']) for candle in data]
        if len(prices) < self.period + 1:
            return []

        # حساب التغييرات
        changes = np.diff(prices)

        # فصل المكاسب والخسائر
        gains = np.where(changes > 0, changes, 0)
        losses = np.where(changes < 0, -changes, 0)

        rsi_values = []

        # حساب أول RSI باستخدام SMA
        avg_gain = np.mean(gains[:self.period])
        avg_loss = np.mean(losses[:self.period])

        if avg_loss == 0:
            rsi = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi = 100.0 - (100.0 / (1.0 + rs))

        rsi_values.append(rsi)

        # حساب باقي قيم RSI باستخدام EMA (Wilder's smoothing)
        alpha = 1.0 / self.period

        for i in range(self.period, len(changes)):
            # تحديث متوسط المكاسب والخسائر باستخدام EMA
            avg_gain = alpha * gains[i] + (1 - alpha) * avg_gain
            avg_loss = alpha * losses[i] + (1 - alpha) * avg_loss

            # حساب RSI
            if avg_loss == 0:
                rsi = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi = 100.0 - (100.0 / (1.0 + rs))

            rsi_values.append(rsi)

        return rsi_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة RSI محسنة

        Args:
            values: قيم RSI
            current_price: السعر الحالي (غير مستخدم)

        Returns:
            str: إشارة التداول
        """
        if not values or len(values) < 2:
            return "NEUTRAL"

        current_rsi = values[-1]
        previous_rsi = values[-2]

        # إشارات التشبع
        if current_rsi >= self.overbought_level:
            return "OVERBOUGHT"  # تشبع شرائي
        elif current_rsi <= self.oversold_level:
            return "OVERSOLD"   # تشبع بيعي

        # إشارات الاتجاه
        elif current_rsi > previous_rsi and current_rsi > 50:
            return "BULLISH"
        elif current_rsi < previous_rsi and current_rsi < 50:
            return "BEARISH"
        else:
            return "NEUTRAL"

    def get_divergence_signal(self, rsi_values: List[float], price_values: List[float]) -> str:
        """
        كشف التباعد بين RSI والسعر

        Args:
            rsi_values: قيم RSI
            price_values: قيم الأسعار

        Returns:
            str: إشارة التباعد
        """
        if len(rsi_values) < 5 or len(price_values) < 5:
            return "NEUTRAL"

        # استخدام جميع القيم المتاحة
        recent_rsi = rsi_values
        recent_prices = price_values

        # التباعد الصاعد: السعر ينخفض و RSI يرتفع
        if (recent_prices[-1] < recent_prices[0] and
            recent_rsi[-1] > recent_rsi[0]):
            return "BULLISH_DIVERGENCE"

        # التباعد الهابط: السعر يرتفع و RSI ينخفض
        elif (recent_prices[-1] > recent_prices[0] and
              recent_rsi[-1] < recent_rsi[0]):
            return "BEARISH_DIVERGENCE"

        return "NEUTRAL"

# إنشاء المؤشرات المطلوبة
class RSI5(RSIIndicator):
    """RSI بفترة 5"""
    def __init__(self):
        super().__init__(5)

class RSI14(RSIIndicator):
    """RSI بفترة 14"""
    def __init__(self):
        super().__init__(14)
