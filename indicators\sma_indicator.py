"""
مؤشر المتوسط المتحرك البسيط (SMA)
Simple Moving Average
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator

class SMAIndicator(BaseIndicator):
    """مؤشر المتوسط المتحرك البسيط"""
    
    def __init__(self, period: int):
        super().__init__(period, f"SMA_{period}")
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """حساب SMA"""
        if not data or len(data) < self.period:
            return []
        
        prices = [float(candle['close']) for candle in data]
        if len(prices) < self.period:
            return []
        
        sma_values = []
        
        # حساب SMA لكل نافذة
        for i in range(self.period - 1, len(prices)):
            window = prices[i - self.period + 1:i + 1]
            sma = sum(window) / self.period
            sma_values.append(sma)
        
        return sma_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """إشارة SMA"""
        if not values or len(values) < 2:
            return "NEUTRAL"
        
        current_sma = values[-1]
        previous_sma = values[-2]
        
        if current_sma > previous_sma:
            if current_price and current_price > current_sma:
                return "STRONG_BULLISH"
            else:
                return "BULLISH"
        elif current_sma < previous_sma:
            if current_price and current_price < current_sma:
                return "STRONG_BEARISH"
            else:
                return "BEARISH"
        else:
            return "NEUTRAL"

# إنشاء المؤشر المطلوب
class SMA10(SMAIndicator):
    """SMA بفترة 10"""
    def __init__(self):
        super().__init__(10)
