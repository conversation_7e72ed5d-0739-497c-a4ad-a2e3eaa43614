"""
مؤشر ستوكاستيك (Stochastic Oscillator)
Stochastic Oscillator Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any, Tuple
from indicators.base_indicator import BaseIndicator
import numpy as np

class StochasticIndicator(BaseIndicator):
    """مؤشر ستوكاستيك - يقيس موقع السعر الحالي نسبة إلى نطاق الأسعار خلال فترة معينة"""
    
    def __init__(self, k_period: int = 14, d_period: int = 3, smooth_k: int = 3):
        """
        تهيئة مؤشر ستوكاستيك
        
        Args:
            k_period: فترة حساب %K (افتراضي 14)
            d_period: فترة تنعيم %D (افتراضي 3)
            smooth_k: فترة تنعيم %K (افتراضي 3)
        """
        super().__init__(k_period, f"Stochastic_{k_period}_{d_period}_{smooth_k}")
        self.k_period = k_period
        self.d_period = d_period
        self.smooth_k = smooth_k
        self.overbought_level = 80
        self.oversold_level = 20
    
    def calculate_raw_k(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب %K الخام
        
        Formula: %K = ((Current Close - Lowest Low) / (Highest High - Lowest Low)) * 100
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم %K الخام
        """
        if not data or len(data) < self.k_period:
            return []
        
        raw_k_values = []
        
        for i in range(self.k_period - 1, len(data)):
            # أخذ آخر k_period من الشموع
            period_data = data[i - self.k_period + 1:i + 1]
            
            # العثور على أعلى وأدنى سعر في الفترة
            highs = [float(candle['high']) for candle in period_data]
            lows = [float(candle['low']) for candle in period_data]
            
            highest_high = max(highs)
            lowest_low = min(lows)
            current_close = float(data[i]['close'])
            
            # حساب %K الخام
            if highest_high != lowest_low:
                raw_k = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100
                raw_k_values.append(raw_k)
            else:
                raw_k_values.append(50.0)  # في المنتصف إذا كان النطاق صفر
        
        return raw_k_values
    
    def smooth_values(self, values: List[float], period: int) -> List[float]:
        """
        تنعيم القيم باستخدام المتوسط المتحرك البسيط
        
        Args:
            values: القيم المراد تنعيمها
            period: فترة التنعيم
            
        Returns:
            List[float]: القيم المنعمة
        """
        if not values or len(values) < period:
            return []
        
        smoothed_values = []
        for i in range(period - 1, len(values)):
            avg = sum(values[i - period + 1:i + 1]) / period
            smoothed_values.append(avg)
        
        return smoothed_values
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب %K المنعم (للتوافق مع BaseIndicator)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم %K المنعم
        """
        stoch_data = self.calculate_full_stochastic(data)
        return stoch_data.get('k_smooth', [])
    
    def calculate_full_stochastic(self, data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        حساب ستوكاستيك الكامل (%K الخام، %K المنعم، %D)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: يحتوي على جميع قيم ستوكاستيك
        """
        if not data or len(data) < self.k_period + self.smooth_k + self.d_period:
            return {
                'k_raw': [],
                'k_smooth': [],
                'd_values': []
            }
        
        # حساب %K الخام
        raw_k = self.calculate_raw_k(data)
        
        if not raw_k or len(raw_k) < self.smooth_k:
            return {
                'k_raw': raw_k,
                'k_smooth': [],
                'd_values': []
            }
        
        # تنعيم %K
        k_smooth = self.smooth_values(raw_k, self.smooth_k)
        
        if not k_smooth or len(k_smooth) < self.d_period:
            return {
                'k_raw': raw_k,
                'k_smooth': k_smooth,
                'd_values': []
            }
        
        # حساب %D (متوسط متحرك لـ %K المنعم)
        d_values = self.smooth_values(k_smooth, self.d_period)
        
        return {
            'k_raw': raw_k,
            'k_smooth': k_smooth,
            'd_values': d_values
        }
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة ستوكاستيك بناءً على %K المنعم
        
        Args:
            values: قيم %K المنعم
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values:
            return 'NEUTRAL'
        
        current_k = values[-1]
        
        # إشارات بناءً على مستويات ستوكاستيك
        if current_k >= self.overbought_level:
            return 'OVERBOUGHT'
        elif current_k <= self.oversold_level:
            return 'OVERSOLD'
        elif current_k > 50:
            return 'BULLISH'
        elif current_k < 50:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def get_full_signal(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        إشارة ستوكاستيك شاملة
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: إشارات مفصلة
        """
        stoch_data = self.calculate_full_stochastic(data)
        
        if not stoch_data['k_smooth'] or not stoch_data['d_values']:
            return {
                'signal': 'NEUTRAL',
                'k_signal': 'NEUTRAL',
                'd_signal': 'NEUTRAL',
                'crossover': 'NONE',
                'divergence': 'NONE',
                'stochastic_data': stoch_data
            }
        
        k_values = stoch_data['k_smooth']
        d_values = stoch_data['d_values']
        
        # إشارة %K
        k_signal = self.get_signal(k_values)
        
        # إشارة %D
        current_d = d_values[-1]
        if current_d >= self.overbought_level:
            d_signal = 'OVERBOUGHT'
        elif current_d <= self.oversold_level:
            d_signal = 'OVERSOLD'
        elif current_d > 50:
            d_signal = 'BULLISH'
        elif current_d < 50:
            d_signal = 'BEARISH'
        else:
            d_signal = 'NEUTRAL'
        
        # كشف العبور
        crossover = 'NONE'
        if len(k_values) >= 2 and len(d_values) >= 2:
            # محاذاة القوائم
            min_length = min(len(k_values), len(d_values))
            aligned_k = k_values[-min_length:]
            aligned_d = d_values[-min_length:]
            
            if len(aligned_k) >= 2 and len(aligned_d) >= 2:
                current_k = aligned_k[-1]
                previous_k = aligned_k[-2]
                current_d = aligned_d[-1]
                previous_d = aligned_d[-2]
                
                # عبور صاعد
                if current_k > current_d and previous_k <= previous_d:
                    crossover = 'BULLISH_CROSSOVER'
                # عبور هابط
                elif current_k < current_d and previous_k >= previous_d:
                    crossover = 'BEARISH_CROSSOVER'
        
        # الإشارة الرئيسية
        main_signal = 'NEUTRAL'
        if crossover == 'BULLISH_CROSSOVER' and current_d < self.oversold_level + 10:
            main_signal = 'STRONG_BULLISH'
        elif crossover == 'BEARISH_CROSSOVER' and current_d > self.overbought_level - 10:
            main_signal = 'STRONG_BEARISH'
        elif k_signal == 'OVERBOUGHT' and d_signal == 'OVERBOUGHT':
            main_signal = 'OVERBOUGHT'
        elif k_signal == 'OVERSOLD' and d_signal == 'OVERSOLD':
            main_signal = 'OVERSOLD'
        elif k_signal in ['BULLISH', 'OVERBOUGHT']:
            main_signal = 'BULLISH'
        elif k_signal in ['BEARISH', 'OVERSOLD']:
            main_signal = 'BEARISH'
        
        return {
            'signal': main_signal,
            'k_signal': k_signal,
            'd_signal': d_signal,
            'crossover': crossover,
            'divergence': 'NONE',  # يمكن تطويرها لاحقاً
            'stochastic_data': stoch_data,
            'current_k': k_values[-1] if k_values else None,
            'current_d': d_values[-1] if d_values else None
        }
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل لستوكاستيك
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: تحليل شامل
        """
        if not data or len(data) < self.k_period:
            return {
                'stochastic_data': {'k_raw': [], 'k_smooth': [], 'd_values': []},
                'signal': 'NEUTRAL',
                'strength': 'WEAK',
                'trend': 'NEUTRAL'
            }
        
        # حساب البيانات والإشارات
        full_signal = self.get_full_signal(data)
        stoch_data = full_signal['stochastic_data']
        
        # تحديد قوة الإشارة
        strength = 'WEAK'
        if full_signal['crossover'] != 'NONE':
            strength = 'STRONG'
        elif full_signal['signal'] in ['OVERBOUGHT', 'OVERSOLD']:
            strength = 'MODERATE'
        
        # تحديد الاتجاه
        trend = 'NEUTRAL'
        if stoch_data['k_smooth']:
            recent_k = stoch_data['k_smooth'][-min(5, len(stoch_data['k_smooth'])):]
            avg_k = sum(recent_k) / len(recent_k)
            
            if avg_k > 70:
                trend = 'STRONG_UPTREND'
            elif avg_k > 50:
                trend = 'UPTREND'
            elif avg_k < 30:
                trend = 'STRONG_DOWNTREND'
            elif avg_k < 50:
                trend = 'DOWNTREND'
        
        return {
            'stochastic_data': stoch_data,
            'signal': full_signal['signal'],
            'k_signal': full_signal['k_signal'],
            'd_signal': full_signal['d_signal'],
            'crossover': full_signal['crossover'],
            'strength': strength,
            'trend': trend,
            'current_k': full_signal['current_k'],
            'current_d': full_signal['current_d']
        }

# إنشاء المؤشر المطلوب
class Stochastic(StochasticIndicator):
    """مؤشر ستوكاستيك بالمعاملات التقليدية (14, 3, 3)"""
    def __init__(self):
        super().__init__(14, 3, 3)
