"""
مؤشر ويليامز %R (<PERSON> %R)
Williams %R Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class WilliamsRIndicator(BaseIndicator):
    """مؤشر ويليامز %R - يقيس مستوى الإغلاق نسبة إلى نطاق الأسعار خلال فترة معينة"""
    
    def __init__(self, period: int = 14):
        """
        تهيئة مؤشر ويليامز %R
        
        Args:
            period: فترة المؤشر (افتراضي 14)
        """
        super().__init__(period, f"WilliamsR_{period}")
        self.overbought_level = -20  # مستوى التشبع الشرائي
        self.oversold_level = -80    # مستوى التشبع البيعي
        self.extreme_overbought = -10  # مستوى التشبع الشرائي الشديد
        self.extreme_oversold = -90    # مستوى التشبع البيعي الشديد
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب Williams %R
        
        Formula: %R = ((Highest High - Current Close) / (Highest High - Lowest Low)) * -100
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم Williams %R
        """
        if not data or len(data) < self.period:
            return []
        
        williams_r_values = []
        
        for i in range(self.period - 1, len(data)):
            # أخذ آخر period من الشموع
            period_data = data[i - self.period + 1:i + 1]
            
            # العثور على أعلى وأدنى سعر في الفترة
            highs = [float(candle['high']) for candle in period_data]
            lows = [float(candle['low']) for candle in period_data]
            
            highest_high = max(highs)
            lowest_low = min(lows)
            current_close = float(data[i]['close'])
            
            # حساب Williams %R
            if highest_high != lowest_low:
                williams_r = ((highest_high - current_close) / (highest_high - lowest_low)) * -100
                williams_r_values.append(williams_r)
            else:
                williams_r_values.append(-50.0)  # في المنتصف إذا كان النطاق صفر
        
        return williams_r_values
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة Williams %R
        
        Args:
            values: قيم Williams %R
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values:
            return 'NEUTRAL'
        
        current_wr = values[-1]
        
        # إشارات بناءً على مستويات Williams %R
        if current_wr >= self.extreme_overbought:
            return 'EXTREME_OVERBOUGHT'
        elif current_wr >= self.overbought_level:
            return 'OVERBOUGHT'
        elif current_wr <= self.extreme_oversold:
            return 'EXTREME_OVERSOLD'
        elif current_wr <= self.oversold_level:
            return 'OVERSOLD'
        elif current_wr > -50:
            return 'BULLISH'
        elif current_wr < -50:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def get_momentum_signal(self, values: List[float]) -> str:
        """
        إشارة الزخم بناءً على Williams %R
        
        Args:
            values: قيم Williams %R
            
        Returns:
            str: إشارة الزخم
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current = values[-1]
        previous = values[-2]
        
        # إشارات الزخم
        if current > previous and current > self.overbought_level:
            return 'STRONG_BULLISH_MOMENTUM'
        elif current < previous and current < self.oversold_level:
            return 'STRONG_BEARISH_MOMENTUM'
        elif current > previous:
            return 'BULLISH_MOMENTUM'
        elif current < previous:
            return 'BEARISH_MOMENTUM'
        else:
            return 'NEUTRAL_MOMENTUM'
    
    def detect_reversal_signals(self, values: List[float]) -> str:
        """
        كشف إشارات الانعكاس
        
        Args:
            values: قيم Williams %R
            
        Returns:
            str: إشارة الانعكاس
        """
        if not values or len(values) < 3:
            return 'NO_REVERSAL'
        
        current = values[-1]
        previous = values[-2]
        before_previous = values[-3]
        
        # انعكاس صاعد من منطقة التشبع البيعي
        if (before_previous <= self.oversold_level and 
            previous <= self.oversold_level and 
            current > self.oversold_level):
            return 'BULLISH_REVERSAL'
        
        # انعكاس هابط من منطقة التشبع الشرائي
        elif (before_previous >= self.overbought_level and 
              previous >= self.overbought_level and 
              current < self.overbought_level):
            return 'BEARISH_REVERSAL'
        
        # انعكاس قوي من المستويات الشديدة
        elif (previous <= self.extreme_oversold and 
              current > self.extreme_oversold + 10):
            return 'STRONG_BULLISH_REVERSAL'
        
        elif (previous >= self.extreme_overbought and 
              current < self.extreme_overbought - 10):
            return 'STRONG_BEARISH_REVERSAL'
        
        return 'NO_REVERSAL'
    
    def calculate_smoothed_williams_r(self, data: List[Dict[str, Any]], smooth_period: int = 3) -> List[float]:
        """
        حساب Williams %R المنعم
        
        Args:
            data: بيانات الشموع
            smooth_period: فترة التنعيم
            
        Returns:
            List[float]: قيم Williams %R المنعمة
        """
        williams_r_values = self.calculate(data)
        
        if not williams_r_values or len(williams_r_values) < smooth_period:
            return []
        
        smoothed_values = []
        for i in range(smooth_period - 1, len(williams_r_values)):
            avg = sum(williams_r_values[i - smooth_period + 1:i + 1]) / smooth_period
            smoothed_values.append(avg)
        
        return smoothed_values
    
    def detect_divergence(self, williams_r_values: List[float], price_values: List[float]) -> str:
        """
        كشف التباعد بين Williams %R والسعر
        
        Args:
            williams_r_values: قيم Williams %R
            price_values: قيم الأسعار
            
        Returns:
            str: نوع التباعد
        """
        if len(williams_r_values) < 5 or len(price_values) < 5:
            return 'NO_DIVERGENCE'
        
        # فحص آخر 5 نقاط
        recent_wr = williams_r_values[-5:]
        recent_prices = price_values[-5:]
        
        # التباعد الصاعد: السعر ينخفض و Williams %R يرتفع
        if (recent_prices[-1] < recent_prices[0] and 
            recent_wr[-1] > recent_wr[0] and 
            recent_wr[-1] < self.oversold_level + 10):
            return 'BULLISH_DIVERGENCE'
        
        # التباعد الهابط: السعر يرتفع و Williams %R ينخفض
        elif (recent_prices[-1] > recent_prices[0] and 
              recent_wr[-1] < recent_wr[0] and 
              recent_wr[-1] > self.overbought_level - 10):
            return 'BEARISH_DIVERGENCE'
        
        return 'NO_DIVERGENCE'
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل لـ Williams %R
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: تحليل شامل
        """
        if not data or len(data) < self.period:
            return {
                'williams_r_values': [],
                'smoothed_values': [],
                'signal': 'NEUTRAL',
                'momentum_signal': 'NEUTRAL',
                'reversal_signal': 'NO_REVERSAL',
                'divergence': 'NO_DIVERGENCE'
            }
        
        # حساب القيم
        williams_r_values = self.calculate(data)
        smoothed_values = self.calculate_smoothed_williams_r(data)
        prices = [float(candle['close']) for candle in data]
        
        # تحليل الإشارات
        signal = self.get_signal(williams_r_values)
        momentum_signal = self.get_momentum_signal(williams_r_values)
        reversal_signal = self.detect_reversal_signals(williams_r_values)
        divergence = self.detect_divergence(williams_r_values, prices)
        
        # إحصائيات إضافية
        current_wr = williams_r_values[-1] if williams_r_values else None
        max_wr = max(williams_r_values) if williams_r_values else None
        min_wr = min(williams_r_values) if williams_r_values else None
        avg_wr = sum(williams_r_values) / len(williams_r_values) if williams_r_values else None
        
        # تحديد قوة الإشارة
        strength = 'WEAK'
        if reversal_signal.startswith('STRONG'):
            strength = 'VERY_STRONG'
        elif reversal_signal != 'NO_REVERSAL':
            strength = 'STRONG'
        elif signal in ['EXTREME_OVERBOUGHT', 'EXTREME_OVERSOLD']:
            strength = 'STRONG'
        elif signal in ['OVERBOUGHT', 'OVERSOLD']:
            strength = 'MODERATE'
        
        return {
            'williams_r_values': williams_r_values,
            'smoothed_values': smoothed_values,
            'signal': signal,
            'momentum_signal': momentum_signal,
            'reversal_signal': reversal_signal,
            'divergence': divergence,
            'strength': strength,
            'current_williams_r': current_wr,
            'max_williams_r': max_wr,
            'min_williams_r': min_wr,
            'average_williams_r': avg_wr,
            'overbought_readings': len([wr for wr in williams_r_values if wr >= self.overbought_level]),
            'oversold_readings': len([wr for wr in williams_r_values if wr <= self.oversold_level])
        }

# إنشاء المؤشر المطلوب
class WilliamsR(WilliamsRIndicator):
    """مؤشر ويليامز %R بفترة 14"""
    def __init__(self):
        super().__init__(14)
