"""
مؤشر النتيجة المعيارية (Z-Score)
Z-Score Indicator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Dict, Any
from indicators.base_indicator import BaseIndicator
import numpy as np

class ZScoreIndicator(BaseIndicator):
    """مؤشر النتيجة المعيارية - يقيس انحراف السعر عن المتوسط بوحدات الانحراف المعياري"""
    
    def __init__(self, period: int = 20):
        """
        تهيئة مؤشر Z-Score
        
        Args:
            period: فترة حساب المتوسط والانحراف المعياري (افتراضي 20)
        """
        super().__init__(period, f"ZScore_{period}")
        self.overbought_level = 2.0   # مستوى التشبع الشرائي
        self.oversold_level = -2.0    # مستوى التشبع البيعي
        self.extreme_overbought = 3.0 # مستوى التشبع الشرائي الشديد
        self.extreme_oversold = -3.0  # مستوى التشبع البيعي الشديد
    
    def calculate_mean(self, prices: List[float]) -> List[float]:
        """
        حساب المتوسط المتحرك
        
        Args:
            prices: قائمة الأسعار
            
        Returns:
            List[float]: قيم المتوسط المتحرك
        """
        if len(prices) < self.period:
            return []
        
        means = []
        for i in range(self.period - 1, len(prices)):
            mean = sum(prices[i - self.period + 1:i + 1]) / self.period
            means.append(mean)
        
        return means
    
    def calculate_std_dev(self, prices: List[float], means: List[float]) -> List[float]:
        """
        حساب الانحراف المعياري المتحرك
        
        Args:
            prices: قائمة الأسعار
            means: قيم المتوسط المتحرك
            
        Returns:
            List[float]: قيم الانحراف المعياري
        """
        if len(prices) < self.period or not means:
            return []
        
        std_devs = []
        start_index = self.period - 1
        
        for i, mean in enumerate(means):
            # أخذ آخر period من الأسعار
            period_prices = prices[start_index + i - self.period + 1:start_index + i + 1]
            
            # حساب الانحراف المعياري
            variance = sum((price - mean) ** 2 for price in period_prices) / self.period
            std_dev = variance ** 0.5
            std_devs.append(std_dev)
        
        return std_devs
    
    def calculate(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب Z-Score
        
        Formula: Z-Score = (Current Price - Mean) / Standard Deviation
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: قيم Z-Score
        """
        if not data or len(data) < self.period:
            return []
        
        prices = [float(candle['close']) for candle in data]
        
        # حساب المتوسط والانحراف المعياري
        means = self.calculate_mean(prices)
        std_devs = self.calculate_std_dev(prices, means)
        
        if not means or not std_devs or len(means) != len(std_devs):
            return []
        
        # حساب Z-Score
        z_scores = []
        start_index = self.period - 1
        
        for i in range(len(means)):
            current_price = prices[start_index + i]
            mean = means[i]
            std_dev = std_devs[i]
            
            if std_dev != 0:
                z_score = (current_price - mean) / std_dev
                z_scores.append(z_score)
            else:
                z_scores.append(0.0)  # إذا كان الانحراف المعياري صفر
        
        return z_scores
    
    def get_signal(self, values: List[float], current_price: float = None) -> str:
        """
        إشارة Z-Score
        
        Args:
            values: قيم Z-Score
            current_price: السعر الحالي (غير مستخدم)
            
        Returns:
            str: إشارة التداول
        """
        if not values:
            return 'NEUTRAL'
        
        current_z = values[-1]
        
        # إشارات بناءً على مستويات Z-Score
        if current_z >= self.extreme_overbought:
            return 'EXTREME_OVERBOUGHT'
        elif current_z >= self.overbought_level:
            return 'OVERBOUGHT'
        elif current_z <= self.extreme_oversold:
            return 'EXTREME_OVERSOLD'
        elif current_z <= self.oversold_level:
            return 'OVERSOLD'
        elif current_z > 0:
            return 'BULLISH'
        elif current_z < 0:
            return 'BEARISH'
        else:
            return 'NEUTRAL'
    
    def get_mean_reversion_signal(self, values: List[float]) -> str:
        """
        إشارة العودة للمتوسط
        
        Args:
            values: قيم Z-Score
            
        Returns:
            str: إشارة العودة للمتوسط
        """
        if not values or len(values) < 2:
            return 'NEUTRAL'
        
        current_z = values[-1]
        previous_z = values[-2]
        
        # إشارات العودة للمتوسط
        if current_z >= self.overbought_level and previous_z > current_z:
            return 'MEAN_REVERSION_SELL'  # بداية العودة من التشبع الشرائي
        elif current_z <= self.oversold_level and previous_z < current_z:
            return 'MEAN_REVERSION_BUY'   # بداية العودة من التشبع البيعي
        elif abs(current_z) < 0.5 and abs(previous_z) > 1.0:
            return 'MEAN_REVERSION_COMPLETE'  # اكتملت العودة للمتوسط
        else:
            return 'NO_REVERSION'
    
    def calculate_probability_of_reversion(self, values: List[float]) -> float:
        """
        حساب احتمالية العودة للمتوسط
        
        Args:
            values: قيم Z-Score
            
        Returns:
            float: احتمالية العودة للمتوسط (0-1)
        """
        if not values:
            return 0.0
        
        current_z = abs(values[-1])
        
        # احتمالية العودة تزيد مع زيادة قيمة Z-Score المطلقة
        if current_z >= 3.0:
            return 0.95  # احتمالية عالية جداً
        elif current_z >= 2.5:
            return 0.85
        elif current_z >= 2.0:
            return 0.70
        elif current_z >= 1.5:
            return 0.50
        elif current_z >= 1.0:
            return 0.30
        else:
            return 0.10  # احتمالية منخفضة
    
    def detect_outliers(self, values: List[float], threshold: float = 2.5) -> List[int]:
        """
        كشف القيم الشاذة
        
        Args:
            values: قيم Z-Score
            threshold: عتبة كشف القيم الشاذة
            
        Returns:
            List[int]: مؤشرات القيم الشاذة
        """
        outliers = []
        for i, z_score in enumerate(values):
            if abs(z_score) >= threshold:
                outliers.append(i)
        
        return outliers
    
    def calculate_rolling_zscore(self, data: List[Dict[str, Any]], window: int = None) -> List[float]:
        """
        حساب Z-Score المتدحرج بنافذة مختلفة
        
        Args:
            data: بيانات الشموع
            window: حجم النافذة (افتراضي نفس الفترة)
            
        Returns:
            List[float]: قيم Z-Score المتدحرج
        """
        if window is None:
            window = self.period
        
        if not data or len(data) < window:
            return []
        
        prices = [float(candle['close']) for candle in data]
        z_scores = []
        
        for i in range(window - 1, len(prices)):
            window_prices = prices[i - window + 1:i + 1]
            
            mean = sum(window_prices) / window
            variance = sum((price - mean) ** 2 for price in window_prices) / window
            std_dev = variance ** 0.5
            
            if std_dev != 0:
                z_score = (prices[i] - mean) / std_dev
                z_scores.append(z_score)
            else:
                z_scores.append(0.0)
        
        return z_scores
    
    def calculate_normalized_price(self, data: List[Dict[str, Any]]) -> List[float]:
        """
        حساب السعر المعياري (بين 0 و 1)
        
        Args:
            data: بيانات الشموع
            
        Returns:
            List[float]: الأسعار المعيارية
        """
        if not data or len(data) < self.period:
            return []
        
        prices = [float(candle['close']) for candle in data]
        normalized_prices = []
        
        for i in range(self.period - 1, len(prices)):
            window_prices = prices[i - self.period + 1:i + 1]
            min_price = min(window_prices)
            max_price = max(window_prices)
            
            if max_price != min_price:
                normalized = (prices[i] - min_price) / (max_price - min_price)
                normalized_prices.append(normalized)
            else:
                normalized_prices.append(0.5)  # في المنتصف إذا كانت الأسعار ثابتة
        
        return normalized_prices
    
    def get_comprehensive_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل شامل لـ Z-Score
        
        Args:
            data: بيانات الشموع
            
        Returns:
            Dict: تحليل شامل
        """
        if not data or len(data) < self.period:
            return {
                'z_scores': [],
                'signal': 'NEUTRAL',
                'mean_reversion_signal': 'NEUTRAL',
                'reversion_probability': 0.0,
                'outliers': [],
                'normalized_prices': []
            }
        
        # حساب القيم
        z_scores = self.calculate(data)
        normalized_prices = self.calculate_normalized_price(data)
        
        # تحليل الإشارات
        signal = self.get_signal(z_scores)
        mean_reversion_signal = self.get_mean_reversion_signal(z_scores)
        reversion_probability = self.calculate_probability_of_reversion(z_scores)
        outliers = self.detect_outliers(z_scores)
        
        # إحصائيات إضافية
        current_z = z_scores[-1] if z_scores else 0.0
        max_z = max(z_scores) if z_scores else 0.0
        min_z = min(z_scores) if z_scores else 0.0
        avg_z = sum(z_scores) / len(z_scores) if z_scores else 0.0
        
        return {
            'z_scores': z_scores,
            'signal': signal,
            'mean_reversion_signal': mean_reversion_signal,
            'reversion_probability': reversion_probability,
            'outliers': outliers,
            'normalized_prices': normalized_prices,
            'current_z_score': current_z,
            'max_z_score': max_z,
            'min_z_score': min_z,
            'average_z_score': avg_z,
            'outlier_count': len(outliers),
            'extreme_readings': len([z for z in z_scores if abs(z) >= 2.5])
        }

# إنشاء المؤشر المطلوب
class ZScore(ZScoreIndicator):
    """مؤشر Z-Score بفترة 20"""
    def __init__(self):
        super().__init__(20)
