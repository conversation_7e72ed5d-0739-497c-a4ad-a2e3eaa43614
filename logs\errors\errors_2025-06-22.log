2025-06-22 03:50:05.968 | ERROR    | ai_models.xgboost_model:_fetch_training_data:331 | خطأ في جلب بيانات التدريب: 'dict' object has no attribute 'id'

2025-06-22 03:50:05.968 | ERROR    | ai_models.xgboost_model:train:226 | خطأ في تدريب النموذج: 'dict' object has no attribute 'id'

2025-06-22 03:50:05.973 | ERROR    | ai_models.lstm_model:_fetch_training_data:373 | خطأ في جلب بيانات التدريب: 'dict' object has no attribute 'id'

2025-06-22 03:50:05.973 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: 'dict' object has no attribute 'id'

2025-06-22 03:50:05.983 | ERROR    | ai_models.random_forest_model:_fetch_training_data:375 | خطأ في جلب بيانات التدريب: 'dict' object has no attribute 'id'

2025-06-22 03:50:05.984 | ERROR    | ai_models.random_forest_model:train:266 | خطأ في تدريب النموذج: 'dict' object has no attribute 'id'

2025-06-22 03:55:30.591 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 146 < 150

2025-06-22 03:56:33.455 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 146 < 150

2025-06-22 03:56:34.488 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:11.740 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 146 < 200

2025-06-22 04:15:11.753 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:15:11.771 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 190 < 200

2025-06-22 04:15:11.782 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:15:11.794 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 142 < 200

2025-06-22 04:15:11.799 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 175 < 200

2025-06-22 04:15:11.817 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:15:11.825 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 147 < 200

2025-06-22 04:15:11.833 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 142 < 200

2025-06-22 04:15:21.476 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.560 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.662 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.745 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.830 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.919 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:21.995 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.077 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.162 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.244 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.328 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.413 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.494 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.578 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.675 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.752 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:15:22.849 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:25:54.493 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 146 < 200

2025-06-22 04:25:54.498 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:25:54.511 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 190 < 200

2025-06-22 04:25:54.524 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:25:54.540 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 142 < 200

2025-06-22 04:25:54.554 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 175 < 200

2025-06-22 04:25:54.563 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 145 < 200

2025-06-22 04:25:54.574 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 147 < 200

2025-06-22 04:25:54.580 | ERROR    | ai_models.lstm_model:train:249 | خطأ في تدريب النموذج: البيانات غير كافية للتدريب: 142 < 200

2025-06-22 04:26:04.134 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.159 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.234 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.256 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.319 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.345 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.406 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.431 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.504 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.525 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.587 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.617 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.674 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.700 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.768 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.790 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.866 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.902 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:04.969 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:04.993 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 04:26:05.082 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 04:26:05.101 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 21:47:22.401 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_realtime_behavior: RealtimeBehaviorAnalyzer.analyze_realtime_behavior() missing 1 required positional argument: 'candles_data'

2025-06-22 21:47:22.469 | ERROR    | ai_models.lstm_model:predict:318 | خطأ في التنبؤ: النموذج غير مدرب

2025-06-22 22:31:00.702 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.704 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.704 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.708 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.708 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.714 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.714 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.714 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.716 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.716 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.720 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.721 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.721 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.721 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.721 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.724 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.724 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.728 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.728 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.728 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.730 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.730 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.731 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.731 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.731 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.731 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.733 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.733 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.733 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.733 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.733 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.733 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.737 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.737 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.737 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.739 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.739 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.743 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.743 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.743 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.745 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.749 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:224 | خطأ في تحليل الطبقة الفنية: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.749 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:277 | خطأ في تحليل الطبقة السلوكية: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.750 | ERROR    | utils.error_handler:wrapper:140 | فشل نهائي غير متزامن في analyze_zscore_deviation: ZScoreDeviationAnalyzer.analyze_zscore_deviation() missing 1 required positional argument: 'candles_data'

2025-06-22 22:31:00.750 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:381 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.750 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:468 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:31:00.750 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'TechnicalSignalsEngine' object has no attribute 'analyze_signals'

2025-06-22 22:31:00.752 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'MomentumAccelerationDetector' object has no attribute 'detect_momentum'

2025-06-22 22:31:00.752 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'filter_by_historical_probability'

2025-06-22 22:31:00.752 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_unified'

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.363 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.369 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.374 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.380 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.383 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.387 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.387 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.388 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.388 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.389 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.391 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.391 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.391 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.391 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.391 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.394 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.396 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.396 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.396 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.399 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.400 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.405 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.405 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.405 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.405 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.405 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.408 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.408 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:34:07.409 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: cannot import name 'repository' from 'database.repository' (D:\python\new\database\repository.py)

2025-06-22 22:40:05.718 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.726 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.736 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.742 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.742 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.742 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.742 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.742 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.744 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.759 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.762 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.770 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.770 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.770 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.770 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.770 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.778 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.778 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.785 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.793 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.805 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.807 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.807 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.810 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.810 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.811 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.811 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.820 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.826 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.835 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.837 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.837 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.837 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.840 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.840 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.843 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.853 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.858 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.859 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.867 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.868 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.868 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.868 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.869 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.869 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.883 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.890 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.890 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.899 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.901 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.901 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.901 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.901 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.904 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:40:05.924 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.933 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.939 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.940 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.940 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:40:05.940 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:40:05.940 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:40:05.940 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:40:05.947 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.710 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.718 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.728 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.728 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.728 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.734 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.734 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.734 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.736 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.743 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.745 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.751 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.761 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.770 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.778 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.779 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.779 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.779 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.779 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.783 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.788 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.793 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.795 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.805 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.815 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.815 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.815 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.820 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.821 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.821 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.821 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.828 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.862 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.882 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.891 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.894 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.894 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.905 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.906 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.906 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.906 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.909 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.913 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.913 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.921 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.928 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.930 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.936 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.937 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.937 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.937 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.937 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.940 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.942 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.948 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.956 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.965 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.965 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.972 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.972 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:45:15.972 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:45:15.972 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:45:15.972 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:45:15.979 | ERROR    | data_layer.final_signal_evaluation_engine:_evaluate_market_condition:255 | خطأ في تقييم حالة السوق: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.982 | ERROR    | data_layer.smart_risk_management_system:_assess_volatility_risk:243 | خطأ في تقييم مخاطر التقلبات: 'dict' object has no attribute 'close'

2025-06-22 22:45:15.982 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:549 | خطأ في حساب مستويات المخاطر والعائد: 'dict' object has no attribute 'close'

2025-06-22 22:51:16.202 | ERROR    | __main__:display_current_system_state:244 | خطأ في عرض حالة النظام: 'high_risk_rejection_rate'

2025-06-22 22:51:16.353 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:51:16.369 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:51:16.379 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:51:16.379 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:51:16.379 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:51:16.384 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:51:16.384 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:51:16.384 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:51:16.394 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.407 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.411 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:51:16.418 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:51:16.428 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:51:16.428 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:51:16.434 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 22:51:16.434 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 22:51:16.434 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 22:51:16.435 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 22:51:16.444 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.457 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.466 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.477 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 22:51:16.485 | ERROR    | data_layer.smart_risk_management_system:_calculate_risk_reward_levels:613 | خطأ في حساب مستويات المخاطر والعائد: cannot access local variable 'risk_amount' where it is not associated with a value

2025-06-22 23:03:13.880 | ERROR    | data_layer.quadruple_convergence_system:_analyze_technical_layer:238 | خطأ في تحليل الطبقة الفنية: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 23:03:13.886 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 23:03:13.886 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 23:03:13.895 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 23:03:13.895 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة technical: 'CandlestickPatternAnalyzer' object has no attribute 'analyze_candlestick_patterns'

2025-06-22 23:03:13.896 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'RealtimeBehaviorAnalyzer' object has no attribute 'analyze_price_behavior'

2025-06-22 23:03:13.896 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'HistoricalProbabilityFilter' object has no attribute 'analyze_historical_probability'

2025-06-22 23:03:13.896 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'UnifiedPredictionInterface' object has no attribute 'predict_direction'

2025-06-22 23:09:20.853 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:09:20.867 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:09:20.867 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:09:20.867 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:09:20.896 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:09:20.896 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:09:20.896 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:09:20.897 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:09:20.902 | ERROR    | data_layer.quadruple_convergence_system:_analyze_behavioral_layer:311 | خطأ في تحليل الطبقة السلوكية: 'ReversePressureAnalyzer' object has no attribute 'analyze_pressure_patterns'

2025-06-22 23:09:20.910 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'SharpeRatioAnalyzer' object has no attribute 'calculate_sharpe_ratio'

2025-06-22 23:09:20.914 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'MarketStateClassifier' object has no attribute 'classify_market_condition'

2025-06-22 23:09:20.914 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة behavioral: 'ReversePressureAnalyzer' object has no attribute 'analyze_pressure_patterns'

2025-06-22 23:09:20.914 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'SharpeRatioAnalyzer' object has no attribute 'calculate_sharpe_ratio'

2025-06-22 23:09:20.915 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'MarketStateClassifier' object has no attribute 'classify_market_condition'

2025-06-22 23:11:34.992 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:11:35.004 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:11:35.004 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:11:35.004 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:11:35.010 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:11:35.017 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_morning_star_pattern:774 | خطأ في كشف نمط نجمة الصباح: float division by zero

2025-06-22 23:11:35.017 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:11:35.017 | ERROR    | data_layer.candlestick_pattern_analyzer:_detect_evening_star_pattern:832 | خطأ في كشف نمط نجمة المساء: float division by zero

2025-06-22 23:11:35.036 | ERROR    | data_layer.quadruple_convergence_system:_analyze_quantitative_layer:429 | خطأ في تحليل الطبقة الكمية: 'DeviationAnalysisResult' object has no attribute 'get'

2025-06-22 23:11:35.043 | ERROR    | data_layer.quadruple_convergence_system:_analyze_ai_layer:530 | خطأ في تحليل طبقة الذكاء الاصطناعي: 'ConfidenceEvaluationEngine' object has no attribute 'evaluate_confidence'

2025-06-22 23:11:35.043 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة quantitative: 'DeviationAnalysisResult' object has no attribute 'get'

2025-06-22 23:11:35.043 | ERROR    | data_layer.quadruple_convergence_system:analyze_convergence:152 | خطأ في طبقة ai: 'ConfidenceEvaluationEngine' object has no attribute 'evaluate_confidence'

2025-06-22 23:56:32.178 | ERROR    | database.connection_manager:cleanup_connections:534 | خطأ في تنظيف الاتصالات: 'QueuePool' object has no attribute 'invalidate'

2025-06-22 23:56:34.232 | ERROR    | database.connection_manager:reset_connection_pool:566 | خطأ في إعادة تهيئة pool الاتصالات: 'DatabaseManager' object has no attribute 'connection_string'

2025-06-22 23:56:34.507 | ERROR    | execution.real_trading_executor:on_error:131 | خطأ في WebSocket: [Errno 11001] getaddrinfo failed

2025-06-22 23:56:35.353 | ERROR    | execution.continuous_operation_system:_health_alert_callback:232 | 🚨 تنبيه صحي حرج: database - استجابة قاعدة البيانات بطيئة جداً: 7719.8ms

2025-06-22 23:56:35.353 | ERROR    | execution.system_health_monitor:_log_health_summary:567 | 🚨 النظام في حالة حرجة: 1 مشكلة حرجة

2025-06-22 23:56:35.353 | ERROR    | execution.system_health_monitor:_log_health_summary:572 | 🚨 database: استجابة قاعدة البيانات بطيئة جداً: 7719.8ms

2025-06-22 23:56:44.411 | ERROR    | execution.real_trading_executor:initialize_connection:113 | ❌ فشل في الاتصال بمنصة Pocket Option

2025-06-22 23:56:44.412 | ERROR    | execution.continuous_operation_system:start_data_collection_operations:330 | خطأ في بدء عمليات جمع البيانات: 'AsyncHistoricalCollectorWrapper' object has no attribute 'collect_single_pair_data_async'

2025-06-22 23:58:22.691 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في GBPUSD: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.691 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في CHFJPY: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.694 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDMYR_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.696 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDJPY_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.696 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في GBPAUD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.696 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDSGD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.697 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDVND_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.697 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDRUB_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.698 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURCHF_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.698 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDCAD: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.698 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في AUDJPY_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.698 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في NGNUSD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.720 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURNZD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.721 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURCAD: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDPKR_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في LBPUSD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في CADCHF_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURGBP: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في TNDUSD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في CADJPY_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في NZDJPY_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDBRL_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURAUD: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDDZD_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDEGP_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDCHF_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDTHB_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في AUDCAD: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:22.725 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في EURGBP_otc: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

2025-06-22 23:58:26.783 | ERROR    | data_layer.realtime_collector:subscribe_to_asset:345 | ❌ فشل في الاشتراك في USDJPY: PocketOptionError, BinaryOptionsToolsError, Failed to execute 'History' task before the maximum allowed time of '16s'

