#!/usr/bin/env python3
"""
ملف التشغيل الرئيسي لنظام التداول السكالبينج مع أنظمة المراقبة المتكاملة
Main execution file for Scalping Trading System with Integrated Monitoring
"""

import sys
import os
import time
import signal
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.logger import scalping_logger
from config.trading_config import TradingConfig

# استيراد النظام الرئيسي
from execution.continuous_operation_system import continuous_system

# استيراد أنظمة المراقبة المتكاملة
from execution.advanced_performance_monitor import advanced_performance_monitor
from execution.advanced_operations_logger import advanced_operations_logger
from execution.advanced_alert_system import advanced_alert_system
from execution.performance_statistics_system import performance_statistics_system
from execution.data_quality_monitor import data_quality_monitor

logger = scalping_logger.get_logger("main")

class ScalpingTradingSystem:
    """نظام التداول السكالبينج الرئيسي مع المراقبة المتكاملة"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.running = False
        self.start_time = None
        
        # حالة الأنظمة
        self.systems_status = {
            'main_system': False,
            'performance_monitor': False,
            'operations_logger': False,
            'alert_system': False,
            'statistics_system': False,
            'quality_monitor': False
        }
        
        # إعداد معالج الإشارات للإيقاف الآمن
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """معالج إشارات الإيقاف المحسن"""
        print(f"\n⏹️ تم استلام إشارة إيقاف ({signum})")
        print("🛑 بدء إيقاف النظام الآمن...")

        # تعيين علامة الإيقاف
        self.running = False

        # إيقاف النظام المستمر أولاً
        if hasattr(continuous_system, 'system_running'):
            continuous_system.system_running = False
            continuous_system.shutdown_requested = True

        # إنشاء thread منفصل للإيقاف لتجنب blocking
        import threading
        shutdown_thread = threading.Thread(target=self._safe_shutdown, daemon=True)
        shutdown_thread.start()

        # انتظار قصير للإيقاف الآمن
        shutdown_thread.join(timeout=15.0)

        if shutdown_thread.is_alive():
            print("⚠️ انتهت مهلة الإيقاف الآمن - إيقاف قسري")

        print("✅ تم إيقاف النظام")
        import os
        os._exit(0)  # إيقاف قسري فوري

    def _safe_shutdown(self):
        """إيقاف آمن للنظام في thread منفصل"""
        try:
            self.stop_system()
        except Exception as e:
            print(f"خطأ في الإيقاف الآمن: {e}")
            logger.error(f"خطأ في الإيقاف الآمن: {e}")

    def start_system(self):
        """بدء النظام الكامل"""
        try:
            self.start_time = datetime.now()
            
            print("🚀 بدء نظام التداول السكالبينج مع المراقبة المتكاملة")
            print("=" * 80)
            print(f"وقت البدء: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 1. بدء أنظمة المراقبة أولاً
            self._start_monitoring_systems()
            
            # 2. بدء النظام الرئيسي
            self._start_main_system()
            
            # 3. تشغيل حلقة المراقبة الرئيسية
            if self.systems_status['main_system']:
                self._run_main_loop()
            else:
                print("❌ فشل في بدء النظام الرئيسي")
                self._run_monitoring_only()
            
            return True
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام بواسطة المستخدم (Ctrl+C)")
            self.running = False
            return True
        except Exception as e:
            logger.error(f"خطأ في بدء النظام: {str(e)}")
            print(f"💥 خطأ في بدء النظام: {str(e)}")
            return False
        finally:
            self.stop_system()

    def _start_monitoring_systems(self):
        """بدء أنظمة المراقبة"""
        print("🔧 بدء أنظمة المراقبة المتكاملة...")
        
        monitoring_systems = [
            ('performance_monitor', 'مراقب الأداء المتقدم', advanced_performance_monitor.start_monitoring),
            ('operations_logger', 'مسجل العمليات المتقدم', advanced_operations_logger.start_logging),
            ('alert_system', 'نظام التنبيهات المتقدم', advanced_alert_system.start_alert_system),
            ('statistics_system', 'نظام الإحصائيات', performance_statistics_system.start_statistics_collection),
            ('quality_monitor', 'مراقب جودة البيانات', data_quality_monitor.start_quality_monitoring)
        ]
        
        for system_key, system_name, start_function in monitoring_systems:
            try:
                started = start_function()
                self.systems_status[system_key] = started
                status = "✅ نجح" if started else "❌ فشل"
                print(f"{status} - {system_name}")
                
                if started:
                    logger.info(f"تم بدء {system_name} بنجاح")
                else:
                    logger.error(f"فشل في بدء {system_name}")
                    
            except Exception as e:
                logger.error(f"خطأ في بدء {system_name}: {str(e)}")
                print(f"❌ خطأ - {system_name}: {str(e)}")
                self.systems_status[system_key] = False
        
        # عرض ملخص أنظمة المراقبة
        active_systems = sum(1 for status in self.systems_status.values() if status)
        total_systems = len(self.systems_status)
        print(f"\n📊 أنظمة المراقبة النشطة: {active_systems}/{total_systems}")
        print()

    async def _start_main_system_async(self):
        """بدء النظام الرئيسي بشكل غير متزامن"""
        print("⚙️ بدء النظام الرئيسي للتداول السكالبينج...")

        try:
            # بدء النظام المستمر بشكل غير متزامن
            main_started = await continuous_system.start_system()
            self.systems_status['main_system'] = main_started

            if main_started:
                print("✅ تم بدء النظام الرئيسي بنجاح")
                logger.info("تم بدء النظام الرئيسي بنجاح")
                self.running = True
            else:
                print("❌ فشل في بدء النظام الرئيسي")
                logger.error("فشل في بدء النظام الرئيسي")
                # حتى لو فشل النظام الرئيسي، سنشغل أنظمة المراقبة
                self.running = True

        except Exception as e:
            logger.error(f"خطأ في بدء النظام الرئيسي: {str(e)}")
            print(f"❌ خطأ في بدء النظام الرئيسي: {str(e)}")
            self.systems_status['main_system'] = False
            # حتى لو فشل النظام الرئيسي، سنشغل أنظمة المراقبة
            self.running = True

    def _start_main_system(self):
        """بدء النظام الرئيسي (wrapper متزامن)"""
        import asyncio
        try:
            # إنشاء حلقة أحداث جديدة إذا لم تكن موجودة
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # تشغيل النظام غير المتزامن
            loop.run_until_complete(self._start_main_system_async())

        except Exception as e:
            logger.error(f"خطأ في wrapper النظام الرئيسي: {str(e)}")
            print(f"❌ خطأ في wrapper النظام الرئيسي: {str(e)}")
            self.systems_status['main_system'] = False
            self.running = True

    def _run_main_loop(self):
        """تشغيل الحلقة الرئيسية للنظام"""
        print("🎯 النظام يعمل بنجاح مع المراقبة المتكاملة!")
        print("📊 جميع أنظمة المراقبة نشطة")
        print("⏱️ النظام سيعمل بشكل مستمر 24/7")
        print("يمكنك إيقافه بـ Ctrl+C")
        print()

        report_interval = 300  # 5 دقائق
        last_report = time.time()

        try:
            while self.running:
                current_time = time.time()

                # تقرير دوري كل 5 دقائق
                if current_time - last_report >= report_interval:
                    self._print_status_report()
                    last_report = current_time

                # فحص حالة النظام (لكن لا نتوقف إذا كان هناك مشكلة)
                self._check_system_health()

                # نوم قصير مع فحص متكرر للإيقاف
                for _ in range(30):  # 30 ثانية مقسمة على ثوانٍ
                    if not self.running:
                        break
                    time.sleep(1)

        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النظام بواسطة المستخدم (Ctrl+C)")
            self.running = False
        except Exception as e:
            logger.error(f"خطأ في الحلقة الرئيسية: {str(e)}")
            print(f"❌ خطأ في الحلقة الرئيسية: {str(e)}")
            # لا نتوقف، نستمر في العمل

    def _run_monitoring_only(self):
        """تشغيل أنظمة المراقبة فقط"""
        print("🔧 تشغيل أنظمة المراقبة فقط...")
        print("⏱️ ستعمل أنظمة المراقبة لمدة 30 دقيقة")
        print("يمكنك إيقافها بـ Ctrl+C")
        print()
        
        start_time = time.time()
        duration = 1800  # 30 دقيقة
        
        try:
            while time.time() - start_time < duration:
                elapsed_minutes = int((time.time() - start_time) // 60)
                remaining_minutes = int((duration - (time.time() - start_time)) // 60)
                
                if elapsed_minutes % 5 == 0 and elapsed_minutes > 0:  # كل 5 دقائق
                    print(f"📊 الدقيقة {elapsed_minutes}: أنظمة المراقبة تعمل، متبقي {remaining_minutes} دقيقة")
                    self._print_monitoring_stats()
                
                time.sleep(60)  # فحص كل دقيقة
                
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف أنظمة المراقبة بواسطة المستخدم")

    def _print_status_report(self):
        """طباعة تقرير حالة النظام"""
        try:
            elapsed_time = datetime.now() - self.start_time
            elapsed_minutes = int(elapsed_time.total_seconds() // 60)
            
            print(f"📊 تقرير الحالة - الدقيقة {elapsed_minutes}")
            print(f"🕐 الوقت الحالي: {datetime.now().strftime('%H:%M:%S')}")
            
            # حالة النظام الرئيسي
            if self.systems_status['main_system']:
                try:
                    system_status = continuous_system.get_system_status()
                    if system_status:
                        running = system_status.get('system_info', {}).get('running', False)
                        pairs_count = system_status.get('system_info', {}).get('currency_pairs_count', 0)
                        print(f"💾 النظام الرئيسي: {'🟢 يعمل' if running else '🔴 متوقف'}")
                        print(f"📈 الأزواج المراقبة: {pairs_count}")
                except Exception as e:
                    print(f"❌ خطأ في جلب حالة النظام الرئيسي: {str(e)}")
            
            # إحصائيات أنظمة المراقبة
            self._print_monitoring_stats()
            
            print("-" * 60)
            
        except Exception as e:
            logger.error(f"خطأ في طباعة تقرير الحالة: {str(e)}")

    def _print_monitoring_stats(self):
        """طباعة إحصائيات أنظمة المراقبة"""
        try:
            # إحصائيات مراقب الأداء
            if self.systems_status['performance_monitor']:
                try:
                    perf_stats = advanced_performance_monitor.get_performance_statistics()
                    measurements = perf_stats.get('monitoring_status', {}).get('total_measurements', 0)
                    print(f"📊 مقاييس الأداء: {measurements:,}")
                except Exception as e:
                    print(f"❌ خطأ في مراقب الأداء: {str(e)}")
            
            # إحصائيات مسجل العمليات
            if self.systems_status['operations_logger']:
                try:
                    ops_stats = advanced_operations_logger.get_logging_statistics()
                    entries = ops_stats.get('entries_statistics', {}).get('total_entries', 0)
                    errors = ops_stats.get('entries_statistics', {}).get('errors_count', 0)
                    print(f"📝 العمليات المسجلة: {entries:,} (أخطاء: {errors})")
                except Exception as e:
                    print(f"❌ خطأ في مسجل العمليات: {str(e)}")
            
            # إحصائيات نظام التنبيهات
            if self.systems_status['alert_system']:
                try:
                    alert_stats = advanced_alert_system.get_alert_statistics()
                    total_alerts = alert_stats.get('alert_counts', {}).get('total_alerts', 0)
                    pending_alerts = alert_stats.get('alert_counts', {}).get('pending_alerts', 0)
                    print(f"🚨 التنبيهات: {total_alerts:,} (معلقة: {pending_alerts})")
                except Exception as e:
                    print(f"❌ خطأ في نظام التنبيهات: {str(e)}")
            
            # إحصائيات نظام الإحصائيات
            if self.systems_status['statistics_system']:
                try:
                    system_stats = performance_statistics_system.get_system_statistics()
                    data_points = system_stats.get('data_statistics', {}).get('total_data_points', 0)
                    print(f"📈 نقاط الإحصائيات: {data_points:,}")
                except Exception as e:
                    print(f"❌ خطأ في نظام الإحصائيات: {str(e)}")
            
            # إحصائيات مراقب الجودة
            if self.systems_status['quality_monitor']:
                try:
                    quality_stats = data_quality_monitor.get_quality_statistics()
                    quality_score = quality_stats.get('quality_overview', {}).get('avg_quality_score', 0)
                    total_issues = quality_stats.get('quality_overview', {}).get('total_issues', 0)
                    print(f"🔍 جودة البيانات: {quality_score:.1f} نقطة (مشاكل: {total_issues})")
                except Exception as e:
                    print(f"❌ خطأ في مراقب الجودة: {str(e)}")
                    
        except Exception as e:
            logger.error(f"خطأ في طباعة إحصائيات المراقبة: {str(e)}")

    def _check_system_health(self):
        """فحص صحة النظام"""
        try:
            # فحص أنظمة المراقبة الحرجة فقط
            critical_systems = ['operations_logger', 'alert_system']
            for system in critical_systems:
                if not self.systems_status[system]:
                    logger.warning(f"النظام الحرج {system} لا يعمل")

            # النظام يعمل طالما أن أنظمة المراقبة تعمل
            return True

        except Exception as e:
            logger.error(f"خطأ في فحص صحة النظام: {str(e)}")
            return True  # استمر في العمل حتى لو كان هناك خطأ

    def stop_system(self):
        """إيقاف النظام الكامل"""
        if not self.running:
            return
            
        print("\n🛑 إيقاف النظام...")
        self.running = False
        
        # إيقاف النظام الرئيسي
        if self.systems_status['main_system']:
            try:
                print("🛑 إيقاف النظام الرئيسي...")

                # إيقاف قسري للنظام المستمر
                continuous_system.running = False

                # محاولة إيقاف غير متزامن
                import asyncio

                async def stop_async_system():
                    try:
                        return await continuous_system.stop_system()
                    except Exception as e:
                        print(f"خطأ في الإيقاف غير المتزامن: {e}")
                        return False

                # إنشاء حلقة أحداث جديدة إذا لم تكن موجودة
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_closed():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                # محاولة الإيقاف مع timeout
                try:
                    stopped = asyncio.wait_for(stop_async_system(), timeout=10.0)
                    stopped = loop.run_until_complete(stopped)
                except asyncio.TimeoutError:
                    print("⚠️ انتهت مهلة الإيقاف - إيقاف قسري")
                    stopped = False

                if stopped:
                    print("✅ تم إيقاف النظام الرئيسي")
                    logger.info("تم إيقاف النظام الرئيسي")
                else:
                    print("⚠️ تم إيقاف النظام الرئيسي قسرياً")

            except Exception as e:
                print(f"❌ خطأ في إيقاف النظام الرئيسي: {str(e)}")
                logger.error(f"خطأ في إيقاف النظام الرئيسي: {str(e)}")
                # إيقاف قسري
                continuous_system.running = False
        
        # إيقاف أنظمة المراقبة
        monitoring_systems = [
            ('performance_monitor', 'مراقب الأداء', advanced_performance_monitor.stop_monitoring),
            ('operations_logger', 'مسجل العمليات', advanced_operations_logger.stop_logging),
            ('alert_system', 'نظام التنبيهات', advanced_alert_system.stop_alert_system),
            ('statistics_system', 'نظام الإحصائيات', performance_statistics_system.stop_statistics_collection),
            ('quality_monitor', 'مراقب الجودة', data_quality_monitor.stop_quality_monitoring)
        ]
        
        for system_key, system_name, stop_function in monitoring_systems:
            if self.systems_status[system_key]:
                try:
                    print(f"🛑 إيقاف {system_name}...")

                    # محاولة الإيقاف مع timeout
                    import threading
                    import time

                    stop_completed = threading.Event()
                    stop_error = None

                    def stop_with_timeout():
                        nonlocal stop_error
                        try:
                            stop_function()
                            stop_completed.set()
                        except Exception as e:
                            stop_error = e
                            stop_completed.set()

                    stop_thread = threading.Thread(target=stop_with_timeout, daemon=True)
                    stop_thread.start()

                    # انتظار لمدة 5 ثوانٍ
                    if stop_completed.wait(timeout=5.0):
                        if stop_error:
                            raise stop_error
                        print(f"✅ تم إيقاف {system_name}")
                        logger.info(f"تم إيقاف {system_name}")
                    else:
                        print(f"⚠️ انتهت مهلة إيقاف {system_name}")

                except Exception as e:
                    print(f"❌ خطأ في إيقاف {system_name}: {str(e)}")
                    logger.error(f"خطأ في إيقاف {system_name}: {str(e)}")
        
        # طباعة ملخص نهائي مفصل
        if self.start_time:
            total_time = datetime.now() - self.start_time
            print(f"\n📊 ملخص التشغيل النهائي:")
            print(f"⏱️ مدة التشغيل: {total_time}")
            print(f"🕐 وقت البدء: {self.start_time.strftime('%H:%M:%S')}")
            print(f"🕐 وقت الانتهاء: {datetime.now().strftime('%H:%M:%S')}")

            # إحصائيات مفصلة
            self._print_final_statistics()

        print("\n🎉 تم إيقاف النظام بنجاح")
        print("=" * 80)

    def _print_final_statistics(self):
        """طباعة الإحصائيات النهائية المفصلة"""
        try:
            print("\n📈 الإحصائيات النهائية:")

            # إحصائيات مراقب الأداء
            if self.systems_status['performance_monitor']:
                try:
                    perf_stats = advanced_performance_monitor.get_performance_statistics()
                    measurements = perf_stats.get('monitoring_status', {}).get('total_measurements', 0)
                    alerts_generated = perf_stats.get('monitoring_status', {}).get('alerts_generated', 0)
                    print(f"📊 مراقب الأداء:")
                    print(f"   - إجمالي المقاييس: {measurements:,}")
                    print(f"   - التنبيهات المُنشأة: {alerts_generated:,}")
                except Exception as e:
                    print(f"❌ خطأ في إحصائيات مراقب الأداء: {str(e)}")

            # إحصائيات مسجل العمليات
            if self.systems_status['operations_logger']:
                try:
                    ops_stats = advanced_operations_logger.get_logging_statistics()
                    total_entries = ops_stats.get('entries_statistics', {}).get('total_entries', 0)
                    errors_count = ops_stats.get('entries_statistics', {}).get('errors_count', 0)
                    success_rate = ops_stats.get('entries_statistics', {}).get('success_rate_percentage', 0)
                    print(f"📝 مسجل العمليات:")
                    print(f"   - إجمالي العمليات: {total_entries:,}")
                    print(f"   - الأخطاء: {errors_count:,}")
                    print(f"   - معدل النجاح: {success_rate:.1f}%")
                except Exception as e:
                    print(f"❌ خطأ في إحصائيات مسجل العمليات: {str(e)}")

            # إحصائيات نظام التنبيهات
            if self.systems_status['alert_system']:
                try:
                    alert_stats = advanced_alert_system.get_alert_statistics()
                    total_alerts = alert_stats.get('alert_counts', {}).get('total_alerts', 0)
                    pending_alerts = alert_stats.get('alert_counts', {}).get('pending_alerts', 0)
                    resolved_alerts = alert_stats.get('alert_counts', {}).get('resolved_alerts', 0)
                    print(f"🚨 نظام التنبيهات:")
                    print(f"   - إجمالي التنبيهات: {total_alerts:,}")
                    print(f"   - التنبيهات المعلقة: {pending_alerts:,}")
                    print(f"   - التنبيهات المحلولة: {resolved_alerts:,}")
                except Exception as e:
                    print(f"❌ خطأ في إحصائيات نظام التنبيهات: {str(e)}")

            # إحصائيات نظام الإحصائيات
            if self.systems_status['statistics_system']:
                try:
                    system_stats = performance_statistics_system.get_system_statistics()
                    data_points = system_stats.get('data_statistics', {}).get('total_data_points', 0)
                    statistics_calculated = system_stats.get('data_statistics', {}).get('statistics_calculated', 0)
                    print(f"📈 نظام الإحصائيات:")
                    print(f"   - نقاط البيانات: {data_points:,}")
                    print(f"   - الإحصائيات المحسوبة: {statistics_calculated:,}")
                except Exception as e:
                    print(f"❌ خطأ في إحصائيات نظام الإحصائيات: {str(e)}")

            # إحصائيات مراقب الجودة
            if self.systems_status['quality_monitor']:
                try:
                    quality_stats = data_quality_monitor.get_quality_statistics()
                    avg_quality = quality_stats.get('quality_overview', {}).get('avg_quality_score', 0)
                    total_issues = quality_stats.get('quality_overview', {}).get('total_issues', 0)
                    assets_monitored = quality_stats.get('assets_breakdown', {}).get('total_assets', 0)
                    print(f"🔍 مراقب جودة البيانات:")
                    print(f"   - الأصول المراقبة: {assets_monitored}")
                    print(f"   - متوسط نقاط الجودة: {avg_quality:.1f}")
                    print(f"   - إجمالي المشاكل: {total_issues:,}")
                except Exception as e:
                    print(f"❌ خطأ في إحصائيات مراقب الجودة: {str(e)}")

            # إحصائيات النظام الرئيسي (إذا كان يعمل)
            if self.systems_status['main_system']:
                try:
                    system_status = continuous_system.get_system_status()
                    if system_status:
                        pairs_count = system_status.get('system_info', {}).get('currency_pairs_count', 0)
                        print(f"🎯 النظام الرئيسي:")
                        print(f"   - أزواج العملات المراقبة: {pairs_count}")

                        # إحصائيات البيانات المجمعة
                        components = system_status.get('components_status', {})
                        if 'parallel_operations' in components:
                            parallel_stats = components['parallel_operations']
                            print(f"   - العمليات المتوازية: {parallel_stats.get('active', 'غير محدد')}")

                except Exception as e:
                    print(f"❌ خطأ في إحصائيات النظام الرئيسي: {str(e)}")

        except Exception as e:
            logger.error(f"خطأ في طباعة الإحصائيات النهائية: {str(e)}")
            print(f"❌ خطأ في طباعة الإحصائيات النهائية: {str(e)}")

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل النظام
        system = ScalpingTradingSystem()
        success = system.start_system()
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"💥 خطأ غير متوقع: {str(e)}")
        logger.error(f"خطأ غير متوقع في main: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
