{"report_id": "comprehensive_hourly_20250623_003431", "generation_time": "2025-06-23T00:34:31.772894", "report_period": "hourly", "period_start": "2025-06-22T23:34:31.772894", "period_end": "2025-06-23T00:34:31.772894", "layers_performance": {"technical": {"layer_name": "Technical Analysis", "total_analyses": 0, "successful_analyses": 0, "failed_analyses": 0, "success_rate": 0.0, "avg_processing_time": 0.0, "accuracy_rate": 0.0, "signal_distribution": {}, "confidence_distribution": {}, "error_types": {}, "performance_trends": [], "recommendations": [], "critical_issues": [], "last_update": null}, "behavioral": {"layer_name": "Behavioral Analysis", "total_analyses": 0, "successful_analyses": 0, "failed_analyses": 0, "success_rate": 0.0, "avg_processing_time": 0.0, "accuracy_rate": 0.0, "signal_distribution": {}, "confidence_distribution": {}, "error_types": {}, "performance_trends": [], "recommendations": [], "critical_issues": [], "last_update": null}, "quantitative": {"layer_name": "Quantitative Analysis", "total_analyses": 0, "successful_analyses": 0, "failed_analyses": 0, "success_rate": 0.0, "avg_processing_time": 0.0, "accuracy_rate": 0.0, "signal_distribution": {}, "confidence_distribution": {}, "error_types": {}, "performance_trends": [], "recommendations": ["تحسين النماذج الإحصائية والكمية"], "critical_issues": ["معدل فشل عالي في التحليل الكمي"], "last_update": "2025-06-23T00:34:31.780680"}, "ai": {"layer_name": "Artificial Intelligence", "total_analyses": 200, "successful_analyses": 165, "failed_analyses": 35, "success_rate": 0.825, "avg_processing_time": 0.45, "accuracy_rate": 0.82, "signal_distribution": {"CALL": 85, "PUT": 80, "NEUTRAL": 35}, "confidence_distribution": {"high": 120, "medium": 60, "low": 20}, "error_types": {"model_error": 8, "data_error": 5, "timeout": 2}, "performance_trends": [], "recommendations": ["تحسين معالجة الأخطاء في نماذج الذكاء الاصطناعي"], "critical_issues": [], "last_update": "2025-06-23T00:34:31.780680"}}, "system_overview": {}, "trading_performance": {"total_trades": 0, "winning_trades": 0, "losing_trades": 0, "win_rate_percent": 0.0, "total_profit": 0.0, "profit_factor": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "avg_trade_duration_minutes": 0.0, "daily_trade_count": 0, "monthly_profit": 0.0, "best_performing_asset": "N/A", "worst_performing_asset": "N/A", "last_update": "2025-06-23T00:34:31.782318"}, "decision_quality": {"total_decisions": 0, "correct_decisions": 0, "incorrect_decisions": 0, "overall_accuracy_percent": 0.0, "excellent_decisions": 0, "good_decisions": 0, "average_decisions": 0, "poor_decisions": 0, "very_poor_decisions": 0, "top_performing_assets": [], "system_recommendations": ["دقة النظام العامة منخفضة - راجع جميع طبقات التحليل", "عد<PERSON> القرارات قليل - انتظر المزيد من البيانات للتحليل الدقيق"], "monitoring_active": false, "last_update": null}, "convergence_analysis": {"total_convergence_analyses": 100, "convergent_signals": 75, "divergent_signals": 20, "neutral_signals": 5, "convergence_rate_percent": 75.0, "layer_agreement_analysis": {"technical": {"total_signals": 80, "correct_signals": 65, "accuracy_percent": 81.25, "contribution_weight": 0.36}, "behavioral": {"total_signals": 70, "correct_signals": 55, "accuracy_percent": 78.57142857142857, "contribution_weight": 0.25}, "quantitative": {"total_signals": 85, "correct_signals": 70, "accuracy_percent": 82.35294117647058, "contribution_weight": 0.3}, "ai": {"total_signals": 75, "correct_signals": 60, "accuracy_percent": 80.0, "contribution_weight": 0.24}}, "signal_strength_distribution": {"strong": 0, "medium": 0, "weak": 0}, "convergence_trends": [], "pattern_insights": ["معدل تقارب جيد - توافق مقبول بين الطبقات", "أفضل طبقة أداءً: quantitative"]}, "performance_comparison": {"accuracy_ranking": [{"layer": "Artificial Intelligence", "accuracy": 0.82}, {"layer": "Technical Analysis", "accuracy": 0.0}, {"layer": "Behavioral Analysis", "accuracy": 0.0}, {"layer": "Quantitative Analysis", "accuracy": 0.0}], "speed_ranking": [{"layer": "Technical Analysis", "processing_time": 0.0}, {"layer": "Behavioral Analysis", "processing_time": 0.0}, {"layer": "Quantitative Analysis", "processing_time": 0.0}, {"layer": "Artificial Intelligence", "processing_time": 0.45}], "reliability_ranking": [{"layer": "Artificial Intelligence", "success_rate": 0.825}, {"layer": "Technical Analysis", "success_rate": 0.0}, {"layer": "Behavioral Analysis", "success_rate": 0.0}, {"layer": "Quantitative Analysis", "success_rate": 0.0}], "overall_ranking": [{"layer": "Artificial Intelligence", "overall_score": 81.55000000000001}, {"layer": "Technical Analysis", "overall_score": 30.0}, {"layer": "Behavioral Analysis", "overall_score": 30.0}, {"layer": "Quantitative Analysis", "overall_score": 30.0}], "performance_matrix": {"Technical Analysis": {"accuracy_score": 0.0, "speed_score": 100.0, "reliability_score": 0.0, "overall_score": 30.0}, "Behavioral Analysis": {"accuracy_score": 0.0, "speed_score": 100.0, "reliability_score": 0.0, "overall_score": 30.0}, "Quantitative Analysis": {"accuracy_score": 0.0, "speed_score": 100.0, "reliability_score": 0.0, "overall_score": 30.0}, "Artificial Intelligence": {"accuracy_score": 82.0, "speed_score": 80.0, "reliability_score": 82.5, "overall_score": 81.55000000000001}}, "improvement_suggestions": {"Technical Analysis": ["تحسين دقة Technical Analysis - مراجعة المعايير والخوارزميات", "تحسين موثوقية Technical Analysis - معالجة الأخطاء والاستثناءات"], "Behavioral Analysis": ["تحسين دقة Behavioral Analysis - مراجعة المعايير والخوارزميات", "تحسين موثوقية Behavioral Analysis - معالجة الأخطاء والاستثناءات"], "Quantitative Analysis": ["تحسين دقة Quantitative Analysis - مراجعة المعايير والخوارزميات", "تحسين موثوقية Quantitative Analysis - معالجة الأخطاء والاستثناءات"], "Artificial Intelligence": ["معالجة خطأ model_error في Artificial Intelligence"]}}, "recommendations": ["الأداء العام للنظام يحتاج تحسين - مراجعة شاملة مطلوبة", "معدل الفوز منخفض (0.0%) - تحسين استراتيجية التداول", "جودة القرارات منخفضة (0.0%) - تحسين معايير اتخاذ القرارات", "تحسين النماذج الإحصائية والكمية", "تحسين معالجة الأخطاء في نماذج الذكاء الاصطناعي"], "metadata": {"total_assets_analyzed": 70, "generation_duration_seconds": 0.009424, "report_size_mb": 0.0}}