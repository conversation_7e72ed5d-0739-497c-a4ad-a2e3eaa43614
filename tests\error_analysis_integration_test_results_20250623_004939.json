{"test_start_time": "2025-06-23T00:48:01.616522", "tests_passed": 17, "tests_failed": 3, "test_details": [{"test_name": "system_start", "status": "✅ نجح", "passed": true, "description": "بدء النظام", "timestamp": "2025-06-23T00:48:01.626534"}, {"test_name": "system_running", "status": "✅ نجح", "passed": true, "description": "النظام يعمل", "timestamp": "2025-06-23T00:48:04.627356"}, {"test_name": "system_stop", "status": "✅ نجح", "passed": true, "description": "إيق<PERSON><PERSON> النظام", "timestamp": "2025-06-23T00:48:14.632622"}, {"test_name": "performance_analyzer_integration", "status": "❌ فشل", "passed": false, "description": "فشل التكامل مع محلل الأداء: 'StrategyPerformanceAnalyzer' object has no attribute 'get_performance_analysis'", "timestamp": "2025-06-23T00:48:16.637943"}, {"test_name": "decision_quality_integration", "status": "❌ فشل", "passed": false, "description": "فشل التكامل مع مراقب جودة القرارات: 'DecisionQualityMonitor' object has no attribute 'get_quality_analysis'", "timestamp": "2025-06-23T00:48:16.637943"}, {"test_name": "operations_logger_integration", "status": "✅ نجح", "passed": true, "description": "التكامل مع مسجل العمليات", "timestamp": "2025-06-23T00:48:16.638972"}, {"test_name": "data_quality_integration", "status": "✅ نجح", "passed": true, "description": "التكامل مع مراقب جودة البيانات", "timestamp": "2025-06-23T00:48:16.639869"}, {"test_name": "error_categorization", "status": "✅ نجح", "passed": true, "description": "تصنيف الأخطاء - 3 فئة", "timestamp": "2025-06-23T00:48:28.656170"}, {"test_name": "error_statistics", "status": "✅ نجح", "passed": true, "description": "إحصائيات الأخطاء - 3 خطأ", "timestamp": "2025-06-23T00:48:28.656170"}, {"test_name": "suggestions_generation", "status": "❌ فشل", "passed": false, "description": "توليد التوصيات - 0 توصية", "timestamp": "2025-06-23T00:48:40.669890"}, {"test_name": "performance_stability", "status": "✅ نجح", "passed": true, "description": "تحليل استقرار الأداء", "timestamp": "2025-06-23T00:48:52.691243"}, {"test_name": "performance_trend", "status": "✅ نجح", "passed": true, "description": "تحليل اتجاه الأداء", "timestamp": "2025-06-23T00:48:52.691243"}, {"test_name": "analysis_report", "status": "✅ نجح", "passed": true, "description": "تقرير التحليل - 8 عنصر", "timestamp": "2025-06-23T00:49:04.704785"}, {"test_name": "report_sections", "status": "✅ نجح", "passed": true, "description": "أقسام التقرير المطلوبة", "timestamp": "2025-06-23T00:49:04.704785"}, {"test_name": "comprehensive_analysis", "status": "✅ نجح", "passed": true, "description": "التحليل الشامل - 5 قسم", "timestamp": "2025-06-23T00:49:04.704785"}, {"test_name": "health_score_calculation", "status": "✅ نجح", "passed": true, "description": "حساب نقاط الصحة - 100.0", "timestamp": "2025-06-23T00:49:16.726793"}, {"test_name": "health_factors", "status": "✅ نجح", "passed": true, "description": "عوامل الصحة - 4 عامل", "timestamp": "2025-06-23T00:49:16.726793"}, {"test_name": "risk_assessment", "status": "✅ نجح", "passed": true, "description": "تقييم المخاطر - low", "timestamp": "2025-06-23T00:49:16.726793"}, {"test_name": "comprehensive_sections", "status": "✅ نجح", "passed": true, "description": "أق<PERSON>ا<PERSON> التحليل الشامل", "timestamp": "2025-06-23T00:49:29.749540"}, {"test_name": "implementation_roadmap", "status": "✅ نجح", "passed": true, "description": "خارطة طريق التنفيذ - 0 مرحلة", "timestamp": "2025-06-23T00:49:29.749540"}], "integration_results": {"operations_logger": true, "data_quality": true}, "test_end_time": "2025-06-23T00:49:39.752708"}