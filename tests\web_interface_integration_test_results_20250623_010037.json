{"test_start_time": "2025-06-23T01:00:30.536109", "tests_passed": 12, "tests_failed": 3, "test_details": [{"test_name": "server_startup", "status": "✅ نجح", "passed": true, "description": "بد<PERSON> خادم الواجهة", "timestamp": "2025-06-23T01:00:33.572476"}, {"test_name": "page__", "status": "✅ نجح", "passed": true, "description": "صفحة الصفحة الرئيسية - كود 200", "timestamp": "2025-06-23T01:00:33.572476"}, {"test_name": "page__dashboard", "status": "✅ نجح", "passed": true, "description": "صفحة لوحة التحكم - كود 200", "timestamp": "2025-06-23T01:00:33.590527"}, {"test_name": "page__trading", "status": "❌ فشل", "passed": false, "description": "صفحة واجهة التداول - كود 500", "timestamp": "2025-06-23T01:00:33.596422"}, {"test_name": "page__analytics", "status": "❌ فشل", "passed": false, "description": "صفحة واجهة التحليلات - كود 500", "timestamp": "2025-06-23T01:00:33.623897"}, {"test_name": "page__settings", "status": "❌ فشل", "passed": false, "description": "صفحة واجهة الإعدادات - كود 500", "timestamp": "2025-06-23T01:00:33.629025"}, {"test_name": "api_get_status", "status": "✅ نجح", "passed": true, "description": "API حالة النظام - تنسيق صحيح: True", "timestamp": "2025-06-23T01:00:33.647873"}, {"test_name": "api_get_pairs", "status": "✅ نجح", "passed": true, "description": "API أزواج العملات - تنسيق صحيح: True", "timestamp": "2025-06-23T01:00:33.675208"}, {"test_name": "api_get_performance", "status": "✅ نجح", "passed": true, "description": "API تحليلات الأداء - تنسيق صحيح: True", "timestamp": "2025-06-23T01:00:33.692358"}, {"test_name": "api_get_stats", "status": "✅ نجح", "passed": true, "description": "API إحصائيات قواعد البيانات - تنسيق صحيح: True", "timestamp": "2025-06-23T01:00:33.724566"}, {"test_name": "system_start", "status": "✅ نجح", "passed": true, "description": "بدء النظام - نجح: True", "timestamp": "2025-06-23T01:00:35.759223"}, {"test_name": "system_stop", "status": "✅ نجح", "passed": true, "description": "إيقاف النظام - نجح: True", "timestamp": "2025-06-23T01:00:37.770333"}, {"test_name": "trading_pairs_data", "status": "✅ نجح", "passed": true, "description": "بيانات أزواج العملات - ع<PERSON><PERSON> الأزواج: 10", "timestamp": "2025-06-23T01:00:37.788036"}, {"test_name": "database_stats", "status": "✅ نجح", "passed": true, "description": "إحصائيات قواعد البيانات - PostgreSQL: True, Redis: True", "timestamp": "2025-06-23T01:00:37.803774"}, {"test_name": "server_shutdown", "status": "✅ نجح", "passed": true, "description": "إيق<PERSON><PERSON> الخ<PERSON>م مجدول", "timestamp": "2025-06-23T01:00:37.803774"}], "server_running": true, "test_end_time": "2025-06-23T01:00:37.803774"}