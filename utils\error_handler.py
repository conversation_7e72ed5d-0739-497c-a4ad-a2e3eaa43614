"""
نظام معالجة الأخطاء المتقدم لنظام السكالبينغ
"""

import traceback
import functools
import asyncio
import time
from typing import Any, Callable, Optional, Type, Union, Dict
from datetime import datetime, timedelta
from utils.logger import scalping_logger

logger = scalping_logger.get_logger("error_handler")

# تعريف الأخطاء المخصصة
class ScalpingError(Exception):
    """خطأ عام في نظام السكالبينغ"""
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "SCALPING_ERROR"
        self.details = details or {}
        self.timestamp = datetime.now()

class ConnectionError(ScalpingError):
    """خطأ في الاتصال"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "CONNECTION_ERROR", details)

class DataError(ScalpingError):
    """خطأ في البيانات"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DATA_ERROR", details)

class IndicatorError(ScalpingError):
    """خطأ في المؤشرات"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "INDICATOR_ERROR", details)

class TradingError(ScalpingError):
    """خطأ في التداول"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "TRADING_ERROR", details)

class AIModelError(ScalpingError):
    """خطأ في نماذج الذكاء الاصطناعي"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "AI_MODEL_ERROR", details)

class DatabaseError(ScalpingError):
    """خطأ في قاعدة البيانات"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DATABASE_ERROR", details)

class ConfigurationError(ScalpingError):
    """خطأ في التكوين"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "CONFIGURATION_ERROR", details)

# مُزخرف معالجة الأخطاء
def handle_errors(
    default_return: Any = None,
    raise_on_error: bool = False,
    log_error: bool = True,
    error_types: tuple = (Exception,),
    max_retries: int = 0,
    retry_delay: float = 1.0
):
    """مُزخرف شامل لمعالجة الأخطاء"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except error_types as e:
                    last_exception = e
                    
                    if log_error:
                        error_details = {
                            "function": func.__name__,
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                            "args": str(args)[:200],  # تحديد طول النص
                            "kwargs": str(kwargs)[:200],
                            "error_type": type(e).__name__,
                            "error_message": str(e)
                        }
                        
                        if attempt < max_retries:
                            logger.warning(f"محاولة {attempt + 1} فشلت في {func.__name__}: {str(e)}")
                        else:
                            logger.error(f"فشل نهائي في {func.__name__}: {str(e)}")
                            logger.debug(f"تفاصيل الخطأ: {traceback.format_exc()}")
                    
                    if attempt < max_retries:
                        time.sleep(retry_delay * (2 ** attempt))  # تأخير متزايد
                    elif raise_on_error:
                        raise
            
            return default_return
        return wrapper
    return decorator

# مُزخرف معالجة الأخطاء غير المتزامن
def handle_async_errors(
    default_return: Any = None,
    raise_on_error: bool = False,
    log_error: bool = True,
    error_types: tuple = (Exception,),
    max_retries: int = 0,
    retry_delay: float = 1.0
):
    """مُزخرف معالجة الأخطاء للوظائف غير المتزامنة"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except error_types as e:
                    last_exception = e
                    
                    if log_error:
                        error_details = {
                            "function": func.__name__,
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                            "error_type": type(e).__name__,
                            "error_message": str(e)
                        }
                        
                        if attempt < max_retries:
                            logger.warning(f"محاولة غير متزامنة {attempt + 1} فشلت في {func.__name__}: {str(e)}")
                        else:
                            logger.error(f"فشل نهائي غير متزامن في {func.__name__}: {str(e)}")
                    
                    if attempt < max_retries:
                        await asyncio.sleep(retry_delay * (2 ** attempt))
                    elif raise_on_error:
                        raise
            
            return default_return
        return wrapper
    return decorator

# مُزخرف مراقبة الأداء
def monitor_performance(log_slow_calls: bool = True, slow_threshold: float = 1.0):
    """مُزخرف لمراقبة أداء الوظائف"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if log_slow_calls and execution_time > slow_threshold:
                    logger.warning(f"استدعاء بطيء: {func.__name__} استغرق {execution_time:.2f} ثانية")
                else:
                    logger.debug(f"{func.__name__} استغرق {execution_time:.3f} ثانية")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} فشل بعد {execution_time:.3f} ثانية: {str(e)}")
                raise
        
        return wrapper
    return decorator

# مُزخرف مراقبة الأداء غير المتزامن
def monitor_async_performance(log_slow_calls: bool = True, slow_threshold: float = 1.0):
    """مُزخرف لمراقبة أداء الوظائف غير المتزامنة"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if log_slow_calls and execution_time > slow_threshold:
                    logger.warning(f"استدعاء غير متزامن بطيء: {func.__name__} استغرق {execution_time:.2f} ثانية")
                else:
                    logger.debug(f"{func.__name__} (غير متزامن) استغرق {execution_time:.3f} ثانية")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} (غير متزامن) فشل بعد {execution_time:.3f} ثانية: {str(e)}")
                raise
        
        return wrapper
    return decorator

# فئة إدارة الأخطاء
class ErrorManager:
    """مدير الأخطاء المركزي"""
    
    def __init__(self):
        self.error_counts = {}
        self.error_history = []
        self.max_history_size = 1000
    
    def record_error(self, error: Exception, context: Dict = None):
        """تسجيل خطأ في النظام"""
        error_info = {
            "timestamp": datetime.now(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {},
            "traceback": traceback.format_exc()
        }
        
        # إضافة إلى التاريخ
        self.error_history.append(error_info)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
        
        # تحديث العدادات
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # تسجيل في السجلات
        logger.error(f"خطأ مسجل: {error_type} - {str(error)}")
        if context:
            logger.debug(f"سياق الخطأ: {context}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        total_errors = sum(self.error_counts.values())
        recent_errors = [
            error for error in self.error_history
            if error["timestamp"] > datetime.now() - timedelta(hours=24)
        ]
        
        return {
            "total_errors": total_errors,
            "error_counts": self.error_counts.copy(),
            "recent_errors_24h": len(recent_errors),
            "most_common_error": max(self.error_counts.items(), key=lambda x: x[1])[0] if self.error_counts else None,
            "error_rate_per_hour": len(recent_errors) / 24 if recent_errors else 0
        }
    
    def clear_error_history(self):
        """مسح تاريخ الأخطاء"""
        self.error_history.clear()
        self.error_counts.clear()
        logger.info("تم مسح تاريخ الأخطاء")
    
    def get_recent_errors(self, hours: int = 1) -> list:
        """الحصول على الأخطاء الحديثة"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            error for error in self.error_history
            if error["timestamp"] > cutoff_time
        ]

# إنشاء instance عام لمدير الأخطاء
error_manager = ErrorManager()

# وظائف مساعدة
def validate_config(config: Any, required_fields: list) -> bool:
    """التحقق من صحة التكوين"""
    try:
        for field in required_fields:
            if not hasattr(config, field):
                raise ConfigurationError(f"الحقل المطلوب '{field}' غير موجود في التكوين")
            
            value = getattr(config, field)
            if value is None or (isinstance(value, str) and not value.strip()):
                raise ConfigurationError(f"الحقل '{field}' فارغ أو غير صالح")
        
        return True
    except Exception as e:
        error_manager.record_error(e, {"config_type": type(config).__name__})
        raise

def safe_divide(a: float, b: float, default: float = 0.0) -> float:
    """قسمة آمنة مع تجنب القسمة على صفر"""
    try:
        if b == 0:
            logger.warning(f"محاولة قسمة على صفر: {a} / {b}")
            return default
        return a / b
    except Exception as e:
        logger.error(f"خطأ في القسمة: {a} / {b} - {str(e)}")
        return default

def safe_get_dict_value(dictionary: dict, key: str, default: Any = None) -> Any:
    """الحصول على قيمة من قاموس بشكل آمن"""
    try:
        return dictionary.get(key, default)
    except Exception as e:
        logger.error(f"خطأ في الوصول للقاموس: {key} - {str(e)}")
        return default

# مُزخرف التحقق من الشروط المسبقة
def require_conditions(**conditions):
    """مُزخرف للتحقق من الشروط المسبقة"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for condition_name, condition_func in conditions.items():
                if not condition_func():
                    raise ScalpingError(f"الشرط المسبق '{condition_name}' غير متحقق")
            return func(*args, **kwargs)
        return wrapper
    return decorator
