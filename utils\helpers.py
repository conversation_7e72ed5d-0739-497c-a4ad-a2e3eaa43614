"""
وظائف مساعدة عامة لنظام السكالبينغ
"""

import json
import hashlib
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from utils.logger import scalping_logger
from utils.error_handler import handle_errors, DataError

logger = scalping_logger.get_logger("helpers")

# وظائف التوقيت والتاريخ
def get_current_timestamp() -> datetime:
    """الحصول على الوقت الحالي مع المنطقة الزمنية"""
    return datetime.now(timezone.utc)

def timestamp_to_string(timestamp: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """تحويل timestamp إلى نص"""
    return timestamp.strftime(format_str)

def string_to_timestamp(date_string: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """تحويل نص إلى timestamp"""
    return datetime.strptime(date_string, format_str)

def is_trading_time(current_time: datetime = None, 
                   start_hour: int = 8, 
                   end_hour: int = 22) -> bool:
    """التحقق من وقت التداول"""
    if current_time is None:
        current_time = get_current_timestamp()
    
    current_hour = current_time.hour
    return start_hour <= current_hour <= end_hour

def is_avoid_time(current_time: datetime = None,
                 avoid_first_minutes: int = 5,
                 avoid_last_minutes: int = 5) -> bool:
    """التحقق من الأوقات المتجنبة (أول وآخر دقائق من كل ساعة)"""
    if current_time is None:
        current_time = get_current_timestamp()
    
    minute = current_time.minute
    return minute < avoid_first_minutes or minute >= (60 - avoid_last_minutes)

def get_timeframe_seconds(timeframe: str) -> int:
    """تحويل الإطار الزمني إلى ثواني"""
    timeframe_map = {
        "1m": 60,
        "5m": 300,
        "15m": 900,
        "30m": 1800,
        "1h": 3600,
        "4h": 14400,
        "1d": 86400
    }
    return timeframe_map.get(timeframe, 60)

# وظائف معالجة البيانات
@handle_errors(default_return=None, log_error=True)
def validate_candle_data(candle: Dict[str, Any]) -> bool:
    """التحقق من صحة بيانات الشمعة"""
    required_fields = ['open', 'high', 'low', 'close']

    # التحقق من وجود الحقول المطلوبة
    for field in required_fields:
        if field not in candle:
            raise DataError(f"الحقل المطلوب '{field}' غير موجود في بيانات الشمعة")

    # التحقق من وجود timestamp أو time
    if 'timestamp' not in candle and 'time' not in candle:
        raise DataError("يجب وجود حقل 'timestamp' أو 'time' في بيانات الشمعة")

    # التحقق من صحة الأسعار
    prices = [candle['open'], candle['high'], candle['low'], candle['close']]
    if any(price <= 0 for price in prices):
        raise DataError("الأسعار يجب أن تكون أكبر من صفر")

    # التحقق من منطقية الأسعار
    if candle['high'] < max(candle['open'], candle['close']):
        raise DataError("السعر الأعلى يجب أن يكون أكبر من أو يساوي أسعار الافتتاح والإغلاق")

    if candle['low'] > min(candle['open'], candle['close']):
        raise DataError("السعر الأدنى يجب أن يكون أقل من أو يساوي أسعار الافتتاح والإغلاق")

    return True

@handle_errors(default_return=[], log_error=True)
def clean_candle_data(candles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """تنظيف بيانات الشموع"""
    cleaned_candles = []

    for i, candle in enumerate(candles):
        try:
            if validate_candle_data(candle):
                # تحويل الأسعار إلى float
                # استخدام time إذا كان موجود، وإلا timestamp
                timestamp_value = candle.get('time', candle.get('timestamp'))

                cleaned_candle = {
                    'time': timestamp_value,
                    'timestamp': timestamp_value,  # للتوافق مع النظام القديم
                    'open': float(candle['open']),
                    'high': float(candle['high']),
                    'low': float(candle['low']),
                    'close': float(candle['close']),
                    'volume': int(candle.get('volume', 0))
                }
                cleaned_candles.append(cleaned_candle)
        except Exception as e:
            logger.warning(f"تجاهل شمعة غير صالحة في الفهرس {i}: {str(e)}")
            continue

    return cleaned_candles

def calculate_price_change(current_price: float, previous_price: float) -> Dict[str, float]:
    """حساب تغيير السعر"""
    if previous_price == 0:
        return {"absolute_change": 0, "percentage_change": 0}
    
    absolute_change = current_price - previous_price
    percentage_change = (absolute_change / previous_price) * 100
    
    return {
        "absolute_change": round(absolute_change, 5),
        "percentage_change": round(percentage_change, 2)
    }

def calculate_volatility(prices: List[float], period: int = 20) -> float:
    """حساب التقلب (التباين المعياري للعوائد)"""
    if len(prices) < period:
        return 0.0
    
    # حساب العوائد
    returns = []
    for i in range(1, len(prices)):
        if prices[i-1] != 0:
            return_rate = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(return_rate)
    
    if len(returns) < 2:
        return 0.0
    
    # حساب التباين المعياري
    return float(np.std(returns[-period:]) * np.sqrt(252))  # سنوي

# وظائف التشفير والأمان
def generate_hash(data: str) -> str:
    """إنشاء hash للبيانات"""
    return hashlib.sha256(data.encode()).hexdigest()

def generate_trade_id(asset: str, timestamp: datetime = None) -> str:
    """إنشاء معرف فريد للصفقة"""
    if timestamp is None:
        timestamp = get_current_timestamp()
    
    data = f"{asset}_{timestamp.isoformat()}_{time.time()}"
    return generate_hash(data)[:16]

def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """إخفاء البيانات الحساسة"""
    if len(data) <= visible_chars * 2:
        return mask_char * len(data)
    
    start = data[:visible_chars]
    end = data[-visible_chars:]
    middle = mask_char * (len(data) - visible_chars * 2)
    
    return f"{start}{middle}{end}"

# وظائف التحويل والتنسيق
def format_currency(amount: float, currency: str = "USD", decimal_places: int = 2) -> str:
    """تنسيق المبلغ كعملة"""
    return f"{amount:.{decimal_places}f} {currency}"

def format_percentage(value: float, decimal_places: int = 2) -> str:
    """تنسيق النسبة المئوية"""
    return f"{value:.{decimal_places}f}%"

def format_large_number(number: float) -> str:
    """تنسيق الأرقام الكبيرة"""
    if abs(number) >= 1_000_000:
        return f"{number/1_000_000:.1f}M"
    elif abs(number) >= 1_000:
        return f"{number/1_000:.1f}K"
    else:
        return f"{number:.2f}"

# وظائف الإحصائيات
def calculate_win_rate(wins: int, total_trades: int) -> float:
    """حساب نسبة النجاح"""
    if total_trades == 0:
        return 0.0
    return (wins / total_trades) * 100

def calculate_profit_factor(gross_profit: float, gross_loss: float) -> float:
    """حساب عامل الربح"""
    if gross_loss == 0:
        return float('inf') if gross_profit > 0 else 0.0
    return gross_profit / abs(gross_loss)

def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
    """حساب نسبة شارب"""
    if len(returns) < 2:
        return 0.0
    
    excess_returns = [r - risk_free_rate/252 for r in returns]  # يومي
    mean_excess_return = np.mean(excess_returns)
    std_excess_return = np.std(excess_returns)
    
    if std_excess_return == 0:
        return 0.0
    
    return (mean_excess_return / std_excess_return) * np.sqrt(252)

def calculate_max_drawdown(equity_curve: List[float]) -> Dict[str, float]:
    """حساب أقصى انخفاض"""
    if len(equity_curve) < 2:
        return {"max_drawdown": 0.0, "max_drawdown_percent": 0.0}
    
    peak = equity_curve[0]
    max_drawdown = 0.0
    max_drawdown_percent = 0.0
    
    for value in equity_curve:
        if value > peak:
            peak = value
        
        drawdown = peak - value
        drawdown_percent = (drawdown / peak) * 100 if peak > 0 else 0
        
        if drawdown > max_drawdown:
            max_drawdown = drawdown
            max_drawdown_percent = drawdown_percent
    
    return {
        "max_drawdown": round(max_drawdown, 2),
        "max_drawdown_percent": round(max_drawdown_percent, 2)
    }

# وظائف التحقق والتصديق
def is_valid_asset(asset: str) -> bool:
    """التحقق من صحة رمز الأصل"""
    if not asset or not isinstance(asset, str):
        return False
    
    # التحقق من التنسيق الأساسي
    if len(asset) < 3 or len(asset) > 20:
        return False
    
    # التحقق من الأحرف المسموحة
    allowed_chars = set("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_")
    return all(c in allowed_chars for c in asset.upper())

def is_valid_timeframe(timeframe: int) -> bool:
    """التحقق من صحة الإطار الزمني"""
    valid_timeframes = [60, 300, 900, 1800, 3600]  # 1m, 5m, 15m, 30m, 1h
    return timeframe in valid_timeframes

def is_valid_amount(amount: float, min_amount: float = 1.0, max_amount: float = 1000.0) -> bool:
    """التحقق من صحة مبلغ التداول"""
    return min_amount <= amount <= max_amount

# وظائف الملفات والبيانات
@handle_errors(default_return={}, log_error=True)
def load_json_file(file_path: str) -> Dict[str, Any]:
    """تحميل ملف JSON"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        logger.error(f"ملف غير موجود: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"خطأ في تحليل JSON: {file_path} - {str(e)}")
        return {}

@handle_errors(default_return=False, log_error=True)
def save_json_file(data: Dict[str, Any], file_path: str) -> bool:
    """حفظ بيانات في ملف JSON"""
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"خطأ في حفظ JSON: {file_path} - {str(e)}")
        return False

# وظائف الأداء
class PerformanceTimer:
    """مؤقت الأداء"""
    
    def __init__(self, name: str = "Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        execution_time = self.end_time - self.start_time
        logger.debug(f"{self.name} استغرق {execution_time:.3f} ثانية")
    
    def get_elapsed_time(self) -> float:
        """الحصول على الوقت المنقضي"""
        if self.start_time is None:
            return 0.0
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time

# وظائف التحويل بين أنواع البيانات
def dict_to_dataframe(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """تحويل قائمة من القواميس إلى DataFrame"""
    if not data:
        return pd.DataFrame()
    
    try:
        df = pd.DataFrame(data)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
        return df
    except Exception as e:
        logger.error(f"خطأ في تحويل البيانات إلى DataFrame: {str(e)}")
        return pd.DataFrame()

def dataframe_to_dict(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """تحويل DataFrame إلى قائمة من القواميس"""
    try:
        if df.empty:
            return []
        
        # إعادة تعيين الفهرس إذا كان timestamp
        if df.index.name == 'timestamp':
            df = df.reset_index()
        
        return df.to_dict('records')
    except Exception as e:
        logger.error(f"خطأ في تحويل DataFrame إلى قاموس: {str(e)}")
        return []
