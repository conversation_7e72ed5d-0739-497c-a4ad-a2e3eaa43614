"""
نظام السجلات المتقدم لنظام السكالبينغ
"""

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
import json

class ScalpingLogger:
    """نظام السجلات المتقدم للنظام"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        self.log_dir = Path(log_dir)
        self.log_level = log_level
        self.log_dir.mkdir(exist_ok=True)
        self._setup_logger()
        self._create_subdirectories()
    
    def _create_subdirectories(self):
        """إنشاء المجلدات الفرعية للسجلات"""
        subdirs = ["trading", "errors", "performance", "ai_models", "data"]
        for subdir in subdirs:
            (self.log_dir / subdir).mkdir(exist_ok=True)
    
    def _setup_logger(self):
        """إعداد نظام السجلات المتقدم"""
        # إزالة المعالج الافتراضي
        logger.remove()
        
        # إعداد سجل الطرفية مع تنسيق ملون
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level=self.log_level,
            colorize=True
        )
        
        # إعداد سجل الملفات العام
        logger.add(
            self.log_dir / "scalping_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # إعداد سجل الأخطاء المنفصل
        logger.add(
            self.log_dir / "errors" / "errors_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}\n{exception}",
            level="ERROR",
            rotation="1 day",
            retention="90 days",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        # إعداد سجل التداول المنفصل
        logger.add(
            self.log_dir / "trading" / "trades_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            filter=lambda record: "TRADE" in record["extra"],
            rotation="1 day",
            retention="365 days",
            encoding="utf-8"
        )
        
        # إعداد سجل الأداء
        logger.add(
            self.log_dir / "performance" / "performance_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
            filter=lambda record: "PERFORMANCE" in record["extra"],
            rotation="1 day",
            retention="90 days",
            encoding="utf-8"
        )
        
        # إعداد سجل نماذج الذكاء الاصطناعي
        logger.add(
            self.log_dir / "ai_models" / "ai_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {message}",
            filter=lambda record: "AI" in record["extra"],
            rotation="1 day",
            retention="60 days",
            encoding="utf-8"
        )
        
        # إعداد سجل البيانات
        logger.add(
            self.log_dir / "data" / "data_{time:YYYY-MM-DD}.log",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {message}",
            filter=lambda record: "DATA" in record["extra"],
            rotation="1 day",
            retention="30 days",
            encoding="utf-8"
        )
    
    def get_logger(self, name: str):
        """الحصول على logger مخصص"""
        return logger.bind(name=name)
    
    def log_trade(self, trade_data: Dict[str, Any]):
        """تسجيل عمليات التداول"""
        trade_message = self._format_trade_message(trade_data)
        logger.bind(TRADE=True).info(trade_message)
    
    def log_performance(self, performance_data: Dict[str, Any]):
        """تسجيل بيانات الأداء"""
        performance_message = self._format_performance_message(performance_data)
        logger.bind(PERFORMANCE=True).info(performance_message)
    
    def log_ai_activity(self, ai_data: Dict[str, Any], level: str = "INFO"):
        """تسجيل أنشطة الذكاء الاصطناعي"""
        ai_message = self._format_ai_message(ai_data)
        log_func = getattr(logger.bind(AI=True), level.lower())
        log_func(ai_message)
    
    def log_data_activity(self, data_info: Dict[str, Any], level: str = "INFO"):
        """تسجيل أنشطة البيانات"""
        data_message = self._format_data_message(data_info)
        log_func = getattr(logger.bind(DATA=True), level.lower())
        log_func(data_message)
    
    def _format_trade_message(self, trade_data: Dict[str, Any]) -> str:
        """تنسيق رسالة التداول"""
        return json.dumps({
            "timestamp": datetime.now().isoformat(),
            "trade_id": trade_data.get("trade_id"),
            "asset": trade_data.get("asset"),
            "direction": trade_data.get("direction"),
            "amount": trade_data.get("amount"),
            "entry_price": trade_data.get("entry_price"),
            "confidence": trade_data.get("confidence"),
            "signals": trade_data.get("signals", {}),
            "result": trade_data.get("result"),
            "profit": trade_data.get("profit")
        }, ensure_ascii=False, indent=None)
    
    def _format_performance_message(self, performance_data: Dict[str, Any]) -> str:
        """تنسيق رسالة الأداء"""
        return json.dumps({
            "timestamp": datetime.now().isoformat(),
            "total_trades": performance_data.get("total_trades"),
            "winning_trades": performance_data.get("winning_trades"),
            "losing_trades": performance_data.get("losing_trades"),
            "win_rate": performance_data.get("win_rate"),
            "total_profit": performance_data.get("total_profit"),
            "daily_profit": performance_data.get("daily_profit"),
            "max_drawdown": performance_data.get("max_drawdown"),
            "consecutive_losses": performance_data.get("consecutive_losses")
        }, ensure_ascii=False, indent=None)
    
    def _format_ai_message(self, ai_data: Dict[str, Any]) -> str:
        """تنسيق رسالة الذكاء الاصطناعي"""
        return json.dumps({
            "timestamp": datetime.now().isoformat(),
            "model_name": ai_data.get("model_name"),
            "action": ai_data.get("action"),  # training, prediction, evaluation
            "asset": ai_data.get("asset"),
            "prediction": ai_data.get("prediction"),
            "confidence": ai_data.get("confidence"),
            "features_used": ai_data.get("features_used"),
            "model_performance": ai_data.get("model_performance"),
            "training_samples": ai_data.get("training_samples"),
            "execution_time": ai_data.get("execution_time")
        }, ensure_ascii=False, indent=None)
    
    def _format_data_message(self, data_info: Dict[str, Any]) -> str:
        """تنسيق رسالة البيانات"""
        return json.dumps({
            "timestamp": datetime.now().isoformat(),
            "action": data_info.get("action"),  # collect, process, store, retrieve
            "asset": data_info.get("asset"),
            "timeframe": data_info.get("timeframe"),
            "data_count": data_info.get("data_count"),
            "data_quality": data_info.get("data_quality"),
            "processing_time": data_info.get("processing_time"),
            "source": data_info.get("source"),
            "errors": data_info.get("errors")
        }, ensure_ascii=False, indent=None)
    
    def create_session_log(self, session_id: str) -> str:
        """إنشاء ملف سجل لجلسة معينة"""
        session_log_path = self.log_dir / f"session_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logger.add(
            session_log_path,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {message}",
            filter=lambda record: record["extra"].get("session_id") == session_id,
            level="DEBUG",
            encoding="utf-8"
        )
        
        return str(session_log_path)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات السجلات"""
        stats = {
            "log_directory": str(self.log_dir),
            "total_log_files": 0,
            "log_files_by_type": {},
            "total_size_mb": 0
        }
        
        for log_file in self.log_dir.rglob("*.log"):
            stats["total_log_files"] += 1
            file_size = log_file.stat().st_size / (1024 * 1024)  # MB
            stats["total_size_mb"] += file_size
            
            # تصنيف حسب النوع
            parent_dir = log_file.parent.name
            if parent_dir not in stats["log_files_by_type"]:
                stats["log_files_by_type"][parent_dir] = {"count": 0, "size_mb": 0}
            
            stats["log_files_by_type"][parent_dir]["count"] += 1
            stats["log_files_by_type"][parent_dir]["size_mb"] += file_size
        
        stats["total_size_mb"] = round(stats["total_size_mb"], 2)
        return stats
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """تنظيف السجلات القديمة"""
        from datetime import timedelta
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        deleted_count = 0
        for log_file in self.log_dir.rglob("*.log"):
            file_date = datetime.fromtimestamp(log_file.stat().st_mtime)
            if file_date < cutoff_date:
                log_file.unlink()
                deleted_count += 1
        
        logger.info(f"تم حذف {deleted_count} ملف سجل قديم")
        return deleted_count

# إنشاء instance عام للنظام
scalping_logger = ScalpingLogger()

# إنشاء loggers مخصصة للمكونات المختلفة
system_logger = scalping_logger.get_logger("system")
trading_logger = scalping_logger.get_logger("trading")
data_logger = scalping_logger.get_logger("data")
ai_logger = scalping_logger.get_logger("ai")
performance_logger = scalping_logger.get_logger("performance")
